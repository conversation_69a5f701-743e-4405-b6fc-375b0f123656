--
-- PostgreSQL database dump
--

-- Dumped from database version 16.0

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
-- SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY treatment_fee_list DROP CONSTRAINT IF EXISTS treatment_fee_list_fk;
ALTER TABLE IF EXISTS ONLY treatment_department DROP CONSTRAINT IF EXISTS treatment_department_fk_4;
ALTER TABLE IF EXISTS ONLY treatment_department DROP CONSTRAINT IF EXISTS treatment_department_fk_3;
ALTER TABLE IF EXISTS ONLY template_document DROP CONSTRAINT IF EXISTS template_document_fk;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_task_status_id_fkey;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_task_category_id_fkey;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_responsible_staff_id_fkey;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_patient_id_fk;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY task_history DROP CONSTRAINT IF EXISTS task_history_task_id_fkey;
ALTER TABLE IF EXISTS ONLY task_history DROP CONSTRAINT IF EXISTS task_history_edited_staff_id_fkey;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_created_staff_id_fkey;
ALTER TABLE IF EXISTS ONLY task_comment DROP CONSTRAINT IF EXISTS task_comment_task_id_fkey;
ALTER TABLE IF EXISTS ONLY survey DROP CONSTRAINT IF EXISTS survey_fk;
ALTER TABLE IF EXISTS ONLY survey_answer DROP CONSTRAINT IF EXISTS survey_answer_fk_2;
ALTER TABLE IF EXISTS ONLY survey_answer DROP CONSTRAINT IF EXISTS survey_answer_fk_1;
ALTER TABLE IF EXISTS ONLY survey_answer DROP CONSTRAINT IF EXISTS survey_answer_fk;
ALTER TABLE IF EXISTS ONLY staff_message DROP CONSTRAINT IF EXISTS staff_message_fk_1;
ALTER TABLE IF EXISTS ONLY staff_message DROP CONSTRAINT IF EXISTS staff_message_fk;
ALTER TABLE IF EXISTS ONLY staff_message_channel_member DROP CONSTRAINT IF EXISTS staff_message_channel_member_fk;
ALTER TABLE IF EXISTS ONLY staff_message_channel DROP CONSTRAINT IF EXISTS staff_message_channel_fk;
ALTER TABLE IF EXISTS ONLY signup_user DROP CONSTRAINT IF EXISTS signup_user_user_id_fkey;
ALTER TABLE IF EXISTS ONLY signup_user DROP CONSTRAINT IF EXISTS signup_user_hp_id_fkey;
ALTER TABLE IF EXISTS ONLY schema_image DROP CONSTRAINT IF EXISTS schema_image_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY reserve DROP CONSTRAINT IF EXISTS reserve_patient_id_fk;
ALTER TABLE IF EXISTS ONLY reserve_detail_history DROP CONSTRAINT IF EXISTS reserve_detail_history_fk_2;
ALTER TABLE IF EXISTS ONLY reserve_detail_history DROP CONSTRAINT IF EXISTS reserve_detail_history_fk_1;
ALTER TABLE IF EXISTS ONLY reserve_detail_history DROP CONSTRAINT IF EXISTS reserve_detail_history_fk;
ALTER TABLE IF EXISTS ONLY reserve_detail DROP CONSTRAINT IF EXISTS reserve_detail_fk4;
ALTER TABLE IF EXISTS ONLY reserve_detail DROP CONSTRAINT IF EXISTS reserve_detail_fk3;
ALTER TABLE IF EXISTS ONLY reserve_detail DROP CONSTRAINT IF EXISTS reserve_detail_fk2;
ALTER TABLE IF EXISTS ONLY reserve_detail DROP CONSTRAINT IF EXISTS reserve_detail_fk1;
ALTER TABLE IF EXISTS ONLY read_system_notice DROP CONSTRAINT IF EXISTS read_system_notice_fk_1;
ALTER TABLE IF EXISTS ONLY pt_inf DROP CONSTRAINT IF EXISTS pt_inf_fk;
ALTER TABLE IF EXISTS ONLY prescription_reception DROP CONSTRAINT IF EXISTS prescription_reception_hp_id_fkey;
ALTER TABLE IF EXISTS ONLY prescription_image DROP CONSTRAINT IF EXISTS prescription_image_prescription_reception_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_staff DROP CONSTRAINT IF EXISTS portal_hospital_staff_fk;
ALTER TABLE IF EXISTS ONLY portal_hospital_notification DROP CONSTRAINT IF EXISTS portal_hospital_notification_portal_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_hospital DROP CONSTRAINT IF EXISTS portal_hospital_hp_inf_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_examination DROP CONSTRAINT IF EXISTS portal_hospital_examination_portal_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_examination DROP CONSTRAINT IF EXISTS portal_hospital_examination_examination_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_customer_survey DROP CONSTRAINT IF EXISTS portal_customer_survey_fk;
ALTER TABLE IF EXISTS ONLY portal_customer_pharmacy DROP CONSTRAINT IF EXISTS portal_customer_pharmacy_reserve_fk;
ALTER TABLE IF EXISTS ONLY portal_customer_pharmacy DROP CONSTRAINT IF EXISTS portal_customer_pharmacy_portal_customer_fk;
ALTER TABLE IF EXISTS ONLY portal_customer_payment DROP CONSTRAINT IF EXISTS portal_customer_payment_fk;
ALTER TABLE IF EXISTS ONLY portal_customer_payment_card DROP CONSTRAINT IF EXISTS portal_customer_payment_card_fk;
ALTER TABLE IF EXISTS ONLY portal_customer_file DROP CONSTRAINT IF EXISTS portal_customer_file_fk;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_status_history DROP CONSTRAINT IF EXISTS pharmacy_reserve_status_history_pharmacy_reserve_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_status_history DROP CONSTRAINT IF EXISTS pharmacy_reserve_status_history_pharmacist_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve DROP CONSTRAINT IF EXISTS pharmacy_reserve_reserve_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve DROP CONSTRAINT IF EXISTS pharmacy_reserve_pharmacist_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve DROP CONSTRAINT IF EXISTS pharmacy_reserve_patient_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_detail DROP CONSTRAINT IF EXISTS pharmacy_reserve_detail_pharmacy_reserve_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_detail DROP CONSTRAINT IF EXISTS pharmacy_reserve_detail_patient_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_detail DROP CONSTRAINT IF EXISTS pharmacy_reserve_detail_fk;
ALTER TABLE IF EXISTS ONLY pharmacy_patient_file DROP CONSTRAINT IF EXISTS pharmacy_patient_file_pharmacy_reserve_detail_fk;
ALTER TABLE IF EXISTS ONLY pharmacy_patient_file DROP CONSTRAINT IF EXISTS pharmacy_patient_file_fkey_2;
ALTER TABLE IF EXISTS ONLY pharmacy_holiday DROP CONSTRAINT IF EXISTS pharmacy_holiday_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_desired_date DROP CONSTRAINT IF EXISTS pharmacy_desired_date_pharmacy_reserve_id_fkey;
ALTER TABLE IF EXISTS ONLY pharmacy_delivery_history DROP CONSTRAINT IF EXISTS pharmacy_delivery_history_pharmacy_reserve_fk;
ALTER TABLE IF EXISTS ONLY pharmacy_delivery_address DROP CONSTRAINT IF EXISTS pharmacy_delivery_address_fk_1;
ALTER TABLE IF EXISTS ONLY pharmacy_delivery_address DROP CONSTRAINT IF EXISTS pharmacy_delivery_address_fk;
ALTER TABLE IF EXISTS ONLY payment_pharmacy_detail DROP CONSTRAINT IF EXISTS payment_pharmacy_detail_pharmacy_reserve_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_pharmacy_detail_history DROP CONSTRAINT IF EXISTS payment_pharmacy_detail_history_pharmacy_reserve_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_pharmacy_detail_history DROP CONSTRAINT IF EXISTS payment_pharmacy_detail_history_payment_pharmacy_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail_history DROP CONSTRAINT IF EXISTS payment_clinic_detail_history_treatment_department_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail_history DROP CONSTRAINT IF EXISTS payment_clinic_detail_history_treatment_category_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail_history DROP CONSTRAINT IF EXISTS payment_clinic_detail_history_reserve_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail_history DROP CONSTRAINT IF EXISTS payment_clinic_detail_history_payment_clinic_detail_id_fkey;
ALTER TABLE IF EXISTS ONLY patient_message DROP CONSTRAINT IF EXISTS patient_message_fk_1;
ALTER TABLE IF EXISTS ONLY patient_message DROP CONSTRAINT IF EXISTS patient_message_fk;
ALTER TABLE IF EXISTS ONLY patient_message_channel_member DROP CONSTRAINT IF EXISTS patient_message_channel_member_fk;
ALTER TABLE IF EXISTS ONLY patient_message_channel DROP CONSTRAINT IF EXISTS patient_message_channel_fk;
ALTER TABLE IF EXISTS ONLY patient_memo DROP CONSTRAINT IF EXISTS patient_memo_patient_id_fk;
ALTER TABLE IF EXISTS ONLY operator_group_permission DROP CONSTRAINT IF EXISTS operator_group_permission_fk_2;
ALTER TABLE IF EXISTS ONLY operator_group_permission DROP CONSTRAINT IF EXISTS operator_group_permission_fk_1;
ALTER TABLE IF EXISTS ONLY meeting DROP CONSTRAINT IF EXISTS meeting_fkey4;
ALTER TABLE IF EXISTS ONLY meeting DROP CONSTRAINT IF EXISTS meeting_fkey3;
ALTER TABLE IF EXISTS ONLY meeting DROP CONSTRAINT IF EXISTS meeting_fkey2;
ALTER TABLE IF EXISTS ONLY meeting DROP CONSTRAINT IF EXISTS meeting_fkey;
ALTER TABLE IF EXISTS ONLY m_prompt DROP CONSTRAINT IF EXISTS m_prompt_llm_model_fkey;
ALTER TABLE IF EXISTS ONLY line_account DROP CONSTRAINT IF EXISTS line_account_portal_customer_fk;
ALTER TABLE IF EXISTS ONLY hp_white_board DROP CONSTRAINT IF EXISTS hp_white_board_fk_1;
ALTER TABLE IF EXISTS ONLY hp_schedule DROP CONSTRAINT IF EXISTS hp_schedule_fk_1;
ALTER TABLE IF EXISTS ONLY hp_permission DROP CONSTRAINT IF EXISTS hp_permission_fk_2;
ALTER TABLE IF EXISTS ONLY hp_permission DROP CONSTRAINT IF EXISTS hp_permission_fk_1;
ALTER TABLE IF EXISTS ONLY hp_fincode_info DROP CONSTRAINT IF EXISTS hp_fincode_info_fk;
ALTER TABLE IF EXISTS ONLY hospital_treatment DROP CONSTRAINT IF EXISTS hospital_treatment_fk;
ALTER TABLE IF EXISTS ONLY portal_hospital_favorite DROP CONSTRAINT IF EXISTS hospital_favorite_portal_hospital_id_fkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_favorite DROP CONSTRAINT IF EXISTS hospital_favorite_customer_id_fkey;
ALTER TABLE IF EXISTS ONLY freee_dummy_payment DROP CONSTRAINT IF EXISTS freee_dummy_payment_deal_id_fkey;
ALTER TABLE IF EXISTS ONLY freee_dummy_detail DROP CONSTRAINT IF EXISTS freee_dummy_detail_deal_id_fkey;
ALTER TABLE IF EXISTS ONLY treatment_department DROP CONSTRAINT IF EXISTS fk_treatment_department_2;
ALTER TABLE IF EXISTS ONLY treatment_department DROP CONSTRAINT IF EXISTS fk_treatment_department_1;
ALTER TABLE IF EXISTS ONLY task_status DROP CONSTRAINT IF EXISTS fk_task_status_hospital_id;
ALTER TABLE IF EXISTS ONLY task_file DROP CONSTRAINT IF EXISTS fk_task_file_1;
ALTER TABLE IF EXISTS ONLY task_comment_file DROP CONSTRAINT IF EXISTS fk_task_comment_file_task_comment_id;
ALTER TABLE IF EXISTS ONLY task_category DROP CONSTRAINT IF EXISTS fk_task_category_hospital_id;
ALTER TABLE IF EXISTS ONLY portal_treat_drug DROP CONSTRAINT IF EXISTS fk_t_treat_drug_1;
ALTER TABLE IF EXISTS ONLY portal_prescription_drug DROP CONSTRAINT IF EXISTS fk_t_presc_drug_3;
ALTER TABLE IF EXISTS ONLY portal_prescription_drug DROP CONSTRAINT IF EXISTS fk_t_presc_drug_1;
ALTER TABLE IF EXISTS ONLY portal_customer_login DROP CONSTRAINT IF EXISTS fk_t_login_1;
ALTER TABLE IF EXISTS ONLY portal_hosp_tag DROP CONSTRAINT IF EXISTS fk_t_hosp_tag_2;
ALTER TABLE IF EXISTS ONLY portal_hosp_tag DROP CONSTRAINT IF EXISTS fk_t_hosp_tag_1;
ALTER TABLE IF EXISTS ONLY portal_hosp_station DROP CONSTRAINT IF EXISTS fk_t_hosp_station_2;
ALTER TABLE IF EXISTS ONLY portal_hosp_station DROP CONSTRAINT IF EXISTS fk_t_hosp_station_1;
ALTER TABLE IF EXISTS ONLY portal_hosp_picture DROP CONSTRAINT IF EXISTS fk_t_hosp_pict_3;
ALTER TABLE IF EXISTS ONLY portal_hosp_picture DROP CONSTRAINT IF EXISTS fk_t_hosp_pict_2;
ALTER TABLE IF EXISTS ONLY portal_exam_detail DROP CONSTRAINT IF EXISTS fk_t_exam_dtl_1;
ALTER TABLE IF EXISTS ONLY portal_customer_delivery_address DROP CONSTRAINT IF EXISTS fk_t_delivery_address_1;
ALTER TABLE IF EXISTS ONLY portal_withdrawal_reason DROP CONSTRAINT IF EXISTS fk_t_cust_usage_ed_reason_1;
ALTER TABLE IF EXISTS ONLY portal_customer DROP CONSTRAINT IF EXISTS fk_t_cust_3;
ALTER TABLE IF EXISTS ONLY portal_contents_pub DROP CONSTRAINT IF EXISTS fk_t_contents_pub_3;
ALTER TABLE IF EXISTS ONLY portal_contents_pub DROP CONSTRAINT IF EXISTS fk_t_contents_pub_1;
ALTER TABLE IF EXISTS ONLY portal_business_time DROP CONSTRAINT IF EXISTS fk_t_business_time_1;
ALTER TABLE IF EXISTS ONLY survey_answer_no_patient DROP CONSTRAINT IF EXISTS fk_survey_id;
ALTER TABLE IF EXISTS ONLY portal_staff_picture DROP CONSTRAINT IF EXISTS fk_portal_staff_picture_2;
ALTER TABLE IF EXISTS ONLY portal_staff_picture DROP CONSTRAINT IF EXISTS fk_portal_staff_picture_1;
ALTER TABLE IF EXISTS ONLY portal_hospital_specialist DROP CONSTRAINT IF EXISTS fk_portal_hospital_specialist_2;
ALTER TABLE IF EXISTS ONLY portal_hospital_specialist DROP CONSTRAINT IF EXISTS fk_portal_hospital_specialist_1;
ALTER TABLE IF EXISTS ONLY portal_customer_browser DROP CONSTRAINT IF EXISTS fk_portal_customer_browser_1;
ALTER TABLE IF EXISTS ONLY portal_ai_chat_content DROP CONSTRAINT IF EXISTS fk_portal_customer;
ALTER TABLE IF EXISTS ONLY portal_agree_log DROP CONSTRAINT IF EXISTS fk_portal_agree_log_2;
ALTER TABLE IF EXISTS ONLY portal_agree_log DROP CONSTRAINT IF EXISTS fk_portal_agree_log_1;
ALTER TABLE IF EXISTS ONLY opesys_reserve_memo DROP CONSTRAINT IF EXISTS fk_opesys_reserve_memo_2;
ALTER TABLE IF EXISTS ONLY opesys_reserve_memo DROP CONSTRAINT IF EXISTS fk_opesys_reserve_memo_1;
ALTER TABLE IF EXISTS ONLY opesys_clinic_memo DROP CONSTRAINT IF EXISTS fk_opesys_clinic_memo_2;
ALTER TABLE IF EXISTS ONLY opesys_clinic_memo DROP CONSTRAINT IF EXISTS fk_opesys_clinic_memo_1;
ALTER TABLE IF EXISTS ONLY portal_m_station DROP CONSTRAINT IF EXISTS fk_m_station_1;
ALTER TABLE IF EXISTS ONLY portal_m_railline DROP CONSTRAINT IF EXISTS fk_m_railline_1;
ALTER TABLE IF EXISTS ONLY portal_m_drug DROP CONSTRAINT IF EXISTS fk_m_drug_1;
ALTER TABLE IF EXISTS ONLY portal_m_drug01_category DROP CONSTRAINT IF EXISTS fk_m_drug01_catg_1;
ALTER TABLE IF EXISTS ONLY hospital_treatment DROP CONSTRAINT IF EXISTS fk_hospital_treatment_treatment_category_id;
ALTER TABLE IF EXISTS ONLY calendar_treatment DROP CONSTRAINT IF EXISTS fk_calendar_treatment_2;
ALTER TABLE IF EXISTS ONLY calendar_basic_setting DROP CONSTRAINT IF EXISTS fk_calendar_basic_setting_1;
ALTER TABLE IF EXISTS ONLY exam_time_slot DROP CONSTRAINT IF EXISTS exam_time_slot_fk;
ALTER TABLE IF EXISTS ONLY comment_history DROP CONSTRAINT IF EXISTS comment_history_comment_id_fkey;
ALTER TABLE IF EXISTS ONLY client_certificate DROP CONSTRAINT IF EXISTS client_certificate_hospital_id_fk;
ALTER TABLE IF EXISTS ONLY calendar_treatment DROP CONSTRAINT IF EXISTS calendar_treatment_fk;
ALTER TABLE IF EXISTS ONLY calendar DROP CONSTRAINT IF EXISTS calendar_fkey;
ALTER TABLE IF EXISTS ONLY agent_config DROP CONSTRAINT IF EXISTS agent_config_type_fk;
DROP TRIGGER IF EXISTS trigger_set_update_date_timestamp_user_mst ON user_mst;
DROP TRIGGER IF EXISTS trigger_set_update_date_timestamp_pt_inf ON pt_inf;
DROP TRIGGER IF EXISTS trigger_set_update_date_timestamp_permission_mst ON permission_mst;
DROP TRIGGER IF EXISTS trigger_set_update_date_timestamp_hp_inf ON hp_inf;
DROP TRIGGER IF EXISTS trigger_set_timestamp_withdrawal_reason ON portal_withdrawal_reason;
DROP TRIGGER IF EXISTS trigger_set_timestamp_treatment_fee_list ON treatment_fee_list;
DROP TRIGGER IF EXISTS trigger_set_timestamp_treatment_department ON treatment_department;
DROP TRIGGER IF EXISTS trigger_set_timestamp_treat_drug ON portal_treat_drug;
DROP TRIGGER IF EXISTS trigger_set_timestamp_template_document ON template_document;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_status ON task_status;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_history ON task_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_file ON task_file;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_comment_file ON task_comment_file;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_comment ON task_comment;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task_category ON task_category;
DROP TRIGGER IF EXISTS trigger_set_timestamp_task ON task;
DROP TRIGGER IF EXISTS trigger_set_timestamp_survey_answer ON agent_token;
DROP TRIGGER IF EXISTS trigger_set_timestamp_survey ON survey;
DROP TRIGGER IF EXISTS trigger_set_timestamp_staff_specialist ON portal_hospital_specialist;
DROP TRIGGER IF EXISTS trigger_set_timestamp_staff_message_channel_member ON staff_message_channel_member;
DROP TRIGGER IF EXISTS trigger_set_timestamp_staff_message_channel ON staff_message_channel;
DROP TRIGGER IF EXISTS trigger_set_timestamp_staff_message ON agent_token;
DROP TRIGGER IF EXISTS trigger_set_timestamp_schema_image ON schema_image;
DROP TRIGGER IF EXISTS trigger_set_timestamp_reserve_detail_history ON reserve_detail_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_reserve_detail ON reserve_detail;
DROP TRIGGER IF EXISTS trigger_set_timestamp_reserve ON reserve;
DROP TRIGGER IF EXISTS trigger_set_timestamp_prompt_log ON prompt_log;
DROP TRIGGER IF EXISTS trigger_set_timestamp_prompt_history ON prompt_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_prescription_reception ON prescription_reception;
DROP TRIGGER IF EXISTS trigger_set_timestamp_prescription_image ON prescription_image;
DROP TRIGGER IF EXISTS trigger_set_timestamp_prescription_drug ON portal_prescription_drug;
DROP TRIGGER IF EXISTS trigger_set_timestamp_potal_hospital_notification ON portal_hospital_notification;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_treatment_dept_grp ON portal_treatment_dept_grp;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_staff_picture ON portal_staff_picture;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_m_examination ON portal_m_examination;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_m_agree ON portal_m_agree;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_hospital_staff ON portal_hospital_staff;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_hospital_examination ON portal_hospital_examination;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_survey ON portal_customer_survey;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_pharmacy ON portal_customer_pharmacy;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_payment_card ON portal_customer_payment_card;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_payment ON portal_customer_payment;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_file ON portal_customer_file;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_customer_browser ON portal_customer_browser;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_business_time ON portal_business_time;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_ai_chat_content ON portal_ai_chat_content;
DROP TRIGGER IF EXISTS trigger_set_timestamp_portal_agree_log ON portal_agree_log;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pict ON portal_pict;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_reserve_status_history ON pharmacy_reserve_status_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_patient_file ON pharmacy_patient_file;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_holiday ON pharmacy_holiday;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_desired_date ON pharmacy_desired_date;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_delivery_history ON pharmacy_delivery_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_pharmacy_delivery_address ON pharmacy_delivery_address;
DROP TRIGGER IF EXISTS trigger_set_timestamp_payment_pharmacy_detail_history ON payment_pharmacy_detail_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_payment_pharmacy_detail ON payment_pharmacy_detail;
DROP TRIGGER IF EXISTS trigger_set_timestamp_payment_fincode_order ON payment_fincode_order;
DROP TRIGGER IF EXISTS trigger_set_timestamp_payment_clinic_detail_history ON payment_clinic_detail_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_payment_clinic_detail ON payment_clinic_detail;
DROP TRIGGER IF EXISTS trigger_set_timestamp_patient_message_channel_member ON patient_message_channel_member;
DROP TRIGGER IF EXISTS trigger_set_timestamp_patient_message_channel ON patient_message_channel;
DROP TRIGGER IF EXISTS trigger_set_timestamp_patient_message ON patient_message;
DROP TRIGGER IF EXISTS trigger_set_timestamp_patient_memo ON patient_memo;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_reserve_memo ON opesys_reserve_memo;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_memo ON opesys_memo;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_info_change_detail_portal ON opesys_info_change_detail_portal;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_info_change_detail_clinic ON opesys_info_change_detail_clinic;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_info_change ON opesys_info_change;
DROP TRIGGER IF EXISTS trigger_set_timestamp_opesys_clinic_memo ON opesys_clinic_memo;
DROP TRIGGER IF EXISTS trigger_set_timestamp_operator_group_permission ON operator_group_permission;
DROP TRIGGER IF EXISTS trigger_set_timestamp_message_item ON message_item;
DROP TRIGGER IF EXISTS trigger_set_timestamp_meeting ON meeting;
DROP TRIGGER IF EXISTS trigger_set_timestamp_mail_verification_requests ON mail_verification_requests;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_treatment_category ON m_treatment_category;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_template_survey ON m_template_survey;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_template_sms ON m_template_sms;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_template_mail ON m_template_mail;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_template_document_parameter ON m_template_document_parameter;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_tag ON portal_m_tag;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_station ON portal_m_station;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_specialist ON portal_m_specialist;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_railline_company ON portal_m_railline_company;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_railline ON portal_m_railline;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_prefecture ON portal_m_prefecture;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_operator_group ON m_operator_group;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_mail_delivery_settings ON m_mail_delivery_settings;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_llm_model ON m_llm_model;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_drug02_category ON portal_m_drug02_category;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_drug01_category ON portal_m_drug01_category;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_drug ON portal_m_drug;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_city ON portal_m_city;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_application_feature ON m_application_feature;
DROP TRIGGER IF EXISTS trigger_set_timestamp_m_agree ON m_agree;
DROP TRIGGER IF EXISTS trigger_set_timestamp_line_account ON line_account;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hp_permission ON hp_permission;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hp_fincode_info ON hp_fincode_info;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hospital_treatment ON hospital_treatment;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hospital_favorite ON portal_hospital_favorite;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hospital ON portal_hospital;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hosp_tag ON portal_hosp_tag;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hosp_station ON portal_hosp_station;
DROP TRIGGER IF EXISTS trigger_set_timestamp_hosp_picture ON portal_hosp_picture;
DROP TRIGGER IF EXISTS trigger_set_timestamp_fax_number ON fax_number;
DROP TRIGGER IF EXISTS trigger_set_timestamp_fax ON fax;
DROP TRIGGER IF EXISTS trigger_set_timestamp_exam_time_slot ON exam_time_slot;
DROP TRIGGER IF EXISTS trigger_set_timestamp_exam_detail ON portal_exam_detail;
DROP TRIGGER IF EXISTS trigger_set_timestamp_every_patient_message_setting ON every_patient_message_setting;
DROP TRIGGER IF EXISTS trigger_set_timestamp_deleted_task_comment ON deleted_task_comment;
DROP TRIGGER IF EXISTS trigger_set_timestamp_deleted_task ON deleted_task;
DROP TRIGGER IF EXISTS trigger_set_timestamp_customer_social_login ON portal_customer_social_login;
DROP TRIGGER IF EXISTS trigger_set_timestamp_customer_login ON portal_customer_login;
DROP TRIGGER IF EXISTS trigger_set_timestamp_customer_delivery_address ON portal_customer_delivery_address;
DROP TRIGGER IF EXISTS trigger_set_timestamp_customer ON portal_customer;
DROP TRIGGER IF EXISTS trigger_set_timestamp_contents_pub ON portal_contents_pub;
DROP TRIGGER IF EXISTS trigger_set_timestamp_contents ON portal_contents;
DROP TRIGGER IF EXISTS trigger_set_timestamp_comment_history ON comment_history;
DROP TRIGGER IF EXISTS trigger_set_timestamp_client_certificate ON client_certificate;
DROP TRIGGER IF EXISTS trigger_set_timestamp_calendar_treatment ON calendar_treatment;
DROP TRIGGER IF EXISTS trigger_set_timestamp_calendar_basic_setting ON calendar_basic_setting;
DROP TRIGGER IF EXISTS trigger_set_timestamp_calendar ON calendar;
DROP TRIGGER IF EXISTS trigger_set_timestamp_agree_log ON agree_log;
DROP TRIGGER IF EXISTS trigger_set_timestamp_agent_token ON agent_token;
DROP TRIGGER IF EXISTS trigger_set_timestamp ON comment_history;
DROP TRIGGER IF EXISTS trigger_before_update_prompt ON m_prompt;
DROP TRIGGER IF EXISTS trigger_before_insert_prompt ON m_prompt;
DROP TRIGGER IF EXISTS pt_inf_history_trigger ON pt_inf;
DROP TRIGGER IF EXISTS pharmacy_reserve_detail_status_update ON pharmacy_reserve_detail;
DROP TRIGGER IF EXISTS copy_id_trigger ON user_mst;
DROP INDEX IF EXISTS unique_customer_id_line_user_id_not_deleted;
DROP INDEX IF EXISTS treatment_department_hospital_id_idx;
DROP INDEX IF EXISTS survey_answer_no_patient_hospital_id_idx;
DROP INDEX IF EXISTS staff_message_idx_1;
DROP INDEX IF EXISTS staff_message_channel_member_idx_1;
DROP INDEX IF EXISTS staff_message_channel_idx_2;
DROP INDEX IF EXISTS staff_message_channel_idx_1;
DROP INDEX IF EXISTS signup_user_idx_1;
DROP INDEX IF EXISTS reserve_detail_patient_id_reserve_type_idx;
DROP INDEX IF EXISTS reserve_detail_calendar_treatment_id_;
DROP INDEX IF EXISTS pt_inf_ukey01;
DROP INDEX IF EXISTS pt_inf_pt_inf_idx01;
DROP INDEX IF EXISTS pt_inf_hp_id_idx;
DROP INDEX IF EXISTS prompt_history_idx_1;
DROP INDEX IF EXISTS portal_m_tag_name_idx;
DROP INDEX IF EXISTS permission_mst_permission_mst_pkey;
DROP INDEX IF EXISTS patient_message_idx_1;
DROP INDEX IF EXISTS patient_message_channel_idx_1;
DROP INDEX IF EXISTS operator_group_operator_idx_1;
DROP INDEX IF EXISTS message_item_idx_1;
DROP INDEX IF EXISTS m_treatment_category_treatment_category_group_id_idx;
DROP INDEX IF EXISTS m_prompt_idx_1;
DROP INDEX IF EXISTS m_medical_institution_idx_1;
DROP INDEX IF EXISTS ix_user_mst_user_id;
DROP INDEX IF EXISTS ix_user_mst_hp_id_login_id;
DROP INDEX IF EXISTS ix_payment_pharmacy_detail_pharmacy_reserve_detail_id;
DROP INDEX IF EXISTS ix_payment_pharmacy_detail_payment_status;
DROP INDEX IF EXISTS ix_payment_pharmacy_detail_patient_id;
DROP INDEX IF EXISTS ix_payment_pharmacy_detail_history_pharmacy_reserve_detail_id;
DROP INDEX IF EXISTS ix_payment_clinic_detail_reserve_detail_id;
DROP INDEX IF EXISTS ix_payment_clinic_detail_payment_status;
DROP INDEX IF EXISTS ix_payment_clinic_detail_patient_id_exam_dete;
DROP INDEX IF EXISTS ix_payment_clinic_detail_customer_id;
DROP INDEX IF EXISTS is_deleted_idx;
DROP INDEX IF EXISTS idx_unique_group_id_treatment_dept_id;
DROP INDEX IF EXISTS idx_survey_hospital_id_created_at_updated_at;
DROP INDEX IF EXISTS idx_survey_answer_no_patient_survey_id_created_at;
DROP INDEX IF EXISTS idx_survey_answer_no_patient_survey_id;
DROP INDEX IF EXISTS idx_staff_id_is_verified;
DROP INDEX IF EXISTS idx_reserve_detail_history_exam_time_slot_id;
DROP INDEX IF EXISTS idx_reserve_detail_exam_time_slot_id;
DROP INDEX IF EXISTS idx_prompt_log_user_deleted_order;
DROP INDEX IF EXISTS idx_portal_m_prefecture_name;
DROP INDEX IF EXISTS idx_portal_m_city_name;
DROP INDEX IF EXISTS idx_hp_inf_status;
DROP INDEX IF EXISTS idx_hospital_favorite_session_portal;
DROP INDEX IF EXISTS idx_hospital_favorite_session_import;
DROP INDEX IF EXISTS idx_hospital_favorite_scuel_id;
DROP INDEX IF EXISTS idx_hospital_favorite_customer_portal;
DROP INDEX IF EXISTS idx_hospital_favorite_customer_import;
DROP INDEX IF EXISTS hospital_id_idx;
DROP INDEX IF EXISTS hospital_favorite_portal_session_unique;
DROP INDEX IF EXISTS hospital_favorite_portal_customer_unique;
DROP INDEX IF EXISTS hospital_favorite_import_session_unique;
DROP INDEX IF EXISTS hospital_favorite_import_customer_unique;
DROP INDEX IF EXISTS exam_time_slot_exam_start_date_end_date_idx;
ALTER TABLE IF EXISTS ONLY treatment_fee_list DROP CONSTRAINT IF EXISTS treatment_fee_list_pk;
ALTER TABLE IF EXISTS ONLY treatment_department DROP CONSTRAINT IF EXISTS treatment_department_pkey;
ALTER TABLE IF EXISTS ONLY template_document DROP CONSTRAINT IF EXISTS template_document_pkey;
ALTER TABLE IF EXISTS ONLY task_status DROP CONSTRAINT IF EXISTS task_status_pkey;
ALTER TABLE IF EXISTS ONLY task_status DROP CONSTRAINT IF EXISTS task_status_hospital_id_name_key;
ALTER TABLE IF EXISTS ONLY task DROP CONSTRAINT IF EXISTS task_pkey;
ALTER TABLE IF EXISTS ONLY task_history DROP CONSTRAINT IF EXISTS task_history_pkey;
ALTER TABLE IF EXISTS ONLY task_file DROP CONSTRAINT IF EXISTS task_file_pkey;
ALTER TABLE IF EXISTS ONLY task_comment DROP CONSTRAINT IF EXISTS task_comment_pkey;
ALTER TABLE IF EXISTS ONLY task_comment_file DROP CONSTRAINT IF EXISTS task_comment_file_pkey;
ALTER TABLE IF EXISTS ONLY task_category DROP CONSTRAINT IF EXISTS task_category_pkey;
ALTER TABLE IF EXISTS ONLY task_category DROP CONSTRAINT IF EXISTS task_category_hospital_id_name_key;
ALTER TABLE IF EXISTS ONLY portal_treat_drug DROP CONSTRAINT IF EXISTS t_treat_drug_pkey;
ALTER TABLE IF EXISTS ONLY portal_prescription_drug DROP CONSTRAINT IF EXISTS t_prescription_drug_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_browser DROP CONSTRAINT IF EXISTS t_portal_customer_browser_pkey;
ALTER TABLE IF EXISTS ONLY portal_pict DROP CONSTRAINT IF EXISTS t_pict_pkey;
ALTER TABLE IF EXISTS ONLY opesys_reserve_memo DROP CONSTRAINT IF EXISTS t_opesys_reserve_memo_pkey;
ALTER TABLE IF EXISTS ONLY opesys_memo DROP CONSTRAINT IF EXISTS t_opesys_memo_pkey;
ALTER TABLE IF EXISTS ONLY opesys_clinic_memo DROP CONSTRAINT IF EXISTS t_opesys_clinic_memo_pkey;
ALTER TABLE IF EXISTS ONLY portal_hospital DROP CONSTRAINT IF EXISTS t_hospital_pkey;
ALTER TABLE IF EXISTS ONLY portal_hosp_picture DROP CONSTRAINT IF EXISTS t_hosp_picture_pkey;
ALTER TABLE IF EXISTS ONLY portal_exam_detail DROP CONSTRAINT IF EXISTS t_exam_detail_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_social_login DROP CONSTRAINT IF EXISTS t_customer_social_login_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer DROP CONSTRAINT IF EXISTS t_customer_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_login DROP CONSTRAINT IF EXISTS t_customer_login_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_delivery_address DROP CONSTRAINT IF EXISTS t_customer_delivery_address_pkey;
ALTER TABLE IF EXISTS ONLY portal_contents DROP CONSTRAINT IF EXISTS t_contents_pkey;
ALTER TABLE IF EXISTS ONLY m_template_survey DROP CONSTRAINT IF EXISTS survey_template_pk;
ALTER TABLE IF EXISTS ONLY survey DROP CONSTRAINT IF EXISTS survey_secret_key;
ALTER TABLE IF EXISTS ONLY survey DROP CONSTRAINT IF EXISTS survey_pk;
ALTER TABLE IF EXISTS ONLY survey_answer DROP CONSTRAINT IF EXISTS survey_answer_un;
ALTER TABLE IF EXISTS ONLY survey_answer DROP CONSTRAINT IF EXISTS survey_answer_pkey;
ALTER TABLE IF EXISTS ONLY survey_answer_no_patient DROP CONSTRAINT IF EXISTS survey_answer_no_patient_pkey;
ALTER TABLE IF EXISTS ONLY staff_message DROP CONSTRAINT IF EXISTS staff_message_pkey;
ALTER TABLE IF EXISTS ONLY staff_message_channel DROP CONSTRAINT IF EXISTS staff_message_channel_pkey;
ALTER TABLE IF EXISTS ONLY staff_message_channel_member DROP CONSTRAINT IF EXISTS staff_message_channel_member_pkey;
ALTER TABLE IF EXISTS ONLY signup_user DROP CONSTRAINT IF EXISTS signup_user_pkey;
ALTER TABLE IF EXISTS ONLY schema_image DROP CONSTRAINT IF EXISTS schema_image_pkey;
ALTER TABLE IF EXISTS ONLY reserve DROP CONSTRAINT IF EXISTS reserve_pkey;
ALTER TABLE IF EXISTS ONLY reserve_detail_history DROP CONSTRAINT IF EXISTS reserve_detail_pkey_1_1;
ALTER TABLE IF EXISTS ONLY reserve_detail DROP CONSTRAINT IF EXISTS reserve_detail_pkey_1;
ALTER TABLE IF EXISTS ONLY read_system_notice DROP CONSTRAINT IF EXISTS read_system_notice_pk;
ALTER TABLE IF EXISTS ONLY pt_inf DROP CONSTRAINT IF EXISTS pt_inf_un;
ALTER TABLE IF EXISTS ONLY prompt_log DROP CONSTRAINT IF EXISTS prompt_log_pkey;
ALTER TABLE IF EXISTS ONLY prompt_history DROP CONSTRAINT IF EXISTS prompt_history_pkey;
ALTER TABLE IF EXISTS ONLY prescription_reception DROP CONSTRAINT IF EXISTS prescription_reception_pkey;
ALTER TABLE IF EXISTS ONLY prescription_image DROP CONSTRAINT IF EXISTS prescription_image_pkey;
ALTER TABLE IF EXISTS ONLY portal_treatment_dept_grp DROP CONSTRAINT IF EXISTS portal_treatment_dept_grp_pk;
ALTER TABLE IF EXISTS ONLY portal_staff_picture DROP CONSTRAINT IF EXISTS portal_staff_picture_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_survey DROP CONSTRAINT IF EXISTS portal_medical_info_un;
ALTER TABLE IF EXISTS ONLY portal_m_search_keyword_equiv DROP CONSTRAINT IF EXISTS portal_m_search_keyword_equiv_pk;
ALTER TABLE IF EXISTS ONLY portal_m_prefecture DROP CONSTRAINT IF EXISTS portal_m_prefecture_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_examination DROP CONSTRAINT IF EXISTS portal_m_examination_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_drug02_category DROP CONSTRAINT IF EXISTS portal_m_drug02_category_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_city DROP CONSTRAINT IF EXISTS portal_m_city_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_agree DROP CONSTRAINT IF EXISTS portal_m_agree_pkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_staff DROP CONSTRAINT IF EXISTS portal_hospital_staff_pk;
ALTER TABLE IF EXISTS ONLY portal_hospital_specialist DROP CONSTRAINT IF EXISTS portal_hospital_specialist_pkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_notification DROP CONSTRAINT IF EXISTS portal_hospital_notification_pkey;
ALTER TABLE IF EXISTS ONLY portal_hospital_examination DROP CONSTRAINT IF EXISTS portal_hospital_examination_pkey;
ALTER TABLE IF EXISTS ONLY portal_event_count_log DROP CONSTRAINT IF EXISTS portal_event_count_log_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_pharmacy DROP CONSTRAINT IF EXISTS portal_customer_pharmacy_pk;
ALTER TABLE IF EXISTS ONLY portal_customer_payment DROP CONSTRAINT IF EXISTS portal_customer_payment_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_payment_card DROP CONSTRAINT IF EXISTS portal_customer_payment_card_pkey;
ALTER TABLE IF EXISTS ONLY portal_business_time DROP CONSTRAINT IF EXISTS portal_business_time_pkey;
ALTER TABLE IF EXISTS ONLY portal_ai_chat_content DROP CONSTRAINT IF EXISTS portal_ai_chat_content_pkey;
ALTER TABLE IF EXISTS ONLY portal_agree_log DROP CONSTRAINT IF EXISTS portal_agree_log_pkey;
ALTER TABLE IF EXISTS ONLY user_mst DROP CONSTRAINT IF EXISTS pk_user_mst;
ALTER TABLE IF EXISTS ONLY portal_hosp_tag DROP CONSTRAINT IF EXISTS pk_t_hosp_tag;
ALTER TABLE IF EXISTS ONLY portal_hosp_station DROP CONSTRAINT IF EXISTS pk_t_hosp_station;
ALTER TABLE IF EXISTS ONLY portal_withdrawal_reason DROP CONSTRAINT IF EXISTS pk_t_cust_usage_ed_reason;
ALTER TABLE IF EXISTS ONLY portal_contents_pub DROP CONSTRAINT IF EXISTS pk_t_contents_pub;
ALTER TABLE IF EXISTS ONLY pt_inf DROP CONSTRAINT IF EXISTS pk_pt_inf;
ALTER TABLE IF EXISTS ONLY permission_mst DROP CONSTRAINT IF EXISTS pk_permission_mst;
ALTER TABLE IF EXISTS ONLY m_medical_institution DROP CONSTRAINT IF EXISTS pk_m_medical_institution;
ALTER TABLE IF EXISTS ONLY hp_inf DROP CONSTRAINT IF EXISTS pk_hp_inf;
ALTER TABLE IF EXISTS ONLY common_center_item_mst DROP CONSTRAINT IF EXISTS pk_common_center_item_mst;
ALTER TABLE IF EXISTS ONLY byomei_sikkan_cd_mst DROP CONSTRAINT IF EXISTS pk_byomei_sikkan_cd_mst;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_status_history DROP CONSTRAINT IF EXISTS pharmacy_reserve_status_history_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve DROP CONSTRAINT IF EXISTS pharmacy_reserve_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_reserve_detail DROP CONSTRAINT IF EXISTS pharmacy_reserve_detail_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_patient_file DROP CONSTRAINT IF EXISTS pharmacy_patient_file_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_holiday DROP CONSTRAINT IF EXISTS pharmacy_holiday_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_desired_date DROP CONSTRAINT IF EXISTS pharmacy_desired_date_pkey;
ALTER TABLE IF EXISTS ONLY pharmacy_delivery_history DROP CONSTRAINT IF EXISTS pharmacy_delivery_history_pk;
ALTER TABLE IF EXISTS ONLY pharmacy_delivery_address DROP CONSTRAINT IF EXISTS pharmacy_delivery_address_pk;
ALTER TABLE IF EXISTS ONLY payment_pharmacy_detail DROP CONSTRAINT IF EXISTS payment_pharmacy_detail_pkey;
ALTER TABLE IF EXISTS ONLY payment_pharmacy_detail_history DROP CONSTRAINT IF EXISTS payment_pharmacy_detail_history_pkey;
ALTER TABLE IF EXISTS ONLY payment_fincode_order DROP CONSTRAINT IF EXISTS payment_fincode_order_pkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail DROP CONSTRAINT IF EXISTS payment_clinic_detail_pkey;
ALTER TABLE IF EXISTS ONLY payment_clinic_detail_history DROP CONSTRAINT IF EXISTS payment_clinic_detail_history_pkey;
ALTER TABLE IF EXISTS ONLY patient_message DROP CONSTRAINT IF EXISTS patient_message_pkey;
ALTER TABLE IF EXISTS ONLY patient_message_channel DROP CONSTRAINT IF EXISTS patient_message_channel_pkey;
ALTER TABLE IF EXISTS ONLY patient_message_channel_member DROP CONSTRAINT IF EXISTS patient_message_channel_member_pkey;
ALTER TABLE IF EXISTS ONLY patient_memo DROP CONSTRAINT IF EXISTS patient_memo_pkey;
ALTER TABLE IF EXISTS ONLY opesys_info_change DROP CONSTRAINT IF EXISTS opesys_info_change_pkey;
ALTER TABLE IF EXISTS ONLY m_operator_group DROP CONSTRAINT IF EXISTS operator_group_pk;
ALTER TABLE IF EXISTS ONLY operator_group_permission DROP CONSTRAINT IF EXISTS operator_group_permission_pk;
ALTER TABLE IF EXISTS ONLY message_item DROP CONSTRAINT IF EXISTS message_item_pkey;
ALTER TABLE IF EXISTS ONLY meeting DROP CONSTRAINT IF EXISTS meeting_pk;
ALTER TABLE IF EXISTS ONLY mail_verification_requests DROP CONSTRAINT IF EXISTS mail_verification_requests_pkey;
ALTER TABLE IF EXISTS ONLY m_treatment_category DROP CONSTRAINT IF EXISTS m_treatment_category_pkey;
ALTER TABLE IF EXISTS ONLY m_treatment_category_group DROP CONSTRAINT IF EXISTS m_treatment_category_group_pk;
ALTER TABLE IF EXISTS ONLY m_template_sms DROP CONSTRAINT IF EXISTS m_template_sms_pkey;
ALTER TABLE IF EXISTS ONLY m_template_mail DROP CONSTRAINT IF EXISTS m_template_mail_pkey;
ALTER TABLE IF EXISTS ONLY m_template_document_parameter DROP CONSTRAINT IF EXISTS m_template_document_parameter_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_tag DROP CONSTRAINT IF EXISTS m_tag_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_station DROP CONSTRAINT IF EXISTS m_station_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_specialist DROP CONSTRAINT IF EXISTS m_specialist_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_railline DROP CONSTRAINT IF EXISTS m_railline_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_railline_company DROP CONSTRAINT IF EXISTS m_railline_company_pkey;
ALTER TABLE IF EXISTS ONLY m_prompt DROP CONSTRAINT IF EXISTS m_prompt_pkey;
ALTER TABLE IF EXISTS ONLY m_mail_delivery_settings DROP CONSTRAINT IF EXISTS m_mail_delivery_settings_pkey;
ALTER TABLE IF EXISTS ONLY m_llm_model DROP CONSTRAINT IF EXISTS m_llm_model_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_drug DROP CONSTRAINT IF EXISTS m_drug_pkey;
ALTER TABLE IF EXISTS ONLY portal_m_drug01_category DROP CONSTRAINT IF EXISTS m_drug01_category_pkey;
ALTER TABLE IF EXISTS ONLY m_agree DROP CONSTRAINT IF EXISTS m_agree_pkey;
ALTER TABLE IF EXISTS ONLY m_agent_setting DROP CONSTRAINT IF EXISTS m_agent_setting_pk;
ALTER TABLE IF EXISTS ONLY line_account DROP CONSTRAINT IF EXISTS line_account_unique;
ALTER TABLE IF EXISTS ONLY hp_white_board DROP CONSTRAINT IF EXISTS hp_white_board_pk;
ALTER TABLE IF EXISTS ONLY hp_schedule DROP CONSTRAINT IF EXISTS hp_schedule_pk;
ALTER TABLE IF EXISTS ONLY hp_permission DROP CONSTRAINT IF EXISTS hp_permission_pk;
ALTER TABLE IF EXISTS ONLY hp_fincode_info DROP CONSTRAINT IF EXISTS hp_fincode_info_pk;
ALTER TABLE IF EXISTS ONLY hospital_treatment DROP CONSTRAINT IF EXISTS hospital_treatment_pk;
ALTER TABLE IF EXISTS ONLY portal_hospital_favorite DROP CONSTRAINT IF EXISTS hospital_favorite_pkey;
ALTER TABLE IF EXISTS ONLY freee_dummy_payment DROP CONSTRAINT IF EXISTS freee_dummy_payment_pkey;
ALTER TABLE IF EXISTS ONLY freee_dummy_detail DROP CONSTRAINT IF EXISTS freee_dummy_detail_pkey;
ALTER TABLE IF EXISTS ONLY freee_dummy_deal DROP CONSTRAINT IF EXISTS freee_dummy_deal_pkey;
ALTER TABLE IF EXISTS ONLY fax DROP CONSTRAINT IF EXISTS fax_pkey;
ALTER TABLE IF EXISTS ONLY fax_number DROP CONSTRAINT IF EXISTS fax_number_pkey;
ALTER TABLE IF EXISTS ONLY exam_time_slot DROP CONSTRAINT IF EXISTS exam_time_slot_pkey;
ALTER TABLE IF EXISTS ONLY every_patient_message_setting DROP CONSTRAINT IF EXISTS every_patient_message_setting_pkey;
ALTER TABLE IF EXISTS ONLY deleted_task DROP CONSTRAINT IF EXISTS deleted_task_pkey;
ALTER TABLE IF EXISTS ONLY deleted_task_comment DROP CONSTRAINT IF EXISTS deleted_task_comment_pkey;
ALTER TABLE IF EXISTS ONLY portal_customer_file DROP CONSTRAINT IF EXISTS customer_file_pkey_1;
ALTER TABLE IF EXISTS ONLY common_kensa_unit_mst DROP CONSTRAINT IF EXISTS common_kensa_unit_mst_pkey;
ALTER TABLE IF EXISTS ONLY comment_history DROP CONSTRAINT IF EXISTS comment_history_pkey;
ALTER TABLE IF EXISTS ONLY client_certificate DROP CONSTRAINT IF EXISTS client_certificate_pkey;
ALTER TABLE IF EXISTS ONLY calendar_treatment DROP CONSTRAINT IF EXISTS calendar_treatment_pkey;
ALTER TABLE IF EXISTS ONLY calendar DROP CONSTRAINT IF EXISTS calendar_pkey;
ALTER TABLE IF EXISTS ONLY calendar_basic_setting DROP CONSTRAINT IF EXISTS calendar_basic_setting_pkey;
ALTER TABLE IF EXISTS ONLY m_application_feature DROP CONSTRAINT IF EXISTS application_feature_pk;
ALTER TABLE IF EXISTS ONLY agree_log DROP CONSTRAINT IF EXISTS agree_log_pkey;
ALTER TABLE IF EXISTS ONLY agent_token DROP CONSTRAINT IF EXISTS agent_token_pkey;
ALTER TABLE IF EXISTS ONLY agent_config DROP CONSTRAINT IF EXISTS agent_config_pk;
ALTER TABLE IF EXISTS user_mst ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS treatment_fee_list ALTER COLUMN fee_id DROP DEFAULT;
ALTER TABLE IF EXISTS treatment_department ALTER COLUMN treatment_department_id DROP DEFAULT;
ALTER TABLE IF EXISTS template_document ALTER COLUMN template_document_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_status ALTER COLUMN task_status_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_history ALTER COLUMN task_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_file ALTER COLUMN task_file_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_comment_file ALTER COLUMN task_comment_file_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_comment ALTER COLUMN task_comment_id DROP DEFAULT;
ALTER TABLE IF EXISTS task_category ALTER COLUMN task_category_id DROP DEFAULT;
ALTER TABLE IF EXISTS task ALTER COLUMN task_id DROP DEFAULT;
ALTER TABLE IF EXISTS survey_answer_no_patient ALTER COLUMN survey_answer_no_patient_id DROP DEFAULT;
ALTER TABLE IF EXISTS survey_answer ALTER COLUMN survey_answer_id DROP DEFAULT;
ALTER TABLE IF EXISTS survey ALTER COLUMN survey_id DROP DEFAULT;
ALTER TABLE IF EXISTS staff_message_channel ALTER COLUMN channel_id DROP DEFAULT;
ALTER TABLE IF EXISTS signup_user ALTER COLUMN signup_user_id DROP DEFAULT;
ALTER TABLE IF EXISTS schema_image ALTER COLUMN schema_image_id DROP DEFAULT;
ALTER TABLE IF EXISTS reserve_detail_history ALTER COLUMN reserve_detail_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS reserve_detail ALTER COLUMN reserve_detail_id DROP DEFAULT;
ALTER TABLE IF EXISTS reserve ALTER COLUMN reserve_id DROP DEFAULT;
ALTER TABLE IF EXISTS pt_inf ALTER COLUMN reference_no DROP DEFAULT;
ALTER TABLE IF EXISTS pt_inf ALTER COLUMN seq_no DROP DEFAULT;
ALTER TABLE IF EXISTS pt_inf ALTER COLUMN pt_id DROP DEFAULT;
ALTER TABLE IF EXISTS prescription_reception ALTER COLUMN prescription_reception_id DROP DEFAULT;
ALTER TABLE IF EXISTS prescription_image ALTER COLUMN prescription_image_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_treatment_dept_grp ALTER COLUMN treatment_dept_grp_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_treat_drug ALTER COLUMN treat_drug_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_prescription_drug ALTER COLUMN presc_drug_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_pict ALTER COLUMN pict_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_tag ALTER COLUMN tag_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_station ALTER COLUMN station_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_specialist ALTER COLUMN specialist_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_search_keyword_equiv ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_railline_company ALTER COLUMN railline_company_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_railline ALTER COLUMN railline_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_prefecture ALTER COLUMN prefecture_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_examination ALTER COLUMN examination_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_drug02_category ALTER COLUMN drug02_category_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_drug01_category ALTER COLUMN drug01_category_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_drug ALTER COLUMN drug_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_m_city ALTER COLUMN city_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_hospital_staff ALTER COLUMN hospital_staff_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_hospital_notification ALTER COLUMN hospital_notification_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_hospital_favorite ALTER COLUMN hospital_favorite_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_hospital ALTER COLUMN hospital_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_exam_detail ALTER COLUMN exam_detail_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_survey ALTER COLUMN customer_survey_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_social_login ALTER COLUMN socl_login_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_pharmacy ALTER COLUMN portal_customer_pharmacy_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_payment_card ALTER COLUMN card_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_payment ALTER COLUMN customer_payment_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_login ALTER COLUMN login_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_file ALTER COLUMN customer_file_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_delivery_address ALTER COLUMN delivery_address_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer_browser ALTER COLUMN browser_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_customer ALTER COLUMN customer_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_contents ALTER COLUMN contents_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_business_time ALTER COLUMN business_time_id DROP DEFAULT;
ALTER TABLE IF EXISTS portal_ai_chat_content ALTER COLUMN content_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_reserve_status_history ALTER COLUMN pharmacy_reserve_status_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_reserve_detail ALTER COLUMN pharmacy_reserve_detail_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_reserve ALTER COLUMN pharmacy_reserve_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_patient_file ALTER COLUMN pharmacy_patient_file_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_holiday ALTER COLUMN pharmacy_holiday_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_desired_date ALTER COLUMN pharmacy_desired_date_id DROP DEFAULT;
ALTER TABLE IF EXISTS pharmacy_delivery_address ALTER COLUMN pharmacy_delivery_address_id DROP DEFAULT;
ALTER TABLE IF EXISTS payment_pharmacy_detail_history ALTER COLUMN payment_pharmacy_detail_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS payment_pharmacy_detail ALTER COLUMN payment_pharmacy_detail_id DROP DEFAULT;
ALTER TABLE IF EXISTS payment_fincode_order ALTER COLUMN payment_fincode_order_id DROP DEFAULT;
ALTER TABLE IF EXISTS payment_clinic_detail_history ALTER COLUMN payment_clinic_detail_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS payment_clinic_detail ALTER COLUMN payment_clinic_detail_id DROP DEFAULT;
ALTER TABLE IF EXISTS patient_message_channel ALTER COLUMN channel_id DROP DEFAULT;
ALTER TABLE IF EXISTS opesys_memo ALTER COLUMN memo_id DROP DEFAULT;
ALTER TABLE IF EXISTS opesys_info_change ALTER COLUMN info_change_id DROP DEFAULT;
ALTER TABLE IF EXISTS meeting ALTER COLUMN meeting_id DROP DEFAULT;
ALTER TABLE IF EXISTS mail_verification_requests ALTER COLUMN request_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_treatment_category_group ALTER COLUMN sort_order DROP DEFAULT;
ALTER TABLE IF EXISTS m_treatment_category_group ALTER COLUMN treatment_category_group_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_treatment_category ALTER COLUMN treatment_category_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_template_survey ALTER COLUMN survey_template_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_template_document_parameter ALTER COLUMN template_document_parameter_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_prompt ALTER COLUMN prompt_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_operator_group ALTER COLUMN operator_group_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_llm_model ALTER COLUMN llm_model_id DROP DEFAULT;
ALTER TABLE IF EXISTS m_application_feature ALTER COLUMN feature_id DROP DEFAULT;
ALTER TABLE IF EXISTS line_account ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS hp_inf ALTER COLUMN hp_id DROP DEFAULT;
ALTER TABLE IF EXISTS hospital_treatment ALTER COLUMN hospital_treatment_id DROP DEFAULT;
ALTER TABLE IF EXISTS freee_dummy_payment ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS freee_dummy_detail ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS freee_dummy_deal ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS fax ALTER COLUMN fax_id DROP DEFAULT;
ALTER TABLE IF EXISTS exam_time_slot ALTER COLUMN exam_time_slot_id DROP DEFAULT;
ALTER TABLE IF EXISTS deleted_task_comment ALTER COLUMN task_comment_id DROP DEFAULT;
ALTER TABLE IF EXISTS deleted_task_comment ALTER COLUMN deleted_task_comment_id DROP DEFAULT;
ALTER TABLE IF EXISTS deleted_task ALTER COLUMN task_id DROP DEFAULT;
ALTER TABLE IF EXISTS deleted_task ALTER COLUMN deleted_task_id DROP DEFAULT;
ALTER TABLE IF EXISTS comment_history ALTER COLUMN comment_history_id DROP DEFAULT;
ALTER TABLE IF EXISTS client_certificate ALTER COLUMN client_certificate_id DROP DEFAULT;
ALTER TABLE IF EXISTS calendar_treatment ALTER COLUMN calendar_treatment_id DROP DEFAULT;
ALTER TABLE IF EXISTS calendar_basic_setting ALTER COLUMN calendar_basic_setting_id DROP DEFAULT;
ALTER TABLE IF EXISTS calendar ALTER COLUMN calendar_id DROP DEFAULT;
DROP SEQUENCE IF EXISTS user_mst_id_seq CASCADE;
DROP TABLE IF EXISTS user_mst CASCADE;
DROP SEQUENCE IF EXISTS treatment_fee_list_fee_id_seq CASCADE;
DROP TABLE IF EXISTS treatment_fee_list CASCADE;
DROP SEQUENCE IF EXISTS treatment_department_treatment_department_id_seq CASCADE;
DROP TABLE IF EXISTS treatment_department CASCADE;
DROP SEQUENCE IF EXISTS template_document_parameter_id_seq CASCADE;
DROP SEQUENCE IF EXISTS template_document_id_seq CASCADE;
DROP TABLE IF EXISTS template_document CASCADE;
DROP SEQUENCE IF EXISTS task_task_id_seq CASCADE;
DROP SEQUENCE IF EXISTS task_status_task_status_id_seq CASCADE;
DROP TABLE IF EXISTS task_status CASCADE;
DROP SEQUENCE IF EXISTS task_history_task_history_id_seq CASCADE;
DROP TABLE IF EXISTS task_history CASCADE;
DROP SEQUENCE IF EXISTS task_file_task_file_id_seq CASCADE;
DROP TABLE IF EXISTS task_file CASCADE;
DROP SEQUENCE IF EXISTS task_comment_task_comment_id_seq CASCADE;
DROP SEQUENCE IF EXISTS task_comment_file_task_comment_file_id_seq CASCADE;
DROP TABLE IF EXISTS task_comment_file CASCADE;
DROP TABLE IF EXISTS task_comment CASCADE;
DROP SEQUENCE IF EXISTS task_category_task_category_id_seq CASCADE;
DROP TABLE IF EXISTS task_category CASCADE;
DROP TABLE IF EXISTS task CASCADE;
DROP SEQUENCE IF EXISTS survey_template_survey_template_id_seq CASCADE;
DROP SEQUENCE IF EXISTS survey_survey_id_seq CASCADE;
DROP SEQUENCE IF EXISTS survey_answer_no_patient_survey_answer_no_patient_id_seq CASCADE;
DROP TABLE IF EXISTS survey_answer_no_patient CASCADE;
DROP SEQUENCE IF EXISTS survey_answer_id_seq CASCADE;
DROP TABLE IF EXISTS survey_answer CASCADE;
DROP TABLE IF EXISTS survey CASCADE;
DROP SEQUENCE IF EXISTS status_id_seq CASCADE;
DROP TABLE IF EXISTS staff_message_channel_member CASCADE;
DROP SEQUENCE IF EXISTS staff_message_channel_channel_id_seq CASCADE;
DROP TABLE IF EXISTS staff_message_channel CASCADE;
DROP TABLE IF EXISTS staff_message CASCADE;
DROP SEQUENCE IF EXISTS signup_user_id_seq CASCADE;
DROP TABLE IF EXISTS signup_user CASCADE;
DROP SEQUENCE IF EXISTS schema_image_id_seq CASCADE;
DROP TABLE IF EXISTS schema_image CASCADE;
DROP SEQUENCE IF EXISTS s_survey_id CASCADE;
DROP SEQUENCE IF EXISTS reserve_reserve_id_seq CASCADE;
DROP SEQUENCE IF EXISTS reserve_detail_reserve_detail_id_seq CASCADE;
DROP SEQUENCE IF EXISTS reserve_detail_history_reserve_detail_history_id_seq CASCADE;
DROP TABLE IF EXISTS reserve_detail_history CASCADE;
DROP TABLE IF EXISTS reserve_detail CASCADE;
DROP TABLE IF EXISTS reserve CASCADE;
DROP TABLE IF EXISTS read_system_notice CASCADE;
DROP SEQUENCE IF EXISTS pt_inf_seq_no_seq CASCADE;
DROP SEQUENCE IF EXISTS pt_inf_reference_no_seq CASCADE;
DROP SEQUENCE IF EXISTS pt_inf_pt_id_seq CASCADE;
DROP TABLE IF EXISTS pt_inf CASCADE;
DROP TABLE IF EXISTS prompt_log CASCADE;
DROP TABLE IF EXISTS prompt_history CASCADE;
DROP SEQUENCE IF EXISTS prescription_reception_prescription_reception_id_seq CASCADE;
DROP TABLE IF EXISTS prescription_reception CASCADE;
DROP SEQUENCE IF EXISTS prescription_image_prescription_image_id_seq CASCADE;
DROP TABLE IF EXISTS prescription_image CASCADE;
DROP SEQUENCE IF EXISTS prescription_id_seq CASCADE;
DROP TABLE IF EXISTS portal_withdrawal_reason CASCADE;
DROP SEQUENCE IF EXISTS portal_treatment_dept_grp_treatment_dept_grp_id_seq CASCADE;
DROP TABLE IF EXISTS portal_treatment_dept_grp CASCADE;
DROP SEQUENCE IF EXISTS portal_treat_drug_treat_drug_id_seq CASCADE;
DROP TABLE IF EXISTS portal_treat_drug CASCADE;
DROP SEQUENCE IF EXISTS portal_t_treatment_treatment_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_treat_drug_treat_drug_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_review_review_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_prescription_drug_presc_drug_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_pict_pict_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_hospital_hospital_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_exam_time_slot_exam_time_slot_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_exam_detail_exam_detail_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_customer_social_login_socl_login_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_customer_login_login_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_customer_customer_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_contents_contents_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_business_time_business_time_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_t_address_addr_id_seq CASCADE;
DROP TABLE IF EXISTS portal_staff_picture CASCADE;
DROP SEQUENCE IF EXISTS portal_prescription_drug_presc_drug_id_seq CASCADE;
DROP TABLE IF EXISTS portal_prescription_drug CASCADE;
DROP SEQUENCE IF EXISTS portal_pict_pict_id_seq CASCADE;
DROP TABLE IF EXISTS portal_pict CASCADE;
DROP SEQUENCE IF EXISTS portal_m_tag_tag_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_tag CASCADE;
DROP SEQUENCE IF EXISTS portal_m_station_station_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_station CASCADE;
DROP SEQUENCE IF EXISTS portal_m_specialist_specialist_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_specialist CASCADE;
DROP SEQUENCE IF EXISTS portal_m_search_keyword_equiv_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_search_keyword_equiv CASCADE;
DROP SEQUENCE IF EXISTS portal_m_railline_railline_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_m_railline_company_railline_company_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_railline_company CASCADE;
DROP TABLE IF EXISTS portal_m_railline CASCADE;
DROP SEQUENCE IF EXISTS portal_m_prefecture_prefecture_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_prefecture CASCADE;
DROP SEQUENCE IF EXISTS portal_m_examination_examination_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_examination CASCADE;
DROP SEQUENCE IF EXISTS portal_m_drug_drug_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_m_drug02_category_drug02_category_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_drug02_category CASCADE;
DROP SEQUENCE IF EXISTS portal_m_drug01_category_drug01_category_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_drug01_category CASCADE;
DROP TABLE IF EXISTS portal_m_drug CASCADE;
DROP SEQUENCE IF EXISTS portal_m_city_city_id_seq CASCADE;
DROP TABLE IF EXISTS portal_m_city CASCADE;
DROP TABLE IF EXISTS portal_m_agree CASCADE;
DROP SEQUENCE IF EXISTS portal_hospital_staff_hospital_staff_id_seq CASCADE;
DROP TABLE IF EXISTS portal_hospital_staff CASCADE;
DROP TABLE IF EXISTS portal_hospital_specialist CASCADE;
DROP SEQUENCE IF EXISTS portal_hospital_notification_hospital_notification_id_seq CASCADE;
DROP TABLE IF EXISTS portal_hospital_notification CASCADE;
DROP SEQUENCE IF EXISTS portal_hospital_hospital_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_hospital_favorite_hospital_favorite_id_seq CASCADE;
DROP TABLE IF EXISTS portal_hospital_favorite CASCADE;
DROP TABLE IF EXISTS portal_hospital_examination CASCADE;
DROP TABLE IF EXISTS portal_hospital CASCADE;
DROP TABLE IF EXISTS portal_hosp_tag CASCADE;
DROP TABLE IF EXISTS portal_hosp_station CASCADE;
DROP TABLE IF EXISTS portal_hosp_picture CASCADE;
DROP SEQUENCE IF EXISTS portal_exam_detail_exam_detail_id_seq CASCADE;
DROP TABLE IF EXISTS portal_exam_detail CASCADE;
DROP TABLE IF EXISTS portal_event_count_log CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_survey_customer_survey_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_survey CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_social_login_socl_login_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_social_login CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_pharmacy_portal_customer_pharmacy_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_pharmacy CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_payment_customer_payment_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_payment_card_card_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_payment_card CASCADE;
DROP TABLE IF EXISTS portal_customer_payment CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_login_login_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_login CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_file_customer_file_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_file CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_delivery_address_delivery_address_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_delivery_address CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_customer_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_customer_browser_browser_id_seq CASCADE;
DROP TABLE IF EXISTS portal_customer_browser CASCADE;
DROP TABLE IF EXISTS portal_customer CASCADE;
DROP TABLE IF EXISTS portal_contents_pub CASCADE;
DROP SEQUENCE IF EXISTS portal_contents_contents_id_seq CASCADE;
DROP TABLE IF EXISTS portal_contents CASCADE;
DROP SEQUENCE IF EXISTS portal_business_time_business_time_id_seq CASCADE;
DROP TABLE IF EXISTS portal_business_time CASCADE;
DROP SEQUENCE IF EXISTS portal_ai_chat_content_id_seq CASCADE;
DROP SEQUENCE IF EXISTS portal_ai_chat_content_content_id_seq CASCADE;
DROP TABLE IF EXISTS portal_ai_chat_content CASCADE;
DROP TABLE IF EXISTS portal_agree_log CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_reserve_status_history CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_reserve_pharmacy_reserve_id_seq CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_reserve_detail CASCADE;
DROP TABLE IF EXISTS pharmacy_reserve CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_holiday_pharmacy_holiday_id_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_holiday CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_desired_date_pharmacy_desired_date_id_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_desired_date CASCADE;
DROP TABLE IF EXISTS pharmacy_delivery_history CASCADE;
DROP SEQUENCE IF EXISTS pharmacy_delivery_address_id_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_delivery_address CASCADE;
DROP TABLE IF EXISTS permission_mst CASCADE;
DROP SEQUENCE IF EXISTS payment_pharmacy_detail_payment_pharmacy_detail_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq CASCADE;
DROP TABLE IF EXISTS payment_pharmacy_detail_history CASCADE;
DROP TABLE IF EXISTS payment_pharmacy_detail CASCADE;
DROP SEQUENCE IF EXISTS payment_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payment_fincode_order_payment_fincode_order_id_seq CASCADE;
DROP TABLE IF EXISTS payment_fincode_order CASCADE;
DROP SEQUENCE IF EXISTS payment_clinic_detail_payment_clinic_detail_id_seq CASCADE;
DROP SEQUENCE IF EXISTS payment_clinic_detail_history_payment_clinic_detail_history_seq CASCADE;
DROP TABLE IF EXISTS payment_clinic_detail_history CASCADE;
DROP TABLE IF EXISTS payment_clinic_detail CASCADE;
DROP SEQUENCE IF EXISTS patient_pharmacy_file_patient_pharmacy_file_id_seq CASCADE;
DROP TABLE IF EXISTS pharmacy_patient_file CASCADE;
DROP TABLE IF EXISTS patient_message_channel_member CASCADE;
DROP SEQUENCE IF EXISTS patient_message_channel_channel_id_seq CASCADE;
DROP TABLE IF EXISTS patient_message_channel CASCADE;
DROP TABLE IF EXISTS patient_message CASCADE;
DROP TABLE IF EXISTS patient_memo CASCADE;
DROP TABLE IF EXISTS opesys_reserve_memo CASCADE;
DROP SEQUENCE IF EXISTS opesys_memo_memo_id_seq CASCADE;
DROP TABLE IF EXISTS opesys_memo CASCADE;
DROP SEQUENCE IF EXISTS opesys_info_change_info_change_id_seq CASCADE;
DROP TABLE IF EXISTS opesys_info_change_detail_portal CASCADE;
DROP TABLE IF EXISTS opesys_info_change_detail_clinic CASCADE;
DROP TABLE IF EXISTS opesys_info_change CASCADE;
DROP TABLE IF EXISTS opesys_clinic_memo CASCADE;
DROP TABLE IF EXISTS operator_group_permission CASCADE;
DROP SEQUENCE IF EXISTS op_id_seq CASCADE;
DROP TABLE IF EXISTS message_item CASCADE;
DROP SEQUENCE IF EXISTS meeting_meeting_id_seq CASCADE;
DROP TABLE IF EXISTS meeting CASCADE;
DROP SEQUENCE IF EXISTS medical_questionnaire_id_seq CASCADE;
DROP SEQUENCE IF EXISTS mail_verification_requests_request_id_seq CASCADE;
DROP TABLE IF EXISTS mail_verification_requests CASCADE;
DROP SEQUENCE IF EXISTS m_treatment_category_treatment_category_id_seq CASCADE;
DROP SEQUENCE IF EXISTS m_treatment_category_group_treatment_category_group_id_seq CASCADE;
DROP SEQUENCE IF EXISTS m_treatment_category_group_sort_order_seq CASCADE;
DROP TABLE IF EXISTS m_treatment_category_group CASCADE;
DROP TABLE IF EXISTS m_treatment_category CASCADE;
DROP TABLE IF EXISTS m_template_survey CASCADE;
DROP TABLE IF EXISTS m_template_sms CASCADE;
DROP TABLE IF EXISTS m_template_mail CASCADE;
DROP TABLE IF EXISTS m_template_document_parameter CASCADE;
DROP SEQUENCE IF EXISTS m_prompt_id_seq CASCADE;
DROP TABLE IF EXISTS m_prompt CASCADE;
DROP SEQUENCE IF EXISTS m_operator_group_operator_group_id_seq CASCADE;
DROP TABLE IF EXISTS m_operator_group CASCADE;
DROP TABLE IF EXISTS m_medical_institution CASCADE;
DROP TABLE IF EXISTS m_mail_delivery_settings CASCADE;
DROP SEQUENCE IF EXISTS m_llm_model_id_seq CASCADE;
DROP TABLE IF EXISTS m_llm_model CASCADE;
DROP SEQUENCE IF EXISTS m_application_feature_feature_id_seq CASCADE;
DROP TABLE IF EXISTS m_application_feature CASCADE;
DROP TABLE IF EXISTS m_agree CASCADE;
DROP TABLE IF EXISTS m_agent_setting CASCADE;
DROP SEQUENCE IF EXISTS line_account_id_seq CASCADE;
DROP TABLE IF EXISTS line_account CASCADE;
DROP TABLE IF EXISTS hp_white_board CASCADE;
DROP TABLE IF EXISTS hp_schedule CASCADE;
DROP TABLE IF EXISTS hp_permission CASCADE;
DROP SEQUENCE IF EXISTS hp_inf_hp_id_seq CASCADE;
DROP TABLE IF EXISTS hp_inf CASCADE;
DROP TABLE IF EXISTS hp_fincode_info CASCADE;
DROP SEQUENCE IF EXISTS hospital_treatment_hospital_treatment_id_seq1 CASCADE;
DROP SEQUENCE IF EXISTS hospital_treatment_hospital_treatment_id_seq CASCADE;
DROP TABLE IF EXISTS hospital_treatment CASCADE;
DROP SEQUENCE IF EXISTS freee_dummy_payment_id_seq CASCADE;
DROP TABLE IF EXISTS freee_dummy_payment CASCADE;
DROP SEQUENCE IF EXISTS freee_dummy_detail_id_seq CASCADE;
DROP TABLE IF EXISTS freee_dummy_detail CASCADE;
DROP SEQUENCE IF EXISTS freee_dummy_deal_id_seq CASCADE;
DROP TABLE IF EXISTS freee_dummy_deal CASCADE;
DROP SEQUENCE IF EXISTS fco_api_keys_fco_api_key_id_seq CASCADE;
DROP TABLE IF EXISTS fax_number CASCADE;
DROP SEQUENCE IF EXISTS fax_fax_id_seq CASCADE;
DROP TABLE IF EXISTS fax CASCADE;
DROP SEQUENCE IF EXISTS exam_time_slot_exam_time_slot_id_seq CASCADE;
DROP TABLE IF EXISTS exam_time_slot CASCADE;
DROP TABLE IF EXISTS every_patient_message_setting CASCADE;
DROP SEQUENCE IF EXISTS deleted_task_task_id_seq CASCADE;
DROP SEQUENCE IF EXISTS deleted_task_deleted_task_id_seq CASCADE;
DROP SEQUENCE IF EXISTS deleted_task_comment_task_comment_id_seq CASCADE;
DROP SEQUENCE IF EXISTS deleted_task_comment_deleted_task_comment_id_seq CASCADE;
DROP TABLE IF EXISTS deleted_task_comment CASCADE;
DROP TABLE IF EXISTS deleted_task CASCADE;
DROP TABLE IF EXISTS common_kensa_unit_mst CASCADE;
DROP TABLE IF EXISTS common_center_item_mst CASCADE;
DROP SEQUENCE IF EXISTS comment_history_comment_history_id_seq CASCADE;
DROP TABLE IF EXISTS comment_history CASCADE;
DROP SEQUENCE IF EXISTS client_certificate_client_certificate_id_seq CASCADE;
DROP TABLE IF EXISTS client_certificate CASCADE;
DROP SEQUENCE IF EXISTS calendar_treatment_calendar_treatment_id_seq CASCADE;
DROP TABLE IF EXISTS calendar_treatment CASCADE;
DROP SEQUENCE IF EXISTS calendar_calendar_id_seq CASCADE;
DROP SEQUENCE IF EXISTS calendar_basic_setting_calendar_basic_setting_id_seq CASCADE;
DROP TABLE IF EXISTS calendar_basic_setting CASCADE;
DROP TABLE IF EXISTS calendar CASCADE;
DROP TABLE IF EXISTS byomei_sikkan_cd_mst CASCADE;
DROP TABLE IF EXISTS agree_log CASCADE;
DROP TABLE IF EXISTS agent_token CASCADE;
DROP TABLE IF EXISTS agent_config CASCADE;
DROP FUNCTION IF EXISTS updateseqgroupnotrigger_partition_function();
DROP FUNCTION IF EXISTS update_raiin_inf_func();
DROP FUNCTION IF EXISTS update_pt_last_visit_date_func();
DROP FUNCTION IF EXISTS update_pharmacy_reserve_update_date();
DROP FUNCTION IF EXISTS trigger_set_update_date_timestamp();
DROP FUNCTION IF EXISTS trigger_set_timestamp();
DROP FUNCTION IF EXISTS trigger_insert_update_prompt_history();
DROP FUNCTION IF EXISTS renkei_req_trg02_func();
DROP FUNCTION IF EXISTS renkei_req_trg01_func();
DROP FUNCTION IF EXISTS insert_limit_inf_history_table();
DROP FUNCTION IF EXISTS insert_history_table();
DROP FUNCTION IF EXISTS insert_history_partition_table();
DROP FUNCTION IF EXISTS copy_id_to_user_id();
DROP FUNCTION IF EXISTS addseqgroupnotrigger_partition_function();
--DROP SCHEMA IF EXISTS local_gmoht;
--
-- Name: local_gmoht; Type: SCHEMA; Schema: -; Owner: -
--

--CREATE SCHEMA local_gmoht;


--
-- Name: addseqgroupnotrigger_partition_function(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION addseqgroupnotrigger_partition_function() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
  if (select count(*) from "kensa_inf_detail" where "irai_cd" = NEW."irai_cd" and "kensa_item_cd" = NEW."kensa_item_cd" and "is_deleted" = 0 and hp_id = new.hp_id) > 0
  --then NEW."SEQ_GROUP_NO" := (select max("SEQ_GROUP_NO") from "KENSA_INF_DETAIL" where "IRAI_CD" = NEW."IRAI_CD" and "KENSA_ITEM_CD" = NEW."KENSA_ITEM_CD" and "IS_DELETED" = 0) + 1;
 then NEW."seq_group_no" := (select count(*) from "kensa_inf_detail" where "irai_cd" = NEW."irai_cd" and "kensa_item_cd" = NEW."kensa_item_cd" and "is_deleted" = 0 and hp_id = new.hp_id);
  end if;
return new;
end;
$$;


--
-- Name: copy_id_to_user_id(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION copy_id_to_user_id() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.user_id := NEW.id;
RETURN NEW;
END;
$$;


--
-- Name: insert_history_partition_table(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION insert_history_partition_table() RETURNS trigger
    LANGUAGE plpgsql
    AS $_$DECLARE
    HISTORY_TABLE_NAME character varying(63);
    clientAddr varchar(100);
    clientHostName varchar(100);
BEGIN
--
-- Create a row in history table to reflect the operation performed on original one,
-- make use of the special variable TG_OP to work out the operation.
--
HISTORY_TABLE_NAME := 'z_' || TG_TABLE_NAME;
select CLIENT_ADDR, CLIENT_HOSTNAME from pg_stat_activity where pid = pg_backend_pid() into clientAddr, clientHostName;

if (TG_OP = 'INSERT') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id", "hp_id") DO NOTHING', HISTORY_TABLE_NAME)
                   using nextval('"op_id_seq"'::regclass), 'INSERT', current_timestamp, clientAddr, clientHostName, new;
    return new;
elsif (TG_OP = 'UPDATE') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id", "hp_id") DO NOTHING', HISTORY_TABLE_NAME)
                    using nextval('"op_id_seq"'::regclass), 'UPDATE', current_timestamp, clientAddr, clientHostName, new;
    return new;
elsif (TG_OP = 'DELETE') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id", "hp_id") DO NOTHING', HISTORY_TABLE_NAME)
                    using nextval('"op_id_seq"'::regclass), 'DELETE', current_timestamp, clientAddr, clientHostName, old;
    return old;
end if;
return null; -- result is ignored since this is an AFTER trigger
end;$_$;


--
-- Name: insert_history_table(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION insert_history_table() RETURNS trigger
    LANGUAGE plpgsql
    AS $_$DECLARE
    HISTORY_TABLE_NAME character varying(63);
clientAddr varchar(100);
    clientHostName varchar(100);
BEGIN
    --
-- Create a row in history table to reflect the operation performed on original one,
-- make use of the special variable TG_OP to work out the operation.
--
    HISTORY_TABLE_NAME := 'z_' || TG_TABLE_NAME;
select CLIENT_ADDR, CLIENT_HOSTNAME from pg_stat_activity where pid = pg_backend_pid() into clientAddr, clientHostName;

if (TG_OP = 'INSERT') then
        execute format('INSERT INTO %I '
                           'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id") DO NOTHING', HISTORY_TABLE_NAME)
            using nextval('op_id_seq'::regclass), 'INSERT', current_timestamp, clientAddr, clientHostName, new;
return new;
elsif (TG_OP = 'UPDATE') then
        execute format('INSERT INTO %I '
                           'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id") DO NOTHING', HISTORY_TABLE_NAME)
            using nextval('op_id_seq'::regclass), 'UPDATE', current_timestamp, clientAddr, clientHostName, new;
return new;
elsif (TG_OP = 'DELETE') then
        execute format('INSERT INTO %I '
                           'SELECT $1, $2, $3, $4, $5, $6.* ON CONFLICT ("op_id") DO NOTHING', HISTORY_TABLE_NAME)
            using nextval('op_id_seq'::regclass), 'DELETE', current_timestamp, clientAddr, clientHostName, old;
return old;
end if;
return null; -- result is ignored since this is an AFTER trigger
end;$_$;


--
-- Name: insert_limit_inf_history_table(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION insert_limit_inf_history_table() RETURNS trigger
    LANGUAGE plpgsql
    AS $_$DECLARE
    REVISION_NUMBER bigint;
    HISTORY_TABLE_NAME character varying(63);
    clientAddr varchar(100);
    clientHostName varchar(100);
	hokenPid int;
BEGIN
--
-- Create a row in history table to reflect the operation performed on original one,
-- make use of the special variable TG_OP to work out the operation.
--
hokenPid = NEW."hoken_pid";
if (hokenPid != 0) then return null;
end if;

hokenPid = OLD."hoken_pid";
if (hokenPid != 0) then return null;
end if;

HISTORY_TABLE_NAME := 'z_' || TG_TABLE_NAME;
execute format('SELECT COALESCE(MAX("op_id"), 0) FROM %I ', HISTORY_TABLE_NAME) into REVISION_NUMBER;
REVISION_NUMBER := REVISION_NUMBER + 1;
select CLIENT_ADDR, CLIENT_HOSTNAME from pg_stat_activity where pid = pg_backend_pid() into clientAddr, clientHostName;

if (TG_OP = 'INSERT') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.*', HISTORY_TABLE_NAME)
                   using REVISION_NUMBER, 'INSERT', current_timestamp, clientAddr, clientHostName, new;
    return new;
elsif (TG_OP = 'UPDATE') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.*', HISTORY_TABLE_NAME)
                    using REVISION_NUMBER, 'UPDATE', current_timestamp, clientAddr, clientHostName, new;
    return new;
elsif (TG_OP = 'DELETE') then
    execute format('INSERT INTO %I '
                   'SELECT $1, $2, $3, $4, $5, $6.*', HISTORY_TABLE_NAME)
                    using REVISION_NUMBER, 'DELETE', current_timestamp, clientAddr, clientHostName, old;
    return old;
end if;
return null; -- result is ignored since this is an AFTER trigger
end;$_$;


--
-- Name: renkei_req_trg01_func(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION renkei_req_trg01_func() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
	DECLARE
		iHpCd     "renkei_req"."hp_id"%TYPE;
		iKanId    "renkei_req"."pt_id"%TYPE;
		sUpdFg    "renkei_req"."req_type"%TYPE;
	BEGIN
		iHpCd  = NEW."hp_id";
		iKanId = NEW."pt_id";

		IF (TG_OP = 'INSERT' AND (NEW."is_delete" = 0 OR NEW."is_delete" IS NULL)) OR
		   (TG_OP = 'UPDATE' AND OLD."is_delete" = 1 AND NEW."is_delete" = 0) THEN
			IF NEW."name" IS NOT NULL AND
			   NEW."kana_name" IS NOT NULL AND
			   NEW."sex" IS NOT NULL        AND NEW."birthday" IS NOT NULL THEN
				sUpdFg = 'I';
			END IF;
		ELSIF TG_OP = 'UPDATE' AND (NEW."is_delete" = 0 OR NEW."is_delete" IS NULL) THEN
			IF (OLD."name" <> NEW."name") OR (OLD."name" IS NULL AND NEW."name" IS NOT NULL) OR
			   (OLD."kana_name" <> NEW."kana_name") OR (OLD."kana_name" IS NULL AND NEW."kana_name" IS NOT NULL) OR
			   (OLD."sex"        <> NEW."sex") OR (OLD."sex" IS NULL AND NEW."sex" IS NOT NULL) OR
			   (OLD."birthday"    <> NEW."birthday") OR  (OLD."birthday" IS NULL AND NEW."birthday" IS NOT NULL) THEN
				sUpdFg = 'U';
			END IF;
		ELSIF (TG_OP = 'INSERT' AND NEW."is_delete" = 1) THEN
			sUpdFg = 'D';
		ELSIF TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND NEW."is_delete" = 1) THEN
			sUpdFg = 'D';
			iHpCd  = OLD."hp_id";
			iKanId = OLD."pt_id";
		END IF;

		IF sUpdFg IS NOT NULL THEN
			INSERT INTO "renkei_req"
				(
					"hp_id",
					"pt_id",
					"raiin_no",
					"req_sbt",
					"req_type",
					"create_date",
					"create_id",
					"update_date",
					"update_id"
				) VALUES (
					iHpCd,
					iKanId,
					0,
					1,
					sUpdFg,
					NOW(),
					0,
					NOW(),
					0
				);
		END IF;
		 RETURN NULL;
	   END;
$$;


--
-- Name: renkei_req_trg02_func(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION renkei_req_trg02_func() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
		sUpdFg    "renkei_req"."req_type"%TYPE;
BEGIN
	IF TG_OP = 'INSERT' AND NEW."status" >= 1 THEN
		  sUpdFg = 'I';
ELSIF TG_OP = 'UPDATE' AND OLD."status" = 0 AND NEW."status" >= 1 THEN
		  sUpdFg = 'U';
END
IF;

		IF sUpdFg IS NOT NULL THEN
INSERT INTO "renkei_req"
	(
	"hp_id",
	"pt_id",
	"raiin_no",
	"req_sbt",
	"req_type",
	"create_date",
	"create_id",
	"update_date",
	"update_id"
	)
VALUES
	(
		NEW."hp_id",
		NEW."pt_id",
		NEW."raiin_no",
		2,
		sUpdFg,
		NOW(),
		0,
		NOW(),
		0
				);
END
IF;
		 RETURN NULL;
END;
$$;


--
-- Name: trigger_insert_update_prompt_history(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION trigger_insert_update_prompt_history() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
new_version integer;
BEGIN
    IF (TG_OP = 'INSERT') THEN
        new_version := 1;
    ELSIF (TG_OP = 'UPDATE') THEN
        new_version := (SELECT COALESCE(MAX(version), 0) + 1 FROM prompt_history WHERE prompt_id = NEW.prompt_id);
END IF;

INSERT INTO prompt_history (
    prompt_history_id,
    prompt_id,
    version,
    prompt_name,
    llm_model,
    prompt,
    created_by,
    updated_by,
    created_at,
    prompt_title,
    usage_type
)
VALUES (
           (SELECT COALESCE(MAX(prompt_history_id), 0) + 1 FROM prompt_history),
           NEW.prompt_id,
           new_version,
           NEW.prompt_name,
           NEW.llm_model,
           NEW.prompt,
           NEW.created_by,
           NEW.updated_by,
           NEW.created_at,
           NEW.prompt_title,
           NEW.usage_type
       );

RETURN NEW;
END;
$$;


--
-- Name: trigger_set_timestamp(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION trigger_set_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at=now();
RETURN NEW;
END;
    $$;


--
-- Name: trigger_set_update_date_timestamp(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION trigger_set_update_date_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.update_date=now();
RETURN NEW;
END;
    $$;


--
-- Name: update_pharmacy_reserve_update_date(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION update_pharmacy_reserve_update_date() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
   -- ステータス更新(other>7 or 7>other)時に服薬指導更新日時を現在時間に更新する
   IF NEW.status <> OLD.status AND (NEW.status = 7 OR OLD.status = 7) THEN
UPDATE pharmacy_reserve
SET reserve_update_date = NOW(), updated_at = NOW()
WHERE pharmacy_reserve_id = NEW.pharmacy_reserve_id;
END IF;
RETURN NEW;
END;
$$;


--
-- Name: update_pt_last_visit_date_func(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION update_pt_last_visit_date_func() RETURNS trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
   _LAST_VISIT_DATE bigint;
   COUNT_LAST_VISIT_DATE bigint;
begin

	EXECUTE format
	('SELECT COALESCE(MAX("sin_date"), 0) FROM "raiin_inf" WHERE "status" >= 3 AND "hp_id"=$1 AND "pt_id"=$2 AND "is_deleted" = 0') USING NEW."hp_id", NEW."pt_id" INTO _LAST_VISIT_DATE;
	EXECUTE format
	('SELECT COUNT(*) FROM "pt_last_visit_date" WHERE "hp_id"=$1 AND "pt_id"=$2') USING NEW."hp_id", NEW."pt_id" INTO COUNT_LAST_VISIT_DATE;
	IF (COUNT_LAST_VISIT_DATE = 0) THEN
	insert into "pt_last_visit_date"
	values(
			new."hp_id",
			new."pt_id",
			_LAST_VISIT_DATE,
			current_timestamp,
			new."update_id",
			new."update_machine",
			current_timestamp,
			new."update_id",
			new."update_machine");
	RETURN NEW;
	else
	UPDATE "pt_last_visit_date" SET
            "last_visit_date" = _LAST_VISIT_DATE,
            "update_id"= new."update_id",
            "update_date"= current_timestamp,
            "update_machine"= new."update_machine"
       WHERE
            "hp_id" = NEW."hp_id"
		AND "pt_id" = NEW."pt_id";
	RETURN NEW;
END
IF;
     RETURN NULL;
END;
$_$;


--
-- Name: update_raiin_inf_func(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION update_raiin_inf_func() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
	IF (NEW."item_cd" = '@SHIN') THEN
	UPDATE "raiin_inf" SET
            "syosaisin_kbn"         = NEW."suryo"
        WHERE
            "hp_id"         = NEW."hp_id"
		AND "pt_id"           = NEW."pt_id"
		AND "raiin_no" = NEW."raiin_no";
	RETURN NEW;
	ELSIF
	(NEW."item_cd" = '@JIKAN') THEN
	UPDATE "raiin_inf" SET
            "jikan_kbn"         = NEW."suryo"
       WHERE
            "hp_id"         = NEW."hp_id"
		AND "pt_id"           = NEW."pt_id"
		AND "raiin_no" = NEW."raiin_no";
	RETURN NEW;
END
IF;
     RETURN NULL;
END;
$$;


--
-- Name: updateseqgroupnotrigger_partition_function(); Type: FUNCTION; Schema: local_gmoht; Owner: -
--

CREATE FUNCTION updateseqgroupnotrigger_partition_function() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    sequennocalculate numeric = 0;
    minSeqNo numeric = 0;
    maxSeqNo numeric = 0;
    cnt numeric = 0;
BEGIN
	IF NEW."is_deleted" = 1 THEN
	    minSeqNo = (select  min("seq_no") from "kensa_inf_detail" where "hp_id" = new."hp_id" and "irai_cd" = new."irai_cd" and "kensa_item_cd" = new."kensa_item_cd" and "is_deleted" = 0 and new."seq_no" != "seq_no");
	    maxSeqNo = (select  max("seq_no") from "kensa_inf_detail" where "hp_id" = new."hp_id" and "irai_cd" = new."irai_cd" and "kensa_item_cd" = new."kensa_item_cd" and "is_deleted" = 0 and new."seq_no" != "seq_no");
	   if minSeqNo is null then return old; end if;
	   if maxSeqNo is null then return old; end if;
	   for cnt in minSeqNo..maxSeqNo loop
	    if (select count(*) from "kensa_inf_detail" where "seq_no" = cnt and "is_deleted" = 0 ) > 0
	    then
	    	update "kensa_inf_detail" set "seq_group_no" = sequennocalculate where "seq_no" = cnt and "is_deleted" = 0 and "hp_id" = new."hp_id";
	    	sequennocalculate = sequennocalculate + 1;
	    END if;
	    end loop;
	END if;
	RETURN new;
end $$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_config; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE agent_config (
    hospital_id integer NOT NULL,
    job_type integer NOT NULL,
    file_type integer NOT NULL,
    is_enabled boolean DEFAULT false NOT NULL,
    shared_folder_path character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: agent_token; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE agent_token (
    hospital_id integer NOT NULL,
    token character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: agree_log; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE agree_log (
    agreement_id integer NOT NULL,
    hospital_id integer NOT NULL,
    agree_datetime timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN agree_log.agreement_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN agree_log.agreement_id IS '同意ID';


--
-- Name: COLUMN agree_log.hospital_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN agree_log.hospital_id IS '病院ID';


--
-- Name: COLUMN agree_log.agree_datetime; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN agree_log.agree_datetime IS '同時日時';


--
-- Name: byomei_sikkan_cd_mst; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE byomei_sikkan_cd_mst (
    hp_id integer NOT NULL,
    byomei_cd character varying(7) NOT NULL,
    start_date integer DEFAULT 0 NOT NULL,
    end_date integer DEFAULT 0 NOT NULL,
    sikkan_cd integer DEFAULT 0 NOT NULL,
    create_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    create_id integer DEFAULT 0 NOT NULL,
    create_machine character varying(60),
    update_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_id integer DEFAULT 0 NOT NULL,
    update_machine character varying(60)
);


--
-- Name: calendar; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE calendar (
    calendar_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    label character varying(10),
    doctor_id integer,
    calendar_name character varying(10),
    number_of_doctors integer,
    calendar_name_setting_type integer,
    reservation_method_type integer,
    reservable_slot_setting_type integer,
    reservable_start_days integer,
    reservable_start_time character varying(10),
    reservable_minutes_before_exam_end_time integer,
    hospital_id integer NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    calendar_time_slot integer DEFAULT 30 NOT NULL
);


--
-- Name: calendar_basic_setting; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE calendar_basic_setting (
    calendar_basic_setting_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    calendar_id integer NOT NULL,
    start_date timestamp(6) with time zone NOT NULL,
    end_date timestamp(6) with time zone,
    days_of_week integer[],
    weeks_of_month integer[],
    start_time character varying(5) NOT NULL,
    end_time character varying(5) NOT NULL,
    close_on_holiday_flag boolean DEFAULT true NOT NULL,
    reservable_slot integer,
    start_waiting_number integer,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: calendar_basic_setting_calendar_basic_setting_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE calendar_basic_setting_calendar_basic_setting_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: calendar_basic_setting_calendar_basic_setting_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE calendar_basic_setting_calendar_basic_setting_id_seq OWNED BY calendar_basic_setting.calendar_basic_setting_id;


--
-- Name: calendar_calendar_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE calendar_calendar_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: calendar_calendar_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE calendar_calendar_id_seq OWNED BY calendar.calendar_id;


--
-- Name: calendar_treatment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE calendar_treatment (
    calendar_treatment_id integer NOT NULL,
    calendar_id integer NOT NULL,
    treatment_department_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: calendar_treatment_calendar_treatment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE calendar_treatment_calendar_treatment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: calendar_treatment_calendar_treatment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE calendar_treatment_calendar_treatment_id_seq OWNED BY calendar_treatment.calendar_treatment_id;


--
-- Name: client_certificate; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE client_certificate (
    client_certificate_id integer NOT NULL,
    common_name text NOT NULL,
    client_certificate bytea NOT NULL,
    download_url text NOT NULL,
    expiration_date timestamp with time zone NOT NULL,
    hospital_id integer NOT NULL,
    install_password text NOT NULL,
    issue_date timestamp with time zone NOT NULL,
    label text NOT NULL,
    token text NOT NULL,
    token_expire_time timestamp with time zone NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: client_certificate_client_certificate_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE client_certificate_client_certificate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: client_certificate_client_certificate_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE client_certificate_client_certificate_id_seq OWNED BY client_certificate.client_certificate_id;


--
-- Name: comment_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE comment_history (
    comment_history_id integer NOT NULL,
    comment_id integer NOT NULL,
    history jsonb,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: comment_history_comment_history_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE comment_history_comment_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: comment_history_comment_history_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE comment_history_comment_history_id_seq OWNED BY comment_history.comment_history_id;


--
-- Name: common_center_item_mst; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE common_center_item_mst (
    center_cd character varying(10) NOT NULL,
    item_cd character varying(10) NOT NULL,
    kensa_item_cd character varying(20) NOT NULL,
    start_date integer DEFAULT 0 NOT NULL,
    end_date integer DEFAULT 0,
    name character varying(120),
    kana_name character varying(20),
    santei_item_cd character varying(10),
    update_date timestamp with time zone
);


--
-- Name: common_kensa_unit_mst; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE common_kensa_unit_mst (
    unit character varying(20) NOT NULL
);


--
-- Name: deleted_task; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE deleted_task (
    deleted_task_id integer NOT NULL,
    task_id integer NOT NULL,
    table_name character varying(50) NOT NULL,
    history jsonb NOT NULL,
    deleted_by character varying(100) NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: deleted_task_comment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE deleted_task_comment (
    deleted_task_comment_id integer NOT NULL,
    task_comment_id integer NOT NULL,
    table_name character varying(50) NOT NULL,
    history jsonb NOT NULL,
    deleted_by character varying(100) NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: deleted_task_comment_deleted_task_comment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE deleted_task_comment_deleted_task_comment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: deleted_task_comment_deleted_task_comment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE deleted_task_comment_deleted_task_comment_id_seq OWNED BY deleted_task_comment.deleted_task_comment_id;


--
-- Name: deleted_task_comment_task_comment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE deleted_task_comment_task_comment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: deleted_task_comment_task_comment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE deleted_task_comment_task_comment_id_seq OWNED BY deleted_task_comment.task_comment_id;


--
-- Name: deleted_task_deleted_task_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE deleted_task_deleted_task_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: deleted_task_deleted_task_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE deleted_task_deleted_task_id_seq OWNED BY deleted_task.deleted_task_id;


--
-- Name: deleted_task_task_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE deleted_task_task_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: deleted_task_task_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE deleted_task_task_id_seq OWNED BY deleted_task.task_id;


--
-- Name: every_patient_message_setting; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE every_patient_message_setting (
    hospital_id integer NOT NULL,
    sendable integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: exam_time_slot; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE exam_time_slot (
    exam_time_slot_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    calendar_id integer NOT NULL,
    exam_start_date timestamp(6) with time zone NOT NULL,
    exam_end_date timestamp(6) with time zone NOT NULL,
    slot_limit_reserve_num integer NOT NULL,
    treatment_type integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: exam_time_slot_exam_time_slot_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE exam_time_slot_exam_time_slot_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: exam_time_slot_exam_time_slot_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE exam_time_slot_exam_time_slot_id_seq OWNED BY exam_time_slot.exam_time_slot_id;


--
-- Name: fax; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE fax (
    fax_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    file_name text,
    s3_key text,
    from_fax_num character varying(15),
    to_fax_num character varying(15),
    media_url text,
    quality character varying(30),
    transmission_id bigint,
    status integer NOT NULL,
    direction integer NOT NULL,
    duration integer,
    num_pages integer,
    request_created timestamp(6) with time zone DEFAULT statement_timestamp(),
    request_updated timestamp(6) with time zone DEFAULT statement_timestamp(),
    fax_result_text text,
    fax_bad_rows integer,
    fax_result_code integer,
    fax_ecm_used boolean,
    fax_integrity_level character varying(30),
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: fax_fax_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE fax_fax_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: fax_fax_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE fax_fax_id_seq OWNED BY fax.fax_id;


--
-- Name: fax_number; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE fax_number (
    fax_number character varying(15) NOT NULL,
    hospital_id integer,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: fco_api_keys_fco_api_key_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE fco_api_keys_fco_api_key_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: freee_dummy_deal; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE freee_dummy_deal (
    id integer NOT NULL,
    issue_date date,
    due_date date,
    type character varying,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: freee_dummy_deal_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE freee_dummy_deal_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: freee_dummy_deal_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE freee_dummy_deal_id_seq OWNED BY freee_dummy_deal.id;


--
-- Name: freee_dummy_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE freee_dummy_detail (
    id integer NOT NULL,
    amount bigint NOT NULL,
    item_type character varying NOT NULL,
    deal_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: freee_dummy_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE freee_dummy_detail_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: freee_dummy_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE freee_dummy_detail_id_seq OWNED BY freee_dummy_detail.id;


--
-- Name: freee_dummy_payment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE freee_dummy_payment (
    id integer NOT NULL,
    amount bigint NOT NULL,
    date date NOT NULL,
    deal_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: freee_dummy_payment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE freee_dummy_payment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: freee_dummy_payment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE freee_dummy_payment_id_seq OWNED BY freee_dummy_payment.id;


--
-- Name: hospital_treatment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE hospital_treatment (
    hospital_treatment_id integer NOT NULL,
    hp_id integer NOT NULL,
    treatment_category_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: hospital_treatment_hospital_treatment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE hospital_treatment_hospital_treatment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: hospital_treatment_hospital_treatment_id_seq1; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE hospital_treatment_hospital_treatment_id_seq1
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: hospital_treatment_hospital_treatment_id_seq1; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE hospital_treatment_hospital_treatment_id_seq1 OWNED BY hospital_treatment.hospital_treatment_id;


--
-- Name: hp_fincode_info; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE hp_fincode_info (
    hp_id integer NOT NULL,
    hp_tenant_shop_id character varying(50) NOT NULL,
    platform_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN hp_fincode_info.platform_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN hp_fincode_info.platform_id IS 'constant.go - 会計プラットフォーム 参照';


--
-- Name: hp_inf; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE hp_inf (
    hp_id integer NOT NULL,
    start_date integer DEFAULT 0 NOT NULL,
    hp_cd character varying(7),
    rousai_hp_cd character varying(7),
    hp_name character varying(80),
    rece_hp_name character varying(80),
    kaisetu_name character varying(40),
    post_cd character varying(7),
    pref_no integer DEFAULT 0 NOT NULL,
    insurance_category character varying(1),
    address1 character varying(100),
    address2 character varying(100),
    tel character varying(15),
    create_date timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    create_id integer DEFAULT 0 NOT NULL,
    create_machine character varying(60),
    update_date timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    update_id integer DEFAULT 0 NOT NULL,
    update_machine character varying(60),
    fax_no character varying(15),
    other_contacts character varying(100),
    status smallint DEFAULT 0 NOT NULL,
    is_open boolean DEFAULT false,
    is_insurance_medical_institution boolean DEFAULT false,
    is_receive_notifications boolean DEFAULT false,
    is_terms_privacy_agreed boolean DEFAULT false,
    homepage_url character varying(2048),
    signup_date timestamp with time zone,
    review_completed_date timestamp with time zone,
    cancellation_date timestamp with time zone,
    pharmacy_flg boolean DEFAULT false,
    is_deleted integer DEFAULT 0 NOT NULL,
    tel2 character varying(15),
    is_sms_authenticated boolean DEFAULT false,
    karte_status bigint DEFAULT 0
);


--
-- Name: COLUMN hp_inf.is_receive_notifications; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN hp_inf.is_receive_notifications IS 'メール配信許諾の可否';


--
-- Name: COLUMN hp_inf.tel2; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN hp_inf.tel2 IS '主に薬局24が利用する2つ目の電話番号を保持するカラム';


--
-- Name: hp_inf_hp_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE hp_inf_hp_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: hp_inf_hp_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE hp_inf_hp_id_seq OWNED BY hp_inf.hp_id;


--
-- Name: hp_permission; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE hp_permission (
    hp_id integer NOT NULL,
    feature_id integer NOT NULL,
    permission integer DEFAULT 0 NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: line_account; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE line_account (
    id integer NOT NULL,
    customer_id integer,
    line_user_id character varying(100) NOT NULL,
    display_name character varying,
    picture_url character varying,
    is_linked numeric(1,0) DEFAULT 0 NOT NULL,
    line_link_token character varying,
    link_token_expiry timestamp with time zone,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: line_account_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE line_account_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: line_account_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE line_account_id_seq OWNED BY line_account.id;


--
-- Name: m_agent_setting; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_agent_setting (
    job_type integer NOT NULL,
    file_type integer NOT NULL,
    setting_name character varying(200) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_agree; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_agree (
    agreement_id integer NOT NULL,
    pre_agree_start_datetime timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    agree_start_datetime timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    title character varying,
    document character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN m_agree.agreement_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_agree.agreement_id IS '同意ID';


--
-- Name: COLUMN m_agree.pre_agree_start_datetime; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_agree.pre_agree_start_datetime IS '事前同時開始日時';


--
-- Name: COLUMN m_agree.agree_start_datetime; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_agree.agree_start_datetime IS '同時開始日時';


--
-- Name: COLUMN m_agree.title; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_agree.title IS 'タイトル';


--
-- Name: COLUMN m_agree.document; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_agree.document IS '交付書面';


--
-- Name: m_application_feature; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_application_feature (
    feature_id integer NOT NULL,
    feature_name character varying NOT NULL,
    description character varying DEFAULT ''::character varying,
    product_type integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN m_application_feature.product_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_application_feature.product_type IS '1:電カル、2:オペシス';


--
-- Name: m_application_feature_feature_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_application_feature_feature_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_application_feature_feature_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_application_feature_feature_id_seq OWNED BY m_application_feature.feature_id;


--
-- Name: m_llm_model; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_llm_model (
    llm_model_id integer NOT NULL,
    llm_model_name character varying NOT NULL,
    model_id character varying NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_llm_model_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_llm_model_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_llm_model_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_llm_model_id_seq OWNED BY m_llm_model.llm_model_id;


--
-- Name: m_mail_delivery_settings; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_mail_delivery_settings (
    staff_id integer NOT NULL,
    allow_login boolean NOT NULL,
    allow_reservation boolean NOT NULL,
    allow_new_message boolean NOT NULL,
    allow_task boolean NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_medical_institution; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_medical_institution (
    medical_institution_number character varying(10) NOT NULL,
    prefecture_code character varying(2) NOT NULL,
    insurance_category character varying(1) NOT NULL,
    medical_institution_name character varying(100),
    post_code character varying(10),
    address character varying(100),
    phone_number character varying(15),
    administrator_name character varying(50),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN m_medical_institution.medical_institution_number; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_medical_institution.medical_institution_number IS '医療機関コード';


--
-- Name: COLUMN m_medical_institution.prefecture_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_medical_institution.prefecture_code IS '都道府県コード、01~47、portal_m_prefecture.prefecture_code参照';


--
-- Name: COLUMN m_medical_institution.insurance_category; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_medical_institution.insurance_category IS '保険区分/点数区分コード/点数表コード、１：医科、３：歯科、４：調剤';


--
-- Name: m_operator_group; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_operator_group (
    operator_group_id integer NOT NULL,
    operator_group_name character varying NOT NULL,
    allowed_ip_list character varying DEFAULT ''::character varying NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_operator_group_operator_group_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_operator_group_operator_group_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_operator_group_operator_group_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_operator_group_operator_group_id_seq OWNED BY m_operator_group.operator_group_id;


--
-- Name: m_prompt; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_prompt (
    prompt_id integer NOT NULL,
    prompt_name character varying NOT NULL,
    llm_model integer NOT NULL,
    prompt text,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL,
    prompt_title character varying DEFAULT ''::character varying NOT NULL,
    usage_type integer DEFAULT 0 NOT NULL,
    base_prompt_id integer
);


--
-- Name: COLUMN m_prompt.prompt_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_prompt.prompt_name IS 'ユーザから設定されたプロンプト：customer';


--
-- Name: COLUMN m_prompt.usage_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_prompt.usage_type IS '利用種別、１：システム、２：ユーザーへ提供するプロンプトテンプレート';


--
-- Name: m_prompt_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_prompt_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_prompt_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_prompt_id_seq OWNED BY m_prompt.prompt_id;


--
-- Name: m_template_document_parameter; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_template_document_parameter (
    template_document_parameter_id integer NOT NULL,
    item_name character varying(255) NOT NULL,
    parameter_name character varying(255) NOT NULL,
    reflected_content character varying(255) NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_template_mail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_template_mail (
    template_mail_code character varying NOT NULL,
    mail_content_type integer NOT NULL,
    allow_type integer NOT NULL,
    mail_subject character varying NOT NULL,
    mail_body character varying NOT NULL,
    pharmacy_flg boolean DEFAULT false,
    from_mail_address character varying NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN m_template_mail.pharmacy_flg; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_template_mail.pharmacy_flg IS 'true=薬局、false=病院';


--
-- Name: COLUMN m_template_mail.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN m_template_mail.is_deleted IS '0=有効、1=削除';


--
-- Name: m_template_sms; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_template_sms (
    sms_code character varying NOT NULL,
    sms_template character varying NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_template_survey; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_template_survey (
    survey_template_id integer NOT NULL,
    f_ques_json jsonb NOT NULL,
    name character varying(250) NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_treatment_category; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_treatment_category (
    treatment_category_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(255) NOT NULL,
    group_type integer DEFAULT 0 NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    search_category_name character varying(255),
    treatment_category_group_id bigint,
    search_sort_order integer
);


--
-- Name: m_treatment_category_group; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE m_treatment_category_group (
    treatment_category_group_id bigint NOT NULL,
    name character varying(50) NOT NULL,
    sort_order bigint NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: m_treatment_category_group_sort_order_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_treatment_category_group_sort_order_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_treatment_category_group_sort_order_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_treatment_category_group_sort_order_seq OWNED BY m_treatment_category_group.sort_order;


--
-- Name: m_treatment_category_group_treatment_category_group_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_treatment_category_group_treatment_category_group_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_treatment_category_group_treatment_category_group_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_treatment_category_group_treatment_category_group_id_seq OWNED BY m_treatment_category_group.treatment_category_group_id;


--
-- Name: m_treatment_category_treatment_category_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE m_treatment_category_treatment_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: m_treatment_category_treatment_category_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE m_treatment_category_treatment_category_id_seq OWNED BY m_treatment_category.treatment_category_id;


--
-- Name: mail_verification_requests; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE mail_verification_requests (
    request_id integer NOT NULL,
    staff_id integer NOT NULL,
    email character varying(500) NOT NULL,
    is_verified integer DEFAULT 0 NOT NULL,
    verification_token character varying NOT NULL,
    token_expire_time timestamp with time zone NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: mail_verification_requests_request_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE mail_verification_requests_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: mail_verification_requests_request_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE mail_verification_requests_request_id_seq OWNED BY mail_verification_requests.request_id;


--
-- Name: medical_questionnaire_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE medical_questionnaire_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: meeting; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE meeting (
    meeting_id integer NOT NULL,
    chime_meeting_id uuid,
    reserve_id integer,
    pharmacy_reserve_id integer,
    patient_id integer NOT NULL,
    hospital_id integer NOT NULL,
    status integer NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_both_joined integer DEFAULT 0 NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: meeting_meeting_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE meeting_meeting_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: meeting_meeting_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE meeting_meeting_id_seq OWNED BY meeting.meeting_id;


--
-- Name: message_item; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE message_item (
    item_id character varying NOT NULL,
    message_id character varying NOT NULL,
    posted_member character varying NOT NULL,
    src character varying,
    item_name character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN message_item.src; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN message_item.src IS 'S3バケット: tf-denkaru-<env>-server-message-files';


--
-- Name: op_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE op_id_seq
    START WITH 10000000
    INCREMENT BY 1
    MINVALUE 10000000
    NO MAXVALUE
    CACHE 1;


--
-- Name: operator_group_permission; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE operator_group_permission (
    operator_group_id integer NOT NULL,
    feature_id integer NOT NULL,
    permission integer DEFAULT 0 NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: opesys_clinic_memo; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_clinic_memo (
    memo_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    hp_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: opesys_info_change; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_info_change (
    info_change_id integer NOT NULL,
    change_target_id integer NOT NULL,
    service_type character varying NOT NULL,
    status smallint NOT NULL,
    recept_date timestamp with time zone NOT NULL,
    recept_id character varying NOT NULL,
    handle_date timestamp with time zone,
    handle_id character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN opesys_info_change.change_target_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change.change_target_id IS '変更対応ID
pt_id/customer_idが入る';


--
-- Name: COLUMN opesys_info_change.service_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change.service_type IS '1:会員
2:クリニック';


--
-- Name: COLUMN opesys_info_change.status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change.status IS '0:承認依頼中
1:承認
2:取り下げ
3:却下';


--
-- Name: COLUMN opesys_info_change.handle_date; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change.handle_date IS '承認依頼対応日時';


--
-- Name: COLUMN opesys_info_change.handle_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change.handle_id IS '承認依頼対応者';


--
-- Name: opesys_info_change_detail_clinic; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_info_change_detail_clinic (
    info_change_id integer NOT NULL,
    doctor_id integer,
    doctor_name_display_name character varying,
    doctor_name_before character varying,
    doctor_name_after character varying,
    birthday_display_name character varying,
    birthday_before date,
    birthday_after date,
    tel_display_name character varying,
    tel_before character varying(15),
    tel_after character varying(15),
    doctor_number_display_name character varying,
    doctor_number_before character varying(8),
    doctor_number_after character varying(8),
    doctor_register_date_display_name character varying,
    doctor_register_date_before date,
    doctor_register_date_after date,
    email_display_name character varying,
    email_before character varying,
    email_after character varying,
    clinic_status_display_name character varying,
    clinic_status_before smallint,
    clinic_status_after smallint,
    doctor_gender_display_name character varying,
    doctor_gender_before smallint,
    doctor_gender_after smallint,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    certificate_label character varying(30),
    certificate_display_name character varying,
    certificate_reissue character varying,
    account_display_name character varying,
    account_unlock character varying,
    account_unlock_and_reset_password character varying,
    reset_password character varying
);


--
-- Name: COLUMN opesys_info_change_detail_clinic.doctor_name_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.doctor_name_display_name IS '申込医師名ー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.birthday_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.birthday_display_name IS '申込医師生年月日ー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.tel_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.tel_display_name IS '申込医師電話番号ー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.doctor_number_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.doctor_number_display_name IS '申込医師の医籍番号ー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.doctor_register_date_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.doctor_register_date_display_name IS '申込医師の医師登録年月日ー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.email_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.email_display_name IS '申込医師メールアドレスー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_clinic.clinic_status_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_clinic.clinic_status_display_name IS 'クリニックステータスー変更項目画面表示名';


--
-- Name: opesys_info_change_detail_portal; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_info_change_detail_portal (
    info_change_id integer NOT NULL,
    email_display_name character varying,
    email_before character varying,
    email_after character varying,
    status_display_name character varying,
    status_before integer,
    status_after integer,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN opesys_info_change_detail_portal.email_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_portal.email_display_name IS '予約サイトユーザーの使用メールアドレスー変更項目画面表示名';


--
-- Name: COLUMN opesys_info_change_detail_portal.status_display_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN opesys_info_change_detail_portal.status_display_name IS '予約サイトステータスー変更項目画面表示名';


--
-- Name: opesys_info_change_info_change_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE opesys_info_change_info_change_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: opesys_info_change_info_change_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE opesys_info_change_info_change_id_seq OWNED BY opesys_info_change.info_change_id;


--
-- Name: opesys_memo; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_memo (
    memo_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    memo_detail text NOT NULL,
    creator_name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: opesys_memo_memo_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE opesys_memo_memo_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: opesys_memo_memo_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE opesys_memo_memo_id_seq OWNED BY opesys_memo.memo_id;


--
-- Name: opesys_reserve_memo; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE opesys_reserve_memo (
    memo_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    customer_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: patient_memo; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE patient_memo (
    hospital_id integer NOT NULL,
    patient_id bigint NOT NULL,
    memo_html text,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: patient_message; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE patient_message (
    message_id character varying NOT NULL,
    message_type character varying NOT NULL,
    channel_id integer NOT NULL,
    content character varying,
    hospital_id integer NOT NULL,
    is_patient integer NOT NULL,
    posted_member character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at character varying,
    updated_at character varying,
    is_deleted integer NOT NULL
);


--
-- Name: patient_message_channel; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE patient_message_channel (
    channel_id integer NOT NULL,
    hospital_id integer NOT NULL,
    patient_id character varying NOT NULL,
    patient_sendable integer NOT NULL,
    latest_posted_message_at timestamp with time zone,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: patient_message_channel_channel_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE patient_message_channel_channel_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: patient_message_channel_channel_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE patient_message_channel_channel_id_seq OWNED BY patient_message_channel.channel_id;


--
-- Name: patient_message_channel_member; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE patient_message_channel_member (
    channel_id integer NOT NULL,
    member_id character varying NOT NULL,
    is_patient integer DEFAULT 0 NOT NULL,
    has_unread integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL,
    is_new integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN patient_message_channel_member.has_unread; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN patient_message_channel_member.has_unread IS 'constants.go - PatientSendableDisable 参照';


--
-- Name: pharmacy_patient_file; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_patient_file (
    pharmacy_patient_file_id integer NOT NULL,
    pharmacy_reserve_detail_id integer NOT NULL,
    patient_id integer NOT NULL,
    original_file_name character varying NOT NULL,
    s3_key character varying NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL,
    type integer DEFAULT 1 NOT NULL,
    mime_type character varying(100)
);


--
-- Name: COLUMN pharmacy_patient_file.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_patient_file.is_deleted IS '0=有効、1=削除';


--
-- Name: COLUMN pharmacy_patient_file.type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_patient_file.type IS 'ファイル形式';


--
-- Name: COLUMN pharmacy_patient_file.mime_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_patient_file.mime_type IS 'MIMEタイプの識別子';


--
-- Name: patient_pharmacy_file_patient_pharmacy_file_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE patient_pharmacy_file_patient_pharmacy_file_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: patient_pharmacy_file_patient_pharmacy_file_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE patient_pharmacy_file_patient_pharmacy_file_id_seq OWNED BY pharmacy_patient_file.pharmacy_patient_file_id;


--
-- Name: payment_clinic_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE payment_clinic_detail (
    payment_clinic_detail_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    patient_id integer NOT NULL,
    customer_id integer NOT NULL,
    action_type integer DEFAULT 1 NOT NULL,
    payment_type integer NOT NULL,
    payment_date timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    payment_status integer DEFAULT 0 NOT NULL,
    first_success_date timestamp(6) with time zone,
    total_amount integer DEFAULT 0 NOT NULL,
    deposit_amount integer DEFAULT 0 NOT NULL,
    is_retryable integer DEFAULT 0 NOT NULL,
    fincode_error_code character varying(15),
    error_message text,
    payment_fincode_order_id bigint,
    reserve_detail_id integer NOT NULL,
    exam_date date NOT NULL,
    treatment_category_id integer NOT NULL,
    treatment_department_id integer NOT NULL,
    card_no character varying(20) NOT NULL,
    expire character varying(4) NOT NULL,
    holder_name character varying(60) NOT NULL,
    brand character varying(60) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN payment_clinic_detail.action_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_clinic_detail.action_type IS 'constant.go - 会計アクション(クリニック,薬局共通) 参照';


--
-- Name: COLUMN payment_clinic_detail.payment_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_clinic_detail.payment_type IS 'constant.go - PaymentTypeの支払い方法 参照constant.go - PaymentTypeの支払い方法 参照';


--
-- Name: COLUMN payment_clinic_detail.payment_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_clinic_detail.payment_status IS 'constant.go - 会計ステータス(クリニック,薬局共通) 参照';


--
-- Name: COLUMN payment_clinic_detail.first_success_date; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_clinic_detail.first_success_date IS '翌月3営業日を超える場合は決済の変更/キャンセルはできないため、その日付計算用';


--
-- Name: payment_clinic_detail_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE payment_clinic_detail_history (
    payment_clinic_detail_history_id bigint NOT NULL,
    payment_clinic_detail_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    patient_id integer NOT NULL,
    customer_id integer NOT NULL,
    action_type integer DEFAULT 1 NOT NULL,
    payment_type integer NOT NULL,
    payment_date timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    payment_status integer DEFAULT 0 NOT NULL,
    first_success_date timestamp(6) with time zone,
    total_amount integer DEFAULT 0 NOT NULL,
    deposit_amount integer DEFAULT 0 NOT NULL,
    is_retryable integer DEFAULT 0 NOT NULL,
    fincode_error_code character varying(15),
    error_message text,
    payment_fincode_order_id bigint,
    reserve_detail_id integer NOT NULL,
    exam_date date NOT NULL,
    treatment_category_id integer NOT NULL,
    treatment_department_id integer NOT NULL,
    card_no character varying(20) NOT NULL,
    expire character varying(4) NOT NULL,
    holder_name character varying(60) NOT NULL,
    brand character varying(60) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: payment_clinic_detail_history_payment_clinic_detail_history_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_clinic_detail_history_payment_clinic_detail_history_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_clinic_detail_history_payment_clinic_detail_history_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE payment_clinic_detail_history_payment_clinic_detail_history_seq OWNED BY payment_clinic_detail_history.payment_clinic_detail_history_id;


--
-- Name: payment_clinic_detail_payment_clinic_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_clinic_detail_payment_clinic_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_clinic_detail_payment_clinic_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE payment_clinic_detail_payment_clinic_detail_id_seq OWNED BY payment_clinic_detail.payment_clinic_detail_id;


--
-- Name: payment_fincode_order; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE payment_fincode_order (
    payment_fincode_order_id bigint NOT NULL,
    payment_fincode_id character varying(40) NOT NULL,
    platform_id integer NOT NULL,
    shop_id character varying(15) NOT NULL,
    pay_type character varying,
    access_id character varying(30) NOT NULL,
    customer_id character varying(60) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN payment_fincode_order.pay_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_fincode_order.pay_type IS 'constant.go - Fincodeの決済種別 参照';


--
-- Name: payment_fincode_order_payment_fincode_order_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_fincode_order_payment_fincode_order_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_fincode_order_payment_fincode_order_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE payment_fincode_order_payment_fincode_order_id_seq OWNED BY payment_fincode_order.payment_fincode_order_id;


--
-- Name: payment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_pharmacy_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE payment_pharmacy_detail (
    payment_pharmacy_detail_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    patient_id integer NOT NULL,
    customer_id integer NOT NULL,
    action_type integer DEFAULT 1 NOT NULL,
    payment_type integer NOT NULL,
    payment_date timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    payment_status integer DEFAULT 1 NOT NULL,
    first_success_date timestamp(6) with time zone,
    is_retryable integer DEFAULT 0 NOT NULL,
    fincode_error_code character varying(15),
    error_message text,
    payment_fincode_order_id bigint,
    pharmacy_reserve_detail_id integer NOT NULL,
    medication_cost integer NOT NULL,
    delivery_fee integer NOT NULL,
    card_no character varying(20) NOT NULL,
    expire character varying(4) NOT NULL,
    holder_name character varying(60) NOT NULL,
    brand character varying(60) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN payment_pharmacy_detail.action_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_pharmacy_detail.action_type IS 'constant.go - 会計アクション(クリニック,薬局共通) 参照';


--
-- Name: COLUMN payment_pharmacy_detail.payment_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_pharmacy_detail.payment_type IS 'constant.go - PaymentTypeの支払い方法 参照';


--
-- Name: COLUMN payment_pharmacy_detail.payment_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN payment_pharmacy_detail.payment_status IS 'constant.go - 会計ステータス(クリニック,薬局共通) 参照';


--
-- Name: payment_pharmacy_detail_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE payment_pharmacy_detail_history (
    payment_pharmacy_detail_history_id bigint NOT NULL,
    payment_pharmacy_detail_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    patient_id integer NOT NULL,
    customer_id integer NOT NULL,
    action_type integer DEFAULT 1 NOT NULL,
    payment_type integer NOT NULL,
    payment_date timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    payment_status integer DEFAULT 1 NOT NULL,
    first_success_date timestamp(6) with time zone,
    is_retryable integer DEFAULT 0 NOT NULL,
    fincode_error_code character varying(15),
    error_message text,
    payment_fincode_order_id bigint,
    pharmacy_reserve_detail_id integer NOT NULL,
    medication_cost integer DEFAULT 0 NOT NULL,
    delivery_fee integer DEFAULT 0 NOT NULL,
    previous_medication_cost integer DEFAULT 0 NOT NULL,
    previous_delivery_fee integer DEFAULT 0 NOT NULL,
    card_no character varying(20) NOT NULL,
    expire character varying(4) NOT NULL,
    holder_name character varying(60) NOT NULL,
    brand character varying(60) NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq OWNED BY payment_pharmacy_detail_history.payment_pharmacy_detail_history_id;


--
-- Name: payment_pharmacy_detail_payment_pharmacy_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE payment_pharmacy_detail_payment_pharmacy_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: payment_pharmacy_detail_payment_pharmacy_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE payment_pharmacy_detail_payment_pharmacy_detail_id_seq OWNED BY payment_pharmacy_detail.payment_pharmacy_detail_id;


--
-- Name: permission_mst; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE permission_mst (
    function_cd character varying(8) NOT NULL,
    permission integer DEFAULT 0 NOT NULL,
    create_date timestamp with time zone NOT NULL,
    create_id integer DEFAULT 0 NOT NULL,
    create_machine character varying(60),
    update_date timestamp with time zone NOT NULL,
    update_id integer DEFAULT 0 NOT NULL,
    update_machine character varying(60),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: pharmacy_delivery_address; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_delivery_address (
    pharmacy_delivery_address_id integer NOT NULL,
    pharmacy_reserve_id integer NOT NULL,
    delivery_address_id integer NOT NULL,
    address1 character varying(2000) NOT NULL,
    address2 character varying(2000),
    post_code character varying(20) NOT NULL,
    phone_number character varying(20) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pharmacy_delivery_address.address1; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_address.address1 IS 'address by post code';


--
-- Name: COLUMN pharmacy_delivery_address.address2; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_address.address2 IS 'more info address';


--
-- Name: COLUMN pharmacy_delivery_address.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_address.is_deleted IS '0=有効、1=削除';


--
-- Name: pharmacy_delivery_address_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_delivery_address_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_delivery_address_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_delivery_address_id_seq OWNED BY pharmacy_delivery_address.delivery_address_id;


--
-- Name: pharmacy_delivery_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_delivery_history (
    pharmacy_reserve_id integer NOT NULL,
    delivery_inquiry_num character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    created_by character varying,
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_by character varying,
    is_deleted integer DEFAULT 0 NOT NULL,
    delivery_name character varying(200) DEFAULT ''::character varying NOT NULL,
    delivery_post_code character varying(20) DEFAULT ''::character varying NOT NULL,
    delivery_address character varying(2000) DEFAULT ''::character varying NOT NULL,
    delivery_phone_number character varying(20) DEFAULT ''::character varying NOT NULL
);


--
-- Name: COLUMN pharmacy_delivery_history.delivery_name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_history.delivery_name IS 'お届け先 名称／お届け先 名称２';


--
-- Name: COLUMN pharmacy_delivery_history.delivery_post_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_history.delivery_post_code IS 'お届け先 郵便番号';


--
-- Name: COLUMN pharmacy_delivery_history.delivery_address; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_history.delivery_address IS 'お届け先 住所／お届け先 住所２／お届け先 住所３';


--
-- Name: COLUMN pharmacy_delivery_history.delivery_phone_number; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_delivery_history.delivery_phone_number IS 'お届け先 電話番号';


--
-- Name: pharmacy_desired_date; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_desired_date (
    pharmacy_desired_date_id integer NOT NULL,
    pharmacy_reserve_id integer NOT NULL,
    desired_type integer NOT NULL,
    desired_date timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pharmacy_desired_date.desired_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_desired_date.desired_type IS '1=午前中、2=時間指定（12時〜21時の1時間区切り）、3=時間指定なし';


--
-- Name: COLUMN pharmacy_desired_date.desired_date; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_desired_date.desired_date IS 'desired_typeが
1=午前中の場合、開始時間（9:00）
2=時間指定の場合、フル日時（12:00〜21:00）
3=時間指定なしの場合、年月日のみ
4=設定なしの場合、9999年';


--
-- Name: COLUMN pharmacy_desired_date.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_desired_date.is_deleted IS '0=有効、1=削除';


--
-- Name: pharmacy_desired_date_pharmacy_desired_date_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_desired_date_pharmacy_desired_date_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_desired_date_pharmacy_desired_date_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_desired_date_pharmacy_desired_date_id_seq OWNED BY pharmacy_desired_date.pharmacy_desired_date_id;


--
-- Name: pharmacy_holiday; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_holiday (
    pharmacy_holiday_id integer NOT NULL,
    hospital_id integer NOT NULL,
    holiday_start_date timestamp with time zone NOT NULL,
    holiday_end_date timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pharmacy_holiday.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_holiday.is_deleted IS '0=有効、1=削除';


--
-- Name: pharmacy_holiday_pharmacy_holiday_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_holiday_pharmacy_holiday_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_holiday_pharmacy_holiday_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_holiday_pharmacy_holiday_id_seq OWNED BY pharmacy_holiday.pharmacy_holiday_id;


--
-- Name: pharmacy_reserve; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_reserve (
    pharmacy_reserve_id integer NOT NULL,
    patient_id integer NOT NULL,
    desired_date_status integer DEFAULT 2 NOT NULL,
    reserve_update_date timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    reserve_id integer,
    sms_status integer DEFAULT 1 NOT NULL,
    videocall_status integer DEFAULT 1 NOT NULL,
    postal_service_type integer DEFAULT 1 NOT NULL,
    csv_status integer DEFAULT 1 NOT NULL,
    pharmacist_status integer DEFAULT 1 NOT NULL,
    pharmacist_id integer,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pharmacy_reserve.desired_date_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.desired_date_status IS '1=確認中、2=設定前、3=設定済';


--
-- Name: COLUMN pharmacy_reserve.sms_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.sms_status IS '1=未通知、2=通知済';


--
-- Name: COLUMN pharmacy_reserve.videocall_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.videocall_status IS '1=未入室、2=患者入室済、3=スタッフ入室済、4=通話中';


--
-- Name: COLUMN pharmacy_reserve.postal_service_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.postal_service_type IS '1=ゆうパケット(3kg以下)、2=ゆうパック(30kg以下)';


--
-- Name: COLUMN pharmacy_reserve.csv_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.csv_status IS '1=出力前、2=取込前、3=取込後';


--
-- Name: COLUMN pharmacy_reserve.pharmacist_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.pharmacist_status IS '1=設定前、2=設定済';


--
-- Name: COLUMN pharmacy_reserve.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve.is_deleted IS '0=有効、1=削除';


--
-- Name: pharmacy_reserve_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_reserve_detail (
    pharmacy_reserve_detail_id integer NOT NULL,
    pharmacy_reserve_id integer NOT NULL,
    reserve_detail_id integer,
    patient_id integer NOT NULL,
    prescription_type integer DEFAULT 1 NOT NULL,
    status integer DEFAULT 1 NOT NULL,
    guidance_status integer DEFAULT 1 NOT NULL,
    payment_status integer DEFAULT 1 NOT NULL,
    has_prescription_record boolean DEFAULT false NOT NULL,
    is_taking_medication boolean DEFAULT false NOT NULL,
    taking_medication_names character varying(1000),
    medication_form_answer_date timestamp with time zone,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    generic_drug_desire integer,
    has_electronic_prescription boolean,
    redemption_number character varying(20),
    uses_electronic_prescription boolean,
    prescription_delivery_method integer
);


--
-- Name: COLUMN pharmacy_reserve_detail.prescription_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.prescription_type IS '1=紙、2=電子';


--
-- Name: COLUMN pharmacy_reserve_detail.status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.status IS '1=処方箋未着、2=処方箋到着、3=在庫なし、4=在庫確認済、5=梱包済、6=発送済、7=キャンセル';


--
-- Name: COLUMN pharmacy_reserve_detail.guidance_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.guidance_status IS '1=指導前、2=未指導、3=指導済';


--
-- Name: COLUMN pharmacy_reserve_detail.payment_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.payment_status IS '1=会計前、2=会計済、3=決済エラー';


--
-- Name: COLUMN pharmacy_reserve_detail.has_prescription_record; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.has_prescription_record IS 'お薬手帳の持ちフラッグ';


--
-- Name: COLUMN pharmacy_reserve_detail.is_taking_medication; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.is_taking_medication IS '現在服用中のお薬はあるフラッグ';


--
-- Name: COLUMN pharmacy_reserve_detail.taking_medication_names; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.taking_medication_names IS '服薬中のお薬名';


--
-- Name: COLUMN pharmacy_reserve_detail.medication_form_answer_date; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.medication_form_answer_date IS '服薬指導希望日時を設定のフォーム完了した日付';


--
-- Name: COLUMN pharmacy_reserve_detail.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.is_deleted IS '0=有効、1=削除';


--
-- Name: COLUMN pharmacy_reserve_detail.has_electronic_prescription; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.has_electronic_prescription IS '処方箋の控えの持ちフラッグ';


--
-- Name: COLUMN pharmacy_reserve_detail.redemption_number; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.redemption_number IS '引換番号';


--
-- Name: COLUMN pharmacy_reserve_detail.uses_electronic_prescription; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.uses_electronic_prescription IS '処方箋の利用フラッグ';


--
-- Name: COLUMN pharmacy_reserve_detail.prescription_delivery_method; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_detail.prescription_delivery_method IS '1=番号、2=紙面、3=FAX';


--
-- Name: pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq OWNED BY pharmacy_reserve_detail.pharmacy_reserve_detail_id;


--
-- Name: pharmacy_reserve_pharmacy_reserve_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_reserve_pharmacy_reserve_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_reserve_pharmacy_reserve_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_reserve_pharmacy_reserve_id_seq OWNED BY pharmacy_reserve.pharmacy_reserve_id;


--
-- Name: pharmacy_reserve_status_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pharmacy_reserve_status_history (
    pharmacy_reserve_status_history_id integer NOT NULL,
    pharmacy_reserve_detail_id integer NOT NULL,
    status integer,
    status_cancel_type integer,
    guidance_status integer,
    pharmacist_id integer,
    pharmacist_memo character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pharmacy_reserve_status_history.status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_status_history.status IS '1=処方箋未着、2=処方箋到着、3=在庫なし、4=在庫確認済、5=梱包済、6=発送済、7=キャンセル';


--
-- Name: COLUMN pharmacy_reserve_status_history.status_cancel_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_status_history.status_cancel_type IS '1=患者キャンセル（診療予約）、2=患者キャンセル（薬局変更）、3=クリニックキャンセル（診療予約）、4=薬局キャンセル（服薬指導予約）';


--
-- Name: COLUMN pharmacy_reserve_status_history.guidance_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_status_history.guidance_status IS '1=指導前、2=未指導、3=指導済';


--
-- Name: COLUMN pharmacy_reserve_status_history.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pharmacy_reserve_status_history.is_deleted IS '0=有効、1=削除';


--
-- Name: pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq OWNED BY pharmacy_reserve_status_history.pharmacy_reserve_status_history_id;


--
-- Name: portal_agree_log; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_agree_log (
    agreement_id integer NOT NULL,
    customer_id integer NOT NULL,
    agree_datetime timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_agree_log.agreement_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_agree_log.agreement_id IS '同意ID';


--
-- Name: COLUMN portal_agree_log.customer_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_agree_log.customer_id IS '患者ID';


--
-- Name: COLUMN portal_agree_log.agree_datetime; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_agree_log.agree_datetime IS '同時日時';


--
-- Name: portal_ai_chat_content; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_ai_chat_content (
    content_id integer NOT NULL,
    portal_customer_id integer NOT NULL,
    content_history_json jsonb NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_ai_chat_content_content_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_ai_chat_content_content_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_ai_chat_content_content_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_ai_chat_content_content_id_seq OWNED BY portal_ai_chat_content.content_id;


--
-- Name: portal_ai_chat_content_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_ai_chat_content_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_ai_chat_content_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_ai_chat_content_id_seq OWNED BY portal_ai_chat_content.content_id;


--
-- Name: portal_business_time; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_business_time (
    business_time_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    hospital_id integer NOT NULL,
    start_time character varying(200) NOT NULL,
    end_time character varying(200) NOT NULL,
    mon_flag numeric(1,0) DEFAULT 0 NOT NULL,
    tue_flag numeric(1,0) DEFAULT 0 NOT NULL,
    wed_flag numeric(1,0) DEFAULT 0 NOT NULL,
    thu_flag numeric(1,0) DEFAULT 0 NOT NULL,
    fri_flag numeric(1,0) DEFAULT 0 NOT NULL,
    sat_flag numeric(1,0) DEFAULT 0 NOT NULL,
    sun_flag numeric(1,0) DEFAULT 0 NOT NULL,
    holiday_flag numeric(1,0) DEFAULT 0 NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_business_time_business_time_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_business_time_business_time_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_business_time_business_time_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_business_time_business_time_id_seq OWNED BY portal_business_time.business_time_id;


--
-- Name: portal_contents; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_contents (
    contents_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    title character varying(500),
    url character varying(1000),
    body_text character varying(4000),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_contents_contents_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_contents_contents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_contents_contents_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_contents_contents_id_seq OWNED BY portal_contents.contents_id;


--
-- Name: portal_contents_pub; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_contents_pub (
    contents_id integer NOT NULL,
    publication_type integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    pict_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer (
    customer_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    birthday date,
    gender smallint,
    telephone character varying(100),
    status integer NOT NULL,
    start_date timestamp(6) with time zone,
    end_date timestamp(6) with time zone,
    parent_id integer,
    is_same_parent_address numeric(1,0),
    insured_org_code character varying(100),
    insured_customer_code character varying(100),
    insure_code character varying(100),
    medical_cert_payer_code character varying(100),
    medical_cert_receipt_code character varying(100),
    insured_branch_code character varying(100),
    name character varying(200),
    kana_name character varying(200),
    is_deleted integer DEFAULT 0 NOT NULL,
    is_receive_notifications boolean DEFAULT false,
    aid character varying(8),
    bid character varying(8),
    cid character varying(8)
);


--
-- Name: COLUMN portal_customer.gender; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer.gender IS '男=1,女=2';


--
-- Name: COLUMN portal_customer.parent_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer.parent_id IS 'nullの場合は主会員、nullでない場合は家族会員';


--
-- Name: COLUMN portal_customer.is_receive_notifications; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer.is_receive_notifications IS 'メール配信許諾の可否';


--
-- Name: COLUMN portal_customer.aid; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer.aid IS '広告からサインアップした際に付与されるクエリパラメータ。bid, cidも同じ。';


--
-- Name: portal_customer_browser; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_browser (
    browser_id integer NOT NULL,
    customer_id integer NOT NULL,
    browser_name text NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_browser_browser_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_browser_browser_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_browser_browser_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_browser_browser_id_seq OWNED BY portal_customer_browser.browser_id;


--
-- Name: portal_customer_customer_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_customer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_customer_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_customer_id_seq OWNED BY portal_customer.customer_id;


--
-- Name: portal_customer_delivery_address; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_delivery_address (
    delivery_address_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    customer_id integer NOT NULL,
    is_home_address boolean NOT NULL,
    address1 character varying(2000) NOT NULL,
    address2 character varying(2000),
    post_code character varying(20) NOT NULL,
    phone_number character varying(20) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_customer_delivery_address.address1; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer_delivery_address.address1 IS 'address by post code';


--
-- Name: COLUMN portal_customer_delivery_address.address2; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer_delivery_address.address2 IS 'more info address';


--
-- Name: portal_customer_delivery_address_delivery_address_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_delivery_address_delivery_address_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_delivery_address_delivery_address_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_delivery_address_delivery_address_id_seq OWNED BY portal_customer_delivery_address.delivery_address_id;


--
-- Name: portal_customer_file; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_file (
    customer_file_id integer NOT NULL,
    customer_id integer NOT NULL,
    original_file_name character varying NOT NULL,
    s3_key character varying NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    size integer NOT NULL,
    type integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_file_customer_file_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_file_customer_file_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_file_customer_file_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_file_customer_file_id_seq OWNED BY portal_customer_file.customer_file_id;


--
-- Name: portal_customer_login; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_login (
    login_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    customer_id integer NOT NULL,
    email character varying(500) NOT NULL,
    password_hash character varying(500) NOT NULL,
    login_refresh_token character varying(500) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    miss_login_count integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_login_login_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_login_login_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_login_login_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_login_login_id_seq OWNED BY portal_customer_login.login_id;


--
-- Name: portal_customer_payment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_payment (
    customer_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    customer_payment_id integer NOT NULL,
    fincode_customer_id character varying(300) NOT NULL,
    platform_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_customer_payment.platform_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer_payment.platform_id IS 'constant.go - 会計プラットフォーム(クリニック,薬局共通) 参照';


--
-- Name: portal_customer_payment_card; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_payment_card (
    card_id integer NOT NULL,
    customer_id integer NOT NULL,
    fincode_insurance_card_id character varying(50) NOT NULL,
    fincode_free_card_id character varying(50) NOT NULL,
    fincode_ht_insurance_card_id character varying(50) NOT NULL,
    fincode_ht_free_card_id character varying(50) NOT NULL,
    is_insurance_3ds_verified boolean DEFAULT false NOT NULL,
    is_free_3ds_verified boolean DEFAULT false NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_payment_card_card_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_payment_card_card_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_payment_card_card_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_payment_card_card_id_seq OWNED BY portal_customer_payment_card.card_id;


--
-- Name: portal_customer_payment_customer_payment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_payment_customer_payment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_payment_customer_payment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_payment_customer_payment_id_seq OWNED BY portal_customer_payment.customer_payment_id;


--
-- Name: portal_customer_pharmacy; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_pharmacy (
    portal_customer_pharmacy_id integer NOT NULL,
    reserve_id integer NOT NULL,
    customer_id integer NOT NULL,
    pharmacy_name character varying(500) NOT NULL,
    pharmacy_store_name character varying(500),
    fax_number character varying(20) NOT NULL,
    address1 character varying(2000) NOT NULL,
    address2 character varying(2000),
    post_code character varying(20) NOT NULL,
    phone_number character varying(20),
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_customer_pharmacy.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_customer_pharmacy.is_deleted IS '0=有効、1=削除';


--
-- Name: portal_customer_pharmacy_portal_customer_pharmacy_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_pharmacy_portal_customer_pharmacy_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_pharmacy_portal_customer_pharmacy_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_pharmacy_portal_customer_pharmacy_id_seq OWNED BY portal_customer_pharmacy.portal_customer_pharmacy_id;


--
-- Name: portal_customer_social_login; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_social_login (
    socl_login_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    social_login_code character varying(100) NOT NULL,
    social_type integer NOT NULL,
    login_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_social_login_socl_login_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_social_login_socl_login_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_social_login_socl_login_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_social_login_socl_login_id_seq OWNED BY portal_customer_social_login.socl_login_id;


--
-- Name: portal_customer_survey; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_customer_survey (
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    customer_survey_id integer NOT NULL,
    customer_id integer NOT NULL,
    common jsonb,
    pharmacy jsonb,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_customer_survey_customer_survey_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_customer_survey_customer_survey_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_customer_survey_customer_survey_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_customer_survey_customer_survey_id_seq OWNED BY portal_customer_survey.customer_survey_id;


--
-- Name: portal_event_count_log; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_event_count_log (
    event_key character varying(255) NOT NULL,
    event_date character varying(10) NOT NULL,
    event_count integer NOT NULL,
    created_by character varying(100) DEFAULT 'Customer'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'Customer'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_exam_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_exam_detail (
    exam_detail_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    exam_id integer,
    status integer NOT NULL,
    reserve_detail_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_exam_detail_exam_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_exam_detail_exam_detail_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_exam_detail_exam_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_exam_detail_exam_detail_id_seq OWNED BY portal_exam_detail.exam_detail_id;


--
-- Name: portal_hosp_picture; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hosp_picture (
    pict_id integer NOT NULL,
    hospital_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    type integer,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_hosp_station; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hosp_station (
    hospital_id integer NOT NULL,
    station_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    walking_minute integer,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_hosp_tag; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hosp_tag (
    hospital_id integer NOT NULL,
    tag_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_hospital; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital (
    hospital_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    hp_inf_id integer NOT NULL,
    name character varying(200) NOT NULL,
    description character varying(4000),
    telephone character varying(100) NOT NULL,
    carpark_detail character varying(4000),
    post_code character varying(100) NOT NULL,
    address1 character varying(2000) NOT NULL,
    address2 character varying(2000),
    latitude numeric(10,7),
    longitude numeric(10,7),
    email character varying(100) NOT NULL,
    fax character varying(100),
    is_carpark boolean DEFAULT false NOT NULL,
    description_title character varying(1000) NOT NULL,
    payment_details character varying(4000) NOT NULL,
    is_active boolean DEFAULT false NOT NULL,
    business_time_detail character varying(2000),
    timeline_description character varying(2000),
    holiday_detail character varying(200),
    access_detail character varying(4000),
    home_page character varying(2000),
    scuel_id character varying(10),
    is_deleted integer DEFAULT 0 NOT NULL,
    director_name character varying(40),
    mail_address character varying(255)
);


--
-- Name: COLUMN portal_hospital.address1; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_hospital.address1 IS 'address by post code';


--
-- Name: COLUMN portal_hospital.address2; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_hospital.address2 IS 'more info address';


--
-- Name: COLUMN portal_hospital.email; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_hospital.email IS 'メールアドレス';


--
-- Name: portal_hospital_examination; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital_examination (
    portal_hospital_id integer NOT NULL,
    examination_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_hospital_favorite; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital_favorite (
    hospital_favorite_id integer NOT NULL,
    portal_hospital_id integer,
    scuel_id character varying(10),
    customer_id integer,
    session_id uuid,
    created_by character varying(100) DEFAULT 'Customer'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'Customer'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    CONSTRAINT check_customer_or_session CHECK (((customer_id IS NOT NULL) OR (session_id IS NOT NULL)))
);


--
-- Name: portal_hospital_favorite_hospital_favorite_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_hospital_favorite_hospital_favorite_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_hospital_favorite_hospital_favorite_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_hospital_favorite_hospital_favorite_id_seq OWNED BY portal_hospital_favorite.hospital_favorite_id;


--
-- Name: portal_hospital_hospital_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_hospital_hospital_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_hospital_hospital_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_hospital_hospital_id_seq OWNED BY portal_hospital.hospital_id;


--
-- Name: portal_hospital_notification; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital_notification (
    hospital_notification_id integer NOT NULL,
    portal_hospital_id integer NOT NULL,
    hospital_staff_id integer NOT NULL,
    title character varying(50) NOT NULL,
    description character varying NOT NULL,
    status numeric(1,0) NOT NULL,
    start_date timestamp(6) with time zone,
    end_date timestamp(6) with time zone,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_hospital_notification.hospital_staff_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_hospital_notification.hospital_staff_id IS 'Id from Denkaru hospital';


--
-- Name: portal_hospital_notification_hospital_notification_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_hospital_notification_hospital_notification_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_hospital_notification_hospital_notification_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_hospital_notification_hospital_notification_id_seq OWNED BY portal_hospital_notification.hospital_notification_id;


--
-- Name: portal_hospital_specialist; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital_specialist (
    hospital_id integer NOT NULL,
    specialist_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_hospital_staff; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_hospital_staff (
    hospital_staff_id integer NOT NULL,
    hospital_id integer NOT NULL,
    name character varying NOT NULL,
    description character varying,
    specialist_detail character varying,
    is_director boolean DEFAULT false NOT NULL,
    "order" integer NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL,
    experience_detail character varying
);


--
-- Name: portal_hospital_staff_hospital_staff_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_hospital_staff_hospital_staff_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_hospital_staff_hospital_staff_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_hospital_staff_hospital_staff_id_seq OWNED BY portal_hospital_staff.hospital_staff_id;


--
-- Name: portal_m_agree; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_agree (
    agreement_id integer NOT NULL,
    agree_start_datetime timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    title character varying,
    document character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_m_agree.agreement_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_agree.agreement_id IS '同意ID';


--
-- Name: COLUMN portal_m_agree.agree_start_datetime; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_agree.agree_start_datetime IS '同時開始日時';


--
-- Name: COLUMN portal_m_agree.title; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_agree.title IS 'タイトル';


--
-- Name: COLUMN portal_m_agree.document; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_agree.document IS '交付書面';


--
-- Name: portal_m_city; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_city (
    city_id integer NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    group_code character varying(6) NOT NULL,
    prefecture_id integer NOT NULL,
    city_code character varying(3) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL
);


--
-- Name: COLUMN portal_m_city.group_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_city.group_code IS '団体コード';


--
-- Name: COLUMN portal_m_city.city_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_city.city_code IS '市区町村コード';


--
-- Name: portal_m_city_city_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_city_city_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_city_city_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_city_city_id_seq OWNED BY portal_m_city.city_id;


--
-- Name: portal_m_drug; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_drug (
    drug_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    drug01_category_id integer,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_drug01_category; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_drug01_category (
    drug01_category_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    drug02_category_id integer,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_drug01_category_drug01_category_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_drug01_category_drug01_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_drug01_category_drug01_category_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_drug01_category_drug01_category_id_seq OWNED BY portal_m_drug01_category.drug01_category_id;


--
-- Name: portal_m_drug02_category; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_drug02_category (
    drug02_category_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_drug02_category_drug02_category_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_drug02_category_drug02_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_drug02_category_drug02_category_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_drug02_category_drug02_category_id_seq OWNED BY portal_m_drug02_category.drug02_category_id;


--
-- Name: portal_m_drug_drug_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_drug_drug_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_drug_drug_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_drug_drug_id_seq OWNED BY portal_m_drug.drug_id;


--
-- Name: portal_m_examination; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_examination (
    examination_id integer NOT NULL,
    name character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    type integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_m_examination.type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_examination.type IS '1: 検査・設備 2: 健診・ドック';


--
-- Name: portal_m_examination_examination_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_examination_examination_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_m_examination_examination_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_examination_examination_id_seq OWNED BY portal_m_examination.examination_id;


--
-- Name: portal_m_prefecture; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_prefecture (
    prefecture_id integer NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    group_code character varying(6) NOT NULL,
    region character varying(150) NOT NULL,
    prefecture_code character varying(2) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL
);


--
-- Name: COLUMN portal_m_prefecture.group_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_prefecture.group_code IS '団体コード';


--
-- Name: COLUMN portal_m_prefecture.prefecture_code; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_m_prefecture.prefecture_code IS '都道府県コード';


--
-- Name: portal_m_prefecture_prefecture_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_prefecture_prefecture_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_prefecture_prefecture_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_prefecture_prefecture_id_seq OWNED BY portal_m_prefecture.prefecture_id;


--
-- Name: portal_m_railline; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_railline (
    railline_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(200) NOT NULL,
    railline_company_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_railline_company; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_railline_company (
    railline_company_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(200) NOT NULL,
    type integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_railline_company_railline_company_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_railline_company_railline_company_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_railline_company_railline_company_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_railline_company_railline_company_id_seq OWNED BY portal_m_railline_company.railline_company_id;


--
-- Name: portal_m_railline_railline_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_railline_railline_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_railline_railline_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_railline_railline_id_seq OWNED BY portal_m_railline.railline_id;


--
-- Name: portal_m_search_keyword_equiv; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_search_keyword_equiv (
    id integer NOT NULL,
    keyword character varying NOT NULL,
    keyword_equiv character varying NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_search_keyword_equiv_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_search_keyword_equiv_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_m_search_keyword_equiv_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_search_keyword_equiv_id_seq OWNED BY portal_m_search_keyword_equiv.id;


--
-- Name: portal_m_specialist; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_specialist (
    specialist_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_specialist_specialist_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_specialist_specialist_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_specialist_specialist_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_specialist_specialist_id_seq OWNED BY portal_m_specialist.specialist_id;


--
-- Name: portal_m_station; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_station (
    station_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    railline_id integer NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_m_station_station_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_station_station_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_station_station_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_station_station_id_seq OWNED BY portal_m_station.station_id;


--
-- Name: portal_m_tag; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_m_tag (
    tag_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    name character varying(200) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    sort_order integer DEFAULT 0
);


--
-- Name: portal_m_tag_tag_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_m_tag_tag_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_m_tag_tag_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_m_tag_tag_id_seq OWNED BY portal_m_tag.tag_id;


--
-- Name: portal_pict; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_pict (
    pict_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    file_name character varying(150) NOT NULL,
    filepath character varying(1000) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_pict_pict_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_pict_pict_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_pict_pict_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_pict_pict_id_seq OWNED BY portal_pict.pict_id;


--
-- Name: portal_prescription_drug; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_prescription_drug (
    presc_drug_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    exam_detail_id integer,
    drug_id integer NOT NULL,
    status integer NOT NULL,
    reserve_detail_id integer,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_prescription_drug_presc_drug_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_prescription_drug_presc_drug_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_prescription_drug_presc_drug_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_prescription_drug_presc_drug_id_seq OWNED BY portal_prescription_drug.presc_drug_id;


--
-- Name: portal_staff_picture; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_staff_picture (
    pict_id integer NOT NULL,
    hospital_staff_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_t_address_addr_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_address_addr_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_business_time_business_time_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_business_time_business_time_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_contents_contents_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_contents_contents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_customer_customer_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_customer_customer_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_customer_login_login_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_customer_login_login_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_customer_social_login_socl_login_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_customer_social_login_socl_login_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_exam_detail_exam_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_exam_detail_exam_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_exam_time_slot_exam_time_slot_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_exam_time_slot_exam_time_slot_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_hospital_hospital_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_hospital_hospital_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_pict_pict_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_pict_pict_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_prescription_drug_presc_drug_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_prescription_drug_presc_drug_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_review_review_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_review_review_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_treat_drug_treat_drug_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_treat_drug_treat_drug_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_t_treatment_treatment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_t_treatment_treatment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    MAXVALUE 2147483647
    CACHE 1;


--
-- Name: portal_treat_drug; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_treat_drug (
    treat_drug_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    treatment_id integer NOT NULL,
    drug_id integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: portal_treat_drug_treat_drug_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_treat_drug_treat_drug_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_treat_drug_treat_drug_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_treat_drug_treat_drug_id_seq OWNED BY portal_treat_drug.treat_drug_id;


--
-- Name: portal_treatment_dept_grp; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_treatment_dept_grp (
    treatment_dept_grp_id integer NOT NULL,
    group_id integer NOT NULL,
    treatment_department_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN portal_treatment_dept_grp.treatment_dept_grp_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_treatment_dept_grp.treatment_dept_grp_id IS 'ID';


--
-- Name: COLUMN portal_treatment_dept_grp.group_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_treatment_dept_grp.group_id IS 'グループID
	1: 一般内科
	2: 花粉症/アレルギー
	3: 生活習慣/体調管理';


--
-- Name: COLUMN portal_treatment_dept_grp.treatment_department_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN portal_treatment_dept_grp.treatment_department_id IS '診療メニューID';


--
-- Name: portal_treatment_dept_grp_treatment_dept_grp_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE portal_treatment_dept_grp_treatment_dept_grp_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: portal_treatment_dept_grp_treatment_dept_grp_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE portal_treatment_dept_grp_treatment_dept_grp_id_seq OWNED BY portal_treatment_dept_grp.treatment_dept_grp_id;


--
-- Name: portal_withdrawal_reason; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE portal_withdrawal_reason (
    customer_id integer NOT NULL,
    type integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    reason_detail character varying(4000),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: prescription_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE prescription_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: prescription_image; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE prescription_image (
    prescription_image_id integer NOT NULL,
    prescription_reception_id integer NOT NULL,
    original_file_name character varying NOT NULL,
    s3_key character varying NOT NULL,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    mime_type character varying(100)
);


--
-- Name: COLUMN prescription_image.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_image.is_deleted IS '0=有効、1=削除';


--
-- Name: prescription_image_prescription_image_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE prescription_image_prescription_image_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: prescription_image_prescription_image_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE prescription_image_prescription_image_id_seq OWNED BY prescription_image.prescription_image_id;


--
-- Name: prescription_reception; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE prescription_reception (
    prescription_reception_id integer NOT NULL,
    hp_id integer NOT NULL,
    reception_timestamp timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    reception_status integer NOT NULL,
    phone_number character varying(15) NOT NULL,
    generic_drug_desire integer NOT NULL,
    prescription_record_bring integer NOT NULL,
    other_request character varying(200),
    print_status integer NOT NULL,
    sms_status integer NOT NULL,
    memo character varying(20),
    is_new boolean DEFAULT true NOT NULL,
    aid character varying(8),
    bid character varying(8),
    cid character varying(8),
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN prescription_reception.reception_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.reception_status IS '1=受付済み、2=キャンセル';


--
-- Name: COLUMN prescription_reception.generic_drug_desire; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.generic_drug_desire IS '1=希望する、2=希望しない、3=お任せ';


--
-- Name: COLUMN prescription_reception.prescription_record_bring; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.prescription_record_bring IS '1=持参する、2=持参しない';


--
-- Name: COLUMN prescription_reception.print_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.print_status IS '1=未印刷、2=印刷済み';


--
-- Name: COLUMN prescription_reception.sms_status; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.sms_status IS '1=未送信、2=送信済';


--
-- Name: COLUMN prescription_reception.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prescription_reception.is_deleted IS '0=有効、1=削除';


--
-- Name: prescription_reception_prescription_reception_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE prescription_reception_prescription_reception_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: prescription_reception_prescription_reception_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE prescription_reception_prescription_reception_id_seq OWNED BY prescription_reception.prescription_reception_id;


--
-- Name: prompt_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE prompt_history (
    prompt_history_id integer NOT NULL,
    prompt_id integer NOT NULL,
    version integer NOT NULL,
    prompt_name character varying NOT NULL,
    llm_model integer NOT NULL,
    prompt text,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL,
    prompt_title character varying DEFAULT ''::character varying NOT NULL,
    usage_type integer DEFAULT 0 NOT NULL
);


--
-- Name: prompt_log; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE prompt_log (
    prompt_id integer NOT NULL,
    user_id integer NOT NULL,
    order_value integer NOT NULL,
    is_template boolean DEFAULT false NOT NULL,
    is_latest boolean DEFAULT false NOT NULL,
    is_active boolean DEFAULT false NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN prompt_log.order_value; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prompt_log.order_value IS 'プロンプト表示順番';


--
-- Name: COLUMN prompt_log.is_template; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prompt_log.is_template IS 'true: システムから設定、false: ユーザから設定';


--
-- Name: COLUMN prompt_log.is_latest; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prompt_log.is_latest IS '最後の使うプロンプト';


--
-- Name: COLUMN prompt_log.is_active; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN prompt_log.is_active IS 'プロンプトの状態、true: 有効、false: 無効';


--
-- Name: pt_inf; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE pt_inf (
    hp_id integer NOT NULL,
    pt_id bigint NOT NULL,
    seq_no bigint NOT NULL,
    pt_num bigint NOT NULL,
    kana_name character varying(200),
    name character varying(200),
    sex integer DEFAULT 0 NOT NULL,
    birthday integer DEFAULT 0 NOT NULL,
    is_dead integer DEFAULT 0 NOT NULL,
    death_date integer DEFAULT 0 NOT NULL,
    home_post character varying(7),
    home_address1 character varying(100),
    home_address2 character varying(100),
    tel1 character varying(15),
    tel2 character varying(15),
    mail character varying(100),
    setainusi character varying(100),
    zokugara character varying(20),
    job character varying(40),
    renraku_name character varying(100),
    renraku_post character varying(7),
    renraku_address1 character varying(100),
    renraku_address2 character varying(100),
    renraku_tel character varying(15),
    renraku_memo character varying(100),
    office_name character varying(100),
    office_post character varying(7),
    office_address1 character varying(100),
    office_address2 character varying(100),
    office_tel character varying(15),
    office_memo character varying(100),
    is_ryosyo_detail integer DEFAULT 1 NOT NULL,
    primary_doctor integer NOT NULL,
    is_tester integer DEFAULT 0 NOT NULL,
    is_delete integer DEFAULT 0 NOT NULL,
    create_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    create_id integer DEFAULT 0 NOT NULL,
    create_machine character varying(60),
    update_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_id integer DEFAULT 0 NOT NULL,
    update_machine character varying(60),
    main_hoken_pid integer DEFAULT 0 NOT NULL,
    reference_no bigint NOT NULL,
    limit_cons_flg integer DEFAULT 0 NOT NULL,
    portal_customer_id integer,
    renraku_name2 character varying(100),
    renraku_tel2 character varying(15),
    houmon_agreed integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN pt_inf.portal_customer_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pt_inf.portal_customer_id IS 'GMOクリニック・マップの会員ID';


--
-- Name: COLUMN pt_inf.houmon_agreed; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN pt_inf.houmon_agreed IS '主治医名';


--
-- Name: pt_inf_pt_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pt_inf_pt_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pt_inf_pt_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pt_inf_pt_id_seq OWNED BY pt_inf.pt_id;


--
-- Name: pt_inf_reference_no_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pt_inf_reference_no_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pt_inf_reference_no_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pt_inf_reference_no_seq OWNED BY pt_inf.reference_no;


--
-- Name: pt_inf_seq_no_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE pt_inf_seq_no_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pt_inf_seq_no_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE pt_inf_seq_no_seq OWNED BY pt_inf.seq_no;


--
-- Name: read_system_notice; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE read_system_notice (
    read_system_notice_id integer NOT NULL,
    hospital_staff_id integer NOT NULL,
    read_list character varying(4000),
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: reserve; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE reserve (
    reserve_id integer NOT NULL,
    patient_id integer,
    prescription_receive_method integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN reserve.prescription_receive_method; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN reserve.prescription_receive_method IS 'constant.go - 薬の受け取り方 参照';


--
-- Name: reserve_detail; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE reserve_detail (
    reserve_detail_id integer NOT NULL,
    reserve_id integer NOT NULL,
    patient_id integer,
    exam_detail_id integer,
    queue_id integer,
    status integer NOT NULL,
    reserve_cancel_type integer,
    reserve_type integer,
    treatment_type integer,
    calendar_treatment_id integer NOT NULL,
    memo character varying(250),
    payment_status integer DEFAULT 1 NOT NULL,
    payment_card_id character varying(300),
    fincode_customer_id character varying(300),
    fincode_tenant_id character varying(50),
    exam_time_slot_id integer NOT NULL,
    is_suspended boolean DEFAULT false NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    is_reminder_sent boolean DEFAULT false NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: reserve_detail_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE reserve_detail_history (
    reserve_detail_history_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    exam_detail_id integer,
    status integer NOT NULL,
    reserve_cancel_type integer,
    reserve_type integer,
    treatment_type integer,
    calendar_treatment_id integer NOT NULL,
    memo character varying(250),
    reserve_detail_id integer NOT NULL,
    exam_time_slot_id integer DEFAULT 1 NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: reserve_detail_history_reserve_detail_history_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE reserve_detail_history_reserve_detail_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reserve_detail_history_reserve_detail_history_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE reserve_detail_history_reserve_detail_history_id_seq OWNED BY reserve_detail_history.reserve_detail_history_id;


--
-- Name: reserve_detail_reserve_detail_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE reserve_detail_reserve_detail_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reserve_detail_reserve_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE reserve_detail_reserve_detail_id_seq OWNED BY reserve_detail.reserve_detail_id;


--
-- Name: reserve_reserve_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE reserve_reserve_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: reserve_reserve_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE reserve_reserve_id_seq OWNED BY reserve.reserve_id;


--
-- Name: s_survey_id; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE s_survey_id
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: schema_image; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE schema_image (
    schema_image_id integer NOT NULL,
    hospital_id integer NOT NULL,
    name text,
    file_name text,
    sort_order integer,
    is_deleted integer NOT NULL,
    s3_url text,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp()
);


--
-- Name: schema_image_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE schema_image_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: schema_image_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE schema_image_id_seq OWNED BY schema_image.schema_image_id;


--
-- Name: signup_user; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE signup_user (
    signup_user_id integer NOT NULL,
    hp_id integer NOT NULL,
    user_id integer NOT NULL,
    name character varying(40) NOT NULL,
    medical_professional_type smallint NOT NULL,
    gender smallint NOT NULL,
    birthday date,
    doctor_licence_number character varying(8) NOT NULL,
    doctor_licence_register_date date,
    phone_number character varying(15),
    aid character varying(8),
    bid character varying(8),
    cid character varying(8),
    is_deleted integer DEFAULT 0 NOT NULL,
    kana_name character varying(40),
    applier_category smallint,
    is_download_pamphlet boolean DEFAULT false NOT NULL
);


--
-- Name: COLUMN signup_user.name; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN signup_user.name IS '改名などによる影響を受けず申し込み時の名前を保持する';


--
-- Name: COLUMN signup_user.medical_professional_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN signup_user.medical_professional_type IS '医師=1,歯科医師=2';


--
-- Name: COLUMN signup_user.gender; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN signup_user.gender IS '男=1,女=2';


--
-- Name: COLUMN signup_user.is_deleted; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN signup_user.is_deleted IS '0=有効、1=削除';


--
-- Name: signup_user_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE signup_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: signup_user_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE signup_user_id_seq OWNED BY signup_user.signup_user_id;


--
-- Name: staff_message; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE staff_message (
    message_id character varying NOT NULL,
    message_type character varying NOT NULL,
    channel_id integer NOT NULL,
    content character varying,
    hospital_id integer NOT NULL,
    posted_member character varying,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at character varying,
    updated_at character varying,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: staff_message_channel; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE staff_message_channel (
    channel_id integer NOT NULL,
    channel_name character varying NOT NULL,
    channel_type integer DEFAULT 0 NOT NULL,
    hospital_id integer NOT NULL,
    latest_posted_message_at timestamp with time zone,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN staff_message_channel.channel_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN staff_message_channel.channel_type IS 'constants.go - チャンネル種別 参照';


--
-- Name: staff_message_channel_channel_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE staff_message_channel_channel_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: staff_message_channel_channel_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE staff_message_channel_channel_id_seq OWNED BY staff_message_channel.channel_id;


--
-- Name: staff_message_channel_member; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE staff_message_channel_member (
    channel_id integer NOT NULL,
    member_id character varying NOT NULL,
    has_unread integer DEFAULT 0 NOT NULL,
    created_by character varying DEFAULT 'system'::character varying,
    updated_by character varying DEFAULT 'system'::character varying,
    created_at timestamp with time zone DEFAULT statement_timestamp(),
    updated_at timestamp with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN staff_message_channel_member.has_unread; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN staff_message_channel_member.has_unread IS '0: 既読, 1: 未読';


--
-- Name: status_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE status_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE survey (
    survey_id integer NOT NULL,
    f_ques_json jsonb NOT NULL,
    name character varying(250) NOT NULL,
    hospital_id integer NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    secret character varying(70)
);


--
-- Name: COLUMN survey.secret; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN survey.secret IS 'sha256.Sum256';


--
-- Name: survey_answer; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE survey_answer (
    survey_answer_id integer NOT NULL,
    survey_answer jsonb,
    reserve_detail_id integer,
    patient_id integer NOT NULL,
    treatment_title text,
    treatment_type integer,
    hospital_id integer NOT NULL,
    common_survey jsonb NOT NULL,
    pharmacy_survey jsonb,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    pharmacy_reserve_detail_id integer
);


--
-- Name: COLUMN survey_answer.treatment_type; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN survey_answer.treatment_type IS '0 = 初診,   1 = 再診';


--
-- Name: survey_answer_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE survey_answer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_answer_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE survey_answer_id_seq OWNED BY survey_answer.survey_answer_id;


--
-- Name: survey_answer_no_patient; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE survey_answer_no_patient (
    survey_answer_no_patient_id bigint NOT NULL,
    hospital_id integer NOT NULL,
    survey_id integer NOT NULL,
    survey_answer jsonb,
    kana_name character varying(200),
    name character varying(200),
    birthday integer NOT NULL,
    created_by character varying(100),
    updated_by character varying(100),
    created_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    is_deleted integer NOT NULL,
    files jsonb
);


--
-- Name: survey_answer_no_patient_survey_answer_no_patient_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE survey_answer_no_patient_survey_answer_no_patient_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_answer_no_patient_survey_answer_no_patient_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE survey_answer_no_patient_survey_answer_no_patient_id_seq OWNED BY survey_answer_no_patient.survey_answer_no_patient_id;


--
-- Name: survey_survey_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE survey_survey_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_survey_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE survey_survey_id_seq OWNED BY survey.survey_id;


--
-- Name: survey_template_survey_template_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE survey_template_survey_template_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: survey_template_survey_template_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE survey_template_survey_template_id_seq OWNED BY m_template_survey.survey_template_id;


--
-- Name: task; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task (
    task_id integer NOT NULL,
    title character varying(500) NOT NULL,
    detail_html text,
    detail_text text,
    expired_at timestamp with time zone,
    hospital_id integer NOT NULL,
    task_category_id integer NOT NULL,
    task_status_id integer NOT NULL,
    created_staff_id integer,
    responsible_staff_id integer NOT NULL,
    is_auto_created boolean DEFAULT false NOT NULL,
    patient_id integer,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: COLUMN task.created_staff_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN task.created_staff_id IS '起票者id';


--
-- Name: COLUMN task.responsible_staff_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN task.responsible_staff_id IS '担当者id';


--
-- Name: COLUMN task.is_auto_created; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN task.is_auto_created IS '自動作成フラグ';


--
-- Name: task_category; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_category (
    task_category_id integer NOT NULL,
    hospital_id integer NOT NULL,
    name character varying(100) NOT NULL,
    "order" smallint NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_category_task_category_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_category_task_category_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_category_task_category_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_category_task_category_id_seq OWNED BY task_category.task_category_id;


--
-- Name: task_comment; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_comment (
    task_comment_id integer NOT NULL,
    task_id integer NOT NULL,
    content_text text,
    content_html text,
    created_staff_id integer NOT NULL,
    task_changed jsonb,
    is_edited boolean DEFAULT false NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_comment_file; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_comment_file (
    task_comment_file_id integer NOT NULL,
    task_comment_id integer NOT NULL,
    original_file_name character varying NOT NULL,
    s3_key character varying NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    size integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_comment_file_task_comment_file_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_comment_file_task_comment_file_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_comment_file_task_comment_file_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_comment_file_task_comment_file_id_seq OWNED BY task_comment_file.task_comment_file_id;


--
-- Name: task_comment_task_comment_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_comment_task_comment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_comment_task_comment_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_comment_task_comment_id_seq OWNED BY task_comment.task_comment_id;


--
-- Name: task_file; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_file (
    task_file_id integer NOT NULL,
    task_id integer NOT NULL,
    original_file_name character varying NOT NULL,
    s3_key character varying NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    size integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_file_task_file_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_file_task_file_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_file_task_file_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_file_task_file_id_seq OWNED BY task_file.task_file_id;


--
-- Name: task_history; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_history (
    task_history_id integer NOT NULL,
    task_id integer NOT NULL,
    history jsonb,
    edited_staff_id integer NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_history_task_history_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_history_task_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_history_task_history_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_history_task_history_id_seq OWNED BY task_history.task_history_id;


--
-- Name: task_status; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE task_status (
    task_status_id integer NOT NULL,
    hospital_id integer NOT NULL,
    name character varying(50) NOT NULL,
    color character varying(10) NOT NULL,
    "order" smallint NOT NULL,
    created_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying(100) DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp(),
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: task_status_task_status_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_status_task_status_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_status_task_status_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_status_task_status_id_seq OWNED BY task_status.task_status_id;


--
-- Name: task_task_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE task_task_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: task_task_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE task_task_id_seq OWNED BY task.task_id;


--
-- Name: template_document; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE template_document (
    template_document_id integer NOT NULL,
    hospital_id integer NOT NULL,
    display_name character varying(255) NOT NULL,
    file_name character varying(255) NOT NULL,
    s3_object_key character varying(500) NOT NULL,
    created_by character varying DEFAULT 'system'::character varying NOT NULL,
    updated_by character varying DEFAULT 'system'::character varying NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: template_document_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE template_document_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: template_document_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE template_document_id_seq OWNED BY template_document.template_document_id;


--
-- Name: template_document_parameter_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE template_document_parameter_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: template_document_parameter_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE template_document_parameter_id_seq OWNED BY m_template_document_parameter.template_document_parameter_id;


--
-- Name: treatment_department; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE treatment_department (
    treatment_department_id integer NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    created_by character varying(150) NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    treatment_category_id integer NOT NULL,
    hospital_id integer NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    note text,
    treatment_type integer NOT NULL,
    treatment_method integer NOT NULL,
    first_consultation_time integer NOT NULL,
    next_consultation_time integer NOT NULL,
    first_medical_interview_form_id integer,
    next_medical_interview_form_id integer,
    treatment_department_status integer NOT NULL,
    portal_public_status integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    "order" smallint NOT NULL,
    special_note text,
    fee_list_note text
);


--
-- Name: treatment_department_treatment_department_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE treatment_department_treatment_department_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: treatment_department_treatment_department_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE treatment_department_treatment_department_id_seq OWNED BY treatment_department.treatment_department_id;


--
-- Name: treatment_fee_list; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE treatment_fee_list (
    fee_id integer NOT NULL,
    created_by character varying(150) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT statement_timestamp() NOT NULL,
    updated_by character varying(150) NOT NULL,
    treatment_department_id integer NOT NULL,
    title character varying NOT NULL,
    min_fee integer,
    max_fee integer,
    is_price_range boolean NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL
);


--
-- Name: treatment_fee_list_fee_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE treatment_fee_list_fee_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: treatment_fee_list_fee_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE treatment_fee_list_fee_id_seq OWNED BY treatment_fee_list.fee_id;


--
-- Name: user_mst; Type: TABLE; Schema: local_gmoht; Owner: -
--

CREATE TABLE user_mst (
    hp_id integer NOT NULL,
    id integer NOT NULL,
    user_id integer NOT NULL,
    job_cd integer NOT NULL,
    manager_kbn integer NOT NULL,
    ka_id integer NOT NULL,
    kana_name character varying(40),
    name character varying(40) NOT NULL,
    sname character varying(20) NOT NULL,
    login_id character varying(30) NOT NULL,
    mayaku_license_no character varying(20),
    start_date integer DEFAULT 0 NOT NULL,
    end_date integer DEFAULT 99999999 NOT NULL,
    sort_no integer NOT NULL,
    is_deleted integer DEFAULT 0 NOT NULL,
    create_date timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    create_id integer DEFAULT 0 NOT NULL,
    create_machine character varying(60),
    update_date timestamp with time zone NOT NULL,
    update_id integer DEFAULT 0 NOT NULL,
    update_machine character varying(60),
    renkei_cd1 character varying(14),
    dr_name character varying(40),
    login_type integer,
    hpki_sn character varying(50),
    hpki_issuer_dn character varying(50),
    hash_password text NOT NULL,
    salt text NOT NULL,
    email character varying(300),
    is_init_login_id integer DEFAULT 0 NOT NULL,
    is_init_password integer DEFAULT 0 NOT NULL,
    miss_login_count integer DEFAULT 0 NOT NULL,
    status integer DEFAULT 0 NOT NULL,
    email_update_date timestamp with time zone,
    medical_license_no character varying(20)
);


--
-- Name: COLUMN user_mst.manager_kbn; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN user_mst.manager_kbn IS 'constants.go - スタッフ管理者区分 参照';


--
-- Name: COLUMN user_mst.is_init_login_id; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN user_mst.is_init_login_id IS '0:変更済みログインID, 1:初回ログインID';


--
-- Name: COLUMN user_mst.is_init_password; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN user_mst.is_init_password IS '0:変更済みパスワード, 1:初回パスワード';


--
-- Name: COLUMN user_mst.medical_license_no; Type: COMMENT; Schema: local_gmoht; Owner: -
--

COMMENT ON COLUMN user_mst.medical_license_no IS '医籍登録番号';


--
-- Name: user_mst_id_seq; Type: SEQUENCE; Schema: local_gmoht; Owner: -
--

CREATE SEQUENCE user_mst_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: user_mst_id_seq; Type: SEQUENCE OWNED BY; Schema: local_gmoht; Owner: -
--

ALTER SEQUENCE user_mst_id_seq OWNED BY user_mst.id;


--
-- Name: calendar calendar_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar ALTER COLUMN calendar_id SET DEFAULT nextval('calendar_calendar_id_seq'::regclass);


--
-- Name: calendar_basic_setting calendar_basic_setting_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_basic_setting ALTER COLUMN calendar_basic_setting_id SET DEFAULT nextval('calendar_basic_setting_calendar_basic_setting_id_seq'::regclass);


--
-- Name: calendar_treatment calendar_treatment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_treatment ALTER COLUMN calendar_treatment_id SET DEFAULT nextval('calendar_treatment_calendar_treatment_id_seq'::regclass);


--
-- Name: client_certificate client_certificate_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY client_certificate ALTER COLUMN client_certificate_id SET DEFAULT nextval('client_certificate_client_certificate_id_seq'::regclass);


--
-- Name: comment_history comment_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY comment_history ALTER COLUMN comment_history_id SET DEFAULT nextval('comment_history_comment_history_id_seq'::regclass);


--
-- Name: deleted_task deleted_task_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task ALTER COLUMN deleted_task_id SET DEFAULT nextval('deleted_task_deleted_task_id_seq'::regclass);


--
-- Name: deleted_task task_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task ALTER COLUMN task_id SET DEFAULT nextval('deleted_task_task_id_seq'::regclass);


--
-- Name: deleted_task_comment deleted_task_comment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task_comment ALTER COLUMN deleted_task_comment_id SET DEFAULT nextval('deleted_task_comment_deleted_task_comment_id_seq'::regclass);


--
-- Name: deleted_task_comment task_comment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task_comment ALTER COLUMN task_comment_id SET DEFAULT nextval('deleted_task_comment_task_comment_id_seq'::regclass);


--
-- Name: exam_time_slot exam_time_slot_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY exam_time_slot ALTER COLUMN exam_time_slot_id SET DEFAULT nextval('exam_time_slot_exam_time_slot_id_seq'::regclass);


--
-- Name: fax fax_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY fax ALTER COLUMN fax_id SET DEFAULT nextval('fax_fax_id_seq'::regclass);


--
-- Name: freee_dummy_deal id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_deal ALTER COLUMN id SET DEFAULT nextval('freee_dummy_deal_id_seq'::regclass);


--
-- Name: freee_dummy_detail id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_detail ALTER COLUMN id SET DEFAULT nextval('freee_dummy_detail_id_seq'::regclass);


--
-- Name: freee_dummy_payment id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_payment ALTER COLUMN id SET DEFAULT nextval('freee_dummy_payment_id_seq'::regclass);


--
-- Name: hospital_treatment hospital_treatment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hospital_treatment ALTER COLUMN hospital_treatment_id SET DEFAULT nextval('hospital_treatment_hospital_treatment_id_seq1'::regclass);


--
-- Name: hp_inf hp_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_inf ALTER COLUMN hp_id SET DEFAULT nextval('hp_inf_hp_id_seq'::regclass);


--
-- Name: line_account id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY line_account ALTER COLUMN id SET DEFAULT nextval('line_account_id_seq'::regclass);


--
-- Name: m_application_feature feature_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_application_feature ALTER COLUMN feature_id SET DEFAULT nextval('m_application_feature_feature_id_seq'::regclass);


--
-- Name: m_llm_model llm_model_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_llm_model ALTER COLUMN llm_model_id SET DEFAULT nextval('m_llm_model_id_seq'::regclass);


--
-- Name: m_operator_group operator_group_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_operator_group ALTER COLUMN operator_group_id SET DEFAULT nextval('m_operator_group_operator_group_id_seq'::regclass);


--
-- Name: m_prompt prompt_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_prompt ALTER COLUMN prompt_id SET DEFAULT nextval('m_prompt_id_seq'::regclass);


--
-- Name: m_template_document_parameter template_document_parameter_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_document_parameter ALTER COLUMN template_document_parameter_id SET DEFAULT nextval('template_document_parameter_id_seq'::regclass);


--
-- Name: m_template_survey survey_template_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_survey ALTER COLUMN survey_template_id SET DEFAULT nextval('survey_template_survey_template_id_seq'::regclass);


--
-- Name: m_treatment_category treatment_category_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_treatment_category ALTER COLUMN treatment_category_id SET DEFAULT nextval('m_treatment_category_treatment_category_id_seq'::regclass);


--
-- Name: m_treatment_category_group treatment_category_group_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_treatment_category_group ALTER COLUMN treatment_category_group_id SET DEFAULT nextval('m_treatment_category_group_treatment_category_group_id_seq'::regclass);


--
-- Name: m_treatment_category_group sort_order; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_treatment_category_group ALTER COLUMN sort_order SET DEFAULT nextval('m_treatment_category_group_sort_order_seq'::regclass);


--
-- Name: mail_verification_requests request_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY mail_verification_requests ALTER COLUMN request_id SET DEFAULT nextval('mail_verification_requests_request_id_seq'::regclass);


--
-- Name: meeting meeting_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting ALTER COLUMN meeting_id SET DEFAULT nextval('meeting_meeting_id_seq'::regclass);


--
-- Name: opesys_info_change info_change_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_info_change ALTER COLUMN info_change_id SET DEFAULT nextval('opesys_info_change_info_change_id_seq'::regclass);


--
-- Name: opesys_memo memo_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_memo ALTER COLUMN memo_id SET DEFAULT nextval('opesys_memo_memo_id_seq'::regclass);


--
-- Name: patient_message_channel channel_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message_channel ALTER COLUMN channel_id SET DEFAULT nextval('patient_message_channel_channel_id_seq'::regclass);


--
-- Name: payment_clinic_detail payment_clinic_detail_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail ALTER COLUMN payment_clinic_detail_id SET DEFAULT nextval('payment_clinic_detail_payment_clinic_detail_id_seq'::regclass);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history ALTER COLUMN payment_clinic_detail_history_id SET DEFAULT nextval('payment_clinic_detail_history_payment_clinic_detail_history_seq'::regclass);


--
-- Name: payment_fincode_order payment_fincode_order_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_fincode_order ALTER COLUMN payment_fincode_order_id SET DEFAULT nextval('payment_fincode_order_payment_fincode_order_id_seq'::regclass);


--
-- Name: payment_pharmacy_detail payment_pharmacy_detail_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail ALTER COLUMN payment_pharmacy_detail_id SET DEFAULT nextval('payment_pharmacy_detail_payment_pharmacy_detail_id_seq'::regclass);


--
-- Name: payment_pharmacy_detail_history payment_pharmacy_detail_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail_history ALTER COLUMN payment_pharmacy_detail_history_id SET DEFAULT nextval('payment_pharmacy_detail_histo_payment_pharmacy_detail_histo_seq'::regclass);


--
-- Name: pharmacy_delivery_address pharmacy_delivery_address_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_address ALTER COLUMN pharmacy_delivery_address_id SET DEFAULT nextval('pharmacy_delivery_address_id_seq'::regclass);


--
-- Name: pharmacy_desired_date pharmacy_desired_date_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_desired_date ALTER COLUMN pharmacy_desired_date_id SET DEFAULT nextval('pharmacy_desired_date_pharmacy_desired_date_id_seq'::regclass);


--
-- Name: pharmacy_holiday pharmacy_holiday_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_holiday ALTER COLUMN pharmacy_holiday_id SET DEFAULT nextval('pharmacy_holiday_pharmacy_holiday_id_seq'::regclass);


--
-- Name: pharmacy_patient_file pharmacy_patient_file_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_patient_file ALTER COLUMN pharmacy_patient_file_id SET DEFAULT nextval('patient_pharmacy_file_patient_pharmacy_file_id_seq'::regclass);


--
-- Name: pharmacy_reserve pharmacy_reserve_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve ALTER COLUMN pharmacy_reserve_id SET DEFAULT nextval('pharmacy_reserve_pharmacy_reserve_id_seq'::regclass);


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_detail ALTER COLUMN pharmacy_reserve_detail_id SET DEFAULT nextval('pharmacy_reserve_detail_pharmacy_reserve_detail_id_seq'::regclass);


--
-- Name: pharmacy_reserve_status_history pharmacy_reserve_status_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_status_history ALTER COLUMN pharmacy_reserve_status_history_id SET DEFAULT nextval('pharmacy_reserve_status_histo_pharmacy_reserve_status_histo_seq'::regclass);


--
-- Name: portal_ai_chat_content content_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_ai_chat_content ALTER COLUMN content_id SET DEFAULT nextval('portal_ai_chat_content_id_seq'::regclass);


--
-- Name: portal_business_time business_time_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_business_time ALTER COLUMN business_time_id SET DEFAULT nextval('portal_business_time_business_time_id_seq'::regclass);


--
-- Name: portal_contents contents_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_contents ALTER COLUMN contents_id SET DEFAULT nextval('portal_contents_contents_id_seq'::regclass);


--
-- Name: portal_customer customer_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer ALTER COLUMN customer_id SET DEFAULT nextval('portal_customer_customer_id_seq'::regclass);


--
-- Name: portal_customer_browser browser_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_browser ALTER COLUMN browser_id SET DEFAULT nextval('portal_customer_browser_browser_id_seq'::regclass);


--
-- Name: portal_customer_delivery_address delivery_address_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_delivery_address ALTER COLUMN delivery_address_id SET DEFAULT nextval('portal_customer_delivery_address_delivery_address_id_seq'::regclass);


--
-- Name: portal_customer_file customer_file_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_file ALTER COLUMN customer_file_id SET DEFAULT nextval('portal_customer_file_customer_file_id_seq'::regclass);


--
-- Name: portal_customer_login login_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_login ALTER COLUMN login_id SET DEFAULT nextval('portal_customer_login_login_id_seq'::regclass);


--
-- Name: portal_customer_payment customer_payment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment ALTER COLUMN customer_payment_id SET DEFAULT nextval('portal_customer_payment_customer_payment_id_seq'::regclass);


--
-- Name: portal_customer_payment_card card_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment_card ALTER COLUMN card_id SET DEFAULT nextval('portal_customer_payment_card_card_id_seq'::regclass);


--
-- Name: portal_customer_pharmacy portal_customer_pharmacy_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_pharmacy ALTER COLUMN portal_customer_pharmacy_id SET DEFAULT nextval('portal_customer_pharmacy_portal_customer_pharmacy_id_seq'::regclass);


--
-- Name: portal_customer_social_login socl_login_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_social_login ALTER COLUMN socl_login_id SET DEFAULT nextval('portal_customer_social_login_socl_login_id_seq'::regclass);


--
-- Name: portal_customer_survey customer_survey_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_survey ALTER COLUMN customer_survey_id SET DEFAULT nextval('portal_customer_survey_customer_survey_id_seq'::regclass);


--
-- Name: portal_exam_detail exam_detail_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_exam_detail ALTER COLUMN exam_detail_id SET DEFAULT nextval('portal_exam_detail_exam_detail_id_seq'::regclass);


--
-- Name: portal_hospital hospital_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital ALTER COLUMN hospital_id SET DEFAULT nextval('portal_hospital_hospital_id_seq'::regclass);


--
-- Name: portal_hospital_favorite hospital_favorite_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_favorite ALTER COLUMN hospital_favorite_id SET DEFAULT nextval('portal_hospital_favorite_hospital_favorite_id_seq'::regclass);


--
-- Name: portal_hospital_notification hospital_notification_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_notification ALTER COLUMN hospital_notification_id SET DEFAULT nextval('portal_hospital_notification_hospital_notification_id_seq'::regclass);


--
-- Name: portal_hospital_staff hospital_staff_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_staff ALTER COLUMN hospital_staff_id SET DEFAULT nextval('portal_hospital_staff_hospital_staff_id_seq'::regclass);


--
-- Name: portal_m_city city_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_city ALTER COLUMN city_id SET DEFAULT nextval('portal_m_city_city_id_seq'::regclass);


--
-- Name: portal_m_drug drug_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug ALTER COLUMN drug_id SET DEFAULT nextval('portal_m_drug_drug_id_seq'::regclass);


--
-- Name: portal_m_drug01_category drug01_category_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug01_category ALTER COLUMN drug01_category_id SET DEFAULT nextval('portal_m_drug01_category_drug01_category_id_seq'::regclass);


--
-- Name: portal_m_drug02_category drug02_category_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug02_category ALTER COLUMN drug02_category_id SET DEFAULT nextval('portal_m_drug02_category_drug02_category_id_seq'::regclass);


--
-- Name: portal_m_examination examination_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_examination ALTER COLUMN examination_id SET DEFAULT nextval('portal_m_examination_examination_id_seq'::regclass);


--
-- Name: portal_m_prefecture prefecture_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_prefecture ALTER COLUMN prefecture_id SET DEFAULT nextval('portal_m_prefecture_prefecture_id_seq'::regclass);


--
-- Name: portal_m_railline railline_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_railline ALTER COLUMN railline_id SET DEFAULT nextval('portal_m_railline_railline_id_seq'::regclass);


--
-- Name: portal_m_railline_company railline_company_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_railline_company ALTER COLUMN railline_company_id SET DEFAULT nextval('portal_m_railline_company_railline_company_id_seq'::regclass);


--
-- Name: portal_m_search_keyword_equiv id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_search_keyword_equiv ALTER COLUMN id SET DEFAULT nextval('portal_m_search_keyword_equiv_id_seq'::regclass);


--
-- Name: portal_m_specialist specialist_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_specialist ALTER COLUMN specialist_id SET DEFAULT nextval('portal_m_specialist_specialist_id_seq'::regclass);


--
-- Name: portal_m_station station_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_station ALTER COLUMN station_id SET DEFAULT nextval('portal_m_station_station_id_seq'::regclass);


--
-- Name: portal_m_tag tag_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_tag ALTER COLUMN tag_id SET DEFAULT nextval('portal_m_tag_tag_id_seq'::regclass);


--
-- Name: portal_pict pict_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_pict ALTER COLUMN pict_id SET DEFAULT nextval('portal_pict_pict_id_seq'::regclass);


--
-- Name: portal_prescription_drug presc_drug_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_prescription_drug ALTER COLUMN presc_drug_id SET DEFAULT nextval('portal_prescription_drug_presc_drug_id_seq'::regclass);


--
-- Name: portal_treat_drug treat_drug_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_treat_drug ALTER COLUMN treat_drug_id SET DEFAULT nextval('portal_treat_drug_treat_drug_id_seq'::regclass);


--
-- Name: portal_treatment_dept_grp treatment_dept_grp_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_treatment_dept_grp ALTER COLUMN treatment_dept_grp_id SET DEFAULT nextval('portal_treatment_dept_grp_treatment_dept_grp_id_seq'::regclass);


--
-- Name: prescription_image prescription_image_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_image ALTER COLUMN prescription_image_id SET DEFAULT nextval('prescription_image_prescription_image_id_seq'::regclass);


--
-- Name: prescription_reception prescription_reception_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_reception ALTER COLUMN prescription_reception_id SET DEFAULT nextval('prescription_reception_prescription_reception_id_seq'::regclass);


--
-- Name: pt_inf pt_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf ALTER COLUMN pt_id SET DEFAULT nextval('pt_inf_pt_id_seq'::regclass);


--
-- Name: pt_inf seq_no; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf ALTER COLUMN seq_no SET DEFAULT nextval('pt_inf_seq_no_seq'::regclass);


--
-- Name: pt_inf reference_no; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf ALTER COLUMN reference_no SET DEFAULT nextval('pt_inf_reference_no_seq'::regclass);


--
-- Name: reserve reserve_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve ALTER COLUMN reserve_id SET DEFAULT nextval('reserve_reserve_id_seq'::regclass);


--
-- Name: reserve_detail reserve_detail_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail ALTER COLUMN reserve_detail_id SET DEFAULT nextval('reserve_detail_reserve_detail_id_seq'::regclass);


--
-- Name: reserve_detail_history reserve_detail_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail_history ALTER COLUMN reserve_detail_history_id SET DEFAULT nextval('reserve_detail_history_reserve_detail_history_id_seq'::regclass);


--
-- Name: schema_image schema_image_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY schema_image ALTER COLUMN schema_image_id SET DEFAULT nextval('schema_image_id_seq'::regclass);


--
-- Name: signup_user signup_user_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY signup_user ALTER COLUMN signup_user_id SET DEFAULT nextval('signup_user_id_seq'::regclass);


--
-- Name: staff_message_channel channel_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message_channel ALTER COLUMN channel_id SET DEFAULT nextval('staff_message_channel_channel_id_seq'::regclass);


--
-- Name: survey survey_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey ALTER COLUMN survey_id SET DEFAULT nextval('survey_survey_id_seq'::regclass);


--
-- Name: survey_answer survey_answer_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer ALTER COLUMN survey_answer_id SET DEFAULT nextval('survey_answer_id_seq'::regclass);


--
-- Name: survey_answer_no_patient survey_answer_no_patient_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer_no_patient ALTER COLUMN survey_answer_no_patient_id SET DEFAULT nextval('survey_answer_no_patient_survey_answer_no_patient_id_seq'::regclass);


--
-- Name: task task_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task ALTER COLUMN task_id SET DEFAULT nextval('task_task_id_seq'::regclass);


--
-- Name: task_category task_category_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_category ALTER COLUMN task_category_id SET DEFAULT nextval('task_category_task_category_id_seq'::regclass);


--
-- Name: task_comment task_comment_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment ALTER COLUMN task_comment_id SET DEFAULT nextval('task_comment_task_comment_id_seq'::regclass);


--
-- Name: task_comment_file task_comment_file_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment_file ALTER COLUMN task_comment_file_id SET DEFAULT nextval('task_comment_file_task_comment_file_id_seq'::regclass);


--
-- Name: task_file task_file_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_file ALTER COLUMN task_file_id SET DEFAULT nextval('task_file_task_file_id_seq'::regclass);


--
-- Name: task_history task_history_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_history ALTER COLUMN task_history_id SET DEFAULT nextval('task_history_task_history_id_seq'::regclass);


--
-- Name: task_status task_status_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_status ALTER COLUMN task_status_id SET DEFAULT nextval('task_status_task_status_id_seq'::regclass);


--
-- Name: template_document template_document_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY template_document ALTER COLUMN template_document_id SET DEFAULT nextval('template_document_id_seq'::regclass);


--
-- Name: treatment_department treatment_department_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department ALTER COLUMN treatment_department_id SET DEFAULT nextval('treatment_department_treatment_department_id_seq'::regclass);


--
-- Name: treatment_fee_list fee_id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_fee_list ALTER COLUMN fee_id SET DEFAULT nextval('treatment_fee_list_fee_id_seq'::regclass);


--
-- Name: user_mst id; Type: DEFAULT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY user_mst ALTER COLUMN id SET DEFAULT nextval('user_mst_id_seq'::regclass);


--
-- Name: agent_config agent_config_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY agent_config
    ADD CONSTRAINT agent_config_pk PRIMARY KEY (hospital_id, job_type, file_type);


--
-- Name: agent_token agent_token_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY agent_token
    ADD CONSTRAINT agent_token_pkey PRIMARY KEY (hospital_id);


--
-- Name: agree_log agree_log_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY agree_log
    ADD CONSTRAINT agree_log_pkey PRIMARY KEY (agreement_id, hospital_id);


--
-- Name: m_application_feature application_feature_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_application_feature
    ADD CONSTRAINT application_feature_pk PRIMARY KEY (feature_id);


--
-- Name: calendar_basic_setting calendar_basic_setting_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_basic_setting
    ADD CONSTRAINT calendar_basic_setting_pkey PRIMARY KEY (calendar_basic_setting_id);


--
-- Name: calendar calendar_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar
    ADD CONSTRAINT calendar_pkey PRIMARY KEY (calendar_id);


--
-- Name: calendar_treatment calendar_treatment_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_treatment
    ADD CONSTRAINT calendar_treatment_pkey PRIMARY KEY (calendar_treatment_id);


--
-- Name: client_certificate client_certificate_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY client_certificate
    ADD CONSTRAINT client_certificate_pkey PRIMARY KEY (client_certificate_id);


--
-- Name: comment_history comment_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY comment_history
    ADD CONSTRAINT comment_history_pkey PRIMARY KEY (comment_history_id);


--
-- Name: common_kensa_unit_mst common_kensa_unit_mst_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY common_kensa_unit_mst
    ADD CONSTRAINT common_kensa_unit_mst_pkey PRIMARY KEY (unit);


--
-- Name: portal_customer_file customer_file_pkey_1; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_file
    ADD CONSTRAINT customer_file_pkey_1 PRIMARY KEY (customer_file_id);


--
-- Name: deleted_task_comment deleted_task_comment_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task_comment
    ADD CONSTRAINT deleted_task_comment_pkey PRIMARY KEY (deleted_task_comment_id);


--
-- Name: deleted_task deleted_task_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY deleted_task
    ADD CONSTRAINT deleted_task_pkey PRIMARY KEY (deleted_task_id);


--
-- Name: every_patient_message_setting every_patient_message_setting_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY every_patient_message_setting
    ADD CONSTRAINT every_patient_message_setting_pkey PRIMARY KEY (hospital_id);


--
-- Name: exam_time_slot exam_time_slot_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY exam_time_slot
    ADD CONSTRAINT exam_time_slot_pkey PRIMARY KEY (exam_time_slot_id);


--
-- Name: fax_number fax_number_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY fax_number
    ADD CONSTRAINT fax_number_pkey PRIMARY KEY (fax_number);


--
-- Name: fax fax_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY fax
    ADD CONSTRAINT fax_pkey PRIMARY KEY (fax_id);


--
-- Name: freee_dummy_deal freee_dummy_deal_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_deal
    ADD CONSTRAINT freee_dummy_deal_pkey PRIMARY KEY (id);


--
-- Name: freee_dummy_detail freee_dummy_detail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_detail
    ADD CONSTRAINT freee_dummy_detail_pkey PRIMARY KEY (id);


--
-- Name: freee_dummy_payment freee_dummy_payment_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_payment
    ADD CONSTRAINT freee_dummy_payment_pkey PRIMARY KEY (id);


--
-- Name: portal_hospital_favorite hospital_favorite_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_favorite
    ADD CONSTRAINT hospital_favorite_pkey PRIMARY KEY (hospital_favorite_id);


--
-- Name: hospital_treatment hospital_treatment_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hospital_treatment
    ADD CONSTRAINT hospital_treatment_pk PRIMARY KEY (hospital_treatment_id);


--
-- Name: hp_fincode_info hp_fincode_info_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_fincode_info
    ADD CONSTRAINT hp_fincode_info_pk PRIMARY KEY (hp_id);


--
-- Name: hp_permission hp_permission_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_permission
    ADD CONSTRAINT hp_permission_pk PRIMARY KEY (hp_id, feature_id);


--
-- Name: hp_schedule hp_schedule_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_schedule
    ADD CONSTRAINT hp_schedule_pk PRIMARY KEY (schedule_id);


--
-- Name: hp_white_board hp_white_board_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_white_board
    ADD CONSTRAINT hp_white_board_pk PRIMARY KEY (white_board_id);


--
-- Name: line_account line_account_unique; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY line_account
    ADD CONSTRAINT line_account_unique UNIQUE (id);


--
-- Name: m_agent_setting m_agent_setting_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_agent_setting
    ADD CONSTRAINT m_agent_setting_pk PRIMARY KEY (job_type, file_type);


--
-- Name: m_agree m_agree_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_agree
    ADD CONSTRAINT m_agree_pkey PRIMARY KEY (agreement_id);


--
-- Name: portal_m_drug01_category m_drug01_category_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug01_category
    ADD CONSTRAINT m_drug01_category_pkey PRIMARY KEY (drug01_category_id);


--
-- Name: portal_m_drug m_drug_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug
    ADD CONSTRAINT m_drug_pkey PRIMARY KEY (drug_id);


--
-- Name: m_llm_model m_llm_model_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_llm_model
    ADD CONSTRAINT m_llm_model_pkey PRIMARY KEY (llm_model_id);


--
-- Name: m_mail_delivery_settings m_mail_delivery_settings_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_mail_delivery_settings
    ADD CONSTRAINT m_mail_delivery_settings_pkey PRIMARY KEY (staff_id);


--
-- Name: m_prompt m_prompt_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_prompt
    ADD CONSTRAINT m_prompt_pkey PRIMARY KEY (prompt_id);


--
-- Name: portal_m_railline_company m_railline_company_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_railline_company
    ADD CONSTRAINT m_railline_company_pkey PRIMARY KEY (railline_company_id);


--
-- Name: portal_m_railline m_railline_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_railline
    ADD CONSTRAINT m_railline_pkey PRIMARY KEY (railline_id);


--
-- Name: portal_m_specialist m_specialist_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_specialist
    ADD CONSTRAINT m_specialist_pkey PRIMARY KEY (specialist_id);


--
-- Name: portal_m_station m_station_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_station
    ADD CONSTRAINT m_station_pkey PRIMARY KEY (station_id);


--
-- Name: portal_m_tag m_tag_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_tag
    ADD CONSTRAINT m_tag_pkey PRIMARY KEY (tag_id);


--
-- Name: m_template_document_parameter m_template_document_parameter_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_document_parameter
    ADD CONSTRAINT m_template_document_parameter_pkey PRIMARY KEY (template_document_parameter_id);


--
-- Name: m_template_mail m_template_mail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_mail
    ADD CONSTRAINT m_template_mail_pkey PRIMARY KEY (template_mail_code);


--
-- Name: m_template_sms m_template_sms_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_sms
    ADD CONSTRAINT m_template_sms_pkey PRIMARY KEY (sms_code);


--
-- Name: m_treatment_category_group m_treatment_category_group_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_treatment_category_group
    ADD CONSTRAINT m_treatment_category_group_pk PRIMARY KEY (treatment_category_group_id);


--
-- Name: m_treatment_category m_treatment_category_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_treatment_category
    ADD CONSTRAINT m_treatment_category_pkey PRIMARY KEY (treatment_category_id);


--
-- Name: mail_verification_requests mail_verification_requests_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY mail_verification_requests
    ADD CONSTRAINT mail_verification_requests_pkey PRIMARY KEY (request_id);


--
-- Name: meeting meeting_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting
    ADD CONSTRAINT meeting_pk PRIMARY KEY (meeting_id);


--
-- Name: message_item message_item_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY message_item
    ADD CONSTRAINT message_item_pkey PRIMARY KEY (item_id, message_id, posted_member);


--
-- Name: operator_group_permission operator_group_permission_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY operator_group_permission
    ADD CONSTRAINT operator_group_permission_pk PRIMARY KEY (operator_group_id, feature_id);


--
-- Name: m_operator_group operator_group_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_operator_group
    ADD CONSTRAINT operator_group_pk PRIMARY KEY (operator_group_id);


--
-- Name: opesys_info_change opesys_info_change_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_info_change
    ADD CONSTRAINT opesys_info_change_pkey PRIMARY KEY (info_change_id);


--
-- Name: patient_memo patient_memo_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_memo
    ADD CONSTRAINT patient_memo_pkey PRIMARY KEY (patient_id);


--
-- Name: patient_message_channel_member patient_message_channel_member_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message_channel_member
    ADD CONSTRAINT patient_message_channel_member_pkey PRIMARY KEY (channel_id, member_id, is_patient);


--
-- Name: patient_message_channel patient_message_channel_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message_channel
    ADD CONSTRAINT patient_message_channel_pkey PRIMARY KEY (channel_id);


--
-- Name: patient_message patient_message_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message
    ADD CONSTRAINT patient_message_pkey PRIMARY KEY (message_id);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history
    ADD CONSTRAINT payment_clinic_detail_history_pkey PRIMARY KEY (payment_clinic_detail_history_id);


--
-- Name: payment_clinic_detail payment_clinic_detail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail
    ADD CONSTRAINT payment_clinic_detail_pkey PRIMARY KEY (payment_clinic_detail_id);


--
-- Name: payment_fincode_order payment_fincode_order_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_fincode_order
    ADD CONSTRAINT payment_fincode_order_pkey PRIMARY KEY (payment_fincode_order_id);


--
-- Name: payment_pharmacy_detail_history payment_pharmacy_detail_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail_history
    ADD CONSTRAINT payment_pharmacy_detail_history_pkey PRIMARY KEY (payment_pharmacy_detail_history_id);


--
-- Name: payment_pharmacy_detail payment_pharmacy_detail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail
    ADD CONSTRAINT payment_pharmacy_detail_pkey PRIMARY KEY (payment_pharmacy_detail_id);


--
-- Name: pharmacy_delivery_address pharmacy_delivery_address_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_address
    ADD CONSTRAINT pharmacy_delivery_address_pk PRIMARY KEY (pharmacy_delivery_address_id);


--
-- Name: pharmacy_delivery_history pharmacy_delivery_history_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_history
    ADD CONSTRAINT pharmacy_delivery_history_pk PRIMARY KEY (pharmacy_reserve_id, delivery_inquiry_num);


--
-- Name: pharmacy_desired_date pharmacy_desired_date_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_desired_date
    ADD CONSTRAINT pharmacy_desired_date_pkey PRIMARY KEY (pharmacy_desired_date_id);


--
-- Name: pharmacy_holiday pharmacy_holiday_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_holiday
    ADD CONSTRAINT pharmacy_holiday_pkey PRIMARY KEY (pharmacy_holiday_id);


--
-- Name: pharmacy_patient_file pharmacy_patient_file_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_patient_file
    ADD CONSTRAINT pharmacy_patient_file_pkey PRIMARY KEY (pharmacy_patient_file_id);


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_detail
    ADD CONSTRAINT pharmacy_reserve_detail_pkey PRIMARY KEY (pharmacy_reserve_detail_id);


--
-- Name: pharmacy_reserve pharmacy_reserve_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve
    ADD CONSTRAINT pharmacy_reserve_pkey PRIMARY KEY (pharmacy_reserve_id);


--
-- Name: pharmacy_reserve_status_history pharmacy_reserve_status_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_status_history
    ADD CONSTRAINT pharmacy_reserve_status_history_pkey PRIMARY KEY (pharmacy_reserve_status_history_id);


--
-- Name: byomei_sikkan_cd_mst pk_byomei_sikkan_cd_mst; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY byomei_sikkan_cd_mst
    ADD CONSTRAINT pk_byomei_sikkan_cd_mst PRIMARY KEY (hp_id, byomei_cd, start_date);


--
-- Name: common_center_item_mst pk_common_center_item_mst; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY common_center_item_mst
    ADD CONSTRAINT pk_common_center_item_mst PRIMARY KEY (center_cd, kensa_item_cd, start_date);


--
-- Name: hp_inf pk_hp_inf; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_inf
    ADD CONSTRAINT pk_hp_inf PRIMARY KEY (hp_id);


--
-- Name: m_medical_institution pk_m_medical_institution; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_medical_institution
    ADD CONSTRAINT pk_m_medical_institution PRIMARY KEY (medical_institution_number, prefecture_code, insurance_category);


--
-- Name: permission_mst pk_permission_mst; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY permission_mst
    ADD CONSTRAINT pk_permission_mst PRIMARY KEY (function_cd, permission);


--
-- Name: pt_inf pk_pt_inf; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf
    ADD CONSTRAINT pk_pt_inf PRIMARY KEY (hp_id, pt_id, seq_no);


--
-- Name: portal_contents_pub pk_t_contents_pub; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_contents_pub
    ADD CONSTRAINT pk_t_contents_pub PRIMARY KEY (contents_id, publication_type);


--
-- Name: portal_withdrawal_reason pk_t_cust_usage_ed_reason; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_withdrawal_reason
    ADD CONSTRAINT pk_t_cust_usage_ed_reason PRIMARY KEY (customer_id, type);


--
-- Name: portal_hosp_station pk_t_hosp_station; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_station
    ADD CONSTRAINT pk_t_hosp_station PRIMARY KEY (hospital_id, station_id);


--
-- Name: portal_hosp_tag pk_t_hosp_tag; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_tag
    ADD CONSTRAINT pk_t_hosp_tag PRIMARY KEY (hospital_id, tag_id);


--
-- Name: user_mst pk_user_mst; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY user_mst
    ADD CONSTRAINT pk_user_mst PRIMARY KEY (id);


--
-- Name: portal_agree_log portal_agree_log_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_agree_log
    ADD CONSTRAINT portal_agree_log_pkey PRIMARY KEY (agreement_id, customer_id);


--
-- Name: portal_ai_chat_content portal_ai_chat_content_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_ai_chat_content
    ADD CONSTRAINT portal_ai_chat_content_pkey PRIMARY KEY (content_id);


--
-- Name: portal_business_time portal_business_time_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_business_time
    ADD CONSTRAINT portal_business_time_pkey PRIMARY KEY (business_time_id, hospital_id);


--
-- Name: portal_customer_payment_card portal_customer_payment_card_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment_card
    ADD CONSTRAINT portal_customer_payment_card_pkey PRIMARY KEY (card_id);


--
-- Name: portal_customer_payment portal_customer_payment_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment
    ADD CONSTRAINT portal_customer_payment_pkey PRIMARY KEY (customer_payment_id);


--
-- Name: portal_customer_pharmacy portal_customer_pharmacy_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_pharmacy
    ADD CONSTRAINT portal_customer_pharmacy_pk PRIMARY KEY (portal_customer_pharmacy_id);


--
-- Name: portal_event_count_log portal_event_count_log_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_event_count_log
    ADD CONSTRAINT portal_event_count_log_pkey PRIMARY KEY (event_key, event_date);


--
-- Name: portal_hospital_examination portal_hospital_examination_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_examination
    ADD CONSTRAINT portal_hospital_examination_pkey PRIMARY KEY (portal_hospital_id, examination_id);


--
-- Name: portal_hospital_notification portal_hospital_notification_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_notification
    ADD CONSTRAINT portal_hospital_notification_pkey PRIMARY KEY (hospital_notification_id);


--
-- Name: portal_hospital_specialist portal_hospital_specialist_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_specialist
    ADD CONSTRAINT portal_hospital_specialist_pkey PRIMARY KEY (hospital_id, specialist_id);


--
-- Name: portal_hospital_staff portal_hospital_staff_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_staff
    ADD CONSTRAINT portal_hospital_staff_pk PRIMARY KEY (hospital_staff_id);


--
-- Name: portal_m_agree portal_m_agree_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_agree
    ADD CONSTRAINT portal_m_agree_pkey PRIMARY KEY (agreement_id);


--
-- Name: portal_m_city portal_m_city_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_city
    ADD CONSTRAINT portal_m_city_pkey PRIMARY KEY (city_id);


--
-- Name: portal_m_drug02_category portal_m_drug02_category_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug02_category
    ADD CONSTRAINT portal_m_drug02_category_pkey PRIMARY KEY (drug02_category_id);


--
-- Name: portal_m_examination portal_m_examination_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_examination
    ADD CONSTRAINT portal_m_examination_pkey PRIMARY KEY (examination_id);


--
-- Name: portal_m_prefecture portal_m_prefecture_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_prefecture
    ADD CONSTRAINT portal_m_prefecture_pkey PRIMARY KEY (prefecture_id);


--
-- Name: portal_m_search_keyword_equiv portal_m_search_keyword_equiv_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_search_keyword_equiv
    ADD CONSTRAINT portal_m_search_keyword_equiv_pk PRIMARY KEY (id);


--
-- Name: portal_customer_survey portal_medical_info_un; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_survey
    ADD CONSTRAINT portal_medical_info_un UNIQUE (customer_id);


--
-- Name: portal_staff_picture portal_staff_picture_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_staff_picture
    ADD CONSTRAINT portal_staff_picture_pkey PRIMARY KEY (hospital_staff_id, pict_id);


--
-- Name: portal_treatment_dept_grp portal_treatment_dept_grp_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_treatment_dept_grp
    ADD CONSTRAINT portal_treatment_dept_grp_pk PRIMARY KEY (treatment_dept_grp_id);


--
-- Name: prescription_image prescription_image_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_image
    ADD CONSTRAINT prescription_image_pkey PRIMARY KEY (prescription_image_id);


--
-- Name: prescription_reception prescription_reception_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_reception
    ADD CONSTRAINT prescription_reception_pkey PRIMARY KEY (prescription_reception_id);


--
-- Name: prompt_history prompt_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prompt_history
    ADD CONSTRAINT prompt_history_pkey PRIMARY KEY (prompt_history_id);


--
-- Name: prompt_log prompt_log_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prompt_log
    ADD CONSTRAINT prompt_log_pkey PRIMARY KEY (prompt_id, user_id);


--
-- Name: pt_inf pt_inf_un; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf
    ADD CONSTRAINT pt_inf_un UNIQUE (pt_id);


--
-- Name: read_system_notice read_system_notice_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY read_system_notice
    ADD CONSTRAINT read_system_notice_pk PRIMARY KEY (read_system_notice_id);


--
-- Name: reserve_detail reserve_detail_pkey_1; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail
    ADD CONSTRAINT reserve_detail_pkey_1 PRIMARY KEY (reserve_detail_id);


--
-- Name: reserve_detail_history reserve_detail_pkey_1_1; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail_history
    ADD CONSTRAINT reserve_detail_pkey_1_1 PRIMARY KEY (reserve_detail_history_id);


--
-- Name: reserve reserve_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve
    ADD CONSTRAINT reserve_pkey PRIMARY KEY (reserve_id);


--
-- Name: schema_image schema_image_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY schema_image
    ADD CONSTRAINT schema_image_pkey PRIMARY KEY (schema_image_id);


--
-- Name: signup_user signup_user_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY signup_user
    ADD CONSTRAINT signup_user_pkey PRIMARY KEY (signup_user_id);


--
-- Name: staff_message_channel_member staff_message_channel_member_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message_channel_member
    ADD CONSTRAINT staff_message_channel_member_pkey PRIMARY KEY (channel_id, member_id);


--
-- Name: staff_message_channel staff_message_channel_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message_channel
    ADD CONSTRAINT staff_message_channel_pkey PRIMARY KEY (channel_id);


--
-- Name: staff_message staff_message_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message
    ADD CONSTRAINT staff_message_pkey PRIMARY KEY (message_id);


--
-- Name: survey_answer_no_patient survey_answer_no_patient_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer_no_patient
    ADD CONSTRAINT survey_answer_no_patient_pkey PRIMARY KEY (survey_answer_no_patient_id);


--
-- Name: survey_answer survey_answer_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer
    ADD CONSTRAINT survey_answer_pkey PRIMARY KEY (survey_answer_id);


--
-- Name: survey_answer survey_answer_un; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer
    ADD CONSTRAINT survey_answer_un UNIQUE (reserve_detail_id);


--
-- Name: survey survey_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey
    ADD CONSTRAINT survey_pk PRIMARY KEY (survey_id);


--
-- Name: survey survey_secret_key; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey
    ADD CONSTRAINT survey_secret_key UNIQUE (secret);


--
-- Name: m_template_survey survey_template_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_template_survey
    ADD CONSTRAINT survey_template_pk PRIMARY KEY (survey_template_id);


--
-- Name: portal_contents t_contents_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_contents
    ADD CONSTRAINT t_contents_pkey PRIMARY KEY (contents_id);


--
-- Name: portal_customer_delivery_address t_customer_delivery_address_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_delivery_address
    ADD CONSTRAINT t_customer_delivery_address_pkey PRIMARY KEY (delivery_address_id);


--
-- Name: portal_customer_login t_customer_login_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_login
    ADD CONSTRAINT t_customer_login_pkey PRIMARY KEY (login_id);


--
-- Name: portal_customer t_customer_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer
    ADD CONSTRAINT t_customer_pkey PRIMARY KEY (customer_id);


--
-- Name: portal_customer_social_login t_customer_social_login_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_social_login
    ADD CONSTRAINT t_customer_social_login_pkey PRIMARY KEY (socl_login_id);


--
-- Name: portal_exam_detail t_exam_detail_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_exam_detail
    ADD CONSTRAINT t_exam_detail_pkey PRIMARY KEY (exam_detail_id);


--
-- Name: portal_hosp_picture t_hosp_picture_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_picture
    ADD CONSTRAINT t_hosp_picture_pkey PRIMARY KEY (hospital_id, pict_id);


--
-- Name: portal_hospital t_hospital_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital
    ADD CONSTRAINT t_hospital_pkey PRIMARY KEY (hospital_id);


--
-- Name: opesys_clinic_memo t_opesys_clinic_memo_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_clinic_memo
    ADD CONSTRAINT t_opesys_clinic_memo_pkey PRIMARY KEY (memo_id, hp_id);


--
-- Name: opesys_memo t_opesys_memo_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_memo
    ADD CONSTRAINT t_opesys_memo_pkey PRIMARY KEY (memo_id);


--
-- Name: opesys_reserve_memo t_opesys_reserve_memo_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_reserve_memo
    ADD CONSTRAINT t_opesys_reserve_memo_pkey PRIMARY KEY (memo_id, customer_id);


--
-- Name: portal_pict t_pict_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_pict
    ADD CONSTRAINT t_pict_pkey PRIMARY KEY (pict_id);


--
-- Name: portal_customer_browser t_portal_customer_browser_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_browser
    ADD CONSTRAINT t_portal_customer_browser_pkey PRIMARY KEY (browser_id);


--
-- Name: portal_prescription_drug t_prescription_drug_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_prescription_drug
    ADD CONSTRAINT t_prescription_drug_pkey PRIMARY KEY (presc_drug_id);


--
-- Name: portal_treat_drug t_treat_drug_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_treat_drug
    ADD CONSTRAINT t_treat_drug_pkey PRIMARY KEY (treat_drug_id);


--
-- Name: task_category task_category_hospital_id_name_key; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_category
    ADD CONSTRAINT task_category_hospital_id_name_key UNIQUE (hospital_id, name);


--
-- Name: task_category task_category_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_category
    ADD CONSTRAINT task_category_pkey PRIMARY KEY (task_category_id);


--
-- Name: task_comment_file task_comment_file_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment_file
    ADD CONSTRAINT task_comment_file_pkey PRIMARY KEY (task_comment_file_id);


--
-- Name: task_comment task_comment_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment
    ADD CONSTRAINT task_comment_pkey PRIMARY KEY (task_comment_id);


--
-- Name: task_file task_file_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_file
    ADD CONSTRAINT task_file_pkey PRIMARY KEY (task_file_id);


--
-- Name: task_history task_history_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_history
    ADD CONSTRAINT task_history_pkey PRIMARY KEY (task_history_id);


--
-- Name: task task_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_pkey PRIMARY KEY (task_id);


--
-- Name: task_status task_status_hospital_id_name_key; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_status
    ADD CONSTRAINT task_status_hospital_id_name_key UNIQUE (hospital_id, name);


--
-- Name: task_status task_status_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_status
    ADD CONSTRAINT task_status_pkey PRIMARY KEY (task_status_id);


--
-- Name: template_document template_document_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY template_document
    ADD CONSTRAINT template_document_pkey PRIMARY KEY (template_document_id);


--
-- Name: treatment_department treatment_department_pkey; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department
    ADD CONSTRAINT treatment_department_pkey PRIMARY KEY (treatment_department_id);


--
-- Name: treatment_fee_list treatment_fee_list_pk; Type: CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_fee_list
    ADD CONSTRAINT treatment_fee_list_pk PRIMARY KEY (fee_id);


--
-- Name: exam_time_slot_exam_start_date_end_date_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX exam_time_slot_exam_start_date_end_date_idx ON exam_time_slot USING btree (exam_start_date, exam_end_date);


--
-- Name: hospital_favorite_import_customer_unique; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX hospital_favorite_import_customer_unique ON portal_hospital_favorite USING btree (scuel_id, customer_id) WHERE (customer_id IS NOT NULL);


--
-- Name: hospital_favorite_import_session_unique; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX hospital_favorite_import_session_unique ON portal_hospital_favorite USING btree (scuel_id, session_id) WHERE (session_id IS NOT NULL);


--
-- Name: hospital_favorite_portal_customer_unique; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX hospital_favorite_portal_customer_unique ON portal_hospital_favorite USING btree (portal_hospital_id, customer_id) WHERE (customer_id IS NOT NULL);


--
-- Name: hospital_favorite_portal_session_unique; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX hospital_favorite_portal_session_unique ON portal_hospital_favorite USING btree (portal_hospital_id, session_id) WHERE (session_id IS NOT NULL);


--
-- Name: hospital_id_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX hospital_id_idx ON template_document USING btree (hospital_id, is_deleted);


--
-- Name: idx_hospital_favorite_customer_import; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hospital_favorite_customer_import ON portal_hospital_favorite USING btree (customer_id, scuel_id) WHERE (customer_id IS NOT NULL);


--
-- Name: idx_hospital_favorite_customer_portal; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hospital_favorite_customer_portal ON portal_hospital_favorite USING btree (customer_id, portal_hospital_id) WHERE (customer_id IS NOT NULL);


--
-- Name: idx_hospital_favorite_scuel_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hospital_favorite_scuel_id ON portal_hospital_favorite USING btree (scuel_id) WHERE (scuel_id IS NOT NULL);


--
-- Name: idx_hospital_favorite_session_import; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hospital_favorite_session_import ON portal_hospital_favorite USING btree (session_id, scuel_id) WHERE (session_id IS NOT NULL);


--
-- Name: idx_hospital_favorite_session_portal; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hospital_favorite_session_portal ON portal_hospital_favorite USING btree (session_id, portal_hospital_id) WHERE (session_id IS NOT NULL);


--
-- Name: idx_hp_inf_status; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_hp_inf_status ON hp_inf USING btree (status);


--
-- Name: idx_portal_m_city_name; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_portal_m_city_name ON portal_m_city USING btree (name);


--
-- Name: idx_portal_m_prefecture_name; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_portal_m_prefecture_name ON portal_m_prefecture USING btree (name);


--
-- Name: idx_prompt_log_user_deleted_order; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_prompt_log_user_deleted_order ON prompt_log USING btree (user_id, is_deleted, order_value);


--
-- Name: idx_reserve_detail_exam_time_slot_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_reserve_detail_exam_time_slot_id ON reserve_detail USING btree (exam_time_slot_id);


--
-- Name: idx_reserve_detail_history_exam_time_slot_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_reserve_detail_history_exam_time_slot_id ON reserve_detail_history USING btree (exam_time_slot_id);


--
-- Name: idx_staff_id_is_verified; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_staff_id_is_verified ON mail_verification_requests USING btree (staff_id, is_verified);


--
-- Name: idx_survey_answer_no_patient_survey_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_survey_answer_no_patient_survey_id ON survey_answer_no_patient USING btree (survey_id);


--
-- Name: idx_survey_answer_no_patient_survey_id_created_at; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_survey_answer_no_patient_survey_id_created_at ON survey_answer_no_patient USING btree (survey_id, created_at);


--
-- Name: idx_survey_hospital_id_created_at_updated_at; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX idx_survey_hospital_id_created_at_updated_at ON survey USING btree (hospital_id, created_at, updated_at);


--
-- Name: idx_unique_group_id_treatment_dept_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX idx_unique_group_id_treatment_dept_id ON portal_treatment_dept_grp USING btree (group_id, treatment_department_id) WHERE (is_deleted = 0);


--
-- Name: is_deleted_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX is_deleted_idx ON m_template_document_parameter USING btree (is_deleted);


--
-- Name: ix_payment_clinic_detail_customer_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_clinic_detail_customer_id ON payment_clinic_detail USING btree (customer_id) WHERE (is_deleted = 0);


--
-- Name: ix_payment_clinic_detail_patient_id_exam_dete; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_clinic_detail_patient_id_exam_dete ON payment_clinic_detail USING btree (patient_id, exam_date) WHERE (is_deleted = 0);


--
-- Name: ix_payment_clinic_detail_payment_status; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_clinic_detail_payment_status ON payment_clinic_detail USING btree (payment_status) WHERE (is_deleted = 0);


--
-- Name: ix_payment_clinic_detail_reserve_detail_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX ix_payment_clinic_detail_reserve_detail_id ON payment_clinic_detail USING btree (reserve_detail_id) WHERE (is_deleted = 0);


--
-- Name: ix_payment_pharmacy_detail_history_pharmacy_reserve_detail_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_pharmacy_detail_history_pharmacy_reserve_detail_id ON payment_pharmacy_detail_history USING btree (pharmacy_reserve_detail_id) WHERE (is_deleted = 0);


--
-- Name: ix_payment_pharmacy_detail_patient_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_pharmacy_detail_patient_id ON payment_pharmacy_detail USING btree (customer_id) WHERE (is_deleted = 0);


--
-- Name: ix_payment_pharmacy_detail_payment_status; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX ix_payment_pharmacy_detail_payment_status ON payment_pharmacy_detail USING btree (payment_status) WHERE (is_deleted = 0);


--
-- Name: ix_payment_pharmacy_detail_pharmacy_reserve_detail_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX ix_payment_pharmacy_detail_pharmacy_reserve_detail_id ON payment_pharmacy_detail USING btree (pharmacy_reserve_detail_id) WHERE (is_deleted = 0);


--
-- Name: ix_user_mst_hp_id_login_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX ix_user_mst_hp_id_login_id ON user_mst USING btree (hp_id, login_id) WHERE (is_deleted = 0);


--
-- Name: ix_user_mst_user_id; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX ix_user_mst_user_id ON user_mst USING btree (user_id) WHERE (is_deleted = 0);


--
-- Name: m_medical_institution_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX m_medical_institution_idx_1 ON m_medical_institution USING btree (medical_institution_number);


--
-- Name: m_prompt_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX m_prompt_idx_1 ON m_prompt USING btree (prompt_name);


--
-- Name: m_treatment_category_treatment_category_group_id_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX m_treatment_category_treatment_category_group_id_idx ON m_treatment_category USING btree (treatment_category_group_id);


--
-- Name: message_item_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX message_item_idx_1 ON message_item USING btree (message_id);


--
-- Name: operator_group_operator_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX operator_group_operator_idx_1 ON m_operator_group USING btree (operator_group_name);


--
-- Name: patient_message_channel_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX patient_message_channel_idx_1 ON patient_message_channel USING btree (hospital_id);


--
-- Name: patient_message_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX patient_message_idx_1 ON patient_message USING btree (channel_id, created_at);


--
-- Name: permission_mst_permission_mst_pkey; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX permission_mst_permission_mst_pkey ON permission_mst USING btree (function_cd, permission);


--
-- Name: portal_m_tag_name_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX portal_m_tag_name_idx ON portal_m_tag USING btree (name, is_deleted);


--
-- Name: prompt_history_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX prompt_history_idx_1 ON prompt_history USING btree (prompt_history_id);


--
-- Name: pt_inf_hp_id_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX pt_inf_hp_id_idx ON pt_inf USING btree (hp_id, pt_id, is_delete);


--
-- Name: pt_inf_pt_inf_idx01; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX pt_inf_pt_inf_idx01 ON pt_inf USING btree (hp_id, pt_num);


--
-- Name: pt_inf_ukey01; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX pt_inf_ukey01 ON pt_inf USING btree (hp_id, pt_id) WHERE (is_delete = 0);


--
-- Name: reserve_detail_calendar_treatment_id_; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX reserve_detail_calendar_treatment_id_ ON reserve_detail USING btree (calendar_treatment_id);


--
-- Name: reserve_detail_patient_id_reserve_type_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX reserve_detail_patient_id_reserve_type_idx ON reserve_detail USING btree (patient_id, reserve_type);


--
-- Name: signup_user_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX signup_user_idx_1 ON signup_user USING btree (hp_id);


--
-- Name: staff_message_channel_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX staff_message_channel_idx_1 ON staff_message_channel USING btree (hospital_id);


--
-- Name: staff_message_channel_idx_2; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX staff_message_channel_idx_2 ON staff_message_channel USING btree (is_deleted);


--
-- Name: staff_message_channel_member_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX staff_message_channel_member_idx_1 ON staff_message_channel_member USING btree (member_id, is_deleted);


--
-- Name: staff_message_idx_1; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX staff_message_idx_1 ON staff_message USING btree (channel_id, created_at);


--
-- Name: survey_answer_no_patient_hospital_id_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX survey_answer_no_patient_hospital_id_idx ON survey_answer_no_patient USING btree (hospital_id);


--
-- Name: treatment_department_hospital_id_idx; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE INDEX treatment_department_hospital_id_idx ON treatment_department USING btree (hospital_id) WHERE (is_deleted = 0);


--
-- Name: unique_customer_id_line_user_id_not_deleted; Type: INDEX; Schema: local_gmoht; Owner: -
--

CREATE UNIQUE INDEX unique_customer_id_line_user_id_not_deleted ON line_account USING btree (customer_id, line_user_id) WHERE (is_deleted = 0);


--
-- Name: user_mst copy_id_trigger; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER copy_id_trigger BEFORE INSERT ON user_mst FOR EACH ROW EXECUTE FUNCTION copy_id_to_user_id();


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_status_update; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER pharmacy_reserve_detail_status_update AFTER UPDATE OF status ON pharmacy_reserve_detail FOR EACH ROW EXECUTE FUNCTION update_pharmacy_reserve_update_date();


--
-- Name: pt_inf pt_inf_history_trigger; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER pt_inf_history_trigger AFTER INSERT OR DELETE OR UPDATE ON pt_inf FOR EACH ROW EXECUTE FUNCTION insert_history_table();


--
-- Name: m_prompt trigger_before_insert_prompt; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_before_insert_prompt BEFORE INSERT ON m_prompt FOR EACH ROW EXECUTE FUNCTION trigger_insert_update_prompt_history();


--
-- Name: m_prompt trigger_before_update_prompt; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_before_update_prompt BEFORE UPDATE ON m_prompt FOR EACH ROW EXECUTE FUNCTION trigger_insert_update_prompt_history();


--
-- Name: comment_history trigger_set_timestamp; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp BEFORE UPDATE ON comment_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: agent_token trigger_set_timestamp_agent_token; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_agent_token BEFORE UPDATE ON agent_token FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: agree_log trigger_set_timestamp_agree_log; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_agree_log BEFORE UPDATE ON agree_log FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: calendar trigger_set_timestamp_calendar; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_calendar BEFORE UPDATE ON calendar FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: calendar_basic_setting trigger_set_timestamp_calendar_basic_setting; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_calendar_basic_setting BEFORE UPDATE ON calendar_basic_setting FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: calendar_treatment trigger_set_timestamp_calendar_treatment; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_calendar_treatment BEFORE UPDATE ON calendar_treatment FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: client_certificate trigger_set_timestamp_client_certificate; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_client_certificate BEFORE UPDATE ON client_certificate FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: comment_history trigger_set_timestamp_comment_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_comment_history BEFORE UPDATE ON comment_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_contents trigger_set_timestamp_contents; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_contents BEFORE UPDATE ON portal_contents FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_contents_pub trigger_set_timestamp_contents_pub; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_contents_pub BEFORE UPDATE ON portal_contents_pub FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer trigger_set_timestamp_customer; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_customer BEFORE UPDATE ON portal_customer FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_delivery_address trigger_set_timestamp_customer_delivery_address; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_customer_delivery_address BEFORE UPDATE ON portal_customer_delivery_address FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_login trigger_set_timestamp_customer_login; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_customer_login BEFORE UPDATE ON portal_customer_login FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_social_login trigger_set_timestamp_customer_social_login; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_customer_social_login BEFORE UPDATE ON portal_customer_social_login FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: deleted_task trigger_set_timestamp_deleted_task; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_deleted_task BEFORE UPDATE ON deleted_task FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: deleted_task_comment trigger_set_timestamp_deleted_task_comment; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_deleted_task_comment BEFORE UPDATE ON deleted_task_comment FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: every_patient_message_setting trigger_set_timestamp_every_patient_message_setting; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_every_patient_message_setting BEFORE UPDATE ON every_patient_message_setting FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_exam_detail trigger_set_timestamp_exam_detail; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_exam_detail BEFORE UPDATE ON portal_exam_detail FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: exam_time_slot trigger_set_timestamp_exam_time_slot; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_exam_time_slot BEFORE UPDATE ON exam_time_slot FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: fax trigger_set_timestamp_fax; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_fax BEFORE UPDATE ON fax FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: fax_number trigger_set_timestamp_fax_number; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_fax_number BEFORE UPDATE ON fax_number FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hosp_picture trigger_set_timestamp_hosp_picture; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hosp_picture BEFORE UPDATE ON portal_hosp_picture FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hosp_station trigger_set_timestamp_hosp_station; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hosp_station BEFORE UPDATE ON portal_hosp_station FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hosp_tag trigger_set_timestamp_hosp_tag; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hosp_tag BEFORE UPDATE ON portal_hosp_tag FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital trigger_set_timestamp_hospital; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hospital BEFORE UPDATE ON portal_hospital FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital_favorite trigger_set_timestamp_hospital_favorite; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hospital_favorite BEFORE UPDATE ON portal_hospital_favorite FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: hospital_treatment trigger_set_timestamp_hospital_treatment; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hospital_treatment BEFORE UPDATE ON hospital_treatment FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: hp_fincode_info trigger_set_timestamp_hp_fincode_info; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hp_fincode_info BEFORE UPDATE ON hp_fincode_info FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: hp_permission trigger_set_timestamp_hp_permission; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_hp_permission BEFORE UPDATE ON hp_permission FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: line_account trigger_set_timestamp_line_account; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_line_account BEFORE UPDATE ON line_account FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_agree trigger_set_timestamp_m_agree; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_agree BEFORE UPDATE ON m_agree FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_application_feature trigger_set_timestamp_m_application_feature; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_application_feature BEFORE UPDATE ON m_application_feature FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_city trigger_set_timestamp_m_city; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_city BEFORE UPDATE ON portal_m_city FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_drug trigger_set_timestamp_m_drug; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_drug BEFORE UPDATE ON portal_m_drug FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_drug01_category trigger_set_timestamp_m_drug01_category; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_drug01_category BEFORE UPDATE ON portal_m_drug01_category FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_drug02_category trigger_set_timestamp_m_drug02_category; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_drug02_category BEFORE UPDATE ON portal_m_drug02_category FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_llm_model trigger_set_timestamp_m_llm_model; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_llm_model BEFORE UPDATE ON m_llm_model FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_mail_delivery_settings trigger_set_timestamp_m_mail_delivery_settings; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_mail_delivery_settings BEFORE UPDATE ON m_mail_delivery_settings FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_operator_group trigger_set_timestamp_m_operator_group; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_operator_group BEFORE UPDATE ON m_operator_group FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_prefecture trigger_set_timestamp_m_prefecture; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_prefecture BEFORE UPDATE ON portal_m_prefecture FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_railline trigger_set_timestamp_m_railline; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_railline BEFORE UPDATE ON portal_m_railline FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_railline_company trigger_set_timestamp_m_railline_company; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_railline_company BEFORE UPDATE ON portal_m_railline_company FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_specialist trigger_set_timestamp_m_specialist; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_specialist BEFORE UPDATE ON portal_m_specialist FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_station trigger_set_timestamp_m_station; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_station BEFORE UPDATE ON portal_m_station FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_tag trigger_set_timestamp_m_tag; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_tag BEFORE UPDATE ON portal_m_tag FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_template_document_parameter trigger_set_timestamp_m_template_document_parameter; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_template_document_parameter BEFORE UPDATE ON m_template_document_parameter FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_template_mail trigger_set_timestamp_m_template_mail; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_template_mail BEFORE UPDATE ON m_template_mail FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_template_sms trigger_set_timestamp_m_template_sms; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_template_sms BEFORE UPDATE ON m_template_sms FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_template_survey trigger_set_timestamp_m_template_survey; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_template_survey BEFORE UPDATE ON m_template_survey FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: m_treatment_category trigger_set_timestamp_m_treatment_category; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_m_treatment_category BEFORE UPDATE ON m_treatment_category FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: mail_verification_requests trigger_set_timestamp_mail_verification_requests; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_mail_verification_requests BEFORE UPDATE ON mail_verification_requests FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: meeting trigger_set_timestamp_meeting; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_meeting BEFORE UPDATE ON meeting FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: message_item trigger_set_timestamp_message_item; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_message_item BEFORE UPDATE ON message_item FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: operator_group_permission trigger_set_timestamp_operator_group_permission; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_operator_group_permission BEFORE UPDATE ON operator_group_permission FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_clinic_memo trigger_set_timestamp_opesys_clinic_memo; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_clinic_memo BEFORE UPDATE ON opesys_clinic_memo FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_info_change trigger_set_timestamp_opesys_info_change; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_info_change BEFORE UPDATE ON opesys_info_change FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_info_change_detail_clinic trigger_set_timestamp_opesys_info_change_detail_clinic; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_info_change_detail_clinic BEFORE UPDATE ON opesys_info_change_detail_clinic FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_info_change_detail_portal trigger_set_timestamp_opesys_info_change_detail_portal; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_info_change_detail_portal BEFORE UPDATE ON opesys_info_change_detail_portal FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_memo trigger_set_timestamp_opesys_memo; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_memo BEFORE UPDATE ON opesys_memo FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: opesys_reserve_memo trigger_set_timestamp_opesys_reserve_memo; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_opesys_reserve_memo BEFORE UPDATE ON opesys_reserve_memo FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: patient_memo trigger_set_timestamp_patient_memo; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_patient_memo BEFORE UPDATE ON patient_memo FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: patient_message trigger_set_timestamp_patient_message; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_patient_message BEFORE UPDATE ON patient_message FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: patient_message_channel trigger_set_timestamp_patient_message_channel; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_patient_message_channel BEFORE UPDATE ON patient_message_channel FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: patient_message_channel_member trigger_set_timestamp_patient_message_channel_member; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_patient_message_channel_member BEFORE UPDATE ON patient_message_channel_member FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: payment_clinic_detail trigger_set_timestamp_payment_clinic_detail; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_payment_clinic_detail BEFORE UPDATE ON payment_clinic_detail FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: payment_clinic_detail_history trigger_set_timestamp_payment_clinic_detail_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_payment_clinic_detail_history BEFORE UPDATE ON payment_clinic_detail_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: payment_fincode_order trigger_set_timestamp_payment_fincode_order; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_payment_fincode_order BEFORE UPDATE ON payment_fincode_order FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: payment_pharmacy_detail trigger_set_timestamp_payment_pharmacy_detail; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_payment_pharmacy_detail BEFORE UPDATE ON payment_pharmacy_detail FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: payment_pharmacy_detail_history trigger_set_timestamp_payment_pharmacy_detail_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_payment_pharmacy_detail_history BEFORE UPDATE ON payment_pharmacy_detail_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_delivery_address trigger_set_timestamp_pharmacy_delivery_address; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_delivery_address BEFORE UPDATE ON pharmacy_delivery_address FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_delivery_history trigger_set_timestamp_pharmacy_delivery_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_delivery_history BEFORE UPDATE ON pharmacy_delivery_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_desired_date trigger_set_timestamp_pharmacy_desired_date; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_desired_date BEFORE UPDATE ON pharmacy_desired_date FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_holiday trigger_set_timestamp_pharmacy_holiday; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_holiday BEFORE UPDATE ON pharmacy_holiday FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_patient_file trigger_set_timestamp_pharmacy_patient_file; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_patient_file BEFORE UPDATE ON pharmacy_patient_file FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: pharmacy_reserve_status_history trigger_set_timestamp_pharmacy_reserve_status_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pharmacy_reserve_status_history BEFORE UPDATE ON pharmacy_reserve_status_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_pict trigger_set_timestamp_pict; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_pict BEFORE UPDATE ON portal_pict FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_agree_log trigger_set_timestamp_portal_agree_log; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_agree_log BEFORE UPDATE ON portal_agree_log FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_ai_chat_content trigger_set_timestamp_portal_ai_chat_content; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_ai_chat_content BEFORE UPDATE ON portal_ai_chat_content FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_business_time trigger_set_timestamp_portal_business_time; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_business_time BEFORE UPDATE ON portal_business_time FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_browser trigger_set_timestamp_portal_customer_browser; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_browser BEFORE UPDATE ON portal_customer_browser FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_file trigger_set_timestamp_portal_customer_file; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_file BEFORE UPDATE ON portal_customer_file FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_payment trigger_set_timestamp_portal_customer_payment; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_payment BEFORE UPDATE ON portal_customer_payment FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_payment_card trigger_set_timestamp_portal_customer_payment_card; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_payment_card BEFORE UPDATE ON portal_customer_payment_card FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_pharmacy trigger_set_timestamp_portal_customer_pharmacy; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_pharmacy BEFORE UPDATE ON portal_customer_pharmacy FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_customer_survey trigger_set_timestamp_portal_customer_survey; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_customer_survey BEFORE UPDATE ON portal_customer_survey FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital_examination trigger_set_timestamp_portal_hospital_examination; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_hospital_examination BEFORE UPDATE ON portal_hospital_examination FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital_staff trigger_set_timestamp_portal_hospital_staff; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_hospital_staff BEFORE UPDATE ON portal_hospital_staff FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_agree trigger_set_timestamp_portal_m_agree; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_m_agree BEFORE UPDATE ON portal_m_agree FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_m_examination trigger_set_timestamp_portal_m_examination; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_m_examination BEFORE UPDATE ON portal_m_examination FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_staff_picture trigger_set_timestamp_portal_staff_picture; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_staff_picture BEFORE UPDATE ON portal_staff_picture FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_treatment_dept_grp trigger_set_timestamp_portal_treatment_dept_grp; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_portal_treatment_dept_grp BEFORE UPDATE ON portal_treatment_dept_grp FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital_notification trigger_set_timestamp_potal_hospital_notification; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_potal_hospital_notification BEFORE UPDATE ON portal_hospital_notification FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_prescription_drug trigger_set_timestamp_prescription_drug; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_prescription_drug BEFORE UPDATE ON portal_prescription_drug FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: prescription_image trigger_set_timestamp_prescription_image; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_prescription_image BEFORE UPDATE ON prescription_image FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: prescription_reception trigger_set_timestamp_prescription_reception; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_prescription_reception BEFORE UPDATE ON prescription_reception FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: prompt_history trigger_set_timestamp_prompt_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_prompt_history BEFORE UPDATE ON prompt_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: prompt_log trigger_set_timestamp_prompt_log; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_prompt_log BEFORE UPDATE ON prompt_log FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: reserve trigger_set_timestamp_reserve; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_reserve BEFORE UPDATE ON reserve FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: reserve_detail trigger_set_timestamp_reserve_detail; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_reserve_detail BEFORE UPDATE ON reserve_detail FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: reserve_detail_history trigger_set_timestamp_reserve_detail_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_reserve_detail_history BEFORE UPDATE ON reserve_detail_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: schema_image trigger_set_timestamp_schema_image; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_schema_image BEFORE UPDATE ON schema_image FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: agent_token trigger_set_timestamp_staff_message; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_staff_message BEFORE UPDATE ON agent_token FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: staff_message_channel trigger_set_timestamp_staff_message_channel; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_staff_message_channel BEFORE UPDATE ON staff_message_channel FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: staff_message_channel_member trigger_set_timestamp_staff_message_channel_member; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_staff_message_channel_member BEFORE UPDATE ON staff_message_channel_member FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_hospital_specialist trigger_set_timestamp_staff_specialist; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_staff_specialist BEFORE UPDATE ON portal_hospital_specialist FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: survey trigger_set_timestamp_survey; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_survey BEFORE UPDATE ON survey FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: agent_token trigger_set_timestamp_survey_answer; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_survey_answer BEFORE UPDATE ON agent_token FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task trigger_set_timestamp_task; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task BEFORE UPDATE ON task FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_category trigger_set_timestamp_task_category; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_category BEFORE UPDATE ON task_category FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_comment trigger_set_timestamp_task_comment; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_comment BEFORE UPDATE ON task_comment FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_comment_file trigger_set_timestamp_task_comment_file; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_comment_file BEFORE UPDATE ON task_comment_file FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_file trigger_set_timestamp_task_file; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_file BEFORE UPDATE ON task_file FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_history trigger_set_timestamp_task_history; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_history BEFORE UPDATE ON task_history FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: task_status trigger_set_timestamp_task_status; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_task_status BEFORE UPDATE ON task_status FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: template_document trigger_set_timestamp_template_document; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_template_document BEFORE UPDATE ON template_document FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_treat_drug trigger_set_timestamp_treat_drug; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_treat_drug BEFORE UPDATE ON portal_treat_drug FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: treatment_department trigger_set_timestamp_treatment_department; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_treatment_department BEFORE UPDATE ON treatment_department FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: treatment_fee_list trigger_set_timestamp_treatment_fee_list; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_treatment_fee_list BEFORE UPDATE ON treatment_fee_list FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: portal_withdrawal_reason trigger_set_timestamp_withdrawal_reason; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_timestamp_withdrawal_reason BEFORE UPDATE ON portal_withdrawal_reason FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();


--
-- Name: hp_inf trigger_set_update_date_timestamp_hp_inf; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_update_date_timestamp_hp_inf BEFORE UPDATE ON hp_inf FOR EACH ROW EXECUTE FUNCTION trigger_set_update_date_timestamp();


--
-- Name: permission_mst trigger_set_update_date_timestamp_permission_mst; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_update_date_timestamp_permission_mst BEFORE UPDATE ON permission_mst FOR EACH ROW EXECUTE FUNCTION trigger_set_update_date_timestamp();


--
-- Name: pt_inf trigger_set_update_date_timestamp_pt_inf; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_update_date_timestamp_pt_inf BEFORE UPDATE ON pt_inf FOR EACH ROW EXECUTE FUNCTION trigger_set_update_date_timestamp();


--
-- Name: user_mst trigger_set_update_date_timestamp_user_mst; Type: TRIGGER; Schema: local_gmoht; Owner: -
--

CREATE TRIGGER trigger_set_update_date_timestamp_user_mst BEFORE UPDATE ON user_mst FOR EACH ROW EXECUTE FUNCTION trigger_set_update_date_timestamp();


--
-- Name: agent_config agent_config_type_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY agent_config
    ADD CONSTRAINT agent_config_type_fk FOREIGN KEY (job_type, file_type) REFERENCES m_agent_setting(job_type, file_type);


--
-- Name: calendar calendar_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar
    ADD CONSTRAINT calendar_fkey FOREIGN KEY (doctor_id) REFERENCES user_mst(id);


--
-- Name: calendar_treatment calendar_treatment_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_treatment
    ADD CONSTRAINT calendar_treatment_fk FOREIGN KEY (calendar_id) REFERENCES calendar(calendar_id);


--
-- Name: client_certificate client_certificate_hospital_id_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY client_certificate
    ADD CONSTRAINT client_certificate_hospital_id_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: comment_history comment_history_comment_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY comment_history
    ADD CONSTRAINT comment_history_comment_id_fkey FOREIGN KEY (comment_id) REFERENCES task_comment(task_comment_id);


--
-- Name: exam_time_slot exam_time_slot_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY exam_time_slot
    ADD CONSTRAINT exam_time_slot_fk FOREIGN KEY (calendar_id) REFERENCES calendar(calendar_id);


--
-- Name: calendar_basic_setting fk_calendar_basic_setting_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_basic_setting
    ADD CONSTRAINT fk_calendar_basic_setting_1 FOREIGN KEY (calendar_id) REFERENCES calendar(calendar_id);


--
-- Name: calendar_treatment fk_calendar_treatment_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY calendar_treatment
    ADD CONSTRAINT fk_calendar_treatment_2 FOREIGN KEY (treatment_department_id) REFERENCES treatment_department(treatment_department_id);


--
-- Name: hospital_treatment fk_hospital_treatment_treatment_category_id; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hospital_treatment
    ADD CONSTRAINT fk_hospital_treatment_treatment_category_id FOREIGN KEY (treatment_category_id) REFERENCES m_treatment_category(treatment_category_id);


--
-- Name: portal_m_drug01_category fk_m_drug01_catg_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug01_category
    ADD CONSTRAINT fk_m_drug01_catg_1 FOREIGN KEY (drug02_category_id) REFERENCES portal_m_drug02_category(drug02_category_id);


--
-- Name: portal_m_drug fk_m_drug_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_drug
    ADD CONSTRAINT fk_m_drug_1 FOREIGN KEY (drug01_category_id) REFERENCES portal_m_drug01_category(drug01_category_id);


--
-- Name: portal_m_railline fk_m_railline_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_railline
    ADD CONSTRAINT fk_m_railline_1 FOREIGN KEY (railline_company_id) REFERENCES portal_m_railline_company(railline_company_id);


--
-- Name: portal_m_station fk_m_station_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_m_station
    ADD CONSTRAINT fk_m_station_1 FOREIGN KEY (railline_id) REFERENCES portal_m_railline(railline_id);


--
-- Name: opesys_clinic_memo fk_opesys_clinic_memo_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_clinic_memo
    ADD CONSTRAINT fk_opesys_clinic_memo_1 FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: opesys_clinic_memo fk_opesys_clinic_memo_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_clinic_memo
    ADD CONSTRAINT fk_opesys_clinic_memo_2 FOREIGN KEY (memo_id) REFERENCES opesys_memo(memo_id);


--
-- Name: opesys_reserve_memo fk_opesys_reserve_memo_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_reserve_memo
    ADD CONSTRAINT fk_opesys_reserve_memo_1 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: opesys_reserve_memo fk_opesys_reserve_memo_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY opesys_reserve_memo
    ADD CONSTRAINT fk_opesys_reserve_memo_2 FOREIGN KEY (memo_id) REFERENCES opesys_memo(memo_id);


--
-- Name: portal_agree_log fk_portal_agree_log_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_agree_log
    ADD CONSTRAINT fk_portal_agree_log_1 FOREIGN KEY (agreement_id) REFERENCES portal_m_agree(agreement_id);


--
-- Name: portal_agree_log fk_portal_agree_log_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_agree_log
    ADD CONSTRAINT fk_portal_agree_log_2 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_ai_chat_content fk_portal_customer; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_ai_chat_content
    ADD CONSTRAINT fk_portal_customer FOREIGN KEY (portal_customer_id) REFERENCES portal_customer(customer_id) ON DELETE CASCADE;


--
-- Name: portal_customer_browser fk_portal_customer_browser_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_browser
    ADD CONSTRAINT fk_portal_customer_browser_1 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_hospital_specialist fk_portal_hospital_specialist_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_specialist
    ADD CONSTRAINT fk_portal_hospital_specialist_1 FOREIGN KEY (hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_hospital_specialist fk_portal_hospital_specialist_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_specialist
    ADD CONSTRAINT fk_portal_hospital_specialist_2 FOREIGN KEY (specialist_id) REFERENCES portal_m_specialist(specialist_id);


--
-- Name: portal_staff_picture fk_portal_staff_picture_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_staff_picture
    ADD CONSTRAINT fk_portal_staff_picture_1 FOREIGN KEY (hospital_staff_id) REFERENCES portal_hospital_staff(hospital_staff_id);


--
-- Name: portal_staff_picture fk_portal_staff_picture_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_staff_picture
    ADD CONSTRAINT fk_portal_staff_picture_2 FOREIGN KEY (pict_id) REFERENCES portal_pict(pict_id);


--
-- Name: survey_answer_no_patient fk_survey_id; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer_no_patient
    ADD CONSTRAINT fk_survey_id FOREIGN KEY (survey_id) REFERENCES survey(survey_id);


--
-- Name: portal_business_time fk_t_business_time_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_business_time
    ADD CONSTRAINT fk_t_business_time_1 FOREIGN KEY (hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_contents_pub fk_t_contents_pub_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_contents_pub
    ADD CONSTRAINT fk_t_contents_pub_1 FOREIGN KEY (pict_id) REFERENCES portal_pict(pict_id);


--
-- Name: portal_contents_pub fk_t_contents_pub_3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_contents_pub
    ADD CONSTRAINT fk_t_contents_pub_3 FOREIGN KEY (contents_id) REFERENCES portal_contents(contents_id);


--
-- Name: portal_customer fk_t_cust_3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer
    ADD CONSTRAINT fk_t_cust_3 FOREIGN KEY (parent_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_withdrawal_reason fk_t_cust_usage_ed_reason_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_withdrawal_reason
    ADD CONSTRAINT fk_t_cust_usage_ed_reason_1 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_customer_delivery_address fk_t_delivery_address_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_delivery_address
    ADD CONSTRAINT fk_t_delivery_address_1 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_exam_detail fk_t_exam_dtl_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_exam_detail
    ADD CONSTRAINT fk_t_exam_dtl_1 FOREIGN KEY (reserve_detail_id) REFERENCES reserve_detail(reserve_detail_id);


--
-- Name: portal_hosp_picture fk_t_hosp_pict_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_picture
    ADD CONSTRAINT fk_t_hosp_pict_2 FOREIGN KEY (hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_hosp_picture fk_t_hosp_pict_3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_picture
    ADD CONSTRAINT fk_t_hosp_pict_3 FOREIGN KEY (pict_id) REFERENCES portal_pict(pict_id);


--
-- Name: portal_hosp_station fk_t_hosp_station_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_station
    ADD CONSTRAINT fk_t_hosp_station_1 FOREIGN KEY (station_id) REFERENCES portal_m_station(station_id);


--
-- Name: portal_hosp_station fk_t_hosp_station_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_station
    ADD CONSTRAINT fk_t_hosp_station_2 FOREIGN KEY (hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_hosp_tag fk_t_hosp_tag_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_tag
    ADD CONSTRAINT fk_t_hosp_tag_1 FOREIGN KEY (tag_id) REFERENCES portal_m_tag(tag_id);


--
-- Name: portal_hosp_tag fk_t_hosp_tag_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hosp_tag
    ADD CONSTRAINT fk_t_hosp_tag_2 FOREIGN KEY (hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_customer_login fk_t_login_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_login
    ADD CONSTRAINT fk_t_login_1 FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_prescription_drug fk_t_presc_drug_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_prescription_drug
    ADD CONSTRAINT fk_t_presc_drug_1 FOREIGN KEY (exam_detail_id) REFERENCES portal_exam_detail(exam_detail_id);


--
-- Name: portal_prescription_drug fk_t_presc_drug_3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_prescription_drug
    ADD CONSTRAINT fk_t_presc_drug_3 FOREIGN KEY (drug_id) REFERENCES portal_m_drug(drug_id);


--
-- Name: portal_treat_drug fk_t_treat_drug_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_treat_drug
    ADD CONSTRAINT fk_t_treat_drug_1 FOREIGN KEY (drug_id) REFERENCES portal_m_drug(drug_id);


--
-- Name: task_category fk_task_category_hospital_id; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_category
    ADD CONSTRAINT fk_task_category_hospital_id FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: task_comment_file fk_task_comment_file_task_comment_id; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment_file
    ADD CONSTRAINT fk_task_comment_file_task_comment_id FOREIGN KEY (task_comment_id) REFERENCES task_comment(task_comment_id);


--
-- Name: task_file fk_task_file_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_file
    ADD CONSTRAINT fk_task_file_1 FOREIGN KEY (task_id) REFERENCES task(task_id);


--
-- Name: task_status fk_task_status_hospital_id; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_status
    ADD CONSTRAINT fk_task_status_hospital_id FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: treatment_department fk_treatment_department_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department
    ADD CONSTRAINT fk_treatment_department_1 FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: treatment_department fk_treatment_department_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department
    ADD CONSTRAINT fk_treatment_department_2 FOREIGN KEY (treatment_category_id) REFERENCES m_treatment_category(treatment_category_id);


--
-- Name: freee_dummy_detail freee_dummy_detail_deal_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_detail
    ADD CONSTRAINT freee_dummy_detail_deal_id_fkey FOREIGN KEY (deal_id) REFERENCES freee_dummy_deal(id);


--
-- Name: freee_dummy_payment freee_dummy_payment_deal_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY freee_dummy_payment
    ADD CONSTRAINT freee_dummy_payment_deal_id_fkey FOREIGN KEY (deal_id) REFERENCES freee_dummy_deal(id);


--
-- Name: portal_hospital_favorite hospital_favorite_customer_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_favorite
    ADD CONSTRAINT hospital_favorite_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_hospital_favorite hospital_favorite_portal_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_favorite
    ADD CONSTRAINT hospital_favorite_portal_hospital_id_fkey FOREIGN KEY (portal_hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: hospital_treatment hospital_treatment_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hospital_treatment
    ADD CONSTRAINT hospital_treatment_fk FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: hp_fincode_info hp_fincode_info_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_fincode_info
    ADD CONSTRAINT hp_fincode_info_fk FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: hp_permission hp_permission_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_permission
    ADD CONSTRAINT hp_permission_fk_1 FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: hp_permission hp_permission_fk_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_permission
    ADD CONSTRAINT hp_permission_fk_2 FOREIGN KEY (feature_id) REFERENCES m_application_feature(feature_id);


--
-- Name: hp_schedule hp_schedule_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_schedule
    ADD CONSTRAINT hp_schedule_fk_1 FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: hp_white_board hp_white_board_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY hp_white_board
    ADD CONSTRAINT hp_white_board_fk_1 FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: line_account line_account_portal_customer_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY line_account
    ADD CONSTRAINT line_account_portal_customer_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: m_prompt m_prompt_llm_model_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY m_prompt
    ADD CONSTRAINT m_prompt_llm_model_fkey FOREIGN KEY (llm_model) REFERENCES m_llm_model(llm_model_id);


--
-- Name: meeting meeting_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting
    ADD CONSTRAINT meeting_fkey FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: meeting meeting_fkey2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting
    ADD CONSTRAINT meeting_fkey2 FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: meeting meeting_fkey3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting
    ADD CONSTRAINT meeting_fkey3 FOREIGN KEY (pharmacy_reserve_id) REFERENCES pharmacy_reserve(pharmacy_reserve_id);


--
-- Name: meeting meeting_fkey4; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY meeting
    ADD CONSTRAINT meeting_fkey4 FOREIGN KEY (reserve_id) REFERENCES reserve(reserve_id);


--
-- Name: operator_group_permission operator_group_permission_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY operator_group_permission
    ADD CONSTRAINT operator_group_permission_fk_1 FOREIGN KEY (operator_group_id) REFERENCES m_operator_group(operator_group_id);


--
-- Name: operator_group_permission operator_group_permission_fk_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY operator_group_permission
    ADD CONSTRAINT operator_group_permission_fk_2 FOREIGN KEY (feature_id) REFERENCES m_application_feature(feature_id);


--
-- Name: patient_memo patient_memo_patient_id_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_memo
    ADD CONSTRAINT patient_memo_patient_id_fk FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: patient_message_channel patient_message_channel_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message_channel
    ADD CONSTRAINT patient_message_channel_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: patient_message_channel_member patient_message_channel_member_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message_channel_member
    ADD CONSTRAINT patient_message_channel_member_fk FOREIGN KEY (channel_id) REFERENCES patient_message_channel(channel_id);


--
-- Name: patient_message patient_message_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message
    ADD CONSTRAINT patient_message_fk FOREIGN KEY (channel_id) REFERENCES patient_message_channel(channel_id);


--
-- Name: patient_message patient_message_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY patient_message
    ADD CONSTRAINT patient_message_fk_1 FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_payment_clinic_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history
    ADD CONSTRAINT payment_clinic_detail_history_payment_clinic_detail_id_fkey FOREIGN KEY (payment_clinic_detail_id) REFERENCES payment_clinic_detail(payment_clinic_detail_id);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_reserve_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history
    ADD CONSTRAINT payment_clinic_detail_history_reserve_detail_id_fkey FOREIGN KEY (reserve_detail_id) REFERENCES reserve_detail(reserve_detail_id);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_treatment_category_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history
    ADD CONSTRAINT payment_clinic_detail_history_treatment_category_id_fkey FOREIGN KEY (treatment_category_id) REFERENCES m_treatment_category(treatment_category_id);


--
-- Name: payment_clinic_detail_history payment_clinic_detail_history_treatment_department_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_clinic_detail_history
    ADD CONSTRAINT payment_clinic_detail_history_treatment_department_id_fkey FOREIGN KEY (treatment_department_id) REFERENCES treatment_department(treatment_department_id);


--
-- Name: payment_pharmacy_detail_history payment_pharmacy_detail_history_payment_pharmacy_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail_history
    ADD CONSTRAINT payment_pharmacy_detail_history_payment_pharmacy_detail_id_fkey FOREIGN KEY (payment_pharmacy_detail_id) REFERENCES payment_pharmacy_detail(payment_pharmacy_detail_id);


--
-- Name: payment_pharmacy_detail_history payment_pharmacy_detail_history_pharmacy_reserve_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail_history
    ADD CONSTRAINT payment_pharmacy_detail_history_pharmacy_reserve_detail_id_fkey FOREIGN KEY (pharmacy_reserve_detail_id) REFERENCES pharmacy_reserve_detail(pharmacy_reserve_detail_id);


--
-- Name: payment_pharmacy_detail payment_pharmacy_detail_pharmacy_reserve_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY payment_pharmacy_detail
    ADD CONSTRAINT payment_pharmacy_detail_pharmacy_reserve_detail_id_fkey FOREIGN KEY (pharmacy_reserve_detail_id) REFERENCES pharmacy_reserve_detail(pharmacy_reserve_detail_id);


--
-- Name: pharmacy_delivery_address pharmacy_delivery_address_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_address
    ADD CONSTRAINT pharmacy_delivery_address_fk FOREIGN KEY (pharmacy_reserve_id) REFERENCES pharmacy_reserve(pharmacy_reserve_id);


--
-- Name: pharmacy_delivery_address pharmacy_delivery_address_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_address
    ADD CONSTRAINT pharmacy_delivery_address_fk_1 FOREIGN KEY (delivery_address_id) REFERENCES portal_customer_delivery_address(delivery_address_id);


--
-- Name: pharmacy_delivery_history pharmacy_delivery_history_pharmacy_reserve_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_delivery_history
    ADD CONSTRAINT pharmacy_delivery_history_pharmacy_reserve_fk FOREIGN KEY (pharmacy_reserve_id) REFERENCES pharmacy_reserve(pharmacy_reserve_id);


--
-- Name: pharmacy_desired_date pharmacy_desired_date_pharmacy_reserve_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_desired_date
    ADD CONSTRAINT pharmacy_desired_date_pharmacy_reserve_id_fkey FOREIGN KEY (pharmacy_reserve_id) REFERENCES pharmacy_reserve(pharmacy_reserve_id);


--
-- Name: pharmacy_holiday pharmacy_holiday_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_holiday
    ADD CONSTRAINT pharmacy_holiday_hospital_id_fkey FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: pharmacy_patient_file pharmacy_patient_file_fkey_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_patient_file
    ADD CONSTRAINT pharmacy_patient_file_fkey_2 FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: pharmacy_patient_file pharmacy_patient_file_pharmacy_reserve_detail_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_patient_file
    ADD CONSTRAINT pharmacy_patient_file_pharmacy_reserve_detail_fk FOREIGN KEY (pharmacy_reserve_detail_id) REFERENCES pharmacy_reserve_detail(pharmacy_reserve_detail_id);


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_detail
    ADD CONSTRAINT pharmacy_reserve_detail_fk FOREIGN KEY (reserve_detail_id) REFERENCES reserve_detail(reserve_detail_id);


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_patient_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_detail
    ADD CONSTRAINT pharmacy_reserve_detail_patient_id_fkey FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: pharmacy_reserve_detail pharmacy_reserve_detail_pharmacy_reserve_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_detail
    ADD CONSTRAINT pharmacy_reserve_detail_pharmacy_reserve_id_fkey FOREIGN KEY (pharmacy_reserve_id) REFERENCES pharmacy_reserve(pharmacy_reserve_id);


--
-- Name: pharmacy_reserve pharmacy_reserve_patient_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve
    ADD CONSTRAINT pharmacy_reserve_patient_id_fkey FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: pharmacy_reserve pharmacy_reserve_pharmacist_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve
    ADD CONSTRAINT pharmacy_reserve_pharmacist_id_fkey FOREIGN KEY (pharmacist_id) REFERENCES user_mst(id);


--
-- Name: pharmacy_reserve pharmacy_reserve_reserve_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve
    ADD CONSTRAINT pharmacy_reserve_reserve_id_fkey FOREIGN KEY (reserve_id) REFERENCES reserve(reserve_id);


--
-- Name: pharmacy_reserve_status_history pharmacy_reserve_status_history_pharmacist_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_status_history
    ADD CONSTRAINT pharmacy_reserve_status_history_pharmacist_id_fkey FOREIGN KEY (pharmacist_id) REFERENCES user_mst(id);


--
-- Name: pharmacy_reserve_status_history pharmacy_reserve_status_history_pharmacy_reserve_detail_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pharmacy_reserve_status_history
    ADD CONSTRAINT pharmacy_reserve_status_history_pharmacy_reserve_detail_id_fkey FOREIGN KEY (pharmacy_reserve_detail_id) REFERENCES pharmacy_reserve_detail(pharmacy_reserve_detail_id);


--
-- Name: portal_customer_file portal_customer_file_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_file
    ADD CONSTRAINT portal_customer_file_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_customer_payment_card portal_customer_payment_card_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment_card
    ADD CONSTRAINT portal_customer_payment_card_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_customer_payment portal_customer_payment_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_payment
    ADD CONSTRAINT portal_customer_payment_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_customer_pharmacy portal_customer_pharmacy_portal_customer_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_pharmacy
    ADD CONSTRAINT portal_customer_pharmacy_portal_customer_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_customer_pharmacy portal_customer_pharmacy_reserve_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_pharmacy
    ADD CONSTRAINT portal_customer_pharmacy_reserve_fk FOREIGN KEY (reserve_id) REFERENCES reserve(reserve_id);


--
-- Name: portal_customer_survey portal_customer_survey_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_customer_survey
    ADD CONSTRAINT portal_customer_survey_fk FOREIGN KEY (customer_id) REFERENCES portal_customer(customer_id);


--
-- Name: portal_hospital_examination portal_hospital_examination_examination_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_examination
    ADD CONSTRAINT portal_hospital_examination_examination_id_fkey FOREIGN KEY (examination_id) REFERENCES portal_m_examination(examination_id);


--
-- Name: portal_hospital_examination portal_hospital_examination_portal_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_examination
    ADD CONSTRAINT portal_hospital_examination_portal_hospital_id_fkey FOREIGN KEY (portal_hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_hospital portal_hospital_hp_inf_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital
    ADD CONSTRAINT portal_hospital_hp_inf_id_fkey FOREIGN KEY (hp_inf_id) REFERENCES hp_inf(hp_id);


--
-- Name: portal_hospital_notification portal_hospital_notification_portal_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_notification
    ADD CONSTRAINT portal_hospital_notification_portal_hospital_id_fkey FOREIGN KEY (portal_hospital_id) REFERENCES portal_hospital(hospital_id);


--
-- Name: portal_hospital_staff portal_hospital_staff_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY portal_hospital_staff
    ADD CONSTRAINT portal_hospital_staff_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: prescription_image prescription_image_prescription_reception_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_image
    ADD CONSTRAINT prescription_image_prescription_reception_id_fkey FOREIGN KEY (prescription_reception_id) REFERENCES prescription_reception(prescription_reception_id);


--
-- Name: prescription_reception prescription_reception_hp_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY prescription_reception
    ADD CONSTRAINT prescription_reception_hp_id_fkey FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: pt_inf pt_inf_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY pt_inf
    ADD CONSTRAINT pt_inf_fk FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: read_system_notice read_system_notice_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY read_system_notice
    ADD CONSTRAINT read_system_notice_fk_1 FOREIGN KEY (hospital_staff_id) REFERENCES portal_hospital_staff(hospital_staff_id);


--
-- Name: reserve_detail reserve_detail_fk1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail
    ADD CONSTRAINT reserve_detail_fk1 FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: reserve_detail reserve_detail_fk2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail
    ADD CONSTRAINT reserve_detail_fk2 FOREIGN KEY (calendar_treatment_id) REFERENCES calendar_treatment(calendar_treatment_id);


--
-- Name: reserve_detail reserve_detail_fk3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail
    ADD CONSTRAINT reserve_detail_fk3 FOREIGN KEY (reserve_id) REFERENCES reserve(reserve_id);


--
-- Name: reserve_detail reserve_detail_fk4; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail
    ADD CONSTRAINT reserve_detail_fk4 FOREIGN KEY (exam_time_slot_id) REFERENCES exam_time_slot(exam_time_slot_id);


--
-- Name: reserve_detail_history reserve_detail_history_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail_history
    ADD CONSTRAINT reserve_detail_history_fk FOREIGN KEY (calendar_treatment_id) REFERENCES calendar_treatment(calendar_treatment_id);


--
-- Name: reserve_detail_history reserve_detail_history_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail_history
    ADD CONSTRAINT reserve_detail_history_fk_1 FOREIGN KEY (reserve_detail_id) REFERENCES reserve_detail(reserve_detail_id);


--
-- Name: reserve_detail_history reserve_detail_history_fk_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve_detail_history
    ADD CONSTRAINT reserve_detail_history_fk_2 FOREIGN KEY (exam_time_slot_id) REFERENCES exam_time_slot(exam_time_slot_id);


--
-- Name: reserve reserve_patient_id_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY reserve
    ADD CONSTRAINT reserve_patient_id_fk FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: schema_image schema_image_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY schema_image
    ADD CONSTRAINT schema_image_hospital_id_fkey FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: signup_user signup_user_hp_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY signup_user
    ADD CONSTRAINT signup_user_hp_id_fkey FOREIGN KEY (hp_id) REFERENCES hp_inf(hp_id);


--
-- Name: signup_user signup_user_user_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY signup_user
    ADD CONSTRAINT signup_user_user_id_fkey FOREIGN KEY (user_id) REFERENCES user_mst(id);


--
-- Name: staff_message_channel staff_message_channel_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message_channel
    ADD CONSTRAINT staff_message_channel_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: staff_message_channel_member staff_message_channel_member_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message_channel_member
    ADD CONSTRAINT staff_message_channel_member_fk FOREIGN KEY (channel_id) REFERENCES staff_message_channel(channel_id);


--
-- Name: staff_message staff_message_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message
    ADD CONSTRAINT staff_message_fk FOREIGN KEY (channel_id) REFERENCES staff_message_channel(channel_id);


--
-- Name: staff_message staff_message_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY staff_message
    ADD CONSTRAINT staff_message_fk_1 FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: survey_answer survey_answer_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer
    ADD CONSTRAINT survey_answer_fk FOREIGN KEY (reserve_detail_id) REFERENCES reserve_detail(reserve_detail_id);


--
-- Name: survey_answer survey_answer_fk_1; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer
    ADD CONSTRAINT survey_answer_fk_1 FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: survey_answer survey_answer_fk_2; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey_answer
    ADD CONSTRAINT survey_answer_fk_2 FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: survey survey_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY survey
    ADD CONSTRAINT survey_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: task_comment task_comment_task_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_comment
    ADD CONSTRAINT task_comment_task_id_fkey FOREIGN KEY (task_id) REFERENCES task(task_id);


--
-- Name: task task_created_staff_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_created_staff_id_fkey FOREIGN KEY (created_staff_id) REFERENCES user_mst(id);


--
-- Name: task_history task_history_edited_staff_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_history
    ADD CONSTRAINT task_history_edited_staff_id_fkey FOREIGN KEY (edited_staff_id) REFERENCES user_mst(id);


--
-- Name: task_history task_history_task_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task_history
    ADD CONSTRAINT task_history_task_id_fkey FOREIGN KEY (task_id) REFERENCES task(task_id);


--
-- Name: task task_hospital_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_hospital_id_fkey FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: task task_patient_id_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_patient_id_fk FOREIGN KEY (patient_id) REFERENCES pt_inf(pt_id);


--
-- Name: task task_responsible_staff_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_responsible_staff_id_fkey FOREIGN KEY (responsible_staff_id) REFERENCES user_mst(id);


--
-- Name: task task_task_category_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_task_category_id_fkey FOREIGN KEY (task_category_id) REFERENCES task_category(task_category_id);


--
-- Name: task task_task_status_id_fkey; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY task
    ADD CONSTRAINT task_task_status_id_fkey FOREIGN KEY (task_status_id) REFERENCES task_status(task_status_id);


--
-- Name: template_document template_document_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY template_document
    ADD CONSTRAINT template_document_fk FOREIGN KEY (hospital_id) REFERENCES hp_inf(hp_id);


--
-- Name: treatment_department treatment_department_fk_3; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department
    ADD CONSTRAINT treatment_department_fk_3 FOREIGN KEY (first_medical_interview_form_id) REFERENCES survey(survey_id);


--
-- Name: treatment_department treatment_department_fk_4; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_department
    ADD CONSTRAINT treatment_department_fk_4 FOREIGN KEY (next_medical_interview_form_id) REFERENCES survey(survey_id);


--
-- Name: treatment_fee_list treatment_fee_list_fk; Type: FK CONSTRAINT; Schema: local_gmoht; Owner: -
--

ALTER TABLE ONLY treatment_fee_list
    ADD CONSTRAINT treatment_fee_list_fk FOREIGN KEY (treatment_department_id) REFERENCES treatment_department(treatment_department_id);


--
-- PostgreSQL database dump complete
--


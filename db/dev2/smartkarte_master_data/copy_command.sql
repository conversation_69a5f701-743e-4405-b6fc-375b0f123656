truncate table accounting_form_mst;
truncate table auto_santei_mst;
truncate table bui_odr_byomei_mst;
truncate table bui_odr_item_byomei_mst;
truncate table bui_odr_item_mst;
truncate table bui_odr_mst;
truncate table byomei_mst;
truncate table byomei_mst_aftercare;
truncate table cmt_kbn_mst;
truncate table common_kensa_mst;
truncate table common_center_kensa_mst;
truncate table common_center_item_mst;
truncate table common_jlac10_analyte;
truncate table common_jlac10_idenfification;
truncate table common_jlac10_methodology;
truncate table common_jlac10_result_id_common;
truncate table common_jlac10_result_id_unique;
truncate table common_jlac10_specimen;
truncate table common_kensa_center_mst;
truncate table def_hoken_no;
truncate table densi_haihan_custom;
truncate table densi_haihan_day;
truncate table densi_haihan_karte;
truncate table densi_haihan_month;
truncate table densi_haihan_week;
truncate table densi_hojyo;
truncate table densi_houkatu;
truncate table densi_houkatu_grp;
truncate table densi_santei_kaisu;
truncate table drug_unit_conv;
truncate table except_hokensya;
truncate table gc_std_mst;
truncate table hoken_mst;
truncate table hokensya_mst;
truncate table holiday_mst;
truncate table ipn_kasan_exclude;
truncate table ipn_kasan_exclude_item;
truncate table ipn_kasan_mst;
truncate table ipn_min_yakka_mst;
truncate table ipn_name_mst;
truncate table item_grp_mst;
truncate table jihi_sbt_mst;
truncate table job_mst;
truncate table ka_mst;
truncate table kacode_mst;
truncate table kacode_rece_yousiki;
truncate table kacode_yousiki_mst;
truncate table kantoku_mst;
truncate table karte_kbn_mst;
truncate table kensa_mst;
truncate table kogaku_limit;
truncate table kohi_priority;
truncate table koui_kbn_mst;
truncate table lock_mst;
truncate table m01_kijyo_cmt;
truncate table m01_kinki;
truncate table m01_kinki_cmt;
truncate table m10_day_limit;
truncate table m12_food_alrgy;
truncate table m12_food_alrgy_kbn;
truncate table m14_age_check;
truncate table m14_cmt_code;
truncate table m28_drug_mst;
truncate table m34_ar_code;
truncate table m34_ar_discon;
truncate table m34_ar_discon_code;
truncate table m34_drug_info_main;
truncate table m34_form_code;
truncate table m34_indication_code;
truncate table m34_interaction_pat;
truncate table m34_interaction_pat_code;
truncate table m34_precaution_code;
truncate table m34_precautions;
truncate table m34_property_code;
truncate table m34_sar_symptom_code;
truncate table m38_class_code;
truncate table m38_ing_code;
truncate table m38_ingredients;
truncate table m38_major_div_code;
truncate table m38_otc_form_code;
truncate table m38_otc_main;
truncate table m38_otc_maker_code;
truncate table m41_supple_indexcode;
truncate table m41_supple_indexdef;
truncate table m41_supple_ingre;
truncate table m42_contra_cmt;
truncate table m42_contraindi_dis_bc;
truncate table m42_contraindi_dis_class;
truncate table m42_contraindi_dis_con;
truncate table m42_contraindi_drug_main_ex;
truncate table m46_dosage_dosage;
truncate table m46_dosage_drug;
truncate table m56_alrgy_derivatives;
truncate table m56_analogue_cd;
truncate table m56_drug_class;
truncate table m56_drvalrgy_code;
truncate table m56_ex_analogue;
truncate table m56_ex_ed_ingredients;
truncate table m56_ex_ing_code;
truncate table m56_ex_ingrdt_main;
truncate table m56_prodrug_cd;
truncate table m56_usage_code;
truncate table m56_yj_drug_class;
truncate table payment_method_mst;
truncate table physical_average;
truncate table pi_inf;
truncate table pi_inf_detail;
truncate table pi_product_inf;
truncate table post_code_mst;
truncate table priority_haihan_mst;
truncate table raiin_status_mst;
truncate table receden_cmt_select;
truncate table renkei_mst;
truncate table renkei_template_mst;
truncate table renkei_timing_mst;
truncate table roudou_mst;
truncate table rousai_gosei_mst;
truncate table santei_auto_order;
truncate table santei_auto_order_detail;
truncate table santei_cnt_check;
truncate table santei_grp_detail;
truncate table santei_grp_mst;
truncate table single_dose_mst;
truncate table sokatu_mst;
truncate table sta_conf;
truncate table sta_grp;
truncate table sta_menu;
truncate table sta_mst;
truncate table syouki_kbn_mst;
truncate table system_conf;
truncate table system_conf_item;
truncate table system_conf_menu;
truncate table system_generation_conf;
truncate table tekiou_byomei_mst;
truncate table ten_mst;
truncate table ten_mst_mother;
truncate table tokki_mst;
truncate table uketuke_sbt_mst;
truncate table unit_mst;
truncate table yakka_syusai_mst;
truncate table yoho_mst;
truncate table yoho_replace_mst;
truncate table yoho_word_mst;

\COPY "accounting_form_mst" (hp_id,form_no,form_name,form_type,print_sort,miseisan_kbn,sai_kbn,misyu_kbn,seikyu_kbn,hoken_kbn,form,sort_no,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,base) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/accounting_form_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "auto_santei_mst" (hp_id,item_cd,seq_no,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/auto_santei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "bui_odr_byomei_mst" (hp_id,bui_id,byomei_bui,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/bui_odr_byomei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "bui_odr_item_byomei_mst" (hp_id,item_cd,byomei_bui,lr_kbn,both_kbn,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/bui_odr_item_byomei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "bui_odr_item_mst" (hp_id,item_cd,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/bui_odr_item_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "bui_odr_mst" (hp_id,bui_id,odr_bui,lr_kbn,both_kbn,koui_30,koui_40,koui_50,koui_60,koui_70,koui_80,update_date,update_id,update_machine,must_lr_kbn) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/bui_odr_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "byomei_mst" (hp_id,byomei_cd,byomei,sbyomei,kana_name1,kana_name2,kana_name3,kana_name4,kana_name5,kana_name6,kana_name7,iko_cd,sikkan_cd,tandoku_kinsi,hoken_gai,byomei_kanri,saitaku_kbn,koukan_cd,syusai_date,upd_date,del_date,nanbyo_cd,icd10_1,icd10_2,icd10_1_2013,icd10_2_2013,is_adopted,syusyoku_kbn,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/byomei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "byomei_mst_aftercare" (byomei_cd,byomei,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/byomei_mst_aftercare.csv.gz' with encoding 'UTF8' csv header
\COPY "cmt_kbn_mst" (id,hp_id,item_cd,start_date,end_date,cmt_kbn,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/cmt_kbn_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "common_kensa_mst" (kensa_item_cd,kensa_name,kensa_kana,jlac10,jlac11,analyte,identification,specimen,methodology,result_id_common,result_id_unique,santei_item_cd,unit,start_date,end_date,irai_kbn,oya_kensa_item_cd,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_kensa_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "common_center_kensa_mst" (center_cd,kensa_item_cd,center_item_cd,start_date,end_date,kensa_name,kensa_kana,unit,material,container,irai_kbn,oya_kensa_cd,sort_no,update_date,jlac10) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_center_kensa_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "common_center_item_mst" (center_cd,item_cd,kensa_item_cd,start_date,end_date,name,kana_name,santei_item_cd,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_center_item_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_analyte" (analyte,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_analyte.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_idenfification" (identification,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_idenfification.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_methodology" (methodology,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_methodology.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_result_id_common" (result_id_common,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_result_id_common.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_result_id_unique" (result_id_unique,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_result_id_unique.csv.gz' with encoding 'UTF8' csv header
\COPY "common_jlac10_specimen" (specimen,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_jlac10_specimen.csv.gz' with encoding 'UTF8' csv header
\COPY "common_kensa_center_mst" (center_cd,center_key,center_name,dsp_center_name,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/common_kensa_center_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "def_hoken_no" (hp_id,digit_1,digit_2,digit_3,digit_4,digit_5,digit_6,seq_no,hoken_no,hoken_eda_no,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,digit_7,digit_8,sort_no) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/def_hoken_no.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_haihan_custom" (hp_id,item_cd1,item_cd2,start_date,seq_no,user_setting,haihan_kbn,sp_jyoken,end_date,term_cnt,term_sbt,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_haihan_custom.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_haihan_day" (hp_id,item_cd1,item_cd2,start_date,seq_no,user_setting,haihan_kbn,sp_jyoken,end_date,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_haihan_day.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_haihan_karte" (hp_id,item_cd1,item_cd2,start_date,seq_no,user_setting,haihan_kbn,sp_jyoken,end_date,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_haihan_karte.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_haihan_month" (hp_id,item_cd1,item_cd2,start_date,seq_no,user_setting,haihan_kbn,sp_jyoken,end_date,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,inc_after,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_haihan_month.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_haihan_week" (hp_id,item_cd1,item_cd2,start_date,seq_no,user_setting,haihan_kbn,sp_jyoken,end_date,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,inc_after,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_haihan_week.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_hojyo" (hp_id,item_cd,start_date,houkatu_term1,houkatu_grp_no1,houkatu_term2,houkatu_grp_no2,houkatu_term3,houkatu_grp_no3,haihan_day,haihan_month,haihan_karte,haihan_week,nyuin_id,santei_kaisu,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_hojyo.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_houkatu" (hp_id,item_cd,start_date,seq_no,user_setting,end_date,target_kbn,houkatu_term,houkatu_grp_no,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_houkatu.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_houkatu_grp" (hp_id,houkatu_grp_no,item_cd,start_date,seq_no,user_setting,sp_jyoken,end_date,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_houkatu_grp.csv.gz' with encoding 'UTF8' csv header
\COPY "densi_santei_kaisu" (hp_id,item_cd,unit_cd,start_date,seq_no,user_setting,max_count,sp_jyoken,end_date,target_kbn,term_count,term_sbt,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,id,item_grp_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/densi_santei_kaisu.csv.gz' with encoding 'UTF8' csv header
\COPY "drug_unit_conv" (item_cd,start_date,end_date,cnv_val,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/drug_unit_conv.csv.gz' with encoding 'UTF8' csv header
\COPY "except_hokensya" (hp_id,pref_no,hoken_no,hoken_eda_no,start_date,hokensya_no,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/except_hokensya.csv.gz' with encoding 'UTF8' csv header
\COPY "gc_std_mst" (std_kbn,sex,point,sd_m25,sd_m20,sd_m10,sd_avg,sd_p10,sd_p20,sd_p25,per_03,per_10,per_25,per_50,per_75,per_90,per_97,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/gc_std_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "hokensya_mst" (hp_id,hokensya_no,name,kana_name,houbetu_kbn,houbetu,hoken_kbn,pref_no,kigo,bango,rate_honnin,rate_kazoku,post_code,address1,address2,tel1,delete_date,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,is_kigo_na) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/hokensya_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "hoken_mst" (hp_id,pref_no,hoken_no,hoken_eda_no,start_date,end_date,sort_no,hoken_sbt_kbn,houbetu,hoken_name,hoken_sname,hoken_name_cd,check_digit,jyukyu_check_digit,is_futansya_no_check,is_jyukyusya_no_check,is_tokusyu_no_check,is_limit_list,is_limit_list_sum,is_other_pref_valid,age_start,age_end,en_ten,seikyu_ym,rece_sp_kbn,rece_seikyu_kbn,rece_futan_round,rece_kisai,rece_zero_kisai,rece_futan_kbn,rece_ten_kisai,calc_sp_kbn,limit_kbn,count_kbn,futan_kbn,futan_rate,kai_futangaku,kai_limit_futan,day_limit_futan,day_limit_count,month_limit_futan,month_limit_count,create_date,create_id,create_machine,update_date,update_id,update_machine,kogaku_tekiyo,hoken_kohi_kbn,month_sp_limit,rece_kisai2,rece_futan_hide,kogaku_total_kbn,futan_yusen,kogaku_total_all,rece_kisai_kokho,kogaku_hairyo_kbn,kogaku_total_exc_futan) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/hoken_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "holiday_mst" (hp_id,sin_date,seq_no,holiday_kbn,holiday_name,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,kyusin_kbn) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/holiday_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ipn_kasan_exclude" (ipn_name_cd,start_date,seq_no,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ipn_kasan_exclude.csv.gz' with encoding 'UTF8' csv header
\COPY "ipn_kasan_exclude_item" (item_cd,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ipn_kasan_exclude_item.csv.gz' with encoding 'UTF8' csv header
\COPY "ipn_kasan_mst" (ipn_name_cd,start_date,seq_no,end_date,kasan1,kasan2,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ipn_kasan_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ipn_min_yakka_mst" (ipn_name_cd,start_date,seq_no,end_date,yakka,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ipn_min_yakka_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ipn_name_mst" (ipn_name_cd,start_date,seq_no,end_date,ipn_name,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ipn_name_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "item_grp_mst" (hp_id,grp_sbt,item_grp_cd,start_date,seq_no,end_date,item_cd,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/item_grp_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "jihi_sbt_mst" (hp_id,jihi_sbt,sort_no,name,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,is_yobo) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/jihi_sbt_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "job_mst" (hp_id,job_cd,job_name,sort_no,create_date,create_id,create_machine,update_date,update_id,update_machine,is_deleted) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/job_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "kacode_mst" (rece_ka_cd,sort_no,ka_name,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kacode_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "kacode_rece_yousiki" (rece_ka_cd,yousiki_ka_cd,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kacode_rece_yousiki.csv.gz' with encoding 'UTF8' csv header
\COPY "kacode_yousiki_mst" (yousiki_ka_cd,sort_no,ka_name,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kacode_yousiki_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "kantoku_mst" (roudou_cd,kantoku_cd,kantoku_name,create_date,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kantoku_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "karte_kbn_mst" (hp_id,karte_kbn,kbn_name,kbn_short_name,can_img,sort_no,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/karte_kbn_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ka_mst" (hp_id,ka_id,sort_no,rece_ka_cd,ka_sname,ka_name,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,id,yousiki_ka_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ka_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "kensa_mst" (hp_id,kensa_item_cd,kensa_item_seq_no,center_cd,kensa_name,kensa_kana,unit,material_cd,container_cd,male_std,male_std_low,male_std_high,female_std,female_std_low,female_std_high,formula,oya_item_cd,oya_item_seq_no,sort_no,center_item_cd1,center_item_cd2,is_delete,create_date,create_id,create_machine,update_date,update_id,update_machine,digit) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kensa_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "kogaku_limit" (kogaku_kbn,income_kbn,start_date,end_date,base_limit,adjust_limit,tasu_limit,create_date,create_id,create_machine,update_date,update_id,update_machine,age_kbn) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kogaku_limit.csv.gz' with encoding 'UTF8' csv header
\COPY "kohi_priority" (pref_no,houbetu,priority_no,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/kohi_priority.csv.gz' with encoding 'UTF8' csv header
\COPY "koui_kbn_mst" (koui_kbn_id,sort_no,koui_kbn1,koui_kbn2,koui_grp_name,create_date,create_id,create_machine,koui_name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/koui_kbn_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "lock_mst" (function_cd_a,function_cd_b,lock_range,lock_level,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,hp_id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/lock_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "m01_kijyo_cmt" (cmt_cd,cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m01_kijyo_cmt.csv.gz' with encoding 'UTF8' csv header
\COPY "m01_kinki" (a_cd,b_cd,cmt_cd,sayokijyo_cd,kyodo_cd,kyodo,data_kbn) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m01_kinki.csv.gz' with encoding 'UTF8' csv header
\COPY "m01_kinki_cmt" (cmt_cd,cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m01_kinki_cmt.csv.gz' with encoding 'UTF8' csv header
\COPY "m10_day_limit" (yj_cd,seq_no,limit_day,st_date,ed_date,cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m10_day_limit.csv.gz' with encoding 'UTF8' csv header
\COPY "m12_food_alrgy" (kikin_cd,yj_cd,food_kbn,tenpu_level,attention_cmt,working_mechanism) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m12_food_alrgy.csv.gz' with encoding 'UTF8' csv header
\COPY "m12_food_alrgy_kbn" (food_kbn,food_name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m12_food_alrgy_kbn.csv.gz' with encoding 'UTF8' csv header
\COPY "m14_age_check" (yj_cd,attention_cmt_cd,working_mechanism,tenpu_level,age_kbn,weight_kbn,sex_kbn,age_min,age_max,weight_min,weight_max) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m14_age_check.csv.gz' with encoding 'UTF8' csv header
\COPY "m14_cmt_code" (attention_cmt_cd,attention_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m14_cmt_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m28_drug_mst" (yj_cd,koseisyo_cd,kikin_cd,drug_name,drug_kana1,drug_kana2,ipn_name,ipn_kana,yakka_val,yakka_unit,seibun_rikika,seibun_rikika_unit,yoryo_jyuryo,yoryo_jyuryo_unit,seiriki_yoryo_rate,seiriki_yoryo_unit,maker_cd,maker_name,drug_kbn_cd,drug_kbn,form_kbn_cd,form_kbn,dokuyaku_flg,gekiyaku_flg,mayaku_flg,koseisinyaku_flg,kakuseizai_flg,kakuseizai_genryo_flg,seibutu_flg,sp_seibutu_flg,kohatu_flg,yakka,kikaku_unit,yakka_rate_formura,yakka_rate_unit,yakka_syusai_date,keikasoti_date,main_drug_cd,main_drug_name,main_drug_kana,key_seibun,haigo_flg,main_drug_name_flg) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m28_drug_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_ar_code" (fukusayo_cd,fukusayo_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_ar_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_ar_discon" (yj_cd,seq_no,fukusayo_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_ar_discon.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_ar_discon_code" (fukusayo_cd,fukusayo_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_ar_discon_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_drug_info_main" (yj_cd,form_cd,color,mark,kono_cd,fukusayo_cd,fukusayo_init_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_drug_info_main.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_form_code" (form_cd,form) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_form_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_indication_code" (kono_cd,kono_detail_cmt,kono_simple_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_indication_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_interaction_pat" (yj_cd,seq_no,interaction_pat_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_interaction_pat.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_interaction_pat_code" (interaction_pat_cd,interaction_pat_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_interaction_pat_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_precautions" (yj_cd,seq_no,precaution_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_precautions.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_precaution_code" (precaution_cd,extend_cd,precaution_cmt,property_cd,age_max,age_min,sex_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_precaution_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_property_code" (property_cd,property) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_property_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m34_sar_symptom_code" (fukusayo_init_cd,fukusayo_init_cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m34_sar_symptom_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_class_code" (class_cd,class_name,major_div_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_class_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_ingredients" (serial_num,seibun_cd,sbt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_ingredients.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_ing_code" (seibun_cd,seibun) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_ing_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_major_div_code" (major_div_cd,major_div_name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_major_div_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_otc_form_code" (form_cd,form) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_otc_form_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_otc_main" (serial_num,otc_cd,trade_name,trade_kana,class_cd,company_cd,trade_cd,drug_form_cd,yoho_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_otc_main.csv.gz' with encoding 'UTF8' csv header
\COPY "m38_otc_maker_code" (maker_cd,maker_name,maker_kana) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m38_otc_maker_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m41_supple_indexcode" (seibun_cd,index_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m41_supple_indexcode.csv.gz' with encoding 'UTF8' csv header
\COPY "m41_supple_indexdef" (seibun_cd,index_word,tokuho_flg) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m41_supple_indexdef.csv.gz' with encoding 'UTF8' csv header
\COPY "m41_supple_ingre" (seibun_cd,seibun) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m41_supple_ingre.csv.gz' with encoding 'UTF8' csv header
\COPY "m42_contraindi_dis_bc" (byotai_cd,byotai_class_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m42_contraindi_dis_bc.csv.gz' with encoding 'UTF8' csv header
\COPY "m42_contraindi_dis_class" (byotai_class_cd,byotai) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m42_contraindi_dis_class.csv.gz' with encoding 'UTF8' csv header
\COPY "m42_contraindi_dis_con" (byotai_cd,standard_byotai,byotai_kbn,byomei,icd10,rece_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m42_contraindi_dis_con.csv.gz' with encoding 'UTF8' csv header
\COPY "m42_contraindi_drug_main_ex" (yj_cd,tenpu_level,byotai_cd,cmt_cd,stage,kio_cd,family_cd,kijyo_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m42_contraindi_drug_main_ex.csv.gz' with encoding 'UTF8' csv header
\COPY "m42_contra_cmt" (cmt_cd,cmt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m42_contra_cmt.csv.gz' with encoding 'UTF8' csv header
\COPY "m46_dosage_dosage" (doei_cd,doei_seq_no,konokoka_cd,kensa_pcd,age_over,age_under,age_cd,weight_over,weight_under,body_over,body_under,drug_route,use_flg,drug_condition,konokoka,usage_dosage,filename_cd,drug_syugi,tekio_bui,youkai_kisyaku,kisyakueki,youkaieki,haita_flg,ng_kisyakueki,ng_youkaieki,combi_drug,drug_link_cd,drug_order,single_drug_flg,kyugen_cd,dosage_check_flg,once_min,once_max,once_unit,once_limit,once_limit_unit,day_min_cnt,day_max_cnt,day_min,day_max,day_unit,day_limit,day_limit_unit,rise,morning,daytime,night,sleep,before_meal,just_before_meal,after_meal,just_after_meal,between_meal,else_time,dosage_limit_term,dosage_limit_unit,unitterm_limit,unitterm_unit,dosage_add_flg,inc_dec_flg,dec_flg,inc_dec_interval,inc_dec_interval_unit,dec_limit,inc_limit,inc_dec_limit_unit,time_depend,judge_term,judge_term_unit,extend_flg,add_term,add_term_unit,interval_warning_flg) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m46_dosage_dosage.csv' with encoding 'UTF8' csv header
\COPY "m46_dosage_drug" (yj_cd,doei_cd,drug_kbn,kikaku_unit,yakka_unit,rikika_rate,rikika_unit,youkaieki_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m46_dosage_drug.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_alrgy_derivatives" (yj_cd,drvalrgy_cd,seibun_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_alrgy_derivatives.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_analogue_cd" (analogue_cd,analogue_name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_analogue_cd.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_drug_class" (class_cd,class_name,class_duplication) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_drug_class.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_drvalrgy_code" (drvalrgy_cd,drvalrgy_name,drvalrgy_grp,rank_no) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_drvalrgy_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_ex_analogue" (seibun_cd,seq_no,analogue_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_ex_analogue.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_ex_ed_ingredients" (yj_cd,seq_no,seibun_cd,seibun_index_cd,sbt,prodrug_check,analogue_check,yokaieki_check,tenkabutu_check) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_ex_ed_ingredients.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_ex_ingrdt_main" (yj_cd,drug_kbn,yoho_cd,haigou_flg,yueki_flg,kanpo_flg,zensinsayo_flg) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_ex_ingrdt_main.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_ex_ing_code" (seibun_cd,seibun_index_cd,seibun_name,yoho_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_ex_ing_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_prodrug_cd" (seibun_cd,seq_no,kasseitai_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_prodrug_cd.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_usage_code" (yoho_cd,yoho) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_usage_code.csv.gz' with encoding 'UTF8' csv header
\COPY "m56_yj_drug_class" (yj_cd,class_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/m56_yj_drug_class.csv.gz' with encoding 'UTF8' csv header
\COPY "payment_method_mst" (hp_id,payment_method_cd,pay_name,pay_sname,sort_no,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/payment_method_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "physical_average" (jissi_year,age_year,age_month,age_day,male_height,male_weight,male_chest,male_head,female_height,female_weight,female_chest,female_head,create_date,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/physical_average.csv.gz' with encoding 'UTF8' csv header
\COPY "pi_inf" (pi_id,w_date,title,r_date,revision,r_type,r_reason,sccjno,therapeuticclassification,preparation_name,highlight,feature,relatedmatter,commonname,genericname) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/pi_inf.csv.gz' with encoding 'UTF8' csv header
\COPY "pi_inf_detail" (pi_id,branch,jpn,seq_no,level,text) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/pi_inf_detail.csv.gz' with encoding 'UTF8' csv header
\COPY "pi_product_inf" (pi_id,branch,jpn,pi_id_full,product_name,unit,maker,vender,marketer,other,yj_cd,hot_cd,sosyo_name,generic_name,generic_eng_name,general_no,ver_date,yakka_reg,yakka_del,is_stoped,stop_date,pi_state,pi_sbt,biko_pi_unit,biko_pi_branch,upd_date_img,upd_date_pi,upd_date_product,upd_date_xml) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/pi_product_inf.csv.gz' with encoding 'UTF8' csv header
\COPY "post_code_mst" (post_cd,pref_kana,city_kana,postal_term_kana,pref_name,city_name,banti,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/post_code_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "priority_haihan_mst" (hp_id,haihan_grp,start_date,user_setting,count,item_cd1,item_cd2,item_cd3,item_cd4,item_cd5,item_cd6,item_cd7,item_cd8,item_cd9,sp_jyoken,end_date,term_cnt,term_sbt,target_kbn,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/priority_haihan_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "receden_cmt_select" (hp_id,item_cd,start_date,comment_cd,end_date,sort_no,is_invalid,create_date,create_id,create_machine,update_date,update_id,update_machine,item_no,eda_no,kbn_no,pt_status,cond_kbn,not_santei_kbn,nyugai_kbn,santei_cnt) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/receden_cmt_select.csv.gz' with encoding 'UTF8' csv header
\COPY "renkei_mst" (hp_id,renkei_id,renkei_name,renkei_sbt,function_type,is_invalid,create_date,update_date,sort_no) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/renkei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "renkei_template_mst" (hp_id,template_id,template_name,param,file,sort_no,create_date,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/renkei_template_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "renkei_timing_mst" (hp_id,renkei_id,event_cd,is_invalid,create_date,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/renkei_timing_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "roudou_mst" (roudou_cd,roudou_name,create_date,update_date) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/roudou_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "rousai_gosei_mst" (gosei_grp,gosei_item_cd,item_cd,sisi_kbn,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/rousai_gosei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "santei_auto_order" (id,hp_id,santei_grp_cd,seq_no,start_date,end_date,add_type,add_target,term_cnt,term_sbt,cnt_type,max_cnt,sp_condition,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/santei_auto_order.csv.gz' with encoding 'UTF8' csv header
\COPY "santei_auto_order_detail" (id,hp_id,santei_grp_cd,seq_no,item_cd,suryo,create_date,create_id,create_machine,update_date,update_id,update_machine,sort_no) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/santei_auto_order_detail.csv.gz' with encoding 'UTF8' csv header
\COPY "santei_cnt_check" (hp_id,santei_grp_cd,seq_no,start_date,end_date,term_cnt,term_sbt,cnt_type,max_cnt,unit_name,err_kbn,target_cd,sp_condition,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/santei_cnt_check.csv.gz' with encoding 'UTF8' csv header
\COPY "santei_grp_detail" (hp_id,santei_grp_cd,item_cd,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/santei_grp_detail.csv.gz' with encoding 'UTF8' csv header
\COPY "santei_grp_mst" (hp_id,santei_grp_cd,santei_grp_name,start_date,end_date,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/santei_grp_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "single_dose_mst" (hp_id,id,unit_name,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/single_dose_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "sokatu_mst" (pref_no,start_ym,report_id,report_eda_no,end_ym,sort_no,report_name,print_type,print_no_type,data_all,data_disk,data_paper,data_kbn,disk_kind,disk_cnt,create_date,create_id,create_machine,update_date,update_id,update_machine,is_sort) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/sokatu_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "sta_conf" (hp_id,menu_id,conf_id,val,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/sta_conf.csv.gz' with encoding 'UTF8' csv header
\COPY "sta_grp" (grp_id,report_id,sort_no,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/sta_grp.csv.gz' with encoding 'UTF8' csv header
\COPY "sta_menu" (menu_id,hp_id,grp_id,report_id,sort_no,menu_name,is_print,is_deleted,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/sta_menu.csv.gz' with encoding 'UTF8' csv header
\COPY "sta_mst" (report_id,report_name,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/sta_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "syouki_kbn_mst" (syouki_kbn,start_ym,end_ym,name) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/syouki_kbn_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "system_conf" (hp_id,grp_cd,grp_eda_no,val,param,biko,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/system_conf.csv.gz' with encoding 'UTF8' csv header
\COPY "system_conf_item" (hp_id,menu_id,seq_no,sort_no,item_name,val,create_date,create_id,create_machine,update_date,update_id,update_machine,param_min,param_max) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/system_conf_item.csv.gz' with encoding 'UTF8' csv header
\COPY "system_conf_menu" (hp_id,menu_id,menu_grp,sort_no,menu_name,grp_cd,grp_eda_no,is_param,param_hint,item_cd,pref_no,is_visible,manager_kbn,create_date,create_id,create_machine,update_date,update_id,update_machine,path_grp_cd,param_mask,val_min,val_max,param_min,param_max,param_type,is_value,param_max_length) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/system_conf_menu.csv.gz' with encoding 'UTF8' csv header
\COPY "system_generation_conf" (hp_id,grp_cd,grp_eda_no,start_date,end_date,val,param,biko,create_date,create_id,create_machine,update_date,update_id,update_machine,id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/system_generation_conf.csv.gz' with encoding 'UTF8' csv header
\COPY "tekiou_byomei_mst" (hp_id,item_cd,byomei_cd,is_invalid,is_invalid_tokusyo,edit_kbn,system_data,create_date,create_id,create_machine,update_date,update_id,update_machine,start_ym,end_ym) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/tekiou_byomei_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ten_mst" (hp_id,item_cd,start_date,end_date,master_sbt,sin_koui_kbn,name,kana_name1,kana_name2,kana_name3,kana_name4,kana_name5,kana_name6,kana_name7,ryosyu_name,rece_name,ten_id,ten,rece_unit_cd,rece_unit_name,odr_unit_name,cnv_unit_name,odr_term_val,cnv_term_val,default_val,is_adopted,kouki_kbn,hokatu_kensa,byomei_kbn,igakukanri,jituday_count,jituday,day_count,drug_kanren_kbn,kizami_id,kizami_min,kizami_max,kizami_val,kizami_ten,kizami_err,max_count,max_count_err,tyu_cd,tyu_seq,tusoku_age,min_age,max_age,time_kasan_kbn,futeki_kbn,futeki_sisetu_kbn,syoti_nyuyoji_kbn,low_weight_kbn,handan_kbn,handan_grp_kbn,teigen_kbn,sekitui_kbn,keibu_kbn,auto_hougou_kbn,gairai_kanri_kbn,tusoku_target_kbn,hokatu_kbn,tyoonpa_naisi_kbn,auto_fungo_kbn,tyoonpa_gyoko_kbn,gazo_kasan,kansatu_kbn,masui_kbn,fukubiku_naisi_kasan,fukubiku_kotunan_kasan,masui_kasan,moniter_kasan,toketu_kasan,ten_kbn_no,shortstay_ope,bui_kbn,sisetucd1,sisetucd2,sisetucd3,sisetucd4,sisetucd5,sisetucd6,sisetucd7,sisetucd8,sisetucd9,sisetucd10,agekasan_min1,agekasan_max1,agekasan_cd1,agekasan_min2,agekasan_max2,agekasan_cd2,agekasan_min3,agekasan_max3,agekasan_cd3,agekasan_min4,agekasan_max4,agekasan_cd4,kensa_cmt,madoku_kbn,sinkei_kbn,seibutu_kbn,zouei_kbn,drug_kbn,zai_kbn,capacity,kohatu_kbn,tokuzai_age_kbn,sanso_kbn,tokuzai_sbt,max_price,max_ten,syukei_saki,cd_kbn,cd_syo,cd_bu,cd_kbnno,cd_edano,cd_kouno,kokuji_kbn,kokuji_syo,kokuji_bu,kokuji_kbn_no,kokuji_eda_no,kokuji_kou_no,kokuji1,kokuji2,kohyo_jun,yj_cd,yakka_cd,syusai_sbt,syohin_kanren,upd_date,del_date,keika_date,rousai_kbn,sisi_kbn,shot_cnt,is_nosearch,is_nodsp_paper_rece,is_nodsp_rece,is_nodsp_ryosyu,is_nodsp_karte,jihi_sbt,kazei_kbn,yoho_kbn,ipn_name_cd,fukuyo_rise,fukuyo_morning,fukuyo_daytime,fukuyo_night,fukuyo_sleep,suryo_roundup_kbn,kouseisin_kbn,santei_item_cd,santeigai_kbn,kensa_item_cd,kensa_item_seq_no,renkei_cd1,renkei_cd2,saiketu_kbn,cmt_kbn,cmt_col1,cmt_col_keta1,cmt_col2,cmt_col_keta2,cmt_col3,cmt_col_keta3,cmt_col4,cmt_col_keta4,select_cmt_id,create_date,create_id,create_machine,update_date,update_id,update_machine,chusya_drug_sbt,kensa_fukusu_santei,age_check,kokuji_betuno,kokuji_kbnno,cmt_sbt,is_nodsp_yakutai,zaikei_point,kensa_label,gairai_kansen,jibi_age_kasan,jibi_syonikokin,is_deleted,yoho_cd,yoho_hosoku_kbn,yoho_hosoku_rec,sentei_ryoyo_kbn,sentei_ryoyo_yakka,baseup1,baseup2,saiseizo_kasan,saiseizo_kiki,tyoki_item_cd,center_cd) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ten_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "ten_mst_mother" (hp_id,item_cd,start_date,end_date,master_sbt,sin_koui_kbn,name,kana_name1,kana_name2,kana_name3,kana_name4,kana_name5,kana_name6,kana_name7,ryosyu_name,rece_name,ten_id,ten,rece_unit_cd,rece_unit_name,odr_unit_name,cnv_unit_name,odr_term_val,cnv_term_val,default_val,is_adopted,kouki_kbn,hokatu_kensa,byomei_kbn,igakukanri,jituday_count,jituday,day_count,drug_kanren_kbn,kizami_id,kizami_min,kizami_max,kizami_val,kizami_ten,kizami_err,max_count,max_count_err,tyu_cd,tyu_seq,tusoku_age,min_age,max_age,age_check,time_kasan_kbn,futeki_kbn,futeki_sisetu_kbn,syoti_nyuyoji_kbn,low_weight_kbn,handan_kbn,handan_grp_kbn,teigen_kbn,sekitui_kbn,keibu_kbn,auto_hougou_kbn,gairai_kanri_kbn,tusoku_target_kbn,hokatu_kbn,tyoonpa_naisi_kbn,auto_fungo_kbn,tyoonpa_gyoko_kbn,gazo_kasan,kansatu_kbn,masui_kbn,fukubiku_naisi_kasan,fukubiku_kotunan_kasan,masui_kasan,moniter_kasan,toketu_kasan,ten_kbn_no,shortstay_ope,bui_kbn,sisetucd1,sisetucd2,sisetucd3,sisetucd4,sisetucd5,sisetucd6,sisetucd7,sisetucd8,sisetucd9,sisetucd10,agekasan_min1,agekasan_max1,agekasan_cd1,agekasan_min2,agekasan_max2,agekasan_cd2,agekasan_min3,agekasan_max3,agekasan_cd3,agekasan_min4,agekasan_max4,agekasan_cd4,kensa_cmt,madoku_kbn,sinkei_kbn,seibutu_kbn,zouei_kbn,drug_kbn,zai_kbn,capacity,kohatu_kbn,tokuzai_age_kbn,sanso_kbn,tokuzai_sbt,max_price,max_ten,syukei_saki,cd_kbn,cd_syo,cd_bu,cd_kbnno,cd_edano,cd_kouno,kokuji_kbn,kokuji_syo,kokuji_bu,kokuji_kbn_no,kokuji_eda_no,kokuji_kou_no,kokuji1,kokuji2,kohyo_jun,yj_cd,yakka_cd,syusai_sbt,syohin_kanren,upd_date,del_date,keika_date,rousai_kbn,sisi_kbn,shot_cnt,is_nosearch,is_nodsp_paper_rece,is_nodsp_rece,is_nodsp_ryosyu,is_nodsp_karte,jihi_sbt,kazei_kbn,yoho_kbn,ipn_name_cd,fukuyo_rise,fukuyo_morning,fukuyo_daytime,fukuyo_night,fukuyo_sleep,suryo_roundup_kbn,kouseisin_kbn,chusya_drug_sbt,kensa_fukusu_santei,santei_item_cd,santeigai_kbn,kensa_item_cd,kensa_item_seq_no,renkei_cd1,renkei_cd2,saiketu_kbn,cmt_kbn,cmt_col1,cmt_col_keta1,cmt_col2,cmt_col_keta2,cmt_col3,cmt_col_keta3,cmt_col4,cmt_col_keta4,select_cmt_id,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/ten_mst_mother.csv.gz' with encoding 'UTF8' csv header
\COPY "tokki_mst" (tokki_cd,tokki_name,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/tokki_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "uketuke_sbt_mst" (hp_id,kbn_id,kbn_name,is_deleted,sort_no,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/uketuke_sbt_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "unit_mst" (unit_cd,unit_name,start_date,end_date,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/unit_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "yakka_syusai_mst" (yakka_cd,item_cd,start_date,end_date,seibun,hinmoku,kbn,syusai_date,keika,biko,jun_senpatu,unit_name,yakka,is_notarget,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/yakka_syusai_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "yoho_mst" (hp_id,yoho_cd,start_date,yoho_kbn_cd,yoho_kbn,yoho_detail_kbn_cd,yoho_detail_kbn,timing_kbn_cd,timing_kbn,yoho_name,reference_no,end_date,yoho_cd_kbn,tonyo_joken,toyo_timing,toyo_time,toyo_interval,bui,yoho_kana_name,chozai_yoho_cd,create_date,create_id,create_machine,update_date,update_id,update_machine) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/yoho_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "yoho_replace_mst" (old_word,replace_word,seq_no) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/yoho_replace_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "yoho_word_mst" (word) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/yoho_word_mst.csv.gz' with encoding 'UTF8' csv header
\COPY "raiin_status_mst" (hp_id,status_kbn,status_name,sort_no,is_deleted,create_date,create_id,update_date,update_id) from PROGRAM 'gunzip -c db/dev2/smartkarte_master_data/raiin_status_mst.csv.gz' with encoding 'UTF8' csv header

-- --update sequence
-- ALTER SEQUENCE cmt_kbn_mst1_id_seq RESTART WITH 20426;

.PHONY: help
help: ## 各種コマンドの役割を表示する
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.PHONY: init
init: ## 開発で使用する各種ツールをインストール（初回のみ実施）
	go install github.com/automation-co/husky@latest
	go install github.com/mitranim/gow@latest
	husky install
	make install
	go mod vendor

.PHONY: env
env: ## 環境変数の設定ファイルを作成（初回のみ実施）
	rm -f .env
	cp .env.example .env

.PHONY: server
server: ## サーバーを起動する
	ENV_APP=development gow run cmd/server/main.go -mod=vendor

.PHONY: install
install: ## 開発で使用する各種ツールをインストール（初回のみ実施）
	go install github.com/automation-co/husky@latest
	go run github.com/automation-co/husky@latest install
	go install github.com/mitranim/gow@latest
	go install github.com/google/wire/cmd/wire@v0.6.0
	go install go.uber.org/mock/mockgen@v0.2.0
	go install golang.org/x/tools/cmd/goimports@latest
	go install github.com/mgechev/revive@latest # golang用軽量linter
	go install github.com/wadey/gocovmerge@latest # カバレッジプロファイルのマージツール
	pip install yapf # python用フォーマッター
	pip install pylint # python用linter
	# GOPROXY を direct に設定
	# GOPRIVATE にプライベートリポジトリを設定
	# パッケージ取得なのでgo getする(他はバイナリインストールなのでgo install)
	export GOPROXY=direct; export GOPRIVATE=github.com/bizleap-healthcare/*; go get github.com/bizleap-healthcare/denkaru-codes@latest; go mod tidy; go mod vendor

.PHONY: gen_gorm
gen_gorm: ## Generate model using GORM.
	ENV_APP=development go run cmd/gengorm/main.go -mod=vendor

.PHONY: gen_graphql
gen_graphql: ## gqlgenで各種ファイルを生成する
	ENV_APP=development go run ./cmd/gqlgen/main.go -mod=vendor

.PHONY: gen_wire
gen_wire: ## DI実行
	wire ./pkg/wire

.PHONY: test
test: ## pkg配下の全てのテストを実行する
	go clean -testcache
	# 通常のカバレッジ測定
	go test -coverprofile=/tmp/coverage.tmp.out -coverpkg=denkaru-server/pkg/... denkaru-server/pkg/...
	# go1.22からテストファイルのないパッケージもカバレッジ対象になったので自動生成のコードを除外する.デモ実装も除外
	cat /tmp/coverage.tmp.out | egrep -v "_gen.go|.gen.go|generate.go|generated.go|test|mock|freee" > /tmp/coverage.out
	## カバレッジが低下したら失敗する
	@COVERAGE_THRESHOLD=75.0; \
	go tool cover -func=/tmp/coverage.out | \
	grep total: | \
	awk -v threshold=$$COVERAGE_THRESHOLD '{if($$3 < threshold) {print "Error! カバレッジが" threshold "%未満です。現在のカバレッジは" $$3 "です"; exit 1} else {print "OK! 現在のカバレッジは" $$3 "です。"}}'

.PHONY: dtest
dtest: ## 差分のあるパッケージのテストを実行する
	go clean -testcache
	@dirs=$$($(go_diff)); \
	if [ -n "$$dirs" ]; then \
		for dir in $$dirs; do \
			go test -v ./$$dir/ -mod=vendor || exit 1; \
		done; \
	else \
		echo "テスト対象のGoファイルがありません。"; \
	fi

.PHONY: kill_embedded_postgres
kill_embedded_postgres: ## postgresqlのテストで使用しているembedded-postgres-goをkillする
	@echo "Killing the embedded postgres process..."
	@ps -eaf | grep "/.embedded-postgres-go/extracted/data" | grep -v grep | awk '{print $$2}' | xargs kill -9
	@ps -eaf | grep "postgres-test-" | grep -v grep | awk '{print $$2}' | xargs kill -9
	@rm -rf /tmp/.s.PGSQL.*
	@ipcs -m | awk -v user=$$(whoami) '$$3 == user {print $$2}' | xargs -n 1 ipcrm -m

.PHONY: notification
notification: ## start notification
	ENV_APP=development go run cmd/notification/main.go -mod=vendor

.PHONY: lint
lint: ## golang lint
	sh ./deploy/checker/hash_checker.sh ./pkg/adapter/graphql/schema/schema.gql cafd813180ed00848e9a4698eb82bb25
	@if [ `uname` = "Darwin" ]; then \
		which golangci-lint > /dev/null || brew install golangci-lint; \
	else \
		which golangci-lint > /dev/null || (curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin); \
	fi
	golangci-lint cache clean
	golangci-lint run -e EXC0015 -c .golangci.yml --out-format=line-number --timeout=5m ./... || (echo "Linting errors detected, stopping"; exit 1)
	go run ./cmd/linter/sql_checker/main.go ./pkg/...
	go run cmd/linter/service_return_error_checker/main.go ./pkg/...


.PHONY: dlint
dlint: ## diff golang lint
	sh ./deploy/checker/hash_checker.sh ./pkg/adapter/graphql/schema/schema.gql cafd813180ed00848e9a4698eb82bb25
	@if [ `uname` = "Darwin" ]; then \
		which golangci-lint > /dev/null || brew install golangci-lint; \
	else \
		which golangci-lint > /dev/null || (curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin); \
	fi
	golangci-lint cache clean
	@dirs=$$($(go_diff)); \
	if [ -n "$$dirs" ]; then \
		for dir in $$dirs; do \
			golangci-lint run -e EXC0015 -c .golangci.yml --out-format=line-number --timeout=5m ./$$dir/ || (echo "Linting errors detected, stopping"; exit 1); \
		done; \
	else \
		echo "lint対象のGoファイルがありません。"; \
	fi

.PHONY: pre_push
pre_push: ## プッシュ前に問題がないか確認する。テストが失敗したり、差分が出た場合、見直しが必要。
	make go_mod
	make gen_gorm
	make gen_graphql
	make gen_wire
	make mockgen
	make dtest
	make dlint
	git status

.PHONY: go_mod
go_mod:
	go mod tidy
	go mod vendor

.PHONY: build_cmd_lambda
build_cmd_lambda: ## Build cmd/lambda
	# mail/send
	cd cmd/lambda/mail/send && rm -f bootstrap* && GOOS=linux GOARCH=amd64 go build -o bootstrap main.go && zip bootstrap.zip bootstrap && \
	aws s3 cp bootstrap.zip s3://tf-denkaru-dev-server-codepipeline-artifacts/lambdaArtifacts/SendMail_dev/ ; \
	aws s3 cp bootstrap.zip s3://tf-denkaru-stg-server-codepipeline-artifacts/lambdaArtifacts/SendMail_stg/
	# mail/remind
	cd cmd/lambda/mail/remind && rm -f bootstrap* && GOOS=linux GOARCH=amd64 go build -o bootstrap main.go && zip bootstrap.zip bootstrap && \
	aws s3 cp bootstrap.zip s3://tf-denkaru-dev-server-codepipeline-artifacts/lambdaArtifacts/RequestReminderMail_dev/ ; \
	aws s3 cp bootstrap.zip s3://tf-denkaru-stg-server-codepipeline-artifacts/lambdaArtifacts/RequestReminderMail_stg/
	# prescription/new
	cd cmd/lambda/prescription/new && rm -f bootstrap* && GOOS=linux GOARCH=amd64 go build -o bootstrap main.go && zip bootstrap.zip bootstrap && \
	aws s3 cp bootstrap.zip s3://tf-denkaru-dev-server-codepipeline-artifacts/lambdaArtifacts/PrescriptionNew_dev/ ; \
	aws s3 cp bootstrap.zip s3://tf-denkaru-stg-server-codepipeline-artifacts/lambdaArtifacts/PrescriptionNew_stg/
	# pharmacy/remind
	cd cmd/lambda/pharmacy/remind && rm -f bootstrap* && GOOS=linux GOARCH=amd64 go build -o bootstrap main.go && zip bootstrap.zip bootstrap && \
	aws s3 cp bootstrap.zip s3://tf-denkaru-dev-server-codepipeline-artifacts/lambdaArtifacts/PharmacyRemind_dev/ ; \
	aws s3 cp bootstrap.zip s3://tf-denkaru-stg-server-codepipeline-artifacts/lambdaArtifacts/PharmacyRemind_stg/
	# payment/retry
	cd cmd/lambda/payment/retry && rm -f bootstrap* && GOOS=linux GOARCH=amd64 go build -o bootstrap main.go && zip bootstrap.zip bootstrap && \
	aws s3 cp bootstrap.zip s3://tf-denkaru-dev-server-codepipeline-artifacts/lambdaArtifacts/RetryPayment_dev/ ; \
	aws s3 cp bootstrap.zip s3://tf-denkaru-stg-server-codepipeline-artifacts/lambdaArtifacts/RetryPayment_stg/

.PHONY: start_es
start_es: ## Start es on docker
	docker compose -f ./docker/open_search/docker-compose.yml --env-file .env up -d

.PHONY: stop_es
stop_es: ## Stop es on docker
	docker compose -f ./docker/open_search/docker-compose.yml --env-file .env down

.PHONY: start_sqs
start_sqs: ## Start sqs on docker
	docker compose -f ./docker/sqs/docker-compose.yml up -d

.PHONY: stop_sqs
stop_sqs: ## Stop sqs on docker
	docker compose -f ./docker/sqs/docker-compose.yml down

.PHONY: setup_messaging_env
setup_messaging_env:
	make start_es
	make start_sqs

.PHONY: start_mail_remind
start_mail_remind: ## Put reminder mail into SQS
	ENV_APP=development go run cmd/lambda/mail/remind/main.go cmd/lambda/mail/remind/constants.go -mod=vendor

.PHONY: start_mail_send
start_mail_send: ## Send mail from SQS
	ENV_APP=development go run cmd/lambda/mail/send/main.go cmd/lambda/mail/send/constants.go -mod=vendor

.PHONY: start_sms_send
start_sms_send: ## Send mail from SQS
	ENV_APP=development go run cmd/lambda/sms/send/main.go cmd/lambda/sms/send/constants.go -mod=vendor

.PHONY: start_send_line_message
start_send_line_message: ## Send LINE message from SQS
	ENV_APP=development go run cmd/lambda/line/main.go cmd/lambda/line/constants.go -mod=vendor

.PHONY: start_batch_import
start_batch_import: ## Import external data
	ENV_APP=development go run cmd/scheduled_batch/main.go -type=ImportData

.PHONY: start_fix_import_data
start_fix_import_data: ## Fix import data
	ENV_APP=development go run cmd/scheduled_batch/main.go -type=FixImportData

.PHONY: start_prescription_new
start_prescription_new: ## start_prescription_new
	ENV_APP=development go run cmd/lambda/prescription/new/main.go cmd/lambda/prescription/new/constants.go -mod=vendor

.PHONY: start_pharmacy_remind
start_pharmacy_remind: ## start_pharmacy_remind
	ENV_APP=development go run cmd/lambda/pharmacy/remind/main.go cmd/lambda/pharmacy/remind/constants.go -mod=vendor

.PHONY: start_payment_retry
start_payment_retry: ## start_payment_retry
	ENV_APP=development go run cmd/lambda/payment/retry/main.go cmd/lambda/payment/retry/constants.go -mod=vendor

.PHONY: teardown_messaging_env
teardown_messaging_env:
	make stop_es
	make stop_sqs

.PHONY: mockgen
mockgen: ## Generate models for testing using mockgen.
	make repository_mockgen
	make service_mockgen
	make client_mockgen
	make validator_mockgen

.PHONY: repository_mockgen
repository_mockgen:
	cd pkg && git status --porcelain ./repository | grep -E "^ ?[AM] ?" | grep ".go" | grep -v "_test.go" | sed 's/^ //g' | cut -d" " -f2- | sed "s/pkg\///" | xargs -I {} -P 4 sh -c 'mockgen -source={} -destination="test_mock/{}"' _

.PHONY: service_mockgen
service_mockgen:
	cd pkg && git status --porcelain ./service | grep -E "^ ?[AM] ?" | grep ".go" | grep -v "_test.go" | sed 's/^ //g' | cut -d" " -f2- | sed "s/pkg\///" | xargs -I {} -P 4 sh -c 'mockgen -source={} -destination="test_mock/{}"' _

.PHONY: client_mockgen
client_mockgen:
	cd pkg && git status --porcelain ./infra/client | grep -E "^ ?[AM] ?" | grep ".go" | grep -v "_test.go" | sed 's/^ //g' | cut -d" " -f2- | sed -e "s/pkg\///" | xargs -I {} -P 4 sh -c 'mockgen -source={} -destination="test_mock/{}"' _

.PHONY: validator_mockgen
validator_mockgen:
	cd pkg && git status --porcelain ./service/validator | grep -E "^ ?[AM] ?" | grep ".go" | grep -v "_test.go" | sed 's/^ //g' | cut -d" " -f2- | sed "s/pkg\///" | xargs -I {} -P 4 sh -c 'mockgen -source={} -destination="test_mock/{}"' _

.PHONY: start
start: ## DB設定を反映する
	./db_start.sh

.PHONY: reset_dev1_x
reset_dev1_x: ## DBテーブルを初期化する
	./db_reset_dev1_x.sh
.PHONY: reset_dev2
reset_dev2: ## DBテーブルを初期化する
	./db_reset_dev2.sh

.PHONY: dump_dev1_x
dump_dev1_x: ## DBテーブルをダンプする、dev1_x環境用
	./db_dump_dev1_x.sh

.PHONY: dump_dev2
dump_dev2: ## DBテーブルをダンプする、dev2環境用
	./db_dump_dev2.sh

.PHONY: dump_prod
dump_prod: ## DBテーブルをダンプする
	./db_dump_prod.sh

.PHONY: down
down: ## コンテナを停止する
	docker compose down

.PHONY: down-all
down-all: ## コンテナ、イメージ、ボリュームを削除する
	docker compose down --rmi all --volumes --remove-orphans

.PHONY: start_paddleocr
start_paddleocr: ## Start paddleocr container
	# 初回起動は10分くらいかかる
	docker compose -f ./docker/ocr/docker-compose.yml up -d

.PHONY: stop_paddleocr
stop_paddleocr: ## Stop paddleocr container
	docker compose -f ./docker/ocr/docker-compose.yml down

.PHONY: etl_to_opensearch
etl_to_opensearch:
	make start_sqs
	make start_es
	ENV_APP=development go run cmd/lambda/open_search/main.go

.PHONY: start_es_hospital
start_es_hospital:
	ENV_APP=development go run cmd/lambda/open_search/portal_hospital/main.go

# 変更差分のあるディレクトリを取得
define go_diff
git diff --name-only --diff-filter=ACMRT | grep .go$ | xargs -I{} dirname {} | sort | uniq
endef

.PHONY: coverage ## テストのカバレッジをhtml形式で確認する
coverage:
	go test -coverprofile=/tmp/coverage.out ./...
	go tool cover -html=/tmp/coverage.out -o coverage.html

.PHONY: import_hasura_metadata
import_hasura_metadata: ## hasuraに設定ファイルをimportする
	./hasura/import_metadata.sh

.PHONY: export_hasura_metadata
export_hasura_metadata: ## hasuraの設定ファイルをexportする
	./hasura/export_metadata.sh

.PHONY: start_nginx
start_nginx: ## Start es on docker
	docker compose -f ./docker/nginx/docker-compose.yml --env-file .env up -d

.PHONY: stop_nginx
stop_nginx: ## Stop es on docker
	docker compose -f ./docker/nginx/docker-compose.yml --env-file .env down

.PHONY: start_import_postcode
start_import_postcode: ## Import postcode
	ENV_APP=development go run cmd/scheduled_batch/main.go -type=ImportPostCode

.PHONY: start_create_clinic_master_data
start_create_clinic_master_data: ## Create master data per clinic
	ENV_APP=development go run cmd/scheduled_batch/main.go -type=CreateClinicMasterData

### PDF作成のライブラリ
### https://gotenberg.dev/
### smartkarte-reportとの使い分け理由
### Web上の主訴・所見入力はHTML形式のリッチテキストウェブであり、データベースにはHTML形式のテキストとして保存されています。
### カルテ２号紙のフォーマットは、主訴・所見（赤枠部分）に表示される形式と同じであるため、データを表示するためにHTMLを使用して、その後、Gotenbergを使用してPDFに変換しています。
.PHONY: gotenberg_start
gotenberg_start: ## Stop gotenberg server
	docker run -d --name gotenberg -p 3001:3000 gotenberg/gotenberg:8

.PHONY: gotenberg_stop
gotenberg_stop: ## Stop gotenberg server
	docker stop gotenberg
	docker rm gotenberg

### PDF作成用のライブラリ(カルテ２号紙以外)
### https://github.com/bizleap-healthcare/smartkarte-report
### 事前にDockerimageのビルドが必要(docs/PH2_DEVELOPMENT.md - smartkarte-reportのDockerimage作成 を参照)
.PHONY: reporting_start
reporting_start: ## Start reporting server
	docker run -dit --name smartkarte-report -p 8070:8080 smartkarte-report:latest

.PHONY: reporting_stop
reporting_stop: ## Stop reporting server
	docker stop smartkarte-report
	docker rm smartkarte-report

.PHONY: flushall_redis
flushall_redis: ## Flush all Redis
	echo -e "FLUSHALL\r\nQUIT\r\n" | nc localhost 6379
.PHONY: clinic_info_check_ivr
clinic_info_check_ivr: ## Import postcode
	python3 cmd/lambda/signup/clinic_info_check_ivr/lambda_function.py

.PHONY: doctor_licence_check
doctor_licence_check: ## Import postcode
	python3 cmd/lambda/signup/doctor_licence_check/lambda_function.py

package postgresql_test

import (
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util"
	"fmt"
	"os"
	"reflect"
	"runtime/debug"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func setUpTablesForCommonPostCodeMst(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.CreateTable(&entity.PostCodeMst{})
	if err != nil {
		fmt.Printf("Failed CreateTable CommonPostCodeMst. %v\n", err)
	}

	now := time.Now()
	// ======== テスト用データの投入 ========
	param := []*entity.PostCodeMst{
		{
			PostCd:         "1000001",
			PrefKana:       "ﾄｳｷｮｳﾄ",
			CityKana:       "ﾁﾖﾀﾞｸ",
			PostalTermKana: "ﾁﾖﾀﾞ",
			PrefName:       "東京都",
			CityName:       "千代田区",
			Banti:          "千代田",
			IsDeleted:      constant.StatusFalse,
			CreateDate:     now,
			CreateID:       constant.SystemCreateID,
			UpdateDate:     now,
			UpdateID:       constant.SystemUpdateID,
			ID:             15313,
		},
		{
			PostCd:         "1000002",
			PrefKana:       "ﾄｳｷｮｳﾄ",
			CityKana:       "ﾁﾖﾀﾞｸ",
			PostalTermKana: "ｺｳｷｮｶﾞｲｴﾝ",
			PrefName:       "東京都",
			CityName:       "千代田区",
			Banti:          "皇居外苑",
			IsDeleted:      constant.StatusFalse,
			CreateDate:     now,
			CreateID:       constant.SystemCreateID,
			UpdateDate:     now,
			UpdateID:       constant.SystemUpdateID,
			ID:             15314,
		},
	}
	db.Create(&param)
	// ==================================
}

func tearDownTablesForCommonPostCodeMst(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.DropTable(&entity.PostCodeMst{})
	if err != nil {
		fmt.Printf("Failed DropTable CommonPostCodeMst. %v\n", err)
	}
}

func Test_commonPostCodeMstRepository_FindByPostCode(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForCommonPostCodeMst(db)
	defer tearDownTablesForCommonPostCodeMst(db)

	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForCommonPostCodeMst(db)
	}()

	testee := postgresql.NewCommonPostCodeMstRepository(db)

	treatments, err := testee.FindByPostCode("1000001")
	assert.NoError(t, err)
	assert.Equal(t, 1, len(treatments))

	treatments, err = testee.FindByPostCode("1000002")
	assert.NoError(t, err)
	assert.Equal(t, 1, len(treatments))
	assert.Equal(t, "1000002", treatments[0].PostCd)
	assert.Equal(t, "ﾄｳｷｮｳﾄ", treatments[0].PrefKana)
	assert.Equal(t, "ﾁﾖﾀﾞｸ", treatments[0].CityKana)
	assert.Equal(t, "ｺｳｷｮｶﾞｲｴﾝ", treatments[0].PostalTermKana)
	assert.Equal(t, "東京都", treatments[0].PrefName)
	assert.Equal(t, "千代田区", treatments[0].CityName)
	assert.Equal(t, "皇居外苑", treatments[0].Banti)
}

func Test_commonPostCodeMstRepository_CreateByCopy(t *testing.T) {
	db, cleanup := getTestDB(t)
	var port string
	if pgDialector, ok := db.Dialector.(*postgres.Dialector); ok {
		dsnField := reflect.ValueOf(pgDialector).Elem().FieldByName("DSN")
		if dsnField.IsValid() {
			dsn := dsnField.String()
			port = strings.Split(strings.Split(dsn, "port=")[1], " ")[0]
		}
	} else {
		panic("Failed to get db_test's port")
	}

	// pgxを使用しているため、DB接続用環境変数をセットする
	err := os.Setenv("DB_USER", testDBUser)
	if err != nil {
		panic(err)
	}
	err = os.Setenv("DB_PASSWORD", testDBPassword)
	if err != nil {
		panic(err)
	}
	err = os.Setenv("DB_HOST", "localhost")
	if err != nil {
		panic(err)
	}
	err = os.Setenv("DB_PORT", port)
	if err != nil {
		panic(err)
	}
	err = os.Setenv("DB_NAME", testDBDatabaseName)
	if err != nil {
		panic(err)
	}

	defer cleanup()

	setUpTablesForCommonPostCodeMst(db)
	defer tearDownTablesForCommonPostCodeMst(db)

	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForCommonPostCodeMst(db)
	}()

	ctx := context.Background()
	commonPostCodeMst := entity.PostCodeMst{
		PostCd:         "0010000",
		PrefKana:       "ﾎｯｶｲﾄﾞｳ",
		CityKana:       "ｻｯﾎﾟﾛｼｷﾀｸ",
		PostalTermKana: "test",
		PrefName:       "北海道",
		CityName:       "札幌市北区",
		Banti:          "banti_data",
	}
	columns, data := util.GetColumnsAndDataFromStruct(commonPostCodeMst)
	dataString := strings.Join(data, "\t")

	testee := postgresql.NewCommonPostCodeMstRepository(db)
	err = testee.CreateByCopy(ctx, []string{dataString}, columns, 1000)
	assert.NoError(t, err)

	commonPostCode, err := testee.FindByPostCode("0010000")
	assert.NoError(t, err)
	assert.Equal(t, 1, len(commonPostCode))
	assert.Equal(t, "ﾎｯｶｲﾄﾞｳ", commonPostCode[0].PrefKana)
}

package postgresql_test

import (
	"context"
	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/test_mock"
	"fmt"
	"runtime/debug"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func setUpTablesForSurvey(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.CreateTable(&entity.Survey{})
	if err != nil {
		fmt.Printf("Failed CreateTable Survey. %v\n", err)
	}

	// ======== テスト用データの投入 ========
	param := []*entity.Survey{
		{
			SurveyID:   1,
			FQuesJSON:  "{\"name\":\"test1\", \"hospital_id\":\"1\"}",
			Name:       "keyword:aaaaa_test1",
			Secret:     "secret1",
			HospitalID: 1,
		},
		{
			SurveyID:   2,
			FQuesJSON:  "{\"name\":\"test2\", \"hospital_id\":\"1\"}",
			Name:       "keyword:bbbbb_test2",
			Secret:     "secret2",
			HospitalID: 1,
		},
		{
			SurveyID:   3,
			FQuesJSON:  "{\"name\":\"test3\", \"hospital_id\":\"2\"}",
			Name:       "keyword:aaaaa_test3",
			Secret:     "secret3",
			HospitalID: 2,
		},
	}
	db.Create(&param)
	// ==================================
}

func tearDownTablesForSurvey(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.DropTable(&entity.Survey{})
	if err != nil {
		fmt.Printf("Failed DropTable Survey. %v\n", err)
	}
}

func Test_surveyRepository_FindSurveys(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	testee := postgresql.NewSurveyRepository(db)

	ctx := context.Background()
	limit := 1
	cursor := int64(1)

	res, err := testee.FindSurveys(ctx, 1, "aaaaa", nil, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, 1, res[0].SurveyID)
	assert.Equal(t, "keyword:aaaaa_test1", res[0].Name)

	res, err = testee.FindSurveys(ctx, 1, "", nil, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(res))
	assert.Equal(t, 1, res[0].SurveyID)
	assert.Equal(t, "keyword:aaaaa_test1", res[0].Name)
	assert.NotEqual(t, res[0].Secret, res[1].Secret, "Secretは一意であるべき")

	res, err = testee.FindSurveys(ctx, 1, "", nil, &cursor, nil)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, 2, res[0].SurveyID)
	assert.Equal(t, "keyword:bbbbb_test2", res[0].Name)

	res, err = testee.FindSurveys(ctx, 1, "", &limit, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, 1, res[0].SurveyID)
	assert.Equal(t, "keyword:aaaaa_test1", res[0].Name)

	res, err = testee.FindSurveys(ctx, 1, "", &limit, &cursor, nil)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, 2, res[0].SurveyID)
	assert.Equal(t, "keyword:bbbbb_test2", res[0].Name)

}

func Test_surveyRepository_FindSurveyByID(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	testee := postgresql.NewSurveyRepository(db)

	// 正常系：削除されていないレコードの取得（引数なし）
	res, err := testee.FindSurveyByID(3)
	assert.NoError(t, err)
	assert.Equal(t, 2, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test3", res.Name)

	// 正常系：削除されていないレコードの取得（includeDeleted = false）
	res, err = testee.FindSurveyByID(3, false)
	assert.NoError(t, err)
	assert.Equal(t, 2, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test3", res.Name)

	// 正常系：削除されていないレコードの取得（includeDeleted = true）
	res, err = testee.FindSurveyByID(3, true)
	assert.NoError(t, err)
	assert.Equal(t, 2, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test3", res.Name)
}

func Test_surveyRepository_CreateSurvey(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	ctx := test_mock.GetTestContextWithDB(db)
	testee := postgresql.NewSurveyRepository(db)

	// 作成前
	res, err := testee.FindSurveyByID(4)
	assert.Equal(t, err, gorm.ErrRecordNotFound)
	assert.Equal(t, 0, res.SurveyID)

	// 作成
	res2, err := testee.CreateSurvey(ctx, &entity.Survey{
		SurveyID:   4,
		FQuesJSON:  "{\"name\":\"test4\", \"hospital_id\":\"1\"}",
		Name:       "keyword:ccccc_test4",
		HospitalID: 1,
	})
	assert.NoError(t, err)
	assert.Equal(t, 4, res2.SurveyID)
	assert.Equal(t, 1, res2.HospitalID)
	assert.Equal(t, "keyword:ccccc_test4", res2.Name)

	// 作成後
	res, err = testee.FindSurveyByID(4)
	assert.NoError(t, err)
	assert.Equal(t, 4, res.SurveyID)
	assert.Equal(t, 1, res.HospitalID)
	assert.Equal(t, "keyword:ccccc_test4", res.Name)
}

func Test_surveyRepository_UpdateSurvey(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	ctx := test_mock.GetTestContextWithDB(db)
	testee := postgresql.NewSurveyRepository(db)

	// 更新前
	res, err := testee.FindSurveyByID(1)
	assert.NoError(t, err)
	assert.Equal(t, 1, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test1", res.Name)

	// 更新
	res2, err := testee.UpdateSurvey(ctx, &entity.Survey{
		SurveyID:   1,
		FQuesJSON:  "{\"name\":\"test1\", \"hospital_id\":\"1\"}",
		Name:       "keyword:aaaaa_test1_updated",
		HospitalID: 1,
	})
	assert.NoError(t, err)
	assert.Equal(t, 1, res2.SurveyID)
	assert.Equal(t, 1, res2.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test1_updated", res2.Name)

	// 更新後
	res, err = testee.FindSurveyByID(1)
	assert.NoError(t, err)
	assert.Equal(t, 1, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test1_updated", res.Name)
}

func Test_surveyRepository_RemoveSurvey(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	testee := postgresql.NewSurveyRepository(db)

	// 削除前
	res, err := testee.FindSurveyByID(2)
	assert.NoError(t, err)
	assert.Equal(t, 1, res.HospitalID)
	assert.Equal(t, "keyword:bbbbb_test2", res.Name)

	// 削除
	err = testee.RemoveSurvey(2)
	assert.NoError(t, err)

	// 削除後
	res, err = testee.FindSurveyByID(2)
	assert.Equal(t, err, gorm.ErrRecordNotFound) // FIXME 想定しない動きだと思うがテストコード作成時においては修正しない、後々修正するときにここも修正する必要がある
	assert.Equal(t, 0, res.SurveyID)
	assert.Equal(t, "", res.Name)
}

func Test_surveyRepository_FindSurveyBySurveyIdAndHospitalId(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	testee := postgresql.NewSurveyRepository(db)

	res, err := testee.FindSurveyBySurveyIdAndHospitalId(1, 1)
	assert.NoError(t, err)
	assert.Equal(t, 1, res.HospitalID)
	assert.Equal(t, "keyword:aaaaa_test1", res.Name)

	res, err = testee.FindSurveyBySurveyIdAndHospitalId(1, 11)
	assert.Equal(t, err, gorm.ErrRecordNotFound)
	assert.Nil(t, res)
}

func Test_surveyRepository_FindSurveyBySurveyNameAndHospitalId(t *testing.T) {
	t.Parallel()

	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForSurvey(db)
	defer func() {
		if r := recover(); r != nil {
			t.Logf("panic:%v, stack: %s", r, debug.Stack())
			t.Fail()
		}
		tearDownTablesForSurvey(db)
	}()

	testee := postgresql.NewSurveyRepository(db)

	res, err := testee.FindSurveyBySurveyNameAndHospitalId("keyword:bbbbb_test2", 1)
	assert.NoError(t, err)
	assert.Equal(t, 2, res.SurveyID)
	assert.Equal(t, "keyword:bbbbb_test2", res.Name)

	res, err = testee.FindSurveyBySurveyNameAndHospitalId("not found name", 1)
	assert.Equal(t, err, gorm.ErrRecordNotFound)
	assert.Nil(t, res)
}

package postgresql_test

import (
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/test_mock"
	"denkaru-server/pkg/util"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

func setUpTablesForPharmacyReserve(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.CreateTable(&entity.PharmacyReserve{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyReserve. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PharmacyDesiredDate{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyDesiredDate. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PharmacyReserveDetail{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyReserveDetail. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PharmacyDeliveryHistory{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyDeliveryHistory. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PharmacyDeliveryAddress{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyDeliveryAddress. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PtInf{})
	if err != nil {
		fmt.Printf("Failed CreateTable PtInf. %v\n", err)
	}

	err = migrator.CreateTable(&entity.HpInf{})
	if err != nil {
		fmt.Printf("Failed CreateTable HpInf. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PortalCustomer{})
	if err != nil {
		fmt.Printf("Failed CreateTable PortalCustomer. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PortalCustomerLogin{})
	if err != nil {
		fmt.Printf("Failed CreateTable PortalCustomerLogin. %v\n", err)
	}

	err = migrator.CreateTable(&entity.Reserve{})
	if err != nil {
		fmt.Printf("Failed CreateTable Reserve. %v\n", err)
	}

	err = migrator.CreateTable(&entity.ReserveDetail{})
	if err != nil {
		fmt.Printf("Failed CreateTable ReserveDetail. %v\n", err)
	}

	err = migrator.CreateTable(&entity.ExamTimeSlot{})
	if err != nil {
		fmt.Printf("Failed CreateTable ExamTimeSlot. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PortalHospital{})
	if err != nil {
		fmt.Printf("Failed CreateTable PortalHospital. %v\n", err)
	}

	err = migrator.CreateTable(&entity.Meeting{})
	if err != nil {
		fmt.Printf("Failed CreateTable Meeting. %v\n", err)
	}

	err = migrator.CreateTable(&entity.UserMst{})
	if err != nil {
		fmt.Printf("Failed CreateTable UserMst. %v\n", err)
	}

	err = migrator.CreateTable(&entity.PharmacyPatientFile{})
	if err != nil {
		fmt.Printf("Failed CreateTable PharmacyPatientFile. %v\n", err)
	}
}

func tearDownTablesForPharmacyReserve(db *gorm.DB) {
	migrator := db.Migrator()

	err := migrator.DropTable(&entity.PharmacyReserve{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyReserve. %v\n", err)
	}

	err = migrator.DropTable(&entity.PharmacyDesiredDate{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyDesiredDate. %v\n", err)
	}

	err = migrator.DropTable(&entity.PharmacyReserveDetail{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyReserveDetail. %v\n", err)
	}

	err = migrator.DropTable(&entity.PharmacyDeliveryHistory{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyDeliveryHistory. %v\n", err)
	}

	err = migrator.DropTable(&entity.PharmacyDeliveryAddress{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyDeliveryAddress. %v\n", err)
	}

	err = migrator.DropTable(&entity.PtInf{})
	if err != nil {
		fmt.Printf("Failed DropTable PtInf. %v\n", err)
	}

	err = migrator.DropTable(&entity.HpInf{})
	if err != nil {
		fmt.Printf("Failed DropTable HpInf. %v\n", err)
	}

	err = migrator.DropTable(&entity.PortalCustomer{})
	if err != nil {
		fmt.Printf("Failed DropTable PortalCustomer. %v\n", err)
	}

	err = migrator.DropTable(&entity.PortalCustomerLogin{})
	if err != nil {
		fmt.Printf("Failed DropTable PortalCustomerLogin. %v\n", err)
	}

	err = migrator.DropTable(&entity.Reserve{})
	if err != nil {
		fmt.Printf("Failed DropTable Reserve. %v\n", err)
	}

	err = migrator.DropTable(&entity.ReserveDetail{})
	if err != nil {
		fmt.Printf("Failed DropTable ReserveDetail. %v\n", err)
	}

	err = migrator.DropTable(&entity.ExamTimeSlot{})
	if err != nil {
		fmt.Printf("Failed DropTable ExamTimeSlot. %v\n", err)
	}

	err = migrator.DropTable(&entity.PortalHospital{})
	if err != nil {
		fmt.Printf("Failed DropTable PortalHospital. %v\n", err)
	}

	err = migrator.DropTable(&entity.Meeting{})
	if err != nil {
		fmt.Printf("Failed DropTable Meeting. %v\n", err)
	}

	err = migrator.DropTable(&entity.UserMst{})
	if err != nil {
		fmt.Printf("Failed DropTable UserMst. %v\n", err)
	}

	err = migrator.DropTable(&entity.PharmacyPatientFile{})
	if err != nil {
		fmt.Printf("Failed DropTable PharmacyPatientFile. %v\n", err)
	}
}

func Test_PharmacyReserve_GetByID(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserveDetails := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			DesiredDateStatus: 1,
		},
	}
	db.Create(&pharmacyReserveDetails)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		paymentStatus     int
		expectedError     error
	}{
		{
			name:              "正常系 取得あり",
			pharmacyReserveID: 1,
		},
		{
			name:              "正常系 取得なし",
			pharmacyReserveID: 2,
			expectedError:     gorm.ErrRecordNotFound,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			result, err := testee.GetByID(testCase.pharmacyReserveID)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}
			assert.Equal(t, result.PharmacyReserveID, testCase.pharmacyReserveID)
		})
	}
}

func Test_FindPharmacyReserveIDsByConditions(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// テスト用日付
	year, month, day := time.Now().Date()
	birthday := year*10000 + int(month)*100 + day

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
		{
			PharmacyReserveID: 2,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
		{
			PharmacyReserveID: 3,
			PatientID:         2,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(2),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(2),
		},
		{
			PharmacyReserveID: 4,
			PatientID:         3,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 21, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(3),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
	}
	db.Create(&pharmacyReserves)

	pharmacyReserveDetails := []entity.PharmacyReserveDetail{
		{
			PharmacyReserveDetailID: 1,
			PharmacyReserveID:       1,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 2,
			PharmacyReserveID:       2,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 3,
			PharmacyReserveID:       3,
			PatientID:               2,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusGuidanceDone,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 4,
			PharmacyReserveID:       3,
			PatientID:               201,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusDrugsPackaged,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusGuidanceDone,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusPaymentDone,
		},
		{
			PharmacyReserveDetailID: 5,
			PharmacyReserveID:       4,
			PatientID:               301,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 6,
			PharmacyReserveID:       4,
			PatientID:               302,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 7,
			PharmacyReserveID:       4,
			PatientID:               303,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
	}
	db.Create(&pharmacyReserveDetails)

	pharmacyDesiredDates := []entity.PharmacyDesiredDate{
		{
			PharmacyDesiredDateID: 1,
			PharmacyReserveID:     1,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
		},
		// PharmacyReserveID=2のPharmacyDesiredDateレコードはなし
	}
	db.Create(&pharmacyDesiredDates)

	ptInfs := []entity.PtInf{
		{ // pharmacy_reserve / pharmacy_reserve_detail
			HpID:             7,
			PtID:             32,
			PtNum:            32,
			KanaName:         "スズキ ショウタ",
			Name:             "鈴木 翔太",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(1),
		},
		{ // pharmacy_reserve / pharmacy_reserve_detail
			HpID:             8,
			PtID:             2,
			PtNum:            10500,
			KanaName:         "サクラギ ハナタロウ",
			Name:             "桜木 花太郎",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(292),
		},
		{ // reserve
			HpID:             10,
			PtID:             22,
			PtNum:            3742,
			KanaName:         "サクラギ ハナタロウ",
			Name:             "桜木 花太郎",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(292),
		},
		{ // pharmacy_reserve_detail
			HpID:             8,
			PtID:             201,
			PtNum:            105001,
			KanaName:         "サクラギ モモキチ",
			Name:             "桜木 百吉",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(304),
		},
		{ // pharmacy_reserve / pharmacy_reserve_detail
			HpID:             8,
			PtID:             3,
			PtNum:            32567,
			KanaName:         "ツクバ アキオ",
			Name:             "筑波 明夫",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(618),
		},
		{ // reserve
			HpID:             20,
			PtID:             22,
			PtNum:            75273,
			KanaName:         "ツクバ アキオ",
			Name:             "筑波 明夫",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(618),
		},
		{ // pharmacy_reserve_detail
			HpID:             8,
			PtID:             301,
			PtNum:            32568,
			KanaName:         "ツクバ カスミ",
			Name:             "筑波 霞",
			Sex:              2,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(620),
		},
		{ // pharmacy_reserve_detail
			HpID:             8,
			PtID:             302,
			PtNum:            32569,
			KanaName:         "ツクバ ゲンジ",
			Name:             "筑波 玄次",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(624),
		},
		{ // pharmacy_reserve_detail
			HpID:             8,
			PtID:             303,
			PtNum:            32540,
			KanaName:         "ツクバ ゼンキチ",
			Name:             "筑波 善吉",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(653),
		},
	}
	db.Create(&ptInfs)

	portalCustomers := []entity.PortalCustomer{ // pharmacy_reserve_detail->pt_inf->portal_customer
		{
			CustomerID: 1,
			KanaName:   "スズキ ショウタ",
			Name:       "鈴木 翔太",
			Gender:     1,
			Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 292,
			KanaName:   "サクラギ ハナタロウチョウナン",
			Name:       "桜木 花太郎長男",
			Gender:     1,
			Birthday:   time.Date(1950, 2, 28, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 304,
			KanaName:   "サクラギ モモキチ",
			Name:       "桜木 百吉",
			Gender:     1,
			Birthday:   time.Date(1980, 7, 26, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 618,
			KanaName:   "ツクバ アキオ",
			Name:       "筑波 明夫",
			Gender:     1,
			Birthday:   time.Date(1990, 12, 24, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 620,
			KanaName:   "ツクバ カスミ",
			Name:       "筑波 霞",
			Gender:     1,
			Birthday:   time.Date(1995, 5, 2, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 624,
			KanaName:   "ツクバ ゲンジ",
			Name:       "筑波 玄次",
			Gender:     1,
			Birthday:   time.Date(2011, 12, 01, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 653,
			KanaName:   "ツクバ ゼンキチ",
			Name:       "筑波 善吉",
			Gender:     1,
			Birthday:   time.Date(2016, 8, 11, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&portalCustomers)

	reserves := []entity.Reserve{
		{
			ReserveID: 2,
			PatientID: util.NewPtr(22),
		},
	}
	db.Create(&reserves)

	portalHospitals := []entity.PortalHospital{
		{
			HospitalID: 7,
			Name:       "渋谷医院",
		},
		{
			HospitalID: 900,
			Name:       "道玄坂中央病院",
			HpInfID:    10,
		},
		{
			HospitalID: 512,
			Name:       "宮益坂クリニック",
			HpInfID:    20,
		},
	}
	db.Create(&portalHospitals)
	// =================================

	testCases := []struct {
		name       string
		hospitalID int
		input      model.FindPharmacyReservesInput
		wantRes    []int
		wantErr    error
	}{
		{
			name:       "正常系：DisplayDateがJST",
			hospitalID: 7,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{
					DisplayDate: time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
				},
				KeywordFilter: &model.KeywordFilterInput{},
				StatusFilter: &model.StatusFilterInput{
					Status:           util.NewPtr(constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived),
					PharmacistStatus: util.NewPtr(constant.PharmacyReservePharmacistStatusBeforeConfigured),
					GuidanceStatus:   util.NewPtr(constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance),
					PaymentStatus:    util.NewPtr(constant.PharmacyReserveDetailPaymentStatusBeforePayment),
					CSVStatus:        util.NewPtr(constant.PharmacyReserveCsvStatusBeforeExport),
				},
				Sort: nil,
			},
			wantRes: []int{1, 2},
			wantErr: nil,
		},
		{
			name:       "正常系：DisplayDateがUTC",
			hospitalID: 7,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{
					DisplayDate: time.Date(2024, 6, 18, 15, 0, 0, 0, time.UTC),
				},
				KeywordFilter: &model.KeywordFilterInput{},
				StatusFilter: &model.StatusFilterInput{
					Status:           util.NewPtr(constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived),
					PharmacistStatus: util.NewPtr(constant.PharmacyReservePharmacistStatusBeforeConfigured),
					GuidanceStatus:   util.NewPtr(constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance),
					PaymentStatus:    util.NewPtr(constant.PharmacyReserveDetailPaymentStatusBeforePayment),
					CSVStatus:        util.NewPtr(constant.PharmacyReserveCsvStatusBeforeExport),
				},
				Sort: nil,
			},
			wantRes: []int{1, 2},
			wantErr: nil,
		},
		{
			name:       "正常系：希望日時が設定前のみ",
			hospitalID: 7,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{
					DisplayDate: time.Date(2024, 6, 28, 15, 0, 0, 0, time.UTC),
				},
				KeywordFilter: &model.KeywordFilterInput{},
				StatusFilter: &model.StatusFilterInput{
					Status:           util.NewPtr(constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived),
					PharmacistStatus: util.NewPtr(constant.PharmacyReservePharmacistStatusBeforeConfigured),
					GuidanceStatus:   util.NewPtr(constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance),
					PaymentStatus:    util.NewPtr(constant.PharmacyReserveDetailPaymentStatusBeforePayment),
					CSVStatus:        util.NewPtr(constant.PharmacyReserveCsvStatusBeforeExport),
				},
				Sort: nil,
			},
			wantRes: []int{2},
			wantErr: nil,
		},
		{
			name:       "正常系：キャンセル指定で０件",
			hospitalID: 7,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{
					DisplayDate: time.Date(2024, 6, 18, 15, 0, 0, 0, time.UTC),
				},
				KeywordFilter: &model.KeywordFilterInput{},
				StatusFilter: &model.StatusFilterInput{
					Status:           util.NewPtr(constant.PharmacyReserveDetailStatusCanceled),
					PharmacistStatus: util.NewPtr(constant.PharmacyReservePharmacistStatusBeforeConfigured),
					GuidanceStatus:   util.NewPtr(constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance),
					PaymentStatus:    util.NewPtr(constant.PharmacyReserveDetailPaymentStatusBeforePayment),
					CSVStatus:        util.NewPtr(constant.PharmacyReserveCsvStatusBeforeExport),
				},
				Sort: nil,
			},
			wantRes: []int{},
			wantErr: nil,
		},
		{
			name:       "正常系：会員キーワード文字列指定（会員ID）1件一致",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Customer: util.NewPtr("292"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：会員キーワード文字列指定（会員ID）一致なし",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Customer: util.NewPtr("92"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{},
			wantErr: nil,
		},
		{
			name:       "正常系：会員キーワード文字列指定（会員カナ）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Customer: util.NewPtr("ツクバ アキオ"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{4},
			wantErr: nil,
		},
		{
			name:       "正常系：会員キーワード指定（会員氏名）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Customer: util.NewPtr("木 花"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：患者キーワード指定（患者番号）1件一致",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Patient: util.NewPtr("32569"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{4},
			wantErr: nil,
		},
		{
			name:       "正常系：患者キーワード指定（患者番号）一致なし",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Patient: util.NewPtr("3256"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{},
			wantErr: nil,
		},
		{
			name:       "正常系：患者キーワード指定（患者氏名）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Patient: util.NewPtr("吉"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3, 4},
			wantErr: nil,
		},
		{
			name:       "正常系：患者キーワード指定（患者カナ）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Patient: util.NewPtr("ンキチ"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{4},
			wantErr: nil,
		},
		{
			name:       "正常系：クリニックキーワード指定（クリニックID）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Clinic: util.NewPtr("900"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：クリニックキーワード指定（クリニック名）",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Clinic: util.NewPtr("坂"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：会員、患者、クリニックキーワード",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Clinic:   util.NewPtr("900"),
					Patient:  util.NewPtr("木"),
					Customer: util.NewPtr("ク"),
				},
				StatusFilter: &model.StatusFilterInput{},
				Sort:         nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：会員、患者、クリニックキーワード、各種ステータス",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter: &model.DateFilterInput{},
				KeywordFilter: &model.KeywordFilterInput{
					Clinic:   util.NewPtr("900"),
					Patient:  util.NewPtr("チョウナン"),
					Customer: util.NewPtr("ク"),
				},
				StatusFilter: &model.StatusFilterInput{
					Status:           util.NewPtr(constant.PharmacyReserveDetailStatusDrugsPackaged),
					PharmacistStatus: util.NewPtr(constant.PharmacyReservePharmacistStatusBeforeConfigured),
					GuidanceStatus:   util.NewPtr(constant.PharmacyReserveDetailGuidanceStatusGuidanceDone),
					PaymentStatus:    util.NewPtr(constant.PharmacyReserveDetailPaymentStatusPaymentDone),
					CSVStatus:        util.NewPtr(constant.PharmacyReserveCsvStatusBeforeExport),
				},
				Sort: nil,
			},
			wantRes: []int{3},
			wantErr: nil,
		},
		{
			name:       "正常系：keywordFilter is nil",
			hospitalID: 8,
			input: model.FindPharmacyReservesInput{
				DateFilter:    &model.DateFilterInput{},
				KeywordFilter: nil,
				StatusFilter:  &model.StatusFilterInput{},
				Sort:          nil,
			},
			wantRes: []int{3, 4},
			wantErr: nil,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			ids, err := testee.FindPharmacyReserveIDsByConditions(tt.hospitalID, tt.input)
			if tt.wantErr == nil {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantRes, ids)
			} else {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			}
		})
	}
}

func Test_FindPharmacyReservesByIDs(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// テスト用日付
	year, month, day := time.Now().Date()
	birthday := year*10000 + int(month)*100 + day
	reserverId := 1

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfirming,
			ReserveUpdateDate: time.Date(2024, 6, 18, 1, 0, 0, 0, time.Local),
			ReserveID:         &reserverId,
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 2,
			PatientID:         33,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 2, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(2),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 3,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 3, 0, 0, 0, time.Local),
			ReserveID:         &reserverId,
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 4,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 4, 0, 0, 0, time.Local),
			ReserveID:         &reserverId,
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 5,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 5, 0, 0, 0, time.Local),
			ReserveID:         &reserverId,
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 6,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 6, 0, 0, 0, time.Local),
			ReserveID:         &reserverId,
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
	}
	db.Create(&pharmacyReserves)

	pharmacyReserveDetails := []entity.PharmacyReserveDetail{
		{
			PharmacyReserveDetailID: 1,
			PharmacyReserveID:       1,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 2,
			PharmacyReserveID:       2,
			PatientID:               33,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 3,
			PharmacyReserveID:       3,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 4,
			PharmacyReserveID:       4,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 5,
			PharmacyReserveID:       5,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 6,
			PharmacyReserveID:       6,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
	}
	db.Create(&pharmacyReserveDetails)

	pharmacyDesiredDates := []entity.PharmacyDesiredDate{
		{
			PharmacyDesiredDateID: 1,
			PharmacyReserveID:     3,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 18, 9, 0, 0, 0, time.Local),
		},
		{
			PharmacyDesiredDateID: 2,
			PharmacyReserveID:     4,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeSpecified,
			DesiredDate:           time.Date(2024, 6, 18, 15, 0, 0, 0, time.Local),
		},
		{
			PharmacyDesiredDateID: 3,
			PharmacyReserveID:     5,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeSpecified,
			DesiredDate:           time.Date(2024, 6, 18, 14, 0, 0, 0, time.Local),
		},
		{
			PharmacyDesiredDateID: 4,
			PharmacyReserveID:     6,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeNotSpecified,
			DesiredDate:           time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&pharmacyDesiredDates)

	ptInfs := []entity.PtInf{
		{
			HpID:             1,
			PtID:             1,
			PtNum:            1,
			KanaName:         "スズキ ショウタ",
			Name:             "鈴木 翔太",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(1),
		},
		{
			HpID:             7,
			PtID:             32,
			PtNum:            32,
			KanaName:         "スズキ ショウタ",
			Name:             "鈴木 翔太",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(1),
		},
		{
			HpID:             7,
			PtID:             33,
			PtNum:            33,
			KanaName:         "スズキ カイト",
			Name:             "鈴木 海斗",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(2),
		},
	}
	db.Create(&ptInfs)

	hpInf := []*custom.HpInf{
		{
			HpID:   1,
			HpName: "病院24",
			Tel:    "**********",
		},
		{
			HpID:   7,
			HpName: "薬局24",
			Tel:    "**********",
		},
	}
	db.Create(&hpInf)

	portalCustomers := []entity.PortalCustomer{
		{
			CustomerID: 1,
			KanaName:   "スズキ ショウタ",
			Name:       "鈴木 翔太",
			Gender:     1,
			Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			IsDeleted:  constant.StatusFalse,
		},
		{
			CustomerID: 2,
			KanaName:   "スズキ カイト",
			Name:       "鈴木 海斗（削除済み）",
			Gender:     1,
			Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			IsDeleted:  constant.StatusTrue,
		},
	}
	db.Create(&portalCustomers)

	portalCustomerLogins := []entity.PortalCustomerLogin{
		{
			CustomerID: 1,
			Email:      "<EMAIL>",
			IsDeleted:  constant.StatusFalse,
		},
		{
			CustomerID: 2,
			Email:      "<EMAIL>",
			IsDeleted:  constant.StatusTrue,
		},
	}
	db.Create(&portalCustomerLogins)

	reserves := []entity.Reserve{
		{
			ReserveID:                 1,
			PatientID:                 util.NewPtr(1),
			PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeNotSpecify,
		},
		{
			ReserveID:                 2,
			PatientID:                 util.NewPtr(2),
			PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeNotSpecify,
			IsDeleted:                 constant.StatusTrue,
		},
	}
	db.Create(&reserves)

	reserveDetails := []entity.ReserveDetail{
		{
			ReserveDetailID: 1,
			ReserveID:       1,
			PatientID:       util.NewPtr(1),
			ExamTimeSlotID:  1,
		},
		{
			ReserveDetailID: 2,
			ReserveID:       2,
			PatientID:       util.NewPtr(2),
			ExamTimeSlotID:  1,
			IsDeleted:       constant.StatusTrue,
		},
	}
	db.Create(&reserveDetails)

	examTimeSlots := []entity.ExamTimeSlot{
		{
			ExamTimeSlotID: 1,
			ExamStartDate:  time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&examTimeSlots)

	portalHospitals := []entity.PortalHospital{
		{
			HospitalID: 1,
			HpInfID:    1,
			Name:       "B病院",
		},
		{
			HospitalID: 7,
			HpInfID:    7,
			Name:       "GMO薬局",
		},
	}
	db.Create(&portalHospitals)

	meetings := []entity.Meeting{
		{
			MeetingID: 1,
			ReserveID: util.NewPtr(1),
			Status:    constant.MeetingReserved,
		},
		{
			MeetingID:         2,
			PharmacyReserveID: util.NewPtr(1),
			Status:            constant.MeetingReserved,
		},
		{
			MeetingID:         3,
			PharmacyReserveID: util.NewPtr(2),
			Status:            constant.MeetingReserved,
		},
		{
			MeetingID:         4,
			PharmacyReserveID: util.NewPtr(3),
			Status:            constant.MeetingReserved,
		},
		{
			MeetingID:         5,
			PharmacyReserveID: util.NewPtr(4),
			Status:            constant.MeetingReserved,
		},
		{
			MeetingID:         6,
			PharmacyReserveID: util.NewPtr(5),
			Status:            constant.MeetingReserved,
		},
	}
	db.Create(&meetings)

	userMsts := []entity.UserMst{
		{
			HpID:    7,
			UserID:  31,
			Name:    "薬局 管理",
			LoginID: "yamada31",
		},
	}
	db.Create(&userMsts)
	// =================================

	testCases := []struct {
		name           string
		inputIDs       []int
		inputSortInput *model.SortInput
		wantRes        []*custom.PharmacyReserve
		wantResOrder   []int
		wantErr        error
	}{
		{
			name:     "正常系",
			inputIDs: []int{3},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderAscend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantRes: []*custom.PharmacyReserve{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 3,
						PatientID:         32,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 18, 3, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							PtID:             32,
							PtNum:            32,
							KanaName:         "スズキ ショウタ",
							Name:             "鈴木 翔太",
							Sex:              1,
							Birthday:         birthday,
							PortalCustomerID: util.NewPtr(1),
						},
						HpInf: &entity.HpInf{
							HpID:   7,
							HpName: "薬局24",
							Tel:    "**********",
						},
						Customer: entity.PortalCustomer{
							CustomerID: 1,
							KanaName:   "スズキ ショウタ",
							Name:       "鈴木 翔太",
							Gender:     1,
							Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
						},
					},
					PharmacyDesiredDates: []*entity.PharmacyDesiredDate{
						{
							PharmacyDesiredDateID: 1,
							PharmacyReserveID:     3,
							DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
							DesiredDate:           time.Date(2024, 6, 18, 9, 0, 0, 0, time.Local),
						},
					},
					ClinicReserve: &custom.ClinicReserve{
						Reserve: entity.Reserve{
							ReserveID:                 1,
							PatientID:                 util.NewPtr(1),
							PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeNotSpecify,
						},
						Patient: &custom.ClinicPtInf{
							PortalHospital: entity.PortalHospital{
								Name: "B病院",
							},
						},
						ClinicReserveDetails: []*custom.ClinicReserveDetail{
							{
								ExamTimeSlot: entity.ExamTimeSlot{
									ExamStartDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
									ExamEndDate:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
								},
							},
						},
						Meeting: &entity.Meeting{
							Status: constant.MeetingReserved,
						},
					},
					PharmacyReserveDetails: []*custom.PharmacyReserveDetail{
						{
							PharmacyReserveDetail: entity.PharmacyReserveDetail{
								PharmacyReserveID: 3,
								PatientID:         32,
								PrescriptionType:  constant.PharmacyReserveDetailPrescriptionTypeAnalog,
								Status:            constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
								GuidanceStatus:    constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
								PaymentStatus:     constant.PharmacyReserveDetailPaymentStatusBeforePayment,
							},
							Patient: &custom.PharmacyPatient{
								PtInf: entity.PtInf{
									PtID:             32,
									PtNum:            32,
									KanaName:         "スズキ ショウタ",
									Name:             "鈴木 翔太",
									Sex:              1,
									Birthday:         birthday,
									PortalCustomerID: util.NewPtr(1),
								},
								Customer: entity.PortalCustomer{
									CustomerID: 1,
									KanaName:   "スズキ ショウタ",
									Name:       "鈴木 翔太",
									Gender:     1,
									Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
								},
							},
						},
					},
					Pharmacist: &entity.UserMst{
						UserID:  31,
						Name:    "薬局 管理",
						LoginID: "yamada31",
					},
					Meeting: &entity.Meeting{
						MeetingID: 4,
						Status:    constant.MeetingReserved,
					},
				},
			},
		},
		{
			name:     "正常系：０件",
			inputIDs: []int{},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderAscend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantRes: []*custom.PharmacyReserve{},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_asc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate: util.NewPtr(model.SortOrderAscend),
			},
			wantResOrder: []int{3, 5, 4, 6, 1, 2},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_desc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate: util.NewPtr(model.SortOrderDescend),
			},
			wantResOrder: []int{2, 1, 6, 4, 5, 3},
		},
		{
			name:     "正常系：ソート：PharmacyReserveUpdateDate_asc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantResOrder: []int{1, 2, 3, 4, 5, 6},
		},
		{
			name:     "正常系：ソート：PharmacyReserveUpdateDate_desc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderDescend),
			},
			wantResOrder: []int{6, 5, 4, 3, 2, 1},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_asc & PharmacyReserveUpdateDate_asc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderAscend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantResOrder: []int{3, 5, 4, 6, 1, 2},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_desc & PharmacyReserveUpdateDate_desc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderDescend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderDescend),
			},
			wantResOrder: []int{2, 1, 6, 4, 5, 3},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_asc & PharmacyReserveUpdateDate_desc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderAscend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderDescend),
			},
			wantResOrder: []int{3, 5, 4, 6, 1, 2},
		},
		{
			name:     "正常系：ソート：PharmacyDesiredDate_desc & PharmacyReserveUpdateDate_asc",
			inputIDs: []int{1, 2, 3, 4, 5, 6},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderDescend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantResOrder: []int{2, 1, 6, 4, 5, 3},
		},
		{
			name:     "正常系：削除済みの家族患者含む場合",
			inputIDs: []int{2},
			inputSortInput: &model.SortInput{
				PharmacyDesiredDate:       util.NewPtr(model.SortOrderAscend),
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			wantRes: []*custom.PharmacyReserve{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 2,
						PatientID:         33,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 18, 2, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(2),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					PharmacyReserveDetails: []*custom.PharmacyReserveDetail{
						{
							PharmacyReserveDetail: entity.PharmacyReserveDetail{
								PharmacyReserveID: 2,
								PatientID:         33,
								PrescriptionType:  constant.PharmacyReserveDetailPrescriptionTypeAnalog,
								Status:            constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
								GuidanceStatus:    constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
								PaymentStatus:     constant.PharmacyReserveDetailPaymentStatusBeforePayment,
							},
							Patient: &custom.PharmacyPatient{
								PtInf: entity.PtInf{
									PtID:             33,
									PtNum:            33,
									KanaName:         "スズキ カイト",
									Name:             "鈴木 海斗",
									Sex:              1,
									Birthday:         birthday,
									PortalCustomerID: util.NewPtr(2),
								},
								Customer: entity.PortalCustomer{
									CustomerID: 2,
									KanaName:   "スズキ カイト",
									Name:       "鈴木 海斗（削除済み）",
									Gender:     1,
									Birthday:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
									IsDeleted:  constant.StatusTrue,
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			pharmacyReserves, err := testee.FindPharmacyReservesByIDs(tt.inputIDs, tt.inputSortInput)

			// 結果がエラー想定の場合
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}

			// ソート系テストの場合
			if tt.wantResOrder != nil {
				var actual []int
				for _, pharmacyReserve := range pharmacyReserves {
					actual = append(actual, pharmacyReserve.PharmacyReserveID)
				}
				assert.Equal(t, tt.wantResOrder, actual)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, len(tt.wantRes), len(pharmacyReserves))
			for i, pharmacyReserve := range pharmacyReserves {
				// check PharmacyReserve
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.PharmacyReserveID, pharmacyReserve.PharmacyReserve.PharmacyReserveID)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.PatientID, pharmacyReserve.PharmacyReserve.PatientID)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.DesiredDateStatus, pharmacyReserve.PharmacyReserve.DesiredDateStatus)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.ReserveUpdateDate.In(time.Local), pharmacyReserve.PharmacyReserve.ReserveUpdateDate.In(time.Local))
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.ReserveID, pharmacyReserve.PharmacyReserve.ReserveID)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.SmsStatus, pharmacyReserve.PharmacyReserve.SmsStatus)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.VideocallStatus, pharmacyReserve.PharmacyReserve.VideocallStatus)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.PostalServiceType, pharmacyReserve.PharmacyReserve.PostalServiceType)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.CsvStatus, pharmacyReserve.PharmacyReserve.CsvStatus)
				assert.Equal(t, tt.wantRes[i].PharmacyReserve.PharmacistStatus, pharmacyReserve.PharmacyReserve.PharmacistStatus)
				assert.Equal(t, *tt.wantRes[i].PharmacyReserve.PharmacistID, *pharmacyReserve.PharmacyReserve.PharmacistID)

				// check Patient
				if tt.wantRes[i].Patient != nil {
					assert.Equal(t, tt.wantRes[i].Patient.PtID, pharmacyReserve.Patient.PtID)
					assert.Equal(t, tt.wantRes[i].Patient.PtNum, pharmacyReserve.Patient.PtNum)
					assert.Equal(t, tt.wantRes[i].Patient.KanaName, pharmacyReserve.Patient.KanaName)
					assert.Equal(t, tt.wantRes[i].Patient.Name, pharmacyReserve.Patient.Name)
					assert.Equal(t, tt.wantRes[i].Patient.Sex, pharmacyReserve.Patient.Sex)
					assert.Equal(t, tt.wantRes[i].Patient.Birthday, pharmacyReserve.Patient.Birthday)
					assert.Equal(t, tt.wantRes[i].Patient.PortalCustomerID, pharmacyReserve.Patient.PortalCustomerID)

					// check HpInf
					assert.Equal(t, tt.wantRes[i].Patient.HpInf.HpID, pharmacyReserve.Patient.HpInf.HpID)
					assert.Equal(t, tt.wantRes[i].Patient.HpInf.HpName, pharmacyReserve.Patient.HpInf.HpName)
					assert.Equal(t, tt.wantRes[i].Patient.HpInf.Tel, pharmacyReserve.Patient.HpInf.Tel)

					// check Customer
					assert.Equal(t, tt.wantRes[i].Patient.Customer.CustomerID, pharmacyReserve.Patient.Customer.CustomerID)
					assert.Equal(t, tt.wantRes[i].Patient.Customer.KanaName, pharmacyReserve.Patient.Customer.KanaName)
					assert.Equal(t, tt.wantRes[i].Patient.Customer.Name, pharmacyReserve.Patient.Customer.Name)
					assert.Equal(t, tt.wantRes[i].Patient.Customer.Gender, pharmacyReserve.Patient.Customer.Gender)
					assert.Equal(t, tt.wantRes[i].Patient.Customer.Birthday, pharmacyReserve.Patient.Customer.Birthday)
				}

				// check PharmacyDesiredDate
				for j, pharmacyDesiredDate := range pharmacyReserve.PharmacyDesiredDates {
					if tt.wantRes[i].PharmacyDesiredDates[j] != nil {
						assert.Equal(t, tt.wantRes[i].PharmacyDesiredDates[j].PharmacyDesiredDateID, pharmacyDesiredDate.PharmacyDesiredDateID)
						assert.Equal(t, tt.wantRes[i].PharmacyDesiredDates[j].PharmacyReserveID, pharmacyDesiredDate.PharmacyReserveID)
						assert.Equal(t, tt.wantRes[i].PharmacyDesiredDates[j].DesiredType, pharmacyDesiredDate.DesiredType)
						assert.Equal(t, tt.wantRes[i].PharmacyDesiredDates[j].DesiredDate.In(time.Local), pharmacyDesiredDate.DesiredDate.In(time.Local))
					}
				}

				// check ClinicReserve
				if tt.wantRes[i].ClinicReserve != nil {
					assert.Equal(t, tt.wantRes[i].ClinicReserve.ReserveID, pharmacyReserve.ClinicReserve.ReserveID)
					assert.Equal(t, tt.wantRes[i].ClinicReserve.PatientID, pharmacyReserve.ClinicReserve.PatientID)
					assert.Equal(t, tt.wantRes[i].ClinicReserve.PrescriptionReceiveMethod, pharmacyReserve.ClinicReserve.PrescriptionReceiveMethod)
					assert.Equal(t, tt.wantRes[i].ClinicReserve.Patient.PortalHospital.Name, pharmacyReserve.ClinicReserve.Patient.PortalHospital.Name)
					assert.Equal(t, tt.wantRes[i].ClinicReserve.Meeting.Status, pharmacyReserve.ClinicReserve.Meeting.Status)

					for j, clinicReserveDetail := range pharmacyReserve.ClinicReserve.ClinicReserveDetails {
						assert.Equal(t, tt.wantRes[i].ClinicReserve.ClinicReserveDetails[j].ExamTimeSlot.ExamStartDate.In(time.Local), clinicReserveDetail.ExamTimeSlot.ExamStartDate.In(time.Local))
						assert.Equal(t, tt.wantRes[i].ClinicReserve.ClinicReserveDetails[j].ExamTimeSlot.ExamEndDate.In(time.Local), clinicReserveDetail.ExamTimeSlot.ExamEndDate.In(time.Local))
					}
				}

				// check PharmacyReserveDetail
				for j, pharmacyReserveDetail := range pharmacyReserve.PharmacyReserveDetails {
					if tt.wantRes[i].PharmacyReserveDetails[j] != nil {
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].PharmacyReserveID, pharmacyReserveDetail.PharmacyReserveID)
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].PatientID, pharmacyReserveDetail.PatientID)
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].PrescriptionType, pharmacyReserveDetail.PrescriptionType)
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Status, pharmacyReserveDetail.Status)
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].GuidanceStatus, pharmacyReserveDetail.GuidanceStatus)
						assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].PaymentStatus, pharmacyReserveDetail.PaymentStatus)

						// check Patient
						if tt.wantRes[i].PharmacyReserveDetails[j].Patient != nil {
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.PtID, pharmacyReserveDetail.Patient.PtID)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.PtNum, pharmacyReserveDetail.Patient.PtNum)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.KanaName, pharmacyReserveDetail.Patient.KanaName)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Name, pharmacyReserveDetail.Patient.Name)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Sex, pharmacyReserveDetail.Patient.Sex)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Birthday, pharmacyReserveDetail.Patient.Birthday)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.PortalCustomerID, pharmacyReserveDetail.Patient.PortalCustomerID)

							// check Customer
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Customer.CustomerID, pharmacyReserveDetail.Patient.Customer.CustomerID)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Customer.KanaName, pharmacyReserveDetail.Patient.Customer.KanaName)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Customer.Name, pharmacyReserveDetail.Patient.Customer.Name)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Customer.Gender, pharmacyReserveDetail.Patient.Customer.Gender)
							assert.Equal(t, tt.wantRes[i].PharmacyReserveDetails[j].Patient.Customer.Birthday, pharmacyReserveDetail.Patient.Customer.Birthday)
						}
					}
				}

				// check Pharmacist
				if tt.wantRes[i].Pharmacist != nil {
					assert.Equal(t, tt.wantRes[i].Pharmacist.UserID, pharmacyReserve.Pharmacist.UserID)
					assert.Equal(t, tt.wantRes[i].Pharmacist.Name, pharmacyReserve.Pharmacist.Name)
					assert.Equal(t, tt.wantRes[i].Pharmacist.LoginID, pharmacyReserve.Pharmacist.LoginID)
				}

				// check Meeting
				if tt.wantRes[i].Meeting != nil {
					assert.Equal(t, tt.wantRes[i].Meeting.MeetingID, pharmacyReserve.Meeting.MeetingID)
					assert.Equal(t, tt.wantRes[i].Meeting.Status, pharmacyReserve.Meeting.Status)
				}
			}
		})
	}
}

func Test_FindPharmacyReservesInRange(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// テスト用日付
	year, month, day := time.Now().Date()
	birthday := year*10000 + int(month)*100 + day

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
	}
	db.Create(&pharmacyReserves)

	pharmacyReserveDetails := []entity.PharmacyReserveDetail{
		{
			PharmacyReserveDetailID: 1,
			PharmacyReserveID:       1,
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
	}
	db.Create(&pharmacyReserveDetails)

	pharmacyDesiredDates := []entity.PharmacyDesiredDate{
		{
			PharmacyDesiredDateID: 1,
			PharmacyReserveID:     1,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&pharmacyDesiredDates)

	ptInfs := []entity.PtInf{
		{
			HpID:             7,
			PtID:             32,
			PtNum:            32,
			KanaName:         "スズキ ショウタ",
			Name:             "鈴木 翔太",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(1),
		},
	}
	db.Create(&ptInfs)
	// =================================

	testCases := []struct {
		name       string
		hospitalID int
		input      model.HasPharmacyReserveInRangeInput
		wantRes    []*entity.PharmacyReserve
		wantErr    error
	}{
		{
			name:       "正常系：StartDate・EndDateがJST",
			hospitalID: 7,
			input: model.HasPharmacyReserveInRangeInput{
				StartDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
				EndDate:   time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			},
			wantRes: []*entity.PharmacyReserve{
				{
					PharmacyReserveID: 1,
					PatientID:         32,
					DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
					ReserveUpdateDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
					ReserveID:         util.NewPtr(1),
					SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
					VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
					PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
					CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
					PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
					PharmacistID:      util.NewPtr(31),
				},
			},
			wantErr: nil,
		},
		{
			name:       "正常系：０件",
			hospitalID: 7,
			input: model.HasPharmacyReserveInRangeInput{
				StartDate: time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
				EndDate:   time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
			},
			wantRes: []*entity.PharmacyReserve{},
			wantErr: nil,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			pharmacyReserves, err := testee.FindPharmacyReservesInRange(tt.hospitalID, tt.input)
			if tt.wantErr == nil {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.wantRes), len(pharmacyReserves))
				for i, pharmacyReserve := range pharmacyReserves {
					// check PharmacyReserve
					assert.Equal(t, tt.wantRes[i].PharmacyReserveID, pharmacyReserve.PharmacyReserveID)
					assert.Equal(t, tt.wantRes[i].PatientID, pharmacyReserve.PatientID)
					assert.Equal(t, tt.wantRes[i].DesiredDateStatus, pharmacyReserve.DesiredDateStatus)
					assert.Equal(t, tt.wantRes[i].ReserveUpdateDate.In(time.Local), pharmacyReserve.ReserveUpdateDate.In(time.Local))
					assert.Equal(t, tt.wantRes[i].ReserveID, pharmacyReserve.ReserveID)
					assert.Equal(t, tt.wantRes[i].SmsStatus, pharmacyReserve.SmsStatus)
					assert.Equal(t, tt.wantRes[i].VideocallStatus, pharmacyReserve.VideocallStatus)
					assert.Equal(t, tt.wantRes[i].PostalServiceType, pharmacyReserve.PostalServiceType)
					assert.Equal(t, tt.wantRes[i].CsvStatus, pharmacyReserve.CsvStatus)
					assert.Equal(t, tt.wantRes[i].PharmacistStatus, pharmacyReserve.PharmacistStatus)
					assert.Equal(t, *tt.wantRes[i].PharmacistID, *pharmacyReserve.PharmacistID)
				}
			} else {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			}
		})
	}
}

func Test_UpdatePostalServiceTypeByID(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	ctx := test_mock.GetTestContextWithDB(db)
	testee := postgresql.NewPharmacyReserveRepository(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		postalServiceType int
		wantErr           error
	}{
		{
			name:              "正常系：ゆうパケット(3kg以下)",
			pharmacyReserveID: 1,
			postalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			wantErr:           nil,
		},
		{
			name:              "正常系：ゆうパック(30kg以下)",
			pharmacyReserveID: 1,
			postalServiceType: constant.PharmacyReservePostalServiceTypeYuuPack,
			wantErr:           nil,
		},
		{
			name:              "正常系：更新対象データなし",
			pharmacyReserveID: 2,
			postalServiceType: constant.PharmacyReservePostalServiceTypeYuuPack,
			wantErr:           gorm.ErrRecordNotFound,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			err := testee.UpdatePostalServiceTypeByID(ctx, 1, tt.pharmacyReserveID, tt.postalServiceType)
			if tt.wantErr == nil {
				assert.NoError(t, err)
			} else {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			}
		})
	}
}

func Test_UpdateSmsStatusByID(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			SmsStatus:         0,
		},
		{
			PharmacyReserveID: 2,
			SmsStatus:         0,
		},
		{
			PharmacyReserveID: 3,
			SmsStatus:         0,
		},
		{
			PharmacyReserveID: 4,
			SmsStatus:         0,
		},
		{
			PharmacyReserveID: 5,
			SmsStatus:         0,
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		smsStatus         int
		expectedError     error
		expectedUpdateAt  time.Time
	}{
		{
			name:              "正常系 更新1",
			smsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			pharmacyReserveID: 1,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新2",
			smsStatus:         constant.PharmacyReserveSMSStatusNotified,
			pharmacyReserveID: 2,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新3",
			smsStatus:         3,
			pharmacyReserveID: 3,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新4",
			smsStatus:         4,
			pharmacyReserveID: 4,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新5",
			smsStatus:         5,
			pharmacyReserveID: 5,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "異常系 存在しないID",
			smsStatus:         6,
			pharmacyReserveID: 6,
			expectedError:     fmt.Errorf("affected rows are not 1. affected rows:[%v]", 0),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			util.Time = test_mock.NewTimeMock(testCase.expectedUpdateAt)
			ctx := test_mock.GetTestContextWithDB(db)
			testee := postgresql.NewPharmacyReserveRepository(db)
			err := testee.UpdateSmsStatusByID(ctx, testCase.pharmacyReserveID, testCase.smsStatus)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}

			var result *entity.PharmacyReserve
			db.Where("pharmacy_reserve_id = ?", testCase.pharmacyReserveID).First(&result)
			assert.Equal(t, testCase.smsStatus, result.SmsStatus)
			assert.Equal(t, testCase.pharmacyReserveID, result.PharmacyReserveID)
			assert.Equal(t, testCase.expectedUpdateAt, result.UpdatedAt)
		})
	}

}

func Test_FindPharmacyReserveByID(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         1,
			DesiredDateStatus: 1,
			ReserveID:         util.NewPtr(1),
			VideocallStatus:   1,
			PostalServiceType: 1,
			CsvStatus:         1,
			PharmacistStatus:  1,
			PharmacistID:      util.NewPtr(1),
			SmsStatus:         1,
		},
		{
			PharmacyReserveID: 2,
			PatientID:         2,
			DesiredDateStatus: 2,
			ReserveID:         util.NewPtr(2),
			VideocallStatus:   2,
			PostalServiceType: 2,
			CsvStatus:         2,
			PharmacistStatus:  2,
			PharmacistID:      util.NewPtr(2),
			SmsStatus:         2,
		},
		{
			PharmacyReserveID: 3,
			PatientID:         3,
			DesiredDateStatus: 3,
			ReserveID:         util.NewPtr(3),
			VideocallStatus:   3,
			PostalServiceType: 3,
			CsvStatus:         3,
			PharmacistStatus:  3,
			PharmacistID:      util.NewPtr(3),
			SmsStatus:         3,
		},
		{
			PharmacyReserveID: 4,
			PatientID:         4,
			DesiredDateStatus: 4,
			ReserveID:         util.NewPtr(4),
			VideocallStatus:   4,
			PostalServiceType: 4,
			CsvStatus:         4,
			PharmacistStatus:  4,
			PharmacistID:      util.NewPtr(4),
			SmsStatus:         4,
		},
		{
			PharmacyReserveID: 5,
			PatientID:         5,
			DesiredDateStatus: 5,
			ReserveID:         util.NewPtr(5),
			VideocallStatus:   5,
			PostalServiceType: 5,
			CsvStatus:         5,
			PharmacistStatus:  5,
			PharmacistID:      util.NewPtr(5),
			SmsStatus:         5,
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		expected          *entity.PharmacyReserve
		expectedError     error
	}{
		{
			name:              "正常系 取得1",
			pharmacyReserveID: 1,
			expected: &entity.PharmacyReserve{
				PharmacyReserveID: 1,
				PatientID:         1,
				DesiredDateStatus: 1,
				ReserveID:         util.NewPtr(1),
				VideocallStatus:   1,
				PostalServiceType: 1,
				CsvStatus:         1,
				PharmacistStatus:  1,
				PharmacistID:      util.NewPtr(1),
				SmsStatus:         1,
			},
		},
		{
			name:              "正常系 取得2",
			pharmacyReserveID: 2,
			expected: &entity.PharmacyReserve{
				PharmacyReserveID: 2,
				PatientID:         2,
				DesiredDateStatus: 2,
				ReserveID:         util.NewPtr(2),
				VideocallStatus:   2,
				PostalServiceType: 2,
				CsvStatus:         2,
				PharmacistStatus:  2,
				PharmacistID:      util.NewPtr(2),
				SmsStatus:         2,
			},
		},
		{
			name:              "正常系 取得3",
			pharmacyReserveID: 3,
			expected: &entity.PharmacyReserve{
				PharmacyReserveID: 3,
				PatientID:         3,
				DesiredDateStatus: 3,
				ReserveID:         util.NewPtr(3),
				VideocallStatus:   3,
				PostalServiceType: 3,
				CsvStatus:         3,
				PharmacistStatus:  3,
				PharmacistID:      util.NewPtr(3),
				SmsStatus:         3,
			},
		},
		{
			name:              "正常系 取得4",
			pharmacyReserveID: 4,
			expected: &entity.PharmacyReserve{
				PharmacyReserveID: 4,
				PatientID:         4,
				DesiredDateStatus: 4,
				ReserveID:         util.NewPtr(4),
				VideocallStatus:   4,
				PostalServiceType: 4,
				CsvStatus:         4,
				PharmacistStatus:  4,
				PharmacistID:      util.NewPtr(4),
				SmsStatus:         4,
			},
		},
		{
			name:              "正常系 取得5",
			pharmacyReserveID: 5,
			expected: &entity.PharmacyReserve{
				PharmacyReserveID: 5,
				PatientID:         5,
				DesiredDateStatus: 5,
				ReserveID:         util.NewPtr(5),
				VideocallStatus:   5,
				PostalServiceType: 5,
				CsvStatus:         5,
				PharmacistStatus:  5,
				PharmacistID:      util.NewPtr(5),
				SmsStatus:         5,
			},
		},
		{
			name:              "異常系 存在しないID",
			pharmacyReserveID: 6,
			expectedError:     gorm.ErrRecordNotFound,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			pharmacyReserve, err := testee.FindPharmacyReserveByID(testCase.pharmacyReserveID)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}

			assert.Equal(t, testCase.expected.PharmacyReserveID, pharmacyReserve.PharmacyReserveID)
			assert.Equal(t, testCase.expected.PatientID, pharmacyReserve.PatientID)
			assert.Equal(t, testCase.expected.DesiredDateStatus, pharmacyReserve.DesiredDateStatus)
			assert.Equal(t, testCase.expected.ReserveID, pharmacyReserve.ReserveID)
			assert.Equal(t, testCase.expected.VideocallStatus, pharmacyReserve.VideocallStatus)
			assert.Equal(t, testCase.expected.PostalServiceType, pharmacyReserve.PostalServiceType)
			assert.Equal(t, testCase.expected.CsvStatus, pharmacyReserve.CsvStatus)
			assert.Equal(t, testCase.expected.PharmacistStatus, pharmacyReserve.PharmacistStatus)
			assert.Equal(t, testCase.expected.PharmacistID, pharmacyReserve.PharmacistID)
			assert.Equal(t, testCase.expected.SmsStatus, pharmacyReserve.SmsStatus)

		})
	}

}

func Test_FindPharmacyReservesRemind(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
		{
			PharmacyReserveID: 2,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(1),
		},
		{
			PharmacyReserveID: 3,
			PatientID:         50,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveID:         util.NewPtr(2),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(2),
		},
		{
			PharmacyReserveID: 4,
			PatientID:         51,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveID:         util.NewPtr(2),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(2),
		},
	}
	db.Create(&pharmacyReserves)

	pharmacyDesiredDates := []entity.PharmacyDesiredDate{
		{
			PharmacyDesiredDateID: 1,
			PharmacyReserveID:     1,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
		{
			PharmacyDesiredDateID: 2,
			PharmacyReserveID:     2,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
		},
		{ // キャンセルのPharmacyReserveDetailしかいない
			PharmacyDesiredDateID: 3,
			PharmacyReserveID:     3,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 22, 0, 0, 0, 0, time.Local),
		},
		{ // キャンセルのPharmacyReserveDetailが2件中1件含まれる
			PharmacyDesiredDateID: 4,
			PharmacyReserveID:     4,
			DesiredType:           constant.PharmacyDesiredDateDesiredTypeAM,
			DesiredDate:           time.Date(2024, 6, 22, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&pharmacyDesiredDates)

	pharmacyReserveDetails := []entity.PharmacyReserveDetail{
		{
			PharmacyReserveDetailID: 1,
			PharmacyReserveID:       1,
			ReserveDetailID:         util.NewPtr(1),
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{
			PharmacyReserveDetailID: 2,
			PharmacyReserveID:       2,
			ReserveDetailID:         util.NewPtr(1),
			PatientID:               32,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionNotYetArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{ // キャンセル
			PharmacyReserveDetailID: 3,
			PharmacyReserveID:       3,
			ReserveDetailID:         util.NewPtr(2),
			PatientID:               50,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusCanceled,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{ // PharmacyReserveにキャンセルのPharmacyReserveDetailが含まれる
			PharmacyReserveDetailID: 4,
			PharmacyReserveID:       4,
			ReserveDetailID:         util.NewPtr(2),
			PatientID:               51,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusPrescriptionArrived,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
		{ // キャンセル
			PharmacyReserveDetailID: 5,
			PharmacyReserveID:       4,
			ReserveDetailID:         util.NewPtr(2),
			PatientID:               52,
			PrescriptionType:        constant.PharmacyReserveDetailPrescriptionTypeAnalog,
			Status:                  constant.PharmacyReserveDetailStatusCanceled,
			GuidanceStatus:          constant.PharmacyReserveDetailGuidanceStatusBeforeGuidance,
			PaymentStatus:           constant.PharmacyReserveDetailPaymentStatusBeforePayment,
		},
	}
	db.Create(&pharmacyReserveDetails)

	reserves := []entity.Reserve{
		{
			ReserveID:                 1,
			PatientID:                 util.NewPtr(1),
			PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeNotSpecify,
		},
		{
			ReserveID:                 2,
			PatientID:                 util.NewPtr(2),
			PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeGMO24Pharmacy,
		},
	}
	db.Create(&reserves)

	reserveDetails := []entity.ReserveDetail{
		{
			ReserveDetailID: 1,
			ReserveID:       1,
			PatientID:       util.NewPtr(1),
			ExamTimeSlotID:  1,
		},
		{
			ReserveDetailID: 2,
			ReserveID:       2,
			PatientID:       util.NewPtr(2),
			ExamTimeSlotID:  2,
		},
	}
	db.Create(&reserveDetails)

	examTimeSlots := []entity.ExamTimeSlot{
		{
			ExamTimeSlotID: 1,
			ExamStartDate:  time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
		},
		{
			ExamTimeSlotID: 2,
			ExamStartDate:  time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&examTimeSlots)
	// =================================

	testCases := []struct {
		name      string
		targetDay time.Time
		wantRes   []int
		wantErr   error
	}{
		{
			name:      "正常系：対象データあり（服薬希望日あり、診療予約日なし）",
			targetDay: time.Date(2024, 6, 19, 0, 0, 0, 0, time.Local),
			wantRes:   []int{2},
			wantErr:   nil,
		},
		{
			name:      "正常系：対象データなし（服薬希望日あり、診療予約日あり）",
			targetDay: time.Date(2024, 6, 18, 0, 0, 0, 0, time.Local),
			wantRes:   []int{},
			wantErr:   nil,
		},
		{
			name:      "正常系：対象データなし（服薬希望日なし、診療予約日なし）",
			targetDay: time.Date(2024, 6, 17, 0, 0, 0, 0, time.Local),
			wantRes:   []int{},
			wantErr:   nil,
		},
		{
			name:      "正常系：対象データなし（服薬希望日あり、診療予約日なし）status=7(キャンセル)のPharmacyReserveDetailあり",
			targetDay: time.Date(2024, 6, 22, 0, 0, 0, 0, time.Local),
			wantRes:   []int{4},
			wantErr:   nil,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			ids, err := testee.FindPharmacyReservesRemind(tt.targetDay)
			if tt.wantErr == nil {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantRes, ids)
			} else {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			}
		})
	}
}

func Test_UpdateDesiredDateStatus(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 2,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 3,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 4,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 5,
			DesiredDateStatus: 0,
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		staffID           int
		desiredDateStatus int
		expectedError     error
		expectedUpdateAt  time.Time
	}{
		{
			name:              "正常系 更新1",
			desiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			pharmacyReserveID: 1,
			staffID:           1,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新2",
			desiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfirming,
			pharmacyReserveID: 2,
			staffID:           2,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新3",
			desiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			pharmacyReserveID: 3,
			staffID:           3,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新4",
			desiredDateStatus: 4,
			pharmacyReserveID: 4,
			staffID:           4,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "正常系 更新5",
			desiredDateStatus: 5,
			pharmacyReserveID: 5,
			staffID:           5,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "異常系 存在しないID",
			desiredDateStatus: 6,
			pharmacyReserveID: 6,
			expectedError:     fmt.Errorf("affected rows are not 1. affected rows:[%v]", 0),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			util.Time = test_mock.NewTimeMock(testCase.expectedUpdateAt)
			ctx := test_mock.GetTestContextWithDB(db)
			testee := postgresql.NewPharmacyReserveRepository(db)
			err := testee.UpdateDesiredDateStatus(ctx, testCase.staffID, testCase.pharmacyReserveID, testCase.desiredDateStatus)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}

			var result *entity.PharmacyReserve
			db.Where("pharmacy_reserve_id = ?", testCase.pharmacyReserveID).First(&result)
			assert.Equal(t, testCase.desiredDateStatus, result.DesiredDateStatus)
			assert.Equal(t, testCase.pharmacyReserveID, result.PharmacyReserveID)
			assert.Equal(t, strconv.Itoa(testCase.staffID), result.UpdatedBy)
		})
	}

}

func Test_UpdatePharmacyReserveCSVStatusByIDs(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 2,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 3,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 4,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 5,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 6,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 7,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 8,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 9,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
		{
			PharmacyReserveID: 10,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name               string
		pharmacyReserveIDs []int
		csvStatus          int
		staffID            int
		expectedLen        int
		expectedError      error
		expectedUpdateAt   time.Time
	}{
		{
			name:               "正常系 更新1",
			csvStatus:          constant.PharmacyReserveCsvStatusBeforeImport,
			pharmacyReserveIDs: []int{1, 2, 3},
			expectedLen:        3,
			staffID:            1,
			expectedUpdateAt:   time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:               "正常系 更新2",
			csvStatus:          constant.PharmacyReserveCsvStatusAlreadyImported,
			pharmacyReserveIDs: []int{4, 5, 6},
			expectedLen:        3,
			staffID:            2,
			expectedUpdateAt:   time.Date(2024, 6, 19, 1, 23, 45, 0, time.Local),
		},
		{
			name:               "正常系 更新対象無し",
			pharmacyReserveIDs: make([]int, 0),
			expectedUpdateAt:   time.Date(2024, 6, 20, 1, 23, 45, 0, time.Local),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			util.Time = test_mock.NewTimeMock(testCase.expectedUpdateAt)
			ctx := test_mock.GetTestContextWithDB(db)
			testee := postgresql.NewPharmacyReserveRepository(db)
			err := testee.UpdatePharmacyReserveCSVStatusByIDs(ctx, testCase.staffID, testCase.pharmacyReserveIDs, testCase.csvStatus)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}

			if len(testCase.pharmacyReserveIDs) == 0 {
				return
			}

			var result []*entity.PharmacyReserve
			db.Where("pharmacy_reserve_id in ?", testCase.pharmacyReserveIDs).Order("pharmacy_reserve_id asc").Find(&result)
			assert.Equal(t, testCase.expectedLen, len(result))
			for _, v := range result {
				assert.Equal(t, testCase.csvStatus, v.CsvStatus)
				assert.Equal(t, strconv.Itoa(testCase.staffID), v.UpdatedBy)
				assert.Equal(t, testCase.expectedUpdateAt, v.UpdatedAt)
			}
		})
	}

}

func Test_FindPharmacyDeliveryInfosByIDs(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 24, 0, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 2,
			PatientID:         33,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 24, 1, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
		{
			PharmacyReserveID: 3,
			PatientID:         34,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 24, 2, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
			SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
			VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
			PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
			CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
			PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
			PharmacistID:      util.NewPtr(31),
		},
	}
	db.Create(&pharmacyReserves)

	ptInfs := []entity.PtInf{
		{
			HpID:             1,
			PtID:             32,
			PtNum:            32,
			KanaName:         "タナカ ヨウイチ",
			Name:             "田中 陽一",
			Sex:              1,
			Birthday:         19951011,
			PortalCustomerID: util.NewPtr(1),
		},
		{
			HpID:             1,
			PtID:             33,
			PtNum:            33,
			KanaName:         "スズキ ショウタ",
			Name:             "鈴木 翔太",
			Sex:              1,
			Birthday:         19670919,
			PortalCustomerID: util.NewPtr(2),
		},
		{
			HpID:             1,
			PtID:             34,
			PtNum:            34,
			KanaName:         "サトウ タカシ",
			Name:             "佐藤 隆",
			Sex:              1,
			Birthday:         19810628,
			PortalCustomerID: util.NewPtr(3),
		},
	}
	db.Create(&ptInfs)

	portalCustomers := []entity.PortalCustomer{
		{
			CustomerID: 1,
			KanaName:   "タナカ ヨウイチ",
			Name:       "田中 陽一",
			Gender:     1,
			Telephone:  "08001234567",
			Birthday:   time.Date(1995, 10, 11, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 2,
			KanaName:   "スズキ ショウタ",
			Name:       "鈴木 翔太",
			Gender:     1,
			Telephone:  "08002345678",
			Birthday:   time.Date(1967, 9, 19, 0, 0, 0, 0, time.Local),
		},
		{
			CustomerID: 3,
			KanaName:   "サトウ タカシ",
			Name:       "佐藤 隆",
			Gender:     1,
			Telephone:  "08003456789",
			Birthday:   time.Date(1981, 6, 28, 0, 0, 0, 0, time.Local),
		},
	}
	db.Create(&portalCustomers)

	portalCustomerLogins := []entity.PortalCustomerLogin{
		{
			CustomerID: 1,
			Email:      "<EMAIL>",
		},
		{
			CustomerID: 2,
			Email:      "<EMAIL>",
		},
		{
			CustomerID: 3,
			Email:      "<EMAIL>",
		},
	}
	db.Create(&portalCustomerLogins)

	deliveryAddresses := []entity.PharmacyDeliveryAddress{
		{
			PharmacyDeliveryAddressID: 100,
			PharmacyReserveID:         1,
			DeliveryAddressID:         1000,
			Address1:                  "東京都江戸川区葛西123-45",
			Address2:                  "ハイタワーマンション3045号室",
			PostCode:                  "1234567",
			PhoneNumber:               "09001234567",
		},
		{
			PharmacyDeliveryAddressID: 101,
			PharmacyReserveID:         2,
			DeliveryAddressID:         1001,
			Address1:                  "東京都世田谷区三軒茶屋123-45",
			Address2:                  "住良ハウス202号室",
			PostCode:                  "2345678",
			PhoneNumber:               "09002345678",
		},
		{
			PharmacyDeliveryAddressID: 102,
			PharmacyReserveID:         3,
			DeliveryAddressID:         1002,
			Address1:                  "千葉県松戸市松戸123-45",
			Address2:                  "ザ・マンション1402号室",
			PostCode:                  "3456789",
			PhoneNumber:               "09003456789",
		},
	}
	db.Create(&deliveryAddresses)
	// =================================

	testCases := []struct {
		name          string
		ids           []int
		sort          *model.SortInput
		expected      []*custom.PharmacyDeliveryInfo
		expectedError error
	}{
		{
			name: "正常系　ソート指定無し",
			ids:  []int{1, 2, 3},
			sort: &model.SortInput{},
			expected: []*custom.PharmacyDeliveryInfo{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 1,
						PatientID:         32,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 0, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             32,
							PtNum:            32,
							KanaName:         "タナカ ヨウイチ",
							Name:             "田中 陽一",
							Sex:              1,
							Birthday:         19951011,
							PortalCustomerID: util.NewPtr(1),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 1,
							KanaName:   "タナカ ヨウイチ",
							Name:       "田中 陽一",
							Gender:     1,
							Telephone:  "08001234567",
							Birthday:   time.Date(1995, 10, 11, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 1,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 100,
						PharmacyReserveID:         1,
						DeliveryAddressID:         1000,
						Address1:                  "東京都江戸川区葛西123-45",
						Address2:                  "ハイタワーマンション3045号室",
						PostCode:                  "1234567",
						PhoneNumber:               "09001234567",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 2,
						PatientID:         33,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 1, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             33,
							PtNum:            33,
							KanaName:         "スズキ ショウタ",
							Name:             "鈴木 翔太",
							Sex:              1,
							Birthday:         19670919,
							PortalCustomerID: util.NewPtr(2),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 2,
							KanaName:   "スズキ ショウタ",
							Name:       "鈴木 翔太",
							Gender:     1,
							Telephone:  "08002345678",
							Birthday:   time.Date(1967, 9, 19, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 2,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 101,
						PharmacyReserveID:         2,
						DeliveryAddressID:         1001,
						Address1:                  "東京都世田谷区三軒茶屋123-45",
						Address2:                  "住良ハウス202号室",
						PostCode:                  "2345678",
						PhoneNumber:               "09002345678",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 3,
						PatientID:         34,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 2, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             34,
							PtNum:            34,
							KanaName:         "サトウ タカシ",
							Name:             "佐藤 隆",
							Sex:              1,
							Birthday:         19810628,
							PortalCustomerID: util.NewPtr(3),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 3,
							KanaName:   "サトウ タカシ",
							Name:       "佐藤 隆",
							Gender:     1,
							Telephone:  "08003456789",
							Birthday:   time.Date(1981, 6, 28, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 3,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 102,
						PharmacyReserveID:         3,
						DeliveryAddressID:         1002,
						Address1:                  "千葉県松戸市松戸123-45",
						Address2:                  "ザ・マンション1402号室",
						PostCode:                  "3456789",
						PhoneNumber:               "09003456789",
					},
				},
			},
		},
		{
			name: "正常系　ソート指定 nil",
			ids:  []int{1, 2, 3},
			sort: nil,
			expected: []*custom.PharmacyDeliveryInfo{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 1,
						PatientID:         32,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 0, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             32,
							PtNum:            32,
							KanaName:         "タナカ ヨウイチ",
							Name:             "田中 陽一",
							Sex:              1,
							Birthday:         19951011,
							PortalCustomerID: util.NewPtr(1),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 1,
							KanaName:   "タナカ ヨウイチ",
							Name:       "田中 陽一",
							Gender:     1,
							Telephone:  "08001234567",
							Birthday:   time.Date(1995, 10, 11, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 1,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 100,
						PharmacyReserveID:         1,
						DeliveryAddressID:         1000,
						Address1:                  "東京都江戸川区葛西123-45",
						Address2:                  "ハイタワーマンション3045号室",
						PostCode:                  "1234567",
						PhoneNumber:               "09001234567",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 2,
						PatientID:         33,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 1, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             33,
							PtNum:            33,
							KanaName:         "スズキ ショウタ",
							Name:             "鈴木 翔太",
							Sex:              1,
							Birthday:         19670919,
							PortalCustomerID: util.NewPtr(2),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 2,
							KanaName:   "スズキ ショウタ",
							Name:       "鈴木 翔太",
							Gender:     1,
							Telephone:  "08002345678",
							Birthday:   time.Date(1967, 9, 19, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 2,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 101,
						PharmacyReserveID:         2,
						DeliveryAddressID:         1001,
						Address1:                  "東京都世田谷区三軒茶屋123-45",
						Address2:                  "住良ハウス202号室",
						PostCode:                  "2345678",
						PhoneNumber:               "09002345678",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 3,
						PatientID:         34,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 2, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             34,
							PtNum:            34,
							KanaName:         "サトウ タカシ",
							Name:             "佐藤 隆",
							Sex:              1,
							Birthday:         19810628,
							PortalCustomerID: util.NewPtr(3),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 3,
							KanaName:   "サトウ タカシ",
							Name:       "佐藤 隆",
							Gender:     1,
							Telephone:  "08003456789",
							Birthday:   time.Date(1981, 6, 28, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 3,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 102,
						PharmacyReserveID:         3,
						DeliveryAddressID:         1002,
						Address1:                  "千葉県松戸市松戸123-45",
						Address2:                  "ザ・マンション1402号室",
						PostCode:                  "3456789",
						PhoneNumber:               "09003456789",
					},
				},
			},
		},
		{
			name: "正常系　ソート指定あり PharmacyReserveUpdateDate ASC",
			ids:  []int{1, 2, 3},
			sort: &model.SortInput{
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderAscend),
			},
			expected: []*custom.PharmacyDeliveryInfo{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 1,
						PatientID:         32,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 0, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             32,
							PtNum:            32,
							KanaName:         "タナカ ヨウイチ",
							Name:             "田中 陽一",
							Sex:              1,
							Birthday:         19951011,
							PortalCustomerID: util.NewPtr(1),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 1,
							KanaName:   "タナカ ヨウイチ",
							Name:       "田中 陽一",
							Gender:     1,
							Telephone:  "08001234567",
							Birthday:   time.Date(1995, 10, 11, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 1,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 100,
						PharmacyReserveID:         1,
						DeliveryAddressID:         1000,
						Address1:                  "東京都江戸川区葛西123-45",
						Address2:                  "ハイタワーマンション3045号室",
						PostCode:                  "1234567",
						PhoneNumber:               "09001234567",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 2,
						PatientID:         33,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 1, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             33,
							PtNum:            33,
							KanaName:         "スズキ ショウタ",
							Name:             "鈴木 翔太",
							Sex:              1,
							Birthday:         19670919,
							PortalCustomerID: util.NewPtr(2),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 2,
							KanaName:   "スズキ ショウタ",
							Name:       "鈴木 翔太",
							Gender:     1,
							Telephone:  "08002345678",
							Birthday:   time.Date(1967, 9, 19, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 2,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 101,
						PharmacyReserveID:         2,
						DeliveryAddressID:         1001,
						Address1:                  "東京都世田谷区三軒茶屋123-45",
						Address2:                  "住良ハウス202号室",
						PostCode:                  "2345678",
						PhoneNumber:               "09002345678",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 3,
						PatientID:         34,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 2, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             34,
							PtNum:            34,
							KanaName:         "サトウ タカシ",
							Name:             "佐藤 隆",
							Sex:              1,
							Birthday:         19810628,
							PortalCustomerID: util.NewPtr(3),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 3,
							KanaName:   "サトウ タカシ",
							Name:       "佐藤 隆",
							Gender:     1,
							Telephone:  "08003456789",
							Birthday:   time.Date(1981, 6, 28, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 3,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 102,
						PharmacyReserveID:         3,
						DeliveryAddressID:         1002,
						Address1:                  "千葉県松戸市松戸123-45",
						Address2:                  "ザ・マンション1402号室",
						PostCode:                  "3456789",
						PhoneNumber:               "09003456789",
					},
				},
			},
		},
		{
			name: "正常系　ソート指定あり PharmacyReserveUpdateDate DESC",
			ids:  []int{1, 2, 3},
			sort: &model.SortInput{
				PharmacyReserveUpdateDate: util.NewPtr(model.SortOrderDescend),
			},
			expected: []*custom.PharmacyDeliveryInfo{
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 3,
						PatientID:         34,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 2, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             34,
							PtNum:            34,
							KanaName:         "サトウ タカシ",
							Name:             "佐藤 隆",
							Sex:              1,
							Birthday:         19810628,
							PortalCustomerID: util.NewPtr(3),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 3,
							KanaName:   "サトウ タカシ",
							Name:       "佐藤 隆",
							Gender:     1,
							Telephone:  "08003456789",
							Birthday:   time.Date(1981, 6, 28, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 3,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 102,
						PharmacyReserveID:         3,
						DeliveryAddressID:         1002,
						Address1:                  "千葉県松戸市松戸123-45",
						Address2:                  "ザ・マンション1402号室",
						PostCode:                  "3456789",
						PhoneNumber:               "09003456789",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 2,
						PatientID:         33,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 1, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             33,
							PtNum:            33,
							KanaName:         "スズキ ショウタ",
							Name:             "鈴木 翔太",
							Sex:              1,
							Birthday:         19670919,
							PortalCustomerID: util.NewPtr(2),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 2,
							KanaName:   "スズキ ショウタ",
							Name:       "鈴木 翔太",
							Gender:     1,
							Telephone:  "08002345678",
							Birthday:   time.Date(1967, 9, 19, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 2,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 101,
						PharmacyReserveID:         2,
						DeliveryAddressID:         1001,
						Address1:                  "東京都世田谷区三軒茶屋123-45",
						Address2:                  "住良ハウス202号室",
						PostCode:                  "2345678",
						PhoneNumber:               "09002345678",
					},
				},
				{
					PharmacyReserve: entity.PharmacyReserve{
						PharmacyReserveID: 1,
						PatientID:         32,
						DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusBeforeConfigured,
						ReserveUpdateDate: time.Date(2024, 6, 24, 0, 0, 0, 0, time.Local),
						ReserveID:         util.NewPtr(1),
						SmsStatus:         constant.PharmacyReserveSMSStatusUnnotified,
						VideocallStatus:   constant.PharmacyReserveVideocallStatusNotYetEntered,
						PostalServiceType: constant.PharmacyReservePostalServiceTypeYuuPacket,
						CsvStatus:         constant.PharmacyReserveCsvStatusBeforeExport,
						PharmacistStatus:  constant.PharmacyReservePharmacistStatusBeforeConfigured,
						PharmacistID:      util.NewPtr(31),
					},
					Patient: &custom.PharmacyPatient{
						PtInf: entity.PtInf{
							HpID:             1,
							PtID:             32,
							PtNum:            32,
							KanaName:         "タナカ ヨウイチ",
							Name:             "田中 陽一",
							Sex:              1,
							Birthday:         19951011,
							PortalCustomerID: util.NewPtr(1),
						},
						Customer: entity.PortalCustomer{
							CustomerID: 1,
							KanaName:   "タナカ ヨウイチ",
							Name:       "田中 陽一",
							Gender:     1,
							Telephone:  "08001234567",
							Birthday:   time.Date(1995, 10, 11, 0, 0, 0, 0, time.Local),
						},
						CustomerLogin: &entity.PortalCustomerLogin{
							CustomerID: 1,
							Email:      "<EMAIL>",
						},
					},
					DeliveryAddress: &entity.PharmacyDeliveryAddress{
						PharmacyDeliveryAddressID: 100,
						PharmacyReserveID:         1,
						DeliveryAddressID:         1000,
						Address1:                  "東京都江戸川区葛西123-45",
						Address2:                  "ハイタワーマンション3045号室",
						PostCode:                  "1234567",
						PhoneNumber:               "09001234567",
					},
				},
			},
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			actual, err := testee.FindPharmacyDeliveryInfosByIDs(testCase.ids, testCase.sort)
			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}
			for i, v := range actual {
				assert.Equal(t, testCase.expected[i].PharmacyReserveID, v.PharmacyReserveID)
				assert.Equal(t, testCase.expected[i].DeliveryAddress.PostCode, v.DeliveryAddress.PostCode)
				assert.Equal(t, testCase.expected[i].DeliveryAddress.Address1, v.DeliveryAddress.Address1)
				assert.Equal(t, testCase.expected[i].DeliveryAddress.Address2, v.DeliveryAddress.Address2)
				assert.Equal(t, testCase.expected[i].Patient.Customer.Name, v.Patient.Customer.Name)
				assert.Equal(t, testCase.expected[i].Patient.Customer.Telephone, v.Patient.Customer.Telephone)
				assert.Equal(t, testCase.expected[i].Patient.CustomerLogin.Email, v.Patient.CustomerLogin.Email)
			}
		})
	}
}

func Test_UpdateVideoCallStatusByID(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 2,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 3,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 4,
			DesiredDateStatus: 0,
		},
		{
			PharmacyReserveID: 5,
			DesiredDateStatus: 0,
		},
	}
	db.Create(&pharmacyReserves)
	// =================================

	testCases := []struct {
		name              string
		pharmacyReserveID int
		videoCallStatus   int
		expectedError     error
		expectedUpdateAt  time.Time
	}{
		{
			name:              "正常系 更新",
			videoCallStatus:   constant.PharmacyReserveVideocallStatusDuringACall,
			pharmacyReserveID: 1,
			expectedUpdateAt:  time.Date(2024, 6, 18, 1, 23, 45, 0, time.Local),
		},
		{
			name:              "異常系 存在しないID",
			videoCallStatus:   constant.PharmacyReserveVideocallStatusDuringACall,
			pharmacyReserveID: 6,
			expectedError:     fmt.Errorf("affected rows are not 1. affected rows:[%v]", 0),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			util.Time = test_mock.NewTimeMock(testCase.expectedUpdateAt)
			ctx := test_mock.GetTestContextWithDB(db)
			testee := postgresql.NewPharmacyReserveRepository(db)
			err := testee.UpdateVideoCallStatusByID(ctx, testCase.pharmacyReserveID, testCase.videoCallStatus)

			if err != nil {
				assert.Equal(t, testCase.expectedError, err)
				return
			}

			var result *entity.PharmacyReserve
			db.Where("pharmacy_reserve_id = ?", testCase.pharmacyReserveID).First(&result)
			assert.Equal(t, testCase.videoCallStatus, result.VideocallStatus)
			assert.Equal(t, testCase.pharmacyReserveID, result.PharmacyReserveID)
		})
	}
}

func TestStringJoin(t *testing.T) {
	str := strings.Join(
		[]string{
			"",
			"input.KeywordFilter.Patient",
			"input.KeywordFilter.Customer",
		}, ",")

	t.Logf("\n\n join:[%s]\n\n", str)
}

func Test_FindPharmacyReservesByIDs_ReserveTimeSort(t *testing.T) {
	t.Parallel()
	db, cleanup := getTestDB(t)
	defer cleanup()

	setUpTablesForPharmacyReserve(db)
	defer tearDownTablesForPharmacyReserve(db)

	// テスト用日付
	year, month, day := time.Now().Date()
	birthday := year*10000 + int(month)*100 + day

	// ==========test data==============
	pharmacyReserves := []entity.PharmacyReserve{
		{
			PharmacyReserveID: 1,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 1, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(1),
		},
		{
			PharmacyReserveID: 2,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 2, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(2),
		},
		{
			PharmacyReserveID: 3,
			PatientID:         32,
			DesiredDateStatus: constant.PharmacyReserveDesiredDateStatusConfigured,
			ReserveUpdateDate: time.Date(2024, 6, 18, 3, 0, 0, 0, time.Local),
			ReserveID:         util.NewPtr(3),
		},
	}
	db.Create(&pharmacyReserves)

	pharmacyReserveDetails := []entity.PharmacyReserveDetail{
		{
			PharmacyReserveDetailID: 1,
			PharmacyReserveID:       1,
			PatientID:               32,
		},
		{
			PharmacyReserveDetailID: 2,
			PharmacyReserveID:       2,
			PatientID:               32,
		},
		{
			PharmacyReserveDetailID: 3,
			PharmacyReserveID:       3,
			PatientID:               32,
		},
	}
	db.Create(&pharmacyReserveDetails)

	ptInfs := []entity.PtInf{
		{
			HpID:             7,
			PtID:             32,
			PtNum:            32,
			KanaName:         "タナカ ヨウイチ",
			Name:             "田中 陽一",
			Sex:              1,
			Birthday:         birthday,
			PortalCustomerID: util.NewPtr(1),
		},
	}
	db.Create(&ptInfs)

	reserves := []entity.Reserve{
		{
			ReserveID: 1,
			PatientID: util.NewPtr(1),
		},
		{
			ReserveID: 2,
			PatientID: util.NewPtr(1),
		},
		{
			ReserveID: 3,
			PatientID: util.NewPtr(1),
		},
	}
	db.Create(&reserves)

	reserveDetails := []entity.ReserveDetail{
		{
			ReserveDetailID: 1,
			ReserveID:       1,
			PatientID:       util.NewPtr(1),
			ExamTimeSlotID:  1,
		},
		{
			ReserveDetailID: 2,
			ReserveID:       2,
			PatientID:       util.NewPtr(1),
			ExamTimeSlotID:  2,
		},
		{
			ReserveDetailID: 3,
			ReserveID:       3,
			PatientID:       util.NewPtr(1),
			ExamTimeSlotID:  3,
		},
	}
	db.Create(&reserveDetails)

	examTimeSlots := []entity.ExamTimeSlot{
		{
			ExamTimeSlotID: 1,
			ExamStartDate:  time.Date(2024, 6, 20, 10, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 20, 10, 30, 0, 0, time.Local),
		},
		{
			ExamTimeSlotID: 2,
			ExamStartDate:  time.Date(2024, 6, 19, 11, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 19, 11, 30, 0, 0, time.Local),
		},
		{
			ExamTimeSlotID: 3,
			ExamStartDate:  time.Date(2024, 6, 18, 14, 0, 0, 0, time.Local),
			ExamEndDate:    time.Date(2024, 6, 18, 14, 30, 0, 0, time.Local),
		},
	}
	db.Create(&examTimeSlots)
	// =================================

	testCases := []struct {
		name      string
		ids       []int
		sortInput *model.SortInput
		wantOrder []int
	}{
		{
			name: "正常系：ReserveTime_asc（診療予約日時の昇順）",
			ids:  []int{1, 2, 3},
			sortInput: &model.SortInput{
				ReserveTime: util.NewPtr(model.SortOrderAscend),
			},
			wantOrder: []int{3, 2, 1}, // ExamStartDate: 6/18, 6/19, 6/20
		},
		{
			name: "正常系：ReserveTime_desc（診療予約日時の降順）",
			ids:  []int{1, 2, 3},
			sortInput: &model.SortInput{
				ReserveTime: util.NewPtr(model.SortOrderDescend),
			},
			wantOrder: []int{1, 2, 3}, // ExamStartDate: 6/20, 6/19, 6/18
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			testee := postgresql.NewPharmacyReserveRepository(db)
			pharmacyReserves, err := testee.FindPharmacyReservesByIDs(tt.ids, tt.sortInput)

			assert.NoError(t, err)
			assert.Equal(t, len(tt.wantOrder), len(pharmacyReserves))

			var actualOrder []int
			for _, reserve := range pharmacyReserves {
				actualOrder = append(actualOrder, reserve.PharmacyReserveID)
			}

			assert.Equal(t, tt.wantOrder, actualOrder)
		})
	}
}

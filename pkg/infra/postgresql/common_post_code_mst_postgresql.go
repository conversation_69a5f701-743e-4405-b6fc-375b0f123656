package postgresql

import (
	"bytes"
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/logger"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/entity"
	"errors"
	"fmt"
	"os"
	"strings"

	"github.com/jackc/pgx/v5"
	"gorm.io/gorm"
)

type commonPostCodeMstRepository struct {
	DB *gorm.DB
}

// NewCommonPostCodeMstRepository return new ICommonPostCodeMstRepository
func NewCommonPostCodeMstRepository(db *gorm.DB) repository.ICommonPostCodeMstRepository {
	return &commonPostCodeMstRepository{
		DB: db,
	}
}

// TableName テーブル名を取得
// nolint: 対象外
func (r *commonPostCodeMstRepository) TableName() string {
	return entity.TableNamePostCodeMst
}

// GetIDColumn IDを取得する
// nolint: 対象外
func (r *commonPostCodeMstRepository) IDColumn() string {
	return "id"
}

// FindByPostCode 郵便番号から検索する
func (repo *commonPostCodeMstRepository) FindByPostCode(postCode string) (address []*entity.PostCodeMst, err error) {
	result := repo.DB.Where("post_cd = ? AND is_deleted = ?", postCode, constant.StatusFalse).Find(&address)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			address = nil
			return
		}
		err = result.Error
	}

	return address, err
}

// CreateByCopy inserts data into the database using COPY and manages table swapping
// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) CreateByCopy(ctx context.Context, values []string, columns []string, batchSize int) error {
	dsn := fmt.Sprintf("postgres://%s:%s@%s:%s/%s",
		os.Getenv("DB_USER"),
		os.Getenv("DB_PASSWORD"),
		os.Getenv("DB_HOST"),
		os.Getenv("DB_PORT"),
		os.Getenv("DB_NAME"),
	)

	conn, err := pgx.Connect(ctx, dsn)
	if err != nil {
		return fmt.Errorf("failed to connect to pgx: %w", err)
	}
	defer func() {
		closeErr := conn.Close(ctx)
		if closeErr != nil {
			err = errors.Join(err, closeErr)
		}
	}()

	tx, err := conn.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if p := recover(); p != nil {
			rollbackErr := tx.Rollback(ctx)
			if rollbackErr != nil {
				err = errors.Join(err, rollbackErr)
			}
			panic(p)
		} else if err != nil {
			rollbackErr := tx.Rollback(ctx)
			if rollbackErr != nil {
				err = errors.Join(err, rollbackErr)
			}
		} else {
			err = tx.Commit(ctx)
		}
	}()

	// Create temporary table
	tmpTableName, seq, err := r.createTmpPostCodeTable(ctx, conn, r.TableName())
	if err != nil {
		return err
	}

	// Perform COPY operation
	copySQL := fmt.Sprintf("COPY %s (%s) FROM STDIN", tmpTableName, strings.Join(columns, ","))
	logger.Log.Info(copySQL)
	var buffer bytes.Buffer
	batchCount := 0
	for _, row := range values {
		buffer.WriteString(row + "\n")
		batchCount++

		if batchCount >= batchSize {
			_, err = tx.Conn().PgConn().CopyFrom(ctx, bytes.NewReader(buffer.Bytes()), copySQL)
			if err != nil {
				return fmt.Errorf("failed to COPY data: %w", err)
			}
			buffer.Reset()
			batchCount = 0
		}
	}

	if batchCount > 0 {
		_, err = tx.Conn().PgConn().CopyFrom(ctx, bytes.NewReader(buffer.Bytes()), copySQL)
		if err != nil {
			return fmt.Errorf("failed to COPY data: %w", err)
		}
	}

	// Finalize table changes
	if err = r.finalizeTable(ctx, conn, r.TableName(), tmpTableName, seq); err != nil {
		return err
	}

	return nil
}

// nolint: 対象外
func (r *commonPostCodeMstRepository) finalizeTable(ctx context.Context, conn *pgx.Conn, mainTable, tmpTable, seq string) error {
	// Synchronize sequence
	if err := r.syncSequence(ctx, conn, seq, tmpTable); err != nil {
		return err
	}

	// Swap tables
	backupTable := fmt.Sprintf("%s_backup", mainTable)
	if err := r.renameTable(ctx, conn, mainTable, backupTable); err != nil {
		return err
	}
	if err := r.renameTable(ctx, conn, tmpTable, mainTable); err != nil {
		return err
	}

	// Drop backup table
	if err := r.deleteTable(ctx, conn, backupTable); err != nil {
		return err
	}

	return nil
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) createTmpPostCodeTable(ctx context.Context, conn *pgx.Conn, tableName string) (tmpTableName, seqName string, err error) {
	tmpTableName = fmt.Sprintf("%s_tmp", tableName)
	seqName = fmt.Sprintf("%s_%s_seq", tmpTableName, r.IDColumn())

	createTableSQL := fmt.Sprintf("CREATE TABLE %s (LIKE %s INCLUDING ALL)", tmpTableName, tableName)
	_, err = conn.Exec(ctx, createTableSQL)
	if err != nil {
		return "", "", fmt.Errorf("failed to create temporary table: %w", err)
	}

	if err := r.dropSeq(ctx, conn, seqName); err != nil {
		return "", "", err
	}
	if err := r.createSequence(ctx, conn, tmpTableName, r.IDColumn(), seqName); err != nil {
		return "", "", err
	}

	return tmpTableName, seqName, nil
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) createSequence(ctx context.Context, conn *pgx.Conn, table, column, seqName string) error {
	sequenceSQL := fmt.Sprintf(`
		CREATE SEQUENCE IF NOT EXISTS %s;
		ALTER TABLE %s ALTER COLUMN %s SET DEFAULT nextval('%s');
	`, seqName, table, column, seqName)
	_, err := conn.Exec(ctx, sequenceSQL)
	return err
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) syncSequence(ctx context.Context, conn *pgx.Conn, seqName, table string) error {
	syncSQL := fmt.Sprintf("SELECT setval('%s', COALESCE((SELECT MAX(%s) FROM %s) + 1, 1), false)", seqName, r.IDColumn(), table)
	_, err := conn.Exec(ctx, syncSQL)
	return err
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) renameTable(ctx context.Context, conn *pgx.Conn, oldName, newName string) error {
	_, err := conn.Exec(ctx, fmt.Sprintf("ALTER TABLE %s RENAME TO %s", oldName, newName))
	return err
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) deleteTable(ctx context.Context, conn *pgx.Conn, tableName string) error {
	_, err := conn.Exec(ctx, fmt.Sprintf("DROP TABLE IF EXISTS %s", tableName))
	return err
}

// nolint: pgxを使うので、対象外
func (r *commonPostCodeMstRepository) dropSeq(ctx context.Context, conn *pgx.Conn, seq string) error {
	_, err := conn.Exec(ctx, fmt.Sprintf("DROP SEQUENCE IF EXISTS %s CASCADE", seq))
	return err
}

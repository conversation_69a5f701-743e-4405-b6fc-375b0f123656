package postgresql_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/test_mock"

	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// nolint: 対象外
func setUpTablesForSurveyAnswerNoPatient(db *gorm.DB) {
	err := db.Migrator().CreateTable(&entity.SurveyAnswerNoPatient{})
	if err != nil {
		fmt.Printf("Failed CreateTable SurveyAnswerNoPatient. %v\n", err)
	}
	err = db.Migrator().CreateTable(&entity.Survey{})
	if err != nil {
		fmt.Printf("Failed CreateTable Survey. %v\n", err)
	}
	err = db.Migrator().CreateTable(&entity.SurveyAnswerNoPatientFiles{})
	if err != nil {
		fmt.Printf("Failed CreateTable SurveyAnswerNoPatientFiles. %v\n", err)
	}
}

// nolint: 対象外
func tearDownTablesForSurveyAnswerNoPatient(db *gorm.DB) {
	err := db.Migrator().DropTable(&entity.SurveyAnswerNoPatient{})
	if err != nil {
		fmt.Printf("Failed DropTable SurveyAnswerNoPatient. %v\n", err)
	}
	err = db.Migrator().DropTable(&entity.Survey{})
	if err != nil {
		fmt.Printf("Failed DropTable Survey. %v\n", err)
	}
}

// nolint: 対象外
func Test_SurveyAnswerNoPatient_Create_Read(t *testing.T) {
	db, cleanup := getTestDB(t)
	ctx := test_mock.GetTestContextWithDB(db)
	defer cleanup()

	setUpTablesForSurveyAnswerNoPatient(db)
	defer tearDownTablesForSurveyAnswerNoPatient(db)

	// surveyテーブルにダミーデータを挿入
	db.Create(&entity.Survey{
		SurveyID:   1,
		FQuesJSON:  "{}",
		Name:       "テスト問診票",
		HospitalID: 1,
		CreatedBy:  "test",
		UpdatedBy:  "test",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsDeleted:  0,
		Secret:     "secret1",
	})

	repo := postgresql.NewSurveyAnswerNoPatientRepository(db, nil)

	// Create
	entityObj := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer",
		KanaName:     "カナ",
		Name:         "名前",
		Birthday:     20000101,
		File:         `[{"FileName":"ファイル名","S3Key":"https://example.com/file.pdf"}]`,
	}
	id, err := repo.Create(ctx, entityObj)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotZero(t, id)

	// FindByIDAndHospitalID
	found, err := repo.FindByIDAndHospitalID(context.Background(), id, 1)
	assert.NoError(t, err)
	assert.Equal(t, entityObj.Name, found.Name)
	// ファイル情報はJSONB形式で保存されている
	assert.Contains(t, found.File, "ファイル名")
	assert.Contains(t, found.File, "https://example.com/file.pdf")
}

// nolint: 対象外
func Test_SurveyAnswerNoPatient_Create(t *testing.T) {
	db, cleanup := getTestDB(t)
	ctx := test_mock.GetTestContextWithDB(db)
	defer cleanup()
	setUpTablesForSurveyAnswerNoPatient(db)
	defer tearDownTablesForSurveyAnswerNoPatient(db)

	// surveyテーブルにダミーデータを挿入
	db.Create(&entity.Survey{
		SurveyID:   1,
		FQuesJSON:  "{}",
		Name:       "テスト問診票",
		HospitalID: 1,
		CreatedBy:  "test",
		UpdatedBy:  "test",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsDeleted:  0,
		Secret:     "secret1",
	})

	repo := postgresql.NewSurveyAnswerNoPatientRepository(db, nil)

	entity1 := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer1",
		KanaName:     "カナ1",
		Name:         "名前1",
		Birthday:     20000101,
		File:         "[]",
	}
	entity2 := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer2",
		KanaName:     "カナ2",
		Name:         "名前2",
		Birthday:     20000102,
		File:         "[]",
	}
	id1, err := repo.Create(ctx, entity1)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotZero(t, id1)
	id2, err := repo.Create(ctx, entity2)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotZero(t, id2)
	assert.Equal(t, id1+1, id2, "survey_answer_no_patient_id が連番になっていません。")
}

// nolint: 対象外
func Test_SurveyAnswerNoPatient_FindByIDAndHospitalID(t *testing.T) {
	db, cleanup := getTestDB(t)
	ctx := test_mock.GetTestContextWithDB(db)
	defer cleanup()
	setUpTablesForSurveyAnswerNoPatient(db)
	defer tearDownTablesForSurveyAnswerNoPatient(db)

	// surveyテーブルにダミーデータを挿入
	db.Create(&entity.Survey{
		SurveyID:   1,
		FQuesJSON:  "{}",
		Name:       "テスト問診票",
		HospitalID: 1,
		CreatedBy:  "test",
		UpdatedBy:  "test",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsDeleted:  0,
		Secret:     "secret1",
	})

	repo := postgresql.NewSurveyAnswerNoPatientRepository(db, nil)

	entity0 := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer0",
		KanaName:     "カナ0",
		Name:         "名前0",
		Birthday:     20000101,
		File:         "[]",
	}
	entity1 := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer1",
		KanaName:     "カナ1",
		Name:         "名前1",
		Birthday:     20000101,
		File:         "[]",
	}
	id0, err := repo.Create(ctx, entity0)
	if err != nil {
		t.Fatal(err)
	}
	id1, err := repo.Create(ctx, entity1)
	if err != nil {
		t.Fatal(err)
	}

	found, err := repo.FindByIDAndHospitalID(context.Background(), id0, 1)
	assert.NoError(t, err)
	assert.Equal(t, "名前0", found.Name)
	assert.Equal(t, "[]", found.File) // ファイル情報は空のJSONB配列

	found, err = repo.FindByIDAndHospitalID(context.Background(), id1, 1)
	assert.NoError(t, err)
	assert.NotNil(t, found, "isDeleted=1 でも survey_answer_no_patient_id を直接指定すれば検索できるはずです。")
	assert.Equal(t, "[]", found.File) // ファイル情報は空のJSONB配列
}

// nolint: 対象外
func Test_SurveyAnswerNoPatient_FindSurveyAnswerNoPatientList(t *testing.T) {
	db, cleanup := getTestDB(t)
	ctx := test_mock.GetTestContextWithDB(db)
	defer cleanup()
	setUpTablesForSurveyAnswerNoPatient(db)
	defer tearDownTablesForSurveyAnswerNoPatient(db)

	// surveyテーブルにダミーデータを挿入
	db.Create(&entity.Survey{
		SurveyID:   1,
		FQuesJSON:  "{}",
		Name:       "削除されてない問診票",
		HospitalID: 1,
		CreatedBy:  "test",
		UpdatedBy:  "test",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsDeleted:  0,
		Secret:     "secret1",
	})
	db.Create(&entity.Survey{
		SurveyID:   2,
		FQuesJSON:  "{}",
		Name:       "削除されている問診票",
		HospitalID: 1,
		CreatedBy:  "test",
		UpdatedBy:  "test",
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		IsDeleted:  0, // 作成時点で IsDeleted=1 にすると CreateBySecret で検索されないため、後で IsDeleted=1 にする。
		Secret:     "secret2",
	})

	repo := postgresql.NewSurveyAnswerNoPatientRepository(db, nil)

	now := time.Now()
	answerDate := now.Format("20060102")

	entity0 := &entity.SurveyAnswerNoPatient{
		SurveyID:     1,
		SurveyAnswer: "answer0",
		KanaName:     "カナ0",
		Name:         "名前0",
		Birthday:     20000101,
		File:         "[]",
	}
	entity1 := &entity.SurveyAnswerNoPatient{
		SurveyID:     2,
		SurveyAnswer: "answer1",
		KanaName:     "カナ1",
		Name:         "名前1",
		Birthday:     20000101,
		File:         "[]",
	}
	_, err := repo.Create(ctx, entity0)
	if err != nil {
		t.Fatal(err)
	}
	_, err = repo.Create(ctx, entity1)
	if err != nil {
		t.Fatal(err)
	}

	// 問診票２を削除する
	db.Model(&entity.Survey{}).Where("survey_id = ?", 2).Update("is_deleted", 1)

	// survey_id を指定した検索（削除されていない問診票）
	sid1 := 1
	results, err := repo.FindSurveyAnswerNoPatientList(answerDate, &sid1, 1)
	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, "名前0", results[0].Name)

	// survey_id を指定した検索（削除されている問診票）
	sid2 := 2
	results, err = repo.FindSurveyAnswerNoPatientList(answerDate, &sid2, 1)
	assert.NoError(t, err)
	assert.Len(t, results, 1, "surveyID を指定した場合は、survey.is_deleted=1 でも検索できるはずです")
	assert.Equal(t, "名前1", results[0].Name)

	// survey_id を指定しない検索
	results, err = repo.FindSurveyAnswerNoPatientList(answerDate, nil, 1)
	assert.NoError(t, err)
	assert.Len(t, results, 2, "surveyID == nil の場合は、survey.is_deletedに関係なく全ての回答が検索されるはずです")
}

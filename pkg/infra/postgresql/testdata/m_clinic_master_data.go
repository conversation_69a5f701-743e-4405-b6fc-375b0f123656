package testdata

import (
	"denkaru-server/pkg/repository/model/entity"
)

var baseHpID = 0

var MasterTables = []interface{}{
	&entity.AccountingFormMst{},
	&entity.AutoSanteiMst{},
	&entity.BuiOdrByomeiMst{},
	&entity.BuiOdrItemByomeiMst{},
	&entity.BuiOdrItemMst{},
	&entity.BuiOdrMst{},
	&entity.ByomeiMst{},
	&entity.CmtKbnMst{},
	&entity.DefHokenNo{},
	&entity.DensiHaihanCustom{},
	&entity.DensiHaihanDay{},
	&entity.DensiHaihanKarte{},
	&entity.DensiHaihanMonth{},
	&entity.DensiHaihanWeek{},
	&entity.DensiHojyo{},
	&entity.DensiHoukatu{},
	&entity.DensiHoukatuGrp{},
	&entity.DensiSanteiKaisu{},
	&entity.ExceptHokensya{},
	&entity.HokenMst{},
	&entity.HokensyaMst{},
	&entity.HolidayMst{},
	&entity.ItemGrpMst{},
	&entity.JihiSbtMst{},
	&entity.JobMst{},
	&entity.KaMst{},
	&entity.KarteKbnMst{},
	&entity.KensaMst{},
	&entity.LockMst{},
	&entity.PaymentMethodMst{},
	&entity.PostCodeMst{},
	&entity.PriorityHaihanMst{},
	&entity.RecedenCmtSelect{},
	&entity.RenkeiMst{},
	&entity.RenkeiTemplateMst{},

	&entity.SanteiAutoOrder{},
	&entity.SanteiAutoOrderDetail{},
	&entity.SanteiCntCheck{},
	&entity.SanteiGrpDetail{},
	&entity.SanteiGrpMst{},
	&entity.StaConf{},
	&entity.StaMenu{},
	&entity.SystemConf{},
	&entity.SystemConfItem{},
	&entity.SystemConfMenu{},
	&entity.SystemGenerationConf{},
	&entity.TekiouByomeiMst{},
	&entity.TenMst{},
	&entity.TenMstMother{},
	&entity.UketukeSbtMst{},
	&entity.YohoMst{},

	&entity.RaiinStatusMst{},
}

// AccountingFormMst 会計帳票マスタ
var AccountingFormMst = []*entity.AccountingFormMst{
	{
		HpID:   baseHpID,
		FormNo: 1,
	},
}

// AutoSanteiMst 自動算定マスタ
var AutoSanteiMst = []*entity.AutoSanteiMst{
	{
		HpID:   baseHpID,
		ItemCd: "*********",
		SeqNo:  1,
	},
}

// BuiOdrByomeiMst 部位オーダーチェックマスタ
var BuiOdrByomeiMst = []*entity.BuiOdrByomeiMst{
	{
		HpID:      baseHpID,
		BuiID:     1,
		ByomeiBui: "膝",
	},
}

// BuiOdrItemByomeiMst 部位項目チェックマスタ
var BuiOdrItemByomeiMst = []*entity.BuiOdrItemByomeiMst{
	{
		HpID:      baseHpID,
		ItemCd:    "*********",
		ByomeiBui: "手関節",
	},
}

// BuiOdrItemMst 部位項目チェックマスタ
var BuiOdrItemMst = []*entity.BuiOdrItemMst{
	{
		HpID:   baseHpID,
		ItemCd: "*********",
	},
}

// BuiOdrMst 部位オーダーチェックマスタ
var BuiOdrMst = []*entity.BuiOdrMst{
	{
		HpID:   baseHpID,
		BuiID:  1,
		OdrBui: "膝",
	},
}

// ByomeiMst 病名マスタ
var ByomeiMst = []*entity.ByomeiMst{
	{
		HpID:     baseHpID,
		ByomeiCd: "1202",
		Byomei:   "胃",
	},
}

// CmtKbnMst コメント区分マスタ
var CmtKbnMst = []*entity.CmtKbnMst{
	{
		HpID:   baseHpID,
		ItemCd: "CMT001",
	},
}

// DefHokenNo デフォルト保険番号マスタ
var DefHokenNo = []*entity.DefHokenNo{
	{
		HpID:   baseHpID,
		Digit1: "1",
	},
}

// DensiHaihanCustom 電子配合カスタムマスタ
var DensiHaihanCustom = []*entity.DensiHaihanCustom{
	{
		HpID:    baseHpID,
		ItemCd1: "ITEM1",
	},
}

// DensiHaihanDay 電子配合日マスタ
var DensiHaihanDay = []*entity.DensiHaihanDay{
	{
		HpID:    baseHpID,
		ItemCd1: "ITEM1",
	},
}

// DensiHaihanKarte 電子配合カルテマスタ
var DensiHaihanKarte = []*entity.DensiHaihanKarte{
	{
		HpID:    baseHpID,
		ItemCd1: "ITEM1",
	},
}

// DensiHaihanMonth 電子配合月マスタ
var DensiHaihanMonth = []*entity.DensiHaihanMonth{
	{
		HpID:    baseHpID,
		ItemCd1: "ITEM1",
	},
}

// DensiHaihanWeek 電子配合週マスタ
var DensiHaihanWeek = []*entity.DensiHaihanWeek{
	{
		HpID:    baseHpID,
		ItemCd1: "ITEM1",
	},
}

// DensiHojyo 電子補助マスタ
var DensiHojyo = []*entity.DensiHojyo{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// DensiHoukatu 電子包括マスタ
var DensiHoukatu = []*entity.DensiHoukatu{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// DensiHoukatuGrp 電子包括グループマスタ
var DensiHoukatuGrp = []*entity.DensiHoukatuGrp{
	{
		HpID:         baseHpID,
		HoukatuGrpNo: "GRP1",
		ItemCd:       "ITEM1",
	},
}

// DensiSanteiKaisu 電子算定回数マスタ
var DensiSanteiKaisu = []*entity.DensiSanteiKaisu{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// ExceptHokensya 除外保険者マスタ
var ExceptHokensya = []*entity.ExceptHokensya{
	{
		HpID:   baseHpID,
		PrefNo: 1,
	},
}

// HokenMst 保険マスタ
var HokenMst = []*entity.HokenMst{
	{
		HpID:       baseHpID,
		PrefNo:     1,
		HokenNo:    123456,
		HokenEdaNo: 1,
	},
}

// HokensyaMst 保険者マスタ
var HokensyaMst = []*entity.HokensyaMst{
	{
		HpID:       baseHpID,
		HokensyaNo: "HOKEN001",
	},
}

// HolidayMst 休日マスタ
var HolidayMst = []*entity.HolidayMst{
	{
		HpID:    baseHpID,
		SinDate: 20220101,
	},
}

// ItemGrpMst 項目グループマスタ
var ItemGrpMst = []*entity.ItemGrpMst{
	{
		HpID:   baseHpID,
		GrpSbt: 1,
	},
}

// JihiSbtMst 自費種別マスタ
var JihiSbtMst = []*entity.JihiSbtMst{
	{
		HpID:    baseHpID,
		JihiSbt: 1,
	},
}

// JobMst 職種マスタ
var JobMst = []*entity.JobMst{
	{
		HpID:  baseHpID,
		JobCd: 1,
	},
}

// KaMst 科マスタ
var KaMst = []*entity.KaMst{
	{
		HpID: baseHpID,
		KaID: 1,
	},
}

// KarteKbnMst カルテ区分マスタ
var KarteKbnMst = []*entity.KarteKbnMst{
	{
		HpID:     baseHpID,
		KarteKbn: 1,
	},
}

// KensaMst 検査マスタ
var KensaMst = []*entity.KensaMst{
	{
		HpID:           baseHpID,
		KensaItemCd:    "V0001",
		KensaItemSeqNo: 1,
	},
}

// LockMst ロックマスタ
var LockMst = []*entity.LockMst{
	{
		HpID:        baseHpID,
		FunctionCdA: "FUNC_A",
	},
}

// PaymentMethodMst 支払方法マスタ
var PaymentMethodMst = []*entity.PaymentMethodMst{
	{
		HpID:            baseHpID,
		PaymentMethodCd: 1,
	},
}

// PostCodeMst 郵便番号マスタ
var PostCodeMst = []*entity.PostCodeMst{
	{
		PostCd: "1000001",
	},
}

// PriorityHaihanMst 優先配合マスタ
var PriorityHaihanMst = []*entity.PriorityHaihanMst{
	{
		HpID:      baseHpID,
		HaihanGrp: 1,
	},
}

// RecedenCmtSelect レセ電コメント選択マスタ
var RecedenCmtSelect = []*entity.RecedenCmtSelect{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// RenkeiMst 連携マスタ
var RenkeiMst = []*entity.RenkeiMst{
	{
		HpID:     baseHpID,
		RenkeiID: 1,
	},
}

// RenkeiTemplateMst 連携テンプレートマスタ
var RenkeiTemplateMst = []*entity.RenkeiTemplateMst{
	{
		HpID:       baseHpID,
		TemplateID: 1,
	},
}

// SanteiAutoOrder 自動算定オーダーマスタ
var SanteiAutoOrder = []*entity.SanteiAutoOrder{
	{
		HpID:        baseHpID,
		SanteiGrpCd: 1,
	},
}

// SanteiAutoOrderDetail 自動算定オーダー明細マスタ
var SanteiAutoOrderDetail = []*entity.SanteiAutoOrderDetail{
	{
		HpID:        baseHpID,
		SanteiGrpCd: 1,
	},
}

// SanteiCntCheck 算定回数チェックマスタ
var SanteiCntCheck = []*entity.SanteiCntCheck{
	{
		HpID:        baseHpID,
		SanteiGrpCd: 1,
	},
}

// SanteiGrpDetail 算定グループ明細マスタ
var SanteiGrpDetail = []*entity.SanteiGrpDetail{
	{
		HpID:        baseHpID,
		SanteiGrpCd: 1,
	},
}

// SanteiGrpMst 算定グループマスタ
var SanteiGrpMst = []*entity.SanteiGrpMst{
	{
		HpID:        baseHpID,
		SanteiGrpCd: 1,
	},
}

// StaConf 設定マスタ
var StaConf = []*entity.StaConf{
	{
		HpID:   baseHpID,
		MenuID: 1,
	},
}

// StaMenu メニューマスタ
var StaMenu = []*entity.StaMenu{
	{
		HpID:   baseHpID,
		MenuID: 1,
	},
}

// SystemConf システム設定マスタ
var SystemConf = []*entity.SystemConf{
	{
		HpID:  baseHpID,
		GrpCd: 1,
	},
}

// SystemConfItem システム設定項目マスタ
var SystemConfItem = []*entity.SystemConfItem{
	{
		HpID:   baseHpID,
		MenuID: 1,
	},
}

// SystemConfMenu システム設定メニューマスタ
var SystemConfMenu = []*entity.SystemConfMenu{
	{
		HpID:   baseHpID,
		MenuID: 1,
	},
}

// SystemGenerationConf システム世代設定マスタ
var SystemGenerationConf = []*entity.SystemGenerationConf{
	{
		HpID:  baseHpID,
		GrpCd: 2001,
	},
}

// TekiouByomeiMst 適用病名マスタ
var TekiouByomeiMst = []*entity.TekiouByomeiMst{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// TenMst 点数マスタ
var TenMst = []*entity.TenMst{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// TenMstMother 点数マスタ母
var TenMstMother = []*entity.TenMstMother{
	{
		HpID:   baseHpID,
		ItemCd: "ITEM1",
	},
}

// UketukeSbtMst 受付種別マスタ
var UketukeSbtMst = []*entity.UketukeSbtMst{
	{
		HpID:  baseHpID,
		KbnID: 1,
	},
}

// YohoMst 用法マスタ
var YohoMst = []*entity.YohoMst{
	{
		HpID:   baseHpID,
		YohoCd: "YH001",
	},
}

var RaiinStatusMst = []*entity.RaiinStatusMst{
	{
		HpID:      baseHpID,
		StatusKbn: 0,
	},
}

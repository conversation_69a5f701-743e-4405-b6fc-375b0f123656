package postgresql

import (
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/infra/rdb"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type surveyRepository struct {
	DB *gorm.DB
}

// NewSurveyRepository return new SurveyRepository
func NewSurveyRepository(db *gorm.DB) repository.ISurveyRepository {
	return &surveyRepository{
		DB: db,
	}
}

// nolint: 複数回のSQL発行が避けられない。
func (repo *surveyRepository) FindSurveys(ctx context.Context, hospitalId int, keyword string, limit *int, cursor *int64, date *string) (surveys []*custom.Survey, err error) {
	var entitySurveys []*entity.Survey
	result := repo.DB.Table("survey").
		Order("created_at").
		Where("hospital_id = ?", hospitalId)

	if keyword != "" {
		likeQuery := fmt.Sprintf("%%%s%%", keyword)
		result = result.Where("name LIKE ?", likeQuery)
	}

	if date != nil {
		// まずis_deleted=0のデータを取得
		var activeSurveys []*entity.Survey
		activeResult := result.Session(&gorm.Session{}).Where("is_deleted = ?", constant.StatusFalse)
		if cursor != nil {
			activeResult = activeResult.Where("survey.survey_id > ?", cursor)
		}
		if limit != nil {
			activeResult = activeResult.Limit(*limit)
		}
		if err := activeResult.Find(&activeSurveys).Error; err != nil {
			return nil, err
		}

		// is_deleted=1 かつ created_at < date <= updated_at のデータを取得
		var deletedSurveys []*entity.Survey

		// date文字列から日付の開始を生成（JSTで）
		parsedDate, err := time.ParseInLocation("20060102", *date, util.JSTLocation)
		if err != nil {
			return nil, fmt.Errorf("failed to parse date: %w", err)
		}
		// 0時0分0秒に設定
		startOfDay := time.Date(parsedDate.Year(), parsedDate.Month(), parsedDate.Day(), 0, 0, 0, 0, util.JSTLocation)
		dateTimeStart := startOfDay.Format("2006-01-02 15:04:05")

		// survey.created_at <= date < survey.updated_at
		deletedResult := result.Session(&gorm.Session{}).
			Where("is_deleted = ?", constant.StatusTrue).
			Where("date(survey.created_at) <= date(to_timestamp(?, 'YYYY-MM-DD HH24:MI:SS')) AND date(to_timestamp(?, 'YYYY-MM-DD HH24:MI:SS')) <= date(survey.updated_at)",
				dateTimeStart, dateTimeStart)

		if cursor != nil {
			deletedResult = deletedResult.Where("survey.survey_id > ?", cursor)
		}
		if limit != nil {
			deletedResult = deletedResult.Limit(*limit)
		}
		if err := deletedResult.Find(&deletedSurveys).Error; err != nil {
			return nil, err
		}

		entitySurveys = append(activeSurveys, deletedSurveys...)
	} else {
		// dateがない場合はis_deleted=0のみ
		if cursor != nil {
			result = result.Where("survey.survey_id > ?", cursor)
		}
		if limit != nil {
			result = result.Limit(*limit)
		}
		if err := result.Where("is_deleted = ?", constant.StatusFalse).Find(&entitySurveys).Error; err != nil {
			return nil, err
		}
	}

	surveys = make([]*custom.Survey, 0, len(entitySurveys))
	for _, e := range entitySurveys {
		surveys = append(surveys, &custom.Survey{
			Survey: *e,
		})
	}
	return surveys, nil
}

// FindSurveyByID IDからアンケートを検索
// includeDeleted: 指定しない場合やfalseの場合は削除されていないレコードのみ検索、trueの場合は削除されたレコードも含めて検索
func (repo *surveyRepository) FindSurveyByID(id int, includeDeleted ...bool) (entity.Survey, error) {
	var survey entity.Survey
	query := repo.DB

	// includeDeleted が指定されていない、または false の場合は削除されていないレコードに限定
	var shouldIncludeDeleted bool
	if len(includeDeleted) > 0 && includeDeleted[0] {
		shouldIncludeDeleted = true
	}

	if !shouldIncludeDeleted {
		query = query.Where("is_deleted = ?", constant.StatusFalse)
	}

	result := query.Find(&survey, id)

	if result.RowsAffected == 0 {
		return survey, gorm.ErrRecordNotFound
	}

	return survey, result.Error
}

func (repo *surveyRepository) CreateSurvey(ctx context.Context, survey *entity.Survey) (*entity.Survey, error) {
	tx := rdb.GetTransaction(ctx)
	result := tx.Create(survey)
	if result.Error != nil {
		return nil, result.Error
	}
	if err := updateSecret(tx, survey); err != nil {
		return nil, err
	}
	return survey, nil
}

// nolint: プライベート関数なので除外
func updateSecret(tx *gorm.DB, survey *entity.Survey) error {
	survey.Secret = util.GenerateSurveyHash(survey.SurveyID, survey.HospitalID)
	return tx.Save(survey).Error
}

func (repo *surveyRepository) UpdateSurvey(ctx context.Context, survey *entity.Survey) (*entity.Survey, error) {
	tx := rdb.GetTransaction(ctx)

	result := tx.
		Where("survey_id = ?", survey.SurveyID).
		Updates(&entity.Survey{Name: survey.Name, FQuesJSON: survey.FQuesJSON})

	if result.RowsAffected == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	return survey, result.Error
}

func (repo *surveyRepository) RemoveSurvey(id int) error {
	result := repo.DB.
		Model(&entity.Survey{}).
		Where("survey_id = ?", id).
		Update("is_deleted", constant.StatusTrue)

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return result.Error
}

func (repo *surveyRepository) FindSurveyBySurveyIdAndHospitalId(surveyId int, hospitalId int) (survey *entity.Survey, err error) {
	result := repo.DB.
		Where("survey_id = ?", surveyId).
		Where("hospital_id = ?", hospitalId).
		Where("is_deleted = ?", constant.StatusFalse).
		Find(&survey)

	if result.RowsAffected == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	return survey, result.Error
}

func (repo *surveyRepository) FindSurveyBySurveyNameAndHospitalId(surveyName string, hospitalId int) (survey *entity.Survey, err error) {
	result := repo.DB.
		Where("name = ?", surveyName).
		Where("hospital_id = ?", hospitalId).
		Where("is_deleted = ?", constant.StatusFalse).
		Find(&survey)

	if result.RowsAffected == 0 {
		return nil, gorm.ErrRecordNotFound
	}

	return survey, result.Error
}

// FindSurveyBySurveyID survey_idで1件取得
func (repo *surveyRepository) FindSurveyBySurveyID(surveyID int) (*entity.Survey, error) {
	var survey entity.Survey
	result := repo.DB.Table("survey").
		Where("survey_id = ?", surveyID).
		Where("is_deleted = ?", constant.StatusFalse).
		First(&survey)
	if result.Error != nil {
		return nil, result.Error
	}
	return &survey, nil
}

// FindSurveyBySecret はsecretから該当Surveyを返す
func (repo *surveyRepository) FindSurveyBySecret(ctx context.Context, secret string) (*custom.Survey, error) {
	var surveyWithClinicName custom.Survey
	result := repo.DB.Table("survey").
		Select("survey.*, hp_inf.hp_name as clinic_name").
		Joins("INNER JOIN hp_inf ON survey.hospital_id = hp_inf.hp_id").
		Where("survey.secret = ?", secret).
		Where("survey.is_deleted = ?", constant.StatusFalse).
		First(&surveyWithClinicName)
	if result.Error != nil {
		return nil, errors.New(util.ErrSurveyNotFound)
	}

	return &surveyWithClinicName, nil
}

// Code generated by MockGen. DO NOT EDIT.
// Source: service/zendesk_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	model "denkaru-server/pkg/adapter/graphql/model"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIZendeskService is a mock of IZendeskService interface.
type MockIZendeskService struct {
	ctrl     *gomock.Controller
	recorder *MockIZendeskServiceMockRecorder
}

// MockIZendeskServiceMockRecorder is the mock recorder for MockIZendeskService.
type MockIZendeskServiceMockRecorder struct {
	mock *MockIZendeskService
}

// NewMockIZendeskService creates a new mock instance.
func NewMockIZendeskService(ctrl *gomock.Controller) *MockIZendeskService {
	mock := &MockIZendeskService{ctrl: ctrl}
	mock.recorder = &MockIZendeskServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIZendeskService) EXPECT() *MockIZendeskServiceMockRecorder {
	return m.recorder
}

// CreateSsoEndPoint mocks base method.
func (m *MockIZendeskService) CreateSsoEndPoint(ctx context.Context, hospitalID, firstPage int) (*model.GetZendeskSsoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSsoEndPoint", ctx, hospitalID, firstPage)
	ret0, _ := ret[0].(*model.GetZendeskSsoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSsoEndPoint indicates an expected call of CreateSsoEndPoint.
func (mr *MockIZendeskServiceMockRecorder) CreateSsoEndPoint(ctx, hospitalID, firstPage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSsoEndPoint", reflect.TypeOf((*MockIZendeskService)(nil).CreateSsoEndPoint), ctx, hospitalID, firstPage)
}

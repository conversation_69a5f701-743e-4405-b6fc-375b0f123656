// Code generated by MockGen. DO NOT EDIT.
// Source: address_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	entity "denkaru-server/pkg/repository/model/entity"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIAddressService is a mock of IAddressService interface.
type MockIAddressService struct {
	ctrl     *gomock.Controller
	recorder *MockIAddressServiceMockRecorder
}

// MockIAddressServiceMockRecorder is the mock recorder for MockIAddressService.
type MockIAddressServiceMockRecorder struct {
	mock *MockIAddressService
}

// NewMockIAddressService creates a new mock instance.
func NewMockIAddressService(ctrl *gomock.Controller) *MockIAddressService {
	mock := &MockIAddressService{ctrl: ctrl}
	mock.recorder = &MockIAddressServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAddressService) EXPECT() *MockIAddressServiceMockRecorder {
	return m.recorder
}

// GetAddressByPostcode mocks base method.
func (m *MockIAddressService) GetAddressByPostcode(postcode string) ([]*entity.PostCodeMst, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddressByPostcode", postcode)
	ret0, _ := ret[0].([]*entity.PostCodeMst)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddressByPostcode indicates an expected call of GetAddressByPostcode.
func (mr *MockIAddressServiceMockRecorder) GetAddressByPostcode(postcode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddressByPostcode", reflect.TypeOf((*MockIAddressService)(nil).GetAddressByPostcode), postcode)
}

// GetPrefectures mocks base method.
func (m *MockIAddressService) GetPrefectures(ctx context.Context) ([]*entity.PortalMPrefecture, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrefectures", ctx)
	ret0, _ := ret[0].([]*entity.PortalMPrefecture)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrefectures indicates an expected call of GetPrefectures.
func (mr *MockIAddressServiceMockRecorder) GetPrefectures(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrefectures", reflect.TypeOf((*MockIAddressService)(nil).GetPrefectures), ctx)
}

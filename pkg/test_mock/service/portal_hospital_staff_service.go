// Code generated by MockGen. DO NOT EDIT.
// Source: service/portal_hospital_staff_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	model "denkaru-server/pkg/adapter/graphql/model"
	entity "denkaru-server/pkg/repository/model/entity"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIPortalHospitalStaffService is a mock of IPortalHospitalStaffService interface.
type MockIPortalHospitalStaffService struct {
	ctrl     *gomock.Controller
	recorder *MockIPortalHospitalStaffServiceMockRecorder
}

// MockIPortalHospitalStaffServiceMockRecorder is the mock recorder for MockIPortalHospitalStaffService.
type MockIPortalHospitalStaffServiceMockRecorder struct {
	mock *MockIPortalHospitalStaffService
}

// NewMockIPortalHospitalStaffService creates a new mock instance.
func NewMockIPortalHospitalStaffService(ctrl *gomock.Controller) *MockIPortalHospitalStaffService {
	mock := &MockIPortalHospitalStaffService{ctrl: ctrl}
	mock.recorder = &MockIPortalHospitalStaffServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPortalHospitalStaffService) EXPECT() *MockIPortalHospitalStaffServiceMockRecorder {
	return m.recorder
}

// CreatePortalHospitalStaff mocks base method.
func (m *MockIPortalHospitalStaffService) CreatePortalHospitalStaff(ctx context.Context, input model.CreatePortalHospitalStaffInput) (*model.CreatePortalStaffRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePortalHospitalStaff", ctx, input)
	ret0, _ := ret[0].(*model.CreatePortalStaffRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePortalHospitalStaff indicates an expected call of CreatePortalHospitalStaff.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) CreatePortalHospitalStaff(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePortalHospitalStaff", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).CreatePortalHospitalStaff), ctx, input)
}

// DeletePortalHospitalStaff mocks base method.
func (m *MockIPortalHospitalStaffService) DeletePortalHospitalStaff(ctx context.Context, input model.DeletePortalHospitalStaffInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePortalHospitalStaff", ctx, input)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePortalHospitalStaff indicates an expected call of DeletePortalHospitalStaff.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) DeletePortalHospitalStaff(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePortalHospitalStaff", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).DeletePortalHospitalStaff), ctx, input)
}

// EditPortalHospitalStaff mocks base method.
func (m *MockIPortalHospitalStaffService) EditPortalHospitalStaff(ctx context.Context, input model.EditPortalHospitalStaffInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditPortalHospitalStaff", ctx, input)
	ret0, _ := ret[0].(error)
	return ret0
}

// EditPortalHospitalStaff indicates an expected call of EditPortalHospitalStaff.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) EditPortalHospitalStaff(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditPortalHospitalStaff", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).EditPortalHospitalStaff), ctx, input)
}

// GetPortalHospitalStaff mocks base method.
func (m *MockIPortalHospitalStaffService) GetPortalHospitalStaff(ctx context.Context, staffID int) (*model.GetPortalHospitalStaffRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPortalHospitalStaff", ctx, staffID)
	ret0, _ := ret[0].(*model.GetPortalHospitalStaffRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortalHospitalStaff indicates an expected call of GetPortalHospitalStaff.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) GetPortalHospitalStaff(ctx, staffID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortalHospitalStaff", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).GetPortalHospitalStaff), ctx, staffID)
}

// GetPortalHospitalStaffUploadFileUrls mocks base method.
func (m *MockIPortalHospitalStaffService) GetPortalHospitalStaffUploadFileUrls(ctx context.Context, hospitalStaffID int, input []*model.GetPortalHospitalStaffUploadFileURLInput) ([]*model.GetPortalHospitalStaffUploadFileURLRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPortalHospitalStaffUploadFileUrls", ctx, hospitalStaffID, input)
	ret0, _ := ret[0].([]*model.GetPortalHospitalStaffUploadFileURLRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortalHospitalStaffUploadFileUrls indicates an expected call of GetPortalHospitalStaffUploadFileUrls.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) GetPortalHospitalStaffUploadFileUrls(ctx, hospitalStaffID, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortalHospitalStaffUploadFileUrls", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).GetPortalHospitalStaffUploadFileUrls), ctx, hospitalStaffID, input)
}

// GetPortalHospitalStaffs mocks base method.
func (m *MockIPortalHospitalStaffService) GetPortalHospitalStaffs(ctx context.Context) ([]*entity.PortalHospitalStaff, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPortalHospitalStaffs", ctx)
	ret0, _ := ret[0].([]*entity.PortalHospitalStaff)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortalHospitalStaffs indicates an expected call of GetPortalHospitalStaffs.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) GetPortalHospitalStaffs(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortalHospitalStaffs", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).GetPortalHospitalStaffs), ctx)
}

// GetPortalStaffFileURL mocks base method.
func (m *MockIPortalHospitalStaffService) GetPortalStaffFileURL(ctx context.Context, input model.GetPortalStaffFileURLInput) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPortalStaffFileURL", ctx, input)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPortalStaffFileURL indicates an expected call of GetPortalStaffFileURL.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) GetPortalStaffFileURL(ctx, input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPortalStaffFileURL", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).GetPortalStaffFileURL), ctx, input)
}

// SortPortalHospitalStaffs mocks base method.
func (m *MockIPortalHospitalStaffService) SortPortalHospitalStaffs(arg0 context.Context, arg1 *model.SortPortalHospitalStaffsInput) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortPortalHospitalStaffs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SortPortalHospitalStaffs indicates an expected call of SortPortalHospitalStaffs.
func (mr *MockIPortalHospitalStaffServiceMockRecorder) SortPortalHospitalStaffs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortPortalHospitalStaffs", reflect.TypeOf((*MockIPortalHospitalStaffService)(nil).SortPortalHospitalStaffs), arg0, arg1)
}

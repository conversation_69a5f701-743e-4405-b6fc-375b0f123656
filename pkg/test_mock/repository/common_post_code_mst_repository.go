// Code generated by MockGen. DO NOT EDIT.
// Source: common_post_code_mst_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	entity "denkaru-server/pkg/repository/model/entity"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockICommonPostCodeMstRepository is a mock of ICommonPostCodeMstRepository interface.
type MockICommonPostCodeMstRepository struct {
	ctrl     *gomock.Controller
	recorder *MockICommonPostCodeMstRepositoryMockRecorder
}

// MockICommonPostCodeMstRepositoryMockRecorder is the mock recorder for MockICommonPostCodeMstRepository.
type MockICommonPostCodeMstRepositoryMockRecorder struct {
	mock *MockICommonPostCodeMstRepository
}

// NewMockICommonPostCodeMstRepository creates a new mock instance.
func NewMockICommonPostCodeMstRepository(ctrl *gomock.Controller) *MockICommonPostCodeMstRepository {
	mock := &MockICommonPostCodeMstRepository{ctrl: ctrl}
	mock.recorder = &MockICommonPostCodeMstRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICommonPostCodeMstRepository) EXPECT() *MockICommonPostCodeMstRepositoryMockRecorder {
	return m.recorder
}

// CreateByCopy mocks base method.
func (m *MockICommonPostCodeMstRepository) CreateByCopy(ctx context.Context, values, columns []string, batchSize int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateByCopy", ctx, values, columns, batchSize)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateByCopy indicates an expected call of CreateByCopy.
func (mr *MockICommonPostCodeMstRepositoryMockRecorder) CreateByCopy(ctx, values, columns, batchSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateByCopy", reflect.TypeOf((*MockICommonPostCodeMstRepository)(nil).CreateByCopy), ctx, values, columns, batchSize)
}

// FindByPostCode mocks base method.
func (m *MockICommonPostCodeMstRepository) FindByPostCode(postCode string) ([]*entity.PostCodeMst, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByPostCode", postCode)
	ret0, _ := ret[0].([]*entity.PostCodeMst)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByPostCode indicates an expected call of FindByPostCode.
func (mr *MockICommonPostCodeMstRepositoryMockRecorder) FindByPostCode(postCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByPostCode", reflect.TypeOf((*MockICommonPostCodeMstRepository)(nil).FindByPostCode), postCode)
}

// IDColumn mocks base method.
func (m *MockICommonPostCodeMstRepository) IDColumn() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IDColumn")
	ret0, _ := ret[0].(string)
	return ret0
}

// IDColumn indicates an expected call of IDColumn.
func (mr *MockICommonPostCodeMstRepositoryMockRecorder) IDColumn() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IDColumn", reflect.TypeOf((*MockICommonPostCodeMstRepository)(nil).IDColumn))
}

// TableName mocks base method.
func (m *MockICommonPostCodeMstRepository) TableName() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TableName")
	ret0, _ := ret[0].(string)
	return ret0
}

// TableName indicates an expected call of TableName.
func (mr *MockICommonPostCodeMstRepositoryMockRecorder) TableName() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TableName", reflect.TypeOf((*MockICommonPostCodeMstRepository)(nil).TableName))
}

// Package constant 定数管理ファイル
package constant

import "time"

const (
	// StatusFalse false
	StatusFalse = 0
	// StatusTrue true
	StatusTrue = 1
)

const (
	// BearerPrefix bearerトークンのプレフィックス
	BearerPrefix = "Bearer "
)

// TxKey Transaction Key
type TxKey string

const (
	// ContextKeyTransaction TransactionのContext Key
	ContextKeyTransaction TxKey = "db_transaction"
)

// TxPgxKey pgx用Transaction Key
type TxPgxKey string

const (
	// ContextKeyPgxTransaction pgx用TransactionのContext Key
	ContextKeyPgxTransaction TxPgxKey = "pgx_transaction"
)

// EchoKey Echo Key
type EchoKey string

const (
	// ContextKeyEcho  EchoのContext Key
	ContextKeyEcho EchoKey = "echo_context_key"
)

// SessionKey session key
type SessionKey string

const (
	// ContextKeySession ...
	ContextKeySession SessionKey = "session_object"
)

type PermissionKey string

const (
	// ContextKeyPermission ...
	ContextKeyPermission PermissionKey = "permission_key"
)

const (
	// ClientCaCnHeader cookieのセッションキー
	ClientCaCnHeader    = "denkaru-cn-header"
	ClientCustomReferer = "X-Custom-Referer"
)

const (
	// InternalAPIKeyHeader  内部通信用のAPIキー
	InternalAPIKeyHeader = "X-Internal-API-Key"
)

// JWT関連 (HTTP Only Cookie)
const (
	DenkaruCookiePrefix = "denkaru_"
	DenkaruAccessToken  = DenkaruCookiePrefix + "accessToken"
	DenkaruRefreshToken = DenkaruCookiePrefix + "refreshToken"
)

// CSオペレーター用Cookie設定値
const (
	DenkaruOperatorIdToken      = DenkaruCookiePrefix + "operatorIdToken"
	DenkaruOperatorRefreshToken = DenkaruCookiePrefix + "operatorRefreshToken"

	DenkaruOperatorHospitalID  = DenkaruCookiePrefix + "operatorHospitalID"
	DenkaruOperatorStaffID     = DenkaruCookiePrefix + "operatorStaffID"
	DenkaruOperatorName        = DenkaruCookiePrefix + "operatorName"
	DenkaruOperatorValuesTTL   = 60 * time.Minute
	DenkaruOperatorKarteStatus = DenkaruCookiePrefix + "operatorKarteStatus"
)

const (
	// ChannelTypeNormal チャンネル種別 チャンネル
	ChannelTypeNormal = 1

	// ChannelTypeDirectMessage チャンネル種別 ダイレクトメッセージ
	ChannelTypeDirectMessage = 2
)

const (
	// PatientSendableDisable ... PatientSendableDisable　患者へのメッセージ送信可否　無効
	PatientSendableDisable = 0

	// PatientSendableEnable 患者へのメッセージ送信可否　有効
	PatientSendableEnable = 1
)

// MessageType メッセージ種別
const (
	// MessageTypeText メッセージ種別 テキスト
	MessageTypeText = "text"

	// MessageTypeImage メッセージ種別 画像
	MessageTypeImage = "image"

	// MessageTypeFile メッセージ種別 ファイル
	MessageTypeFile = "file"
)

// NotifyType 通知種別
type NotifyType string

const (
	// NotifyTypeNew 通知種別 テキスト
	NotifyTypeNew NotifyType = "new"

	// NotifyTypeEdit 通知種別 編集
	NotifyTypeEdit NotifyType = "edit"

	// NotifyTypeDelete 通知種別 削除
	NotifyTypeDelete NotifyType = "delete"

	// NotifyTypeAlreadyReadByPatient 通知種別 患者既読
	NotifyTypeAlreadyReadByPatient NotifyType = "alreadyReadByPatient"
)

// EventType イベントの種類
type EventType string

const (
	// EventTypePDFGenerate カルテPDF生成
	EventTypePDFGenerate EventType = "PDFGenerate"

	// EventTypeStaffMessaging スタッフ間のメッセージング（チャット）
	EventTypeStaffMessaging EventType = "staffMessaging"

	// EventTypePatientMessaging 患者-スタッフ間のメッセージング（チャット）
	EventTypePatientMessaging EventType = "patientMessaging"

	// EventTypeStaffChannel スタッフ間のメッセージングチャンネル
	EventTypeStaffChannel EventType = "staffChannel"

	// EventTypePatientChannel 患者-スタッフ間のメッセージングチャンネル
	EventTypePatientChannel EventType = "patientChannel"

	// EventTypeMail メール
	EventTypeMail EventType = "mail"

	// EventTypeSendSMS SMS送信
	EventTypeSendSMS EventType = "SendSMS"

	// EventTypeFax Fax
	EventTypeFax EventType = "fax"

	// EventTypeHospitalSearch 病院探索
	EventTypeHospitalSearch EventType = "hospitalSearch"

	// EventTypeLine LINE
	EventTypeLine EventType = "line"
)

// QueueType キューの種類 インフラの設定と合わせる必要がある
type QueueType string

const (
	// QueueTypeStandard 標準キュー
	QueueTypeStandard QueueType = "standard"

	// QueueTypeFIFO FIFO (first-in-first-out) キュー
	QueueTypeFIFO QueueType = "fifo"
)

const (
	// StaffPubSubKeyTemplate スタッフ用 Pub/Sub Key テンプレート
	StaffPubSubKeyTemplate = "staff_%v_%v"

	// PatientPubSubKeyTemplate 患者用 Pub/Sub Key テンプレート
	PatientPubSubKeyTemplate = "patient_%v"
)

const (
	// PostedMemberSystem ...　systemによる投稿時にpostedMemberとして使われる
	PostedMemberSystem = "system"
)

// Constants representing different server types
const (
	DirectMessageNameDelimiter = "_"
	NormalChannelMaxMembersNum = 50
	NormalChannelMaxNumber     = 100

	// NotificationTxWaitTime 投稿者のトランザクションを待つ時間
	NotificationTxWaitTime = 100 * time.Millisecond
)

// OpenSearch関連
const (
	// OpenSearchIndexStaffMessage staffメッセージ用のElasticSearchのIndex
	OpenSearchIndexStaffMessage = "staff-message-%v"

	// OpenSearchIndexPatientMessage patientメッセージ用のElasticSearchのIndex
	OpenSearchIndexPatientMessage = "patient-message-%v"

	// OpenSearchCreateAtUnixMilliToTimestampPipeline unix_milliをtimestamp形式に変換するパイプライン
	OpenSearchCreateAtUnixMilliToTimestampPipeline = "createAt_unix_milli_to_timestamp"

	// OpenSearchIndexPortalHospital 予約サイトで病院検索用のElasticSearchのIndex
	OpenSearchIndexPortalHospital = "hospital"

	// ExamTimeSlot Integration Number
	ExamTimeSlotIntegration = 1000000
)

// Lambda関連
const (

	// LambdaSSMParameterStoreExtensionURL AWSの固定値なので、環境変数にしない
	// 参考: https://docs.aws.amazon.com/ja_jp/systems-manager/latest/userguide/ps-integration-lambda-extensions.html
	LambdaSSMParameterStoreExtensionURL = "http://localhost:2773/systemsmanager/parameters/get"

	// LambdaAWSParametersSecretsTokenHeader SSMParameterStoreExtensionから値を取得するために要設定のHeader
	LambdaAWSParametersSecretsTokenHeader = "X-Aws-Parameters-Secrets-Token"

	// LambdaAWSSessionTokenEnvName AWS_SESSION_TOKENはLambdaにデフォルトで設定されてる
	LambdaAWSSessionTokenEnvName = "AWS_SESSION_TOKEN"
)

// ServerType サーバー種別
type ServerType string

// Constants representing different server typecm.user_id = ?s
const (
	ServerTypeAPIServer            ServerType = "API_SERVER"
	ServerTypeNotificationServer   ServerType = "NOTIFICATION_SERVER"
	ServerTypeScheduledBatchServer ServerType = "SCHEDULED_BATCH"
	ServerTypeDevTools             ServerType = "DEVELOP_TOOLS"
	ServerTypePDFGen               ServerType = "PDF_GEN"
	ServerTypeETLToElasticSearch   ServerType = "ETL_TO_ELASTIC_SEARCH"
	ServerTypeSendSMS              ServerType = "SEND_SMS"
	ServerTypeSendSMSSecondary     ServerType = "SEND_SMS_SECONDARY"
	ServerTypeRemindSMS            ServerType = "REMIND_SMS"
	ServerTypeMail                 ServerType = "MAIL"
	ServerTypeHandleExamTimeSlot   ServerType = "HANDLE_EXAM_TIME_SLOT"
	ServerTypeHospitalSearch       ServerType = "HOSPITAL_SEARCH"
	ServerTypeRetryPayment         ServerType = "RETRY_PAYMENT"
	ServerTypeLine                 ServerType = "LINE"
	ServerTypeBatchImport          ServerType = "BATCH_IMPORT"
	ServerTypeCompleteReservation  ServerType = "COMPLETE_RESERVATION"
	ServerTypePrescription         ServerType = "PRESCRIPTION"
)

type ServerTypeContextKey string

const ServerTypeKey ServerTypeContextKey = "ServerTypeContextKey"

// api path
const (
	APIPathAgentWebsocket    = "/agentws"
	APIPathClientCertificate = "/download/certificate"
	APIPathVerifyMail        = "/verify_mail"
	APIPathGraphql           = "/graphql"
	APIPathHealthCheck       = "/health_check"
	APIPathMeetings          = "/meetings"
	APIPathPatient           = "/patient"
	APIPathPatientGraphql    = "/patient/graphql"
	APIPathAuth              = "/auth"
	APIPathAuthGraphql       = "/auth/graphql"
	APIPathOperator          = "/operator"
	APIPathOperatorGraphql   = "/operator/graphql"
	APIPathRoot              = "/"
	APIPathInternal          = "/internal"
	APIPathInternalGraphql   = "/internal/graphql"
	APIPathPharmacy          = "/pharmacy"
)

const PresignPutUrlExpireDuration = 10 * time.Minute
const PresignGetUrlExpireDuration = 10 * time.Minute

const (
	RFC3339Milli              = "2006-01-02T15:04:05.999Z"
	ISO8601                   = "2006-01-02T15:04:05"
	KebabDateFormat           = "2006-01-02"
	YYYYMMDD                  = "20060102"
	TimeFormat_YYYYMDEHHMM_JP = "2006年1月2日(Mon) 15:04"
	TimeFormat_YYYYMDE_JP     = "2006年1月2日(Mon)"
	TimeFormat_HHMMM          = "15:04"
	TimeFormat_E              = "Mon"
	TimeFormat_ISO8601        = "2006/01/02 15:04"
)

// スタッフステータス
const (
	StaffDisabled = 0 // 無効化
	StaffEnabled  = 1 // 有効化
)

// 連係情報のお知らせ　Portal hospital notification ステータス
const (
	Inactive  = 0
	Active    = 1
	OutOfDate = 2
)

// スタッフ職種 アイソルと共用(user_mst.job_cd)
const (
	JobCodeDoctor           = 1  // 医師
	JobCodeOther            = 13 // その他
	JobCodePharmacistOffice = 14 // 薬剤師(本店)
	JobCodePharmacistHome   = 15 // 薬剤師(在宅)
)

// スタッフ管理者区分 アイソルと共用(user_mst.manager_kbn)
// 権限の強さは「10 > 7 > 0」
// 10は申し込み時の初期ユーザーとして作成され、他のスタッフから削除不可
// 同意系は10のみ可能
const (
	ManagerKbnGeneral     = 0  // 一般
	ManagerKbnAdmin       = 7  // 管理者
	ManagerKbnSystemAdmin = 9  // システム管理者、アイソルのテーブル定義にのみ存在、未使用
	ManagerKbnOwnerAdmin  = 10 // オーナー管理者
)

// スタッフ権限区分 アイソルと共用
const (
	PermissionWrite   = 0  // 制限なし
	PermissionRead    = 1  // 読み込み
	PermissionDisable = 99 // 利用不可
)

const PaginationPageSize = 7

// MemberKbn スタッフか患者かを区別する
type MemberKbn int

const (
	Staff   MemberKbn = 1
	Patient MemberKbn = 2
	System  MemberKbn = 3
)

const TaskStatusMax = 10

var TaskStatusColors = []string{
	"#ed7f77",
	"#4588c5",
	"#b0be3d",
	"#d41e1e",
	"#3e5eb0",
	"#02c1b6",
	"#f46d43",
	"#9c45c5",
	"#1ba852",
	"#ed1da6",
}

const (
	TodoTaskStatusName  = "未着手"
	DoingTaskStatusName = "進行中"
	DoneTaskStatusName  = "完了"

	TodoTaskStatusColor  = "#ed7f77"
	DoingTaskStatusColor = "#4588c5"
	DoneTaskStatusColor  = "#b0be3d"
)

var DefaultTaskCategoryNames = []string{"未設定"}
var DefaultTaskStatusNames = []string{
	TodoTaskStatusName,
	DoingTaskStatusName,
	DoneTaskStatusName,
}

var DefaultTaskStatusNameAndColorMap = map[string]string{
	TodoTaskStatusName:  TodoTaskStatusColor,
	DoingTaskStatusName: DoingTaskStatusColor,
	DoneTaskStatusName:  DoneTaskStatusColor,
}

const FindIndexNotFound = -1

const AddressByPostcodeAPIUrl = "https://zipcloud.ibsnet.co.jp/api/search?zipcode="

const GetLatitudeLongitudeAPIUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=%s&key=%s"

const JapanAddress = "日本、〒%s%s%s"

type ReservationStatus int

// 予約ステータス(reserve_detail.status)
const (
	Reserved             ReservationStatus = 0 // 予約済
	AppointmentStarted   ReservationStatus = 1 // 診察開始
	AppointmentCompleted ReservationStatus = 2 // 診察完了
	Canceled             ReservationStatus = 3 // キャンセル
	Absent               ReservationStatus = 4 // お呼び出し済み（不在）
)

// raiin_inf.status
const (
	RaiinInfStatusScheduled       = 0 // 来院予定
	RaiinInfStatusVerified        = 1 // 資格確認済
	RaiinInfStatusCheckedIn       = 2 // 受付済
	RaiinInfStatusCalled          = 3 // 呼出済
	RaiinInfStatusInConsultation  = 4 // 診察中
	RaiinInfStatusConsultationEnd = 5 // 診察終了
	RaiinInfStatusAmountFixed     = 6 // 金額確定
	RaiinInfStatusPaid            = 7 // 支払済
	RaiinInfStatusDeleted         = 9 // 削除
)

// MailContentType メールコンテンツタイプ
type MailContentType = int

const (
	// MailContentTypeTEXT メールコンテンツタイプ text
	MailContentTypeTEXT MailContentType = 1

	// MailContentTypeHTML メールコンテンツタイプ html
	MailContentTypeHTML MailContentType = 2
)

// メール配信設定タイプ
const (
	MailDeliveryAllowTypeImportantNotice = 0 // 重要な通知（オプトアウト不可）
	MailDeliveryAllowTypeLogin           = 1 // ログイン通知
	MailDeliveryAllowTypeReservation     = 2 // 予約通知
	MailDeliveryAllowTypeNewMessage      = 3 // 新着メッセージ通知
	MailDeliveryAllowTypeTask            = 4 // タスク通知
)

// テンプレートメールコード m_template_mail.template_mail_code
const (
	TemplateMailCodeNewReservation          = "NewReservation"          // 新規予約のお知らせ
	TemplateMailCodeCancelReservation       = "CancelReservation"       // 予約キャンセルのお知らせ
	TemplateMailCodeUpdateReservation       = "UpdateReservation"       // 予約変更のお知らせ
	TemplateMailCodeLogin                   = "Login"                   // ログインのお知らせ
	TemplateMailCodeChangedPassword         = "ChangedPassword"         // パスワード変更完了のお知らせ
	TemplateMailCodeInitialSetting          = "InitialSetting"          // 初期設定完了のお知らせ
	TemplateMailCodeLockedAccount           = "LockedAccount"           // アカウントがロックされました
	TemplateMailCodeChangeEmailConfirmation = "ChangeEmailConfirmation" // メールアドレス変更のお知らせ
	TemplateMailCodeChangeEmailCompleted    = "ChangeEmailCompleted"    // メールアドレス変更完了のお知らせ
	TemplateMailCodeNewMessage              = "NewMessage"              // 新着メッセージがあります（クリニックフタッフ宛）
	TemplateMailCodeNewMessageToPatient     = "NewMessageToPatient"     // 新着メッセージがあります（患者宛）
	TemplateMailCodeNewTask                 = "NewTask"                 // 新しいタスクが登録されました
	TemplateMailCodeForReserveDayBefore     = "ForReserveDayBefore"     // 患者向け予約リマインドメール(前日)
	TemplateMailCodeForReserveJustBefore    = "ForReserveJustBefore"    // 患者向け予約リマインドメール(10分前)
	TemplateMailCodeIVRAccess               = "IVRAccess"               // IVRプロセス開始のご案内
	TemplateMailCodeWaitingForScreening     = "WaitingForScreening"     // 手動審査待ちの連絡
)

// 予約テンプレートメールコード m_template_mail.template_mail_code
const (
	TemplateMailCodePortalNewReservationInPerson    = "PortalNewReservationInPerson"    // 新規予約のお知らせ
	TemplateMailCodePortalNewReservationOnline      = "PortalNewReservationOnline"      // 新規予約のお知らせ
	TemplateMailCodePortalUpdateReservationInPerson = "PortalUpdateReservationInPerson" // 予約更新のお知らせ
	TemplateMailCodePortalUpdateReservationOnline   = "PortalUpdateReservationOnline"   // 予約更新のお知らせ
	TemplateMailCodePortalCancelReservation         = "PortalCancelReservation"         // 予約削除のお知らせ
	TemplateMailCodePortalPaymentComplete           = "PortalPaymentComplete"           // 決済完了のお知らせ
	TemplateMailCodePortalPaymentError              = "PortalPaymentError"              // 決済エラーのお知らせ
	TemplateMailCodePortalPaymentCancel             = "PortalPaymentCancel"             // 決済取消のお知らせ
	TemplateMailCodePortalPaymentUpdate             = "PortalPaymentUpdate"             // 決済金額変更のお知らせ
)

// テンプレートメールコード 薬局用
const (
	TemplateMailCodePharmacyDesiredDateUpdate            = "PharmacyDesiredDateUpdate"            // ご希望日時登録のお知らせ
	TemplateMailCodePharmacyReserveRemind                = "PharmacyReserveRemind"                // 明日の服薬指導のお知らせ
	TemplateMailCodePharmacyReserveCancel                = "PharmacyReserveCancel"                // オンライン服薬指導キャンセルのお知らせ
	TemplateMailCodePharmacyPaymentComple                = "PharmacyPaymentComple"                // 決済完了のお知らせ
	TemplateMailCodePharmacyPaymentAmountChange          = "PharmacyPaymentAmountChange"          // 決済金額変更のお知らせ
	TemplateMailCodePharmacyPaymentError                 = "PharmacyPaymentError"                 // 決済エラーのお知らせ
	TemplateMailCodePharmacyPaymentCancel                = "PharmacyPaymentCancel"                // 請求取消のお知らせ
	TemplateMailCodePharmacyUploadDeliveryCsv            = "PharmacyUploadDeliveryCsv"            // 発送完了のお知らせ
	TemplateMailCodePrescriptionReceptionNewNotification = "PrescriptionReceptionNewNotification" // 【新着のお知らせ】処方箋モバイルオーダー
)

// テンプレートメールコード 薬局24宛
const (
	TemplateMailCodeToPharmacyForCancelReserve = "ToPharmacyForCancelReserve" // オンライン服薬指導キャンセルのお知らせ
)

// テンプレートメール埋め込み文字：クリニック
const (
	TemplateMailKeyStaffName                        = "StaffName"                        // スタッフ名
	TemplateMailKeyNowTime                          = "NowTime"                          // 現在時間
	TemplateMailKeyIPAddress                        = "IPAddress"                        // IPAddress
	TemplateMailKeyUserAgent                        = "UserAgent"                        // UserAgent
	TemplateMailKeyWebBrowser                       = "WebBrowser"                       // ブラウザ名
	TemplateMailKeyExamTimePeriod                   = "ExamTimePeriod"                   // 予約日時
	TemplateMailKeyTreatmentAndReserve              = "TreatmentAndReserve"              // 初診/再診および対面/オンラインの選択
	TemplateMailKeyTreatmentDepartment              = "TreatmentDepartment"              // 診療メニュー
	TemplateMailKeyCalendarLabel                    = "CalendarLabel"                    // カレンダーのラベル
	TemplateMailKeyReservationDetailURL             = "ReservationDetailURL"             // 予約詳細URL
	TemplateMailKeyReservationChanges               = "ReservationChanges"               // 予約変更箇所
	TemplateMailKeyToEmailAddress                   = "ToEmailAddress"                   // 送信先メールアドレス:user_mst.email以外の送信したい場合
	TemplateMailKeyChangeEmailConfirmationURL       = "ChangeEmailConfirmationURL"       // メールアドレス変更確認URL
	TemplateMailKeyChangeEmailTokenExpireTimeMinute = "ChangeEmailTokenExpireTimeMinute" // メールアドレス変更確認有効期限(分)
	TemplateMailKeyClinicName                       = "ClinicName"                       // クリニック名
	TemplateMailKeyClinicHomePage                   = "ClinicHomePage"                   // クリニックホームページ
	TemplateMailKeyClinicAddress                    = "ClinicAddress"                    // クリニック所在地
	TemplateMailKeyClinicPhoneNumber                = "ClinicPhoneNumber"                // クリニック電話番号
	TemplateMailKeyClinicPaymentMethod              = "ClinicPaymentMethod"              // 支払い方法
	TemplateMailKeyAllClinicPatientName             = "AllClinicPatientName"             // 受診者（複数患者）
	TemplateMailKeyPharmacyInfo                     = "PharmacyInfo"                     // 薬局情報
	TemplateMailKeyTreatmentType                    = "TreatmentType"                    // 初診or再診
	TemplateMailKeyReserveType                      = "ReserveType"                      // 対面orオンライン
	TemplateMailKeyIVRUrl                           = "IVRUrl"                           // IVRURL
	TemplateMailKeyIVRPhoneNumber                   = "IVRPhoneNumber"                   // IVR確認先電話番号
)

// テンプレートメール埋め込み文字：オンライン服薬指導
const (
	TemplateMailKeyDesiredDate                   = "DesiredDate"                   // 希望日時
	TemplateMailKeyAllPatientName                = "AllPatientName"                // 対象者（複数患者）
	TemplateMailKeyPharmacyName                  = "PharmacyName"                  // 薬局名
	TemplateMailKeyPharmacyPhoneNumber           = "PharmacyPhoneNumber"           // 薬局電話番号
	TemplateMailKeyUrlBookingFrontWeb            = "UrlBookingFrontWeb"            // 予約サイトURL
	TemplateMailKeyGuidanceDay                   = "GuidanceDay"                   // 服薬指導日
	TemplateMailKeyDeliveryDay                   = "DeliveryDay"                   // 発送日
	TemplateMailKeyDeliveryInquiryNum            = "DeliveryInquiryNum"            // お問い合わせ番号
	TemplateMailKeyPatientName                   = "PatientName"                   // 対象者（個別患者）
	TemplateMailKeyBeforePaymentSettlementDate   = "BeforePaymentSettlementDate"   // 決済日時
	TemplateMailKeyBeforePaymentMedicationFee    = "BeforePaymentMedicationFee"    // 薬代
	TemplateMailKeyBeforePaymentPostageFee       = "BeforePaymentPostageFee"       // 送料
	TemplateMailKeyBeforePaymentSettlementAmount = "BeforePaymentSettlementAmount" // 決済金額
	TemplateMailKeyBeforePaymentMethod           = "BeforePaymentMethod"           // 支払い方法
	TemplateMailKeyPaymentSettlementDate         = "PaymentSettlementDate"         // 決済日時
	TemplateMailKeyPaymentMedicationFee          = "PaymentMedicationFee"          // 薬代
	TemplateMailKeyPaymentPostageFee             = "PaymentPostageFee"             // 送料
	TemplateMailKeyPaymentSettlementAmount       = "PaymentSettlementAmount"       // 決済金額
	TemplateMailKeyPaymentMethod                 = "PaymentMethod"                 // 支払い方法
	TemplateMailKeyPaymentCancelDate             = "PaymentCancelDate"             // 取消日時
	TemplateMailKeyPaymentCancelAmount           = "PaymentCancelAmount"           // 取消金額
)

// テンプレートメールportal
const (
	TemplateMailKeyServiceName               = "ServiceName"               // サービス名
	TemplateMailKeyReservationTime           = "ReservationTime"           // 予約日時
	TemplateMailKeyReservationType           = "ReservationType"           // 予約タイプ
	TemplateMailKeyPatientsName              = "PatientsName"              // 患者名
	TemplateMailKeyPrescriptionReceiveMethod = "PrescriptionReceiveMethod" // 処方箋を受け取る方法
	TemplateMailKeyPharmacyFaxNumber         = "PharmacyFaxNumber"         // 薬局Fax番号
	TemplateMailKeyPharmacyAddress           = "PharmacyAddress"           // 薬局住所
	TemplateMailKeyReservationListURL        = "ReservationListUrl"        // 予約一覧
	TemplateMailKeySupportURL                = "SupportUrl"                // お問い合わせURL
	TemplateMailKeyHomePageURL               = "HomePageUrl"               // ホームページ
	TemplateMailKeySurveyLink                = "SurveyLink"                // サーベイURL
	TemplateMailKeyClinicPayment             = "ClinicPayment"             // クリリック支払い方法
	TemplateMailKeyPharmacyPayment           = "PharmacyPayment"           // 薬局支払い方法
	TemplateMailKeyHasPharmacy               = "HasPharmacy"               // 薬局情報がある確認
)

// メール内で使用される単語
const (
	MailTextExamTimePeriod      = "予約日時"
	MailTextTreatmentAndReserve = "初診/再診および対面/オンラインの選択"
	MailTextTreatmentDepartment = "診療メニュー"
	MailTextCalendarLabel       = "カレンダー"
	MailTextMemo                = "メモ"
	MailTextTreatmentTypeFirst  = "初診"    // TreatmentType 0:初診
	MailTextTreatmentTypeRe     = "再診"    // TreatmentType 1:再診
	MailTextReserveTypeInPerson = "対面"    // ReserveType 0:対面
	MailTextReserveTypeOnline   = "オンライン" // ReserveType 1:オンライン
)

// FunctionCd 権限コード 参照(https://docs.google.com/spreadsheets/d/11xaOXoou6OK5-DKtQWhZwMgKb2d2vTvs/)
const (
	FuncCdReservationCalendar = "G0000000" // 予約カレンダー管理
	FuncCdTreatmentDepartment = "G0000100" // 診察メニュー設定
	FuncCdSurvey              = "G0000200" // web問診票管理
	FuncCdPortalIntegration   = "G0000300" // ポータル連携
	FuncCdPatientMessage      = "G0100100" // メッセージの送受信(患者)
)

// event_cd 監査ログイベントコード 参照(https://docs.google.com/spreadsheets/d/1yVzAhyP4c1SLKz2r4RRLnrsYQIimhquPYG-JcFNIm5A/edit#gid=459534490)
const (
	EventCdGmoLogin                = "96001000000" // 認証：ログイン
	EventCdGmoLogout               = "96001000999" // 認証：ログアウト
	EventCdGmoPatientSearch        = "95000000001" // 患者検索：検索
	EventCdGmoPatientInfoView      = "95001000001" // 患者情報：閲覧
	EventCdGmoPatientInfoEdit      = "95001001001" // 患者情報：編集
	EventCdGmoPatientPageView      = "95002000001" // 患者ページ：閲覧
	EventCdGmoPatientPageEdit      = "95002001001" // 患者ページ：編集
	EventCdGmoPatientMemoView      = "95003000001" // 患者メモ：閲覧
	EventCdGmoPatientMemoEdit      = "95003001001" // 患者メモ：編集
	EventCdGmoCalenderView         = "94001000001" // カレンダー：一覧閲覧
	EventCdGmoCalenderCreate       = "94002001001" // カレンダー：登録
	EventCdGmoCalenderEdit         = "94003001001" // カレンダー：編集
	EventCdGmoCalenderDelete       = "94003002001" // カレンダー：削除
	EventCdGmoMessageChannelView   = "93001000001" // メッセージ：チャンネル閲覧
	EventCdGmoMessageView          = "***********" // メッセージ：メッセージ閲覧
	EventCdGmoMessageEdit          = "***********" // メッセージ：メッセージ編集
	EventCdGmoMessageDelete        = "***********" // メッセージ：メッセージ削除
	EventCdGmoTaskView             = "***********" // タスク管理：一覧閲覧
	EventCdGmoTaskEdit             = "***********" // タスク管理：編集
	EventCdGmoTaskDelete           = "***********" // タスク管理：削除
	EventCdGmoAccountAdd           = "***********" // 設定：アカウント管理：アカウントの追加
	EventCdGmoAccountEdit          = "***********" // 設定：アカウント管理：アカウントの更新
	EventCdGmoAccountInvalid       = "***********" // 設定：アカウント管理：アカウントの無効
	EventCdGmoAccountPasswordReset = "***********" // 設定：アカウント管理：パスワードリセット
	EventCdGmoAccountPasswordEdit  = "***********" // 設定：アカウント管理：パスワード変更

	EventCdGmoWebSurveyReadList   = "***********" // 問診：一覧閲覧
	EventCdGmoWebSurveyReadDetail = "***********" // 問診：閲覧

	EventCdGmoReceptionListView                   = "***********" // 服薬指導：受付一覧：一覧閲覧
	EventCdGmoReceptionListSearchCustomer         = "***********" // 服薬指導：受付一覧：検索（会員）
	EventCdGmoReceptionListStatusChange           = "***********" // 服薬指導：受付一覧：ステータス変更
	EventCdGmoReceptionListStatusCanceledChange   = "***********" // 服薬指導：受付一覧：キャンセル
	EventCdGmoReceptionListPharmacistChange       = "***********" // 服薬指導：受付一覧：担当薬剤師変更
	EventCdGmoReceptionListSearchPatient          = "***********" // 服薬指導：受付一覧：検索（患者）
	EventCdGmoReceptionListSearchClinic           = "90001005001" // 服薬指導：受付一覧：検索（クリニック）
	EventCdGmoDownloadDeliveryCSV                 = "90002000001" // 服薬指導：配送CSV出力ダイアログ：出力
	EventCdGmoUploadDeliveryCSV                   = "90002001001" // 服薬指導：配送CSV取込ダイアログ：取込
	EventCdGmoDesiredDateTimeChangeDialogView     = "90003000001" // 服薬指導：希望日時変更ダイアログ：閲覧
	EventCdGmoDesiredDateTimeChangeDialogRegister = "90003001001" // 服薬指導：希望日時変更ダイアログ：登録
	EventCdGmoDesiredDateTimeChangeDialogChange   = "90003002001" // 服薬指導：希望日時変更ダイアログ：変更
	EventCdGmoDesiredDateTimeChangeDialogCancel   = "90003003001" // 服薬指導：希望日時変更ダイアログ：キャンセル
	// EventCdGmoSMSCodeNotifyOnlineDrugGuidanceURL          = "90004000001" // 服薬指導：SMS送信ダイアログ：オンライン服薬指導URL通知 ※廃番
	// EventCdGmoSMSCodeDrugGuidanceIsSkipped                = "90004000002" // 服薬指導：SMS送信ダイアログ：オンライン服薬指導スキップ通知 ※廃番
	// EventCdGmoSMSCodeRequestSetUpDateForDrugGuidance      = "90004000003" // 服薬指導：SMS送信ダイアログ：希望日時設定依頼通知 ※廃番
	EventCdGmoSMSCodeNotifyOnlineDrugGuidanceURL     = "90013001001" // 服薬指導：通知ダイアログ：ビデオ通話への入室依頼通知
	EventCdGmoSMSCodeDrugGuidanceIsSkipped           = "90013002001" // 服薬指導：通知ダイアログ：ビデオ通話の予定キャンセル通知
	EventCdGmoSMSCodeRequestSetUpDateForDrugGuidance = "90013003001" // 服薬指導：通知ダイアログ：希望日時の設定依頼通知
	EventCdGmoSMSCodeInventoryShortage               = "90013004001" // 服薬指導：通知ダイアログ：医薬品の在庫不足通知
	EventCdGmoSMSCodePrescriptionNotYetReceived      = "90013005001" // 服薬指導：通知ダイアログ：処方せん未着通知
	EventCdGmoSMSCodeReturnedDeliveryItems           = "90013006001" // 服薬指導：通知ダイアログ：配送の持ち戻り通知
	EventCdGmoSMSCodeFreeFormat                      = "90013007001" // 服薬指導：通知ダイアログ：自由記載通知

	EventCdGmoVideoCallJoined                             = "90005000001" // 服薬指導：ビデオ通話画面：入室
	EventCdGmoVideoCallLeft                               = "90005000002" // 服薬指導：ビデオ通話画面：退出
	EventCdGmoPaymentDialogView                           = "90006000001" // 服薬指導：会計ダイアログ：閲覧
	EventCdGmoPaymentDialogRegister                       = "90006000002" // 服薬指導：会計ダイアログ：会計登録
	EventCdGmoPaymentDialogChange                         = "90006000003" // 服薬指導：会計ダイアログ：金額変更
	EventCdGmoPatientDetailScreenContactView              = "90007000001" // 服薬指導：患者詳細画面（連絡先）：閲覧
	EventCdGmoPatientDetailScreenInsuranceInformationView = "90008000001" // 服薬指導：患者詳細画面（保険情報）：閲覧
	EventCdGmoPatientDetailScreenMedicalInterviewView     = "90009000001" // 服薬指導：患者詳細画面（問診）：閲覧
	EventCdGmoPatientDetailScreenMedicineNotebookView     = "90010000001" // 服薬指導：患者詳細画面（お薬手帳）：閲覧
	EventCdGmoPatientDetailScreenPrescriptionView         = "90012000001" // 服薬指導：患者詳細画面（処方箋）：閲覧
	EventCdGmoPatientDetailScreenHistoryView              = "90011000001" // 服薬指導：患者詳細画面（履歴）：閲覧

	EventCdGmoPrescriptionReceptionListView          = "89001000001" // 処方箋：処方箋受付：一覧閲覧
	EventCdGmoPrescriptionReceptionPrint             = "89001001001" // 処方箋：処方箋受付：処方箋印刷
	EventCdGmoPrescriptionReceptionSMS               = "89001002001" // 処方箋：処方箋受付：SMS送信
	EventCdGmoPrescriptionReceptionCancel            = "89001003001" // 処方箋：処方箋受付：キャンセル
	EventCdGmoPrescriptionReceptionDownload          = "89001004001" // 処方箋：処方箋受付：処方箋ダウンロード
	EventCdGmoPrescriptionSMSDialogMedicationReady   = "89002001001" // 処方箋：SMS通知ダイアログ：調剤の完了通知
	EventCdGmoPrescriptionSMSDialogInventoryShortage = "89002002001" // 処方箋：SMS通知ダイアログ：医薬品の在庫不足通知
	EventCdGmoPrescriptionSMSDialogFreeFormat        = "89002003001" // 処方箋：SMS通知ダイアログ：自由記載通知
)

var (
	// EventCdGmoPrescriptionAuditLogList 監査ログ表示の処方箋区分でHosokuをptNameに置き換えるコード一覧
	EventCdGmoPrescriptionAuditLogList = []string{
		EventCdGmoPrescriptionReceptionPrint,
		EventCdGmoPrescriptionReceptionSMS,
		EventCdGmoPrescriptionReceptionCancel,
		EventCdGmoPrescriptionReceptionDownload,
		EventCdGmoPrescriptionSMSDialogMedicationReady,
		EventCdGmoPrescriptionSMSDialogInventoryShortage,
		EventCdGmoPrescriptionSMSDialogFreeFormat,
	}
)

// デフォルトページング共通
const (
	DefaultPageLimit = 30
)

const GracefulShutdownTime = 10

// 初回ログインIDフラグ
const (
	InitLoginIDTrue  = 1 // 初回ログインID
	InitLoginIDFalse = 0 // 変更済みログインID
)

// 初回パスワードフラグ
const (
	InitPasswordTrue  = 1 // 初回パスワード
	InitPasswordFalse = 0 // 変更済みパスワード
)

// 職種
const (
	MedicalProfessionalTypeDoctor  = 1 // 医師
	MedicalProfessionalTypeDentist = 2 // 歯科医師
)

// 性別
const (
	GenderMale   = 1 // 男性
	GenderFeMale = 2 // 女性
)

// オンライ診療ステータス
const (
	MeetingReserved      = 0
	MeetingPatientJoined = 1
	MeetingClinicJoined  = 2
	MeetingBothJoined    = 3
	MeetingEnded         = 4
	MeetingDisabled      = 5
)

// Chimeの会議イベント
const (
	ChimeEventStart           = "chime:MeetingStarted"
	ChimeEventEnd             = "chime:MeetingEnded"
	ChimeEventAttendeeJoined  = "chime:AttendeeJoined"
	ChimeEventAttendeeDropped = "chime:AttendeeDropped"
	ChimeEventAttendeeLeft    = "chime:AttendeeLeft"
)

// 患者情報ファイルType
const (
	Insurance          = 1
	MedicalCertificate = 2
)

// オンライ診療参加者のタイプ
const (
	AttendeePatientType = 0
	AttendeeClinicType  = 1
)

// 権限ステータス
const (
	// FeaturePermissionNone 権限なし or デフォルト値
	FeaturePermissionNone = 0
	// FeaturePermissionReadOnly queryの実行のみOK、mutationの実行はNG
	FeaturePermissionReadOnly = 1
	// FeaturePermissionReadWrite query/mutationどちらの実行OK
	FeaturePermissionReadWrite = 2
)

// 代理ログイン用の値
const (
	OperatorFunctionNameSetting = "setting"
	OperatorAuditLogName        = "ヘルステック担当者"
	OperatorAuditLogLoginID     = "-"
)

// バッチ用の監査ログ値
const (
	BatchUserID          = "0"
	BatchAuditLogLoginID = "System"
	BatchAuditLogName    = "バッチ"
)

// TreatmentType
const (
	TreatmentTypeFirst = 0 // 初診
	TreatmentTypeRe    = 1 // 再診
)

const (
	// SystemCreateID ID系は1から採番されるのでシステムIDとして0を使う
	SystemCreateID = 0
	// SystemUpdateID ID系は1から採番されるのでシステムIDとして0を使う
	SystemUpdateID = 0
)

const (
	// DefaultKaID user_mst.ka_idのデフォルト値
	// ka_mstテーブルに存在するka_idがないとスマクリがエラーとなる可能性があるため設定
	// ka_mstにka_id=1は必ず存在するの想定
	DefaultKaID = 1
)

// Fincodeの処理区分
// 参照：https://docs.fincode.jp/api#tag/%E6%B1%BA%E6%B8%88/operation/postPayments
const (
	// 有効性チェック
	FincodeJobCodeCheck = "CHECK"
	// 仮売上
	FincodeJobCodeAuth = "AUTH"
	// 即時売上
	FincodeJobCodeCapture = "CAPTURE"
)

// Fincodeの支払方法
// 参照：https://docs.fincode.jp/api#tag/%E6%B1%BA%E6%B8%88/operation/postPayments
const (
	// 一括
	FincodePaymentMethodOneTime = "1"
	// 分割
	FincodePaymentMethodByInstallments = "2"
	// リボ
	FincodeJobCodeRibo = "5"
)

// Fincodeの決済種別
// payment_fincode_order.pay_type
// 参照：https://docs.fincode.jp/api#tag/%E6%B1%BA%E6%B8%88/operation/postPayments
const (
	// クレジットカード決済
	FincodePaymentTypeCard = "Card"
)

// FincodeのAPIバージョン
const (
	// クレジットカード決済
	FincodeAPIv1 = "v1"

	// テナントショップ本番環境申請情報
	FincodeExaminationsAPIv2 = "v2"
)

// Fincodeとそのショップとの契約ステータス
const (
	// 未契約
	FincodeNotContract = 101
	// 利用審査中
	FincodeContractUnderReview = 102
	// 利用審査中（VISA/Mastercard利用可）
	FincodeContractUnderReviewImmediateUse = 103
	// 解約済
	FincodeContractCancelled = 105
	// 契約不成立
	FincodeContractNotValid = 106
	// 稼働中
	FincodeContractValid = 107
)

// Fincodeの決済ステータス
// 参照: https://docs.fincode.jp/api#tag/%E6%B1%BA%E6%B8%88/operation/getPaymentsId
const (
	FincodePaymentStatusCanceled = "CANCELED" // キャンセル
)

// PaymentTypeの支払い方法
// payment_clinic_detail.payment_type
// payment_pharmacy_detail.payment_type
const (
	PaymentTypeNo   = 0 // 支払い方法なし(請求なし)
	PaymentTypeCard = 1 // カード払い
	PaymentTypeCash = 2 // 現金払い
)

const (
	// ProductTypeDenkaru m_application_feature.product_typeの電カル
	ProductTypeDenkaru = 1
	// ProductTypeOpesys m_application_feature.product_typeのオペシス
	ProductTypeOpesys = 2
)

// SMS送信リザルト
type SendSMSResultCode string

const (
	// SendSMSResultCodeSMSLinkSucceed 配信対象に登録しました
	// SMSLINKで定義されたもの
	SendSMSResultCodeSMSLinkSucceed SendSMSResultCode = "RST200001"

	// SendSMSResultCodeAWSPinpointSucceed AWS Pinpointでの配信が成功（独自定義）
	SendSMSResultCodeAWSPinpointSucceed SendSMSResultCode = "AWSPinpointSucceed"
)

const (
	ReserveTypeInPerson = 0 // ReserveTypeInPerson 対面
	ReserveTypeOnline   = 1 // ReserveTypeOnline オンライン
	ReserveTypeBoth     = 2 // ReserveTypeBoth 対面またはオンライン
)

// テンプレートSMSコード m_template_sms.sms_code
const (
	SMSCodeRemindToPatient                        = "RemindToPatient"                        // SMSCodeRemindToPatient 患者向け予約リマインドSMS(10分前)
	SMSCodeNotifyToPatientJustBefore              = "NotifyToPatientJustBefore"              // SMSCodeNotifyToPatientJustBefore 直前の呼び出し時のSMS
	SMSCodeAdvanceNotifyToPatient                 = "AdvanceNotifyToPatient"                 // SMSCodeAdvanceNotifyToPatient 事前の呼び出し時のSMS
	SMSCodeNotifyOnlineDrugGuidanceURL            = "NotifyOnlineDrugGuidanceURL"            // SMSCodeNotifyOnlineDrugGuidanceURL オンライン服薬指導のミーティングURL通知
	SMSCodeDrugGuidanceIsSkipped                  = "DrugGuidanceIsSkipped"                  // SMSCodeDrugGuidanceIsSkipped オンライン服薬指導スキップ通知
	SMSCodeRequestSetUpDateForDrugGuidance        = "RequestSetUpDateForDrugGuidance"        // SMSCodeRequestSetUpDateForDrugGuidance オンライン服薬指導の希望日時設定を依頼
	SMSCodeMedicationReady                        = "MedicationReady"                        // SMSCodeMedicationReady 処方箋モバイルオーダーのお薬準備OK通知
	SMSCodePrescriptionReceiptCompleted           = "PrescriptionReceiptCompleted"           // SMSCodePrescriptionReceiptCompleted 処方箋モバイルオーダーの受付完了通知
	SMSCodeInventoryShortagePharmacyReserve       = "InventoryShortagePharmacyReserve"       // SMSCodeInventoryShortage オンライン服薬指導予約：その他の通知 医薬品の在庫不足 入力用テンプレート
	SMSCodePrescriptionNotYetReceived             = "PrescriptionNotYetReceived"             // SMSCodePrescriptionNotYetReceived オンライン服薬指導予約：その他の通知 処方箋が未着 入力用テンプレート
	SMSCodeReturnedDeliveryItems                  = "ReturnedDeliveryItems"                  // SMSCodeReturnedDeliveryItems オンライン服薬指導予約：その他の通知 配送の持ち戻り 入力用テンプレート
	SMSCodeFreeFormatPharmacyReserve              = "FreeFormatPharmacyReserve"              // SMSCodeFreeFormatPharmacyReceive オンライン服薬指導予約：その他の通知 自由記載 入力用テンプレート
	SMSCodeInventoryShortagePrescriptionReception = "InventoryShortagePrescriptionReception" // SMSCodeInventoryShortagePrescriptionReception 処方箋モバイルオーダー 医薬品の在庫不足 入力用テンプレート
	SMSCodeFreeFormatPrescriptionReception        = "FreeFormatPrescriptionReception"        // SMSCodeFreeFormatPrescriptionReception 処方箋モバイルオーダー 自由記載 入力用テンプレート
	SMSCodeSendFreeFormat                         = "SendFreeFormat"                         // SMSCodeFreeFormat 共通：テンプレートによるSMS送信用
	SMSCodeSignUp                                 = "SignUp"                                 // SMSCodeSignUp GMOヘルステックアカウントサインアップ用SMS
)

// Fax送受信タイプ
const (
	DirectionInbound  = 1 // 受信
	DirectionOutbound = 2 // 送信
)

// Faxステータス
const (
	FaxStatusUnprocessed         = 1  // 未処理 cloudfaxに取込前
	FaxStatusPending             = 2  // 受付中
	FaxStatusProcessing          = 3  // 処理中
	FaxStatusCompleted           = 4  // 成功
	FaxStatusFailed              = 5  // 失敗
	FaxStatusBusy                = 6  // 失敗(通話中)
	FaxStatusNoAnswer            = 7  // 失敗(無応答)
	FaxStatusChannelUnAcceptable = 8  // 失敗(同時実行チャネル数オーバー)
	FaxStatusUnknown             = 99 // 未ステータス
)

var FaxStatusMapName = map[string]int{
	"pending":              FaxStatusPending,
	"processing":           FaxStatusProcessing,
	"completed":            FaxStatusCompleted,
	"failed":               FaxStatusFailed,
	"busy":                 FaxStatusBusy,
	"no-answer":            FaxStatusNoAnswer,
	"CHANNEL_UNACCEPTABLE": FaxStatusChannelUnAcceptable,
}

const (
	// ReservationMethodTypeTimeSlot ... 時間帯
	ReservationMethodTypeTimeSlot = 0
	// ReservationMethodTypeOrder ... 順番
	ReservationMethodTypeOrder = 1
)

const (
	// ReservationTypeInPerson ... 対面
	ReservationTypeInPerson = 0
	// ReservationTypeOnline ... オンライン
	ReservationTypeOnline = 1
)

const (
	// SentryLogSeparator Sentryのログのタイトルとメッセージを分割するセパレーター
	SentryLogSeparator = "@"
)

const (
	// ReservableSlotSettingTypeByMinutes ... 診療メニューに設定された所要時間を使用する
	ReservableSlotSettingTypeByMinutes = 0
	// ReservableSlotSettingTypeByNumberOfPerson ... 診療時間ごとに予約可能人数を設定する
	ReservableSlotSettingTypeByNumberOfPerson = 1
)

type ReservationType = string

const (
	ReservationTypeCanceled ReservationType = "canceled"
	ReservationTypeNew      ReservationType = "new"
)

// クライアント証明書
const (
	// ※ DC means Digital Certificate.
	DCBaseSecretLetters  = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	DCBaseDownloadURL    = "/download/certificate/"
	DCBitLength          = 4096
	DCExpirationDate     = "2024-11-16 00:00:00"
	DCRootCACountry      = "JP"
	DCRootCAProvince     = "Tokyo"
	DCRootCALocality     = "Shibuya"
	DCRootCAOrganization = "GMO Healthtech, Inc." // ドメイン変更時はここも変更する点に注意
)

// ドメイン関連
const (
	// 環境変数で設定するとテストで失敗するので、定数で管理する
	DomainRegexPattern = `^(gmo-healthcare\.com|gmo-healthtech\.com)-(\d+)-`
	// denkaru-cn-headerからクライアント証明書のIDを取り出すときのインデックス
	ClientCertIDIndex   = 2
	DomainGMOHealthcare = "gmo-healthcare.com" // テストでのみ使用
	DomainGMOHealthtech = "gmo-healthtech.com"
)

// HospitalSearchAction 病院検索アクション
type HospitalSearchAction = int

// HospitalSearchAction Enum
const (
	ActionTypeCreatePortalHospital HospitalSearchAction = iota + 1
	ActionTypeEditPortalHospital
	ActionTypeCreateOrUpdateTreatmentDates
	ActionTypeDeleteTreatmentDates
	ActionTypeDeletePortalHospital
	ActionTypeCreateImportPortalHospital
	ActionTypeDeleteImportPortalHospital
)

// pharmacy_reserveテーブルで使用されるステータスの定数
const (
	// desired_date_status　服薬指導希望日時ステータス
	PharmacyReserveDesiredDateStatusConfirming       = 1 // PharmacyReserveDesiredDateStatusConfirming 確認中
	PharmacyReserveDesiredDateStatusBeforeConfigured = 2 // PharmacyReserveDesiredDateStatusBeforeConfigured 設定前
	PharmacyReserveDesiredDateStatusConfigured       = 3 // PharmacyReserveDesiredDateStatusConfigured 設定後

	// sms_status　SMSステータス
	PharmacyReserveSMSStatusUnnotified = 1 // PharmacyReserveSMSStatusUnnotified 未通知
	PharmacyReserveSMSStatusNotified   = 2 // PharmacyReserveSMSStatusNotified 通知済み

	// videocall_status ビデオ通話ステータス
	PharmacyReserveVideocallStatusNotYetEntered  = 1 // PharmacyReserveVideocallStatusNotYetEntered 未入室
	PharmacyReserveVideocallStatusPatientEntered = 2 // PharmacyReserveVideocallStatusPatientEntered 患者入室済み
	PharmacyReserveVideocallStatusStaffEntered   = 3 // PharmacyReserveVideocallStatusStaffEntered スタッフ入室済み
	PharmacyReserveVideocallStatusDuringACall    = 4 // PharmacyReserveVideocallStatusDuringACall 通話中

	// postal_service_type 郵送サービス種別
	PharmacyReservePostalServiceTypeYuuPacket = 1 // PharmacyReservePostalServiceType3kgOrLess ゆうパケット(3kg以下)
	PharmacyReservePostalServiceTypeYuuPack   = 2 // PharmacyReservePostalServiceTypeYuuPack ゆうパック(30kg以下)

	// csv_status CSVステータス
	PharmacyReserveCsvStatusBeforeExport    = 1 // PharmacyReserveCsvStatusBeforeExport 出力前
	PharmacyReserveCsvStatusBeforeImport    = 2 // PharmacyReserveCsvStatusBeforeImport 取込前
	PharmacyReserveCsvStatusAlreadyImported = 3 // PharmacyReserveCsvStatusAlreadyImported 取込済

	// pharmacist_status 担当薬剤師ステータス
	PharmacyReservePharmacistStatusBeforeConfigured = 1 // PharmacyReservePharmacistStatusBeforeConfigured 未設定
	PharmacyReservePharmacistStatusConfigured       = 2 // PharmacyReservePharmacistStatusConfigured 設定済
)

// pharmacy_reserve_detailテーブルで使用されるステータスの定数
const (
	// prescription_type 処方箋形態
	PharmacyReserveDetailPrescriptionTypeAnalog  = 1 // PharmacyReserveDetailPrescriptionTypeAnalog 紙
	PharmacyReserveDetailPrescriptionTypeDigital = 2 // PharmacyReserveDetailPrescriptionTypeDigital 電子データ

	// status ステータス
	PharmacyReserveDetailStatusPrescriptionNotYetArrived = 1 // PharmacyReserveDetailStatusPrescriptionNotYetArrived 処方箋未着
	PharmacyReserveDetailStatusPrescriptionArrived       = 2 // PharmacyReserveDetailStatusPrescriptionArrived 処方箋到着
	PharmacyReserveDetailStatusDrugStocksNotExist        = 3 // PharmacyReserveDetailStatusDrugStocksNotExist 在庫なし
	PharmacyReserveDetailStatusDrugStocksConfirmed       = 4 // PharmacyReserveDetailStatusDrugStocksConfirmed 在庫確認済
	PharmacyReserveDetailStatusDrugsPackaged             = 5 // PharmacyReserveDetailStatusDrugsPackaged 梱包済
	PharmacyReserveDetailStatusDrugsShipped              = 6 // PharmacyReserveDetailStatusDrugsShipped 発送済
	PharmacyReserveDetailStatusCanceled                  = 7 // PharmacyReserveDetailStatusCanceled キャンセル

	// guidance_status 服薬指導ステータス
	PharmacyReserveDetailGuidanceStatusBeforeGuidance = 1 // PharmacyReserveDetailGuidanceStatusBeforeGuidance 指導前
	PharmacyReserveDetailGuidanceStatusNotYetGuidance = 2 // PharmacyReserveDetailGuidanceStatusNotYetGuidance 未指導
	PharmacyReserveDetailGuidanceStatusGuidanceDone   = 3 // PharmacyReserveDetailGuidanceStatusGuidanceDone 指導済

	// payment_status 会計ステータス
	PharmacyReserveDetailPaymentStatusBeforePayment = 1 // PharmacyReserveDetailPaymentStatusBeforePayment 会計前
	PharmacyReserveDetailPaymentStatusPaymentDone   = 2 // PharmacyReserveDetailPaymentStatusPaymentDone 会計済
	PharmacyReserveDetailPaymentStatusPaymentFailed = 3 // PharmacyReserveDetailPaymentStatusPaymentFailed 決済エラー

)

// 薬局の営業時間
const (
	PharmacySaturdayOpenHour  = 9  // 薬局の土曜日の営業開始時
	PharmacySaturdayNoonHour  = 12 // 薬局の土曜日の午前中終了時
	PharmacySaturdayCloseHour = 19 // 薬局の土曜日の営業終了時
	PharmacyWeekdayOpenHour   = 9  // 薬局の月-金曜日の営業開始時
	PharmacyWeekdayNoonHour   = 12 // 薬局の月-金曜日の午前中終了時
	PharmacyWeekdayCloseHour  = 21 // 薬局の月-金曜日の営業終了時
)

// 会計アクション(クリニック,薬局共通)
// payment_clinic_detail.action_type
// payment_pharmacy_detail.action_type
const (
	PaymentActionTypeNew       = 1 // 新規会計
	PaymentActionTypeUpdate    = 2 // 会計変更
	PaymentActionTypeCancel    = 3 // 会計キャンセル
	PaymentActionTypeNoBilling = 4 // 会計請求なし 子供の医療費が無料等で顧客に請求しない場合に利用することを想定
)

// 会計ステータス(クリニック,薬局共通)
// payment_clinic_detail.payment_status
// payment_pharmacy_detail.payment_status
const (
	PaymentStatusBefore = 1 // 未会計
	PaymentStatusDone   = 2 // 会計済み
	PaymentStatusFailed = 3 // 決済エラー
)

// 会計プラットフォーム(クリニック,薬局共通)
// hp_fincode_info.platform_id
// portal_customer_payment.platform_id
const (
	PaymentPlatformIDInsuranceMedicalTreatment   = 1 // 保険診療(病院,薬局)ー
	PaymentPlatformIDFreeMedicalTreatment        = 2 // 自由診療(病院)
	PaymentPlatformHTIDInsuranceMedicalTreatment = 3 // 保険診療(病院,薬局) HealthTech
	PaymentPlatformHTIDFreeMedicalTreatment      = 4 // 自由診療(病院) HealthTech
)

// pharmacy_desired_dateテーブルで使用されるステータスの定数
// pharmacy_desired_date.desired_type
const (
	// desired_type 希望日時タイプ
	PharmacyDesiredDateDesiredTypeAM           = 1 // PharmacyDesiredDateDesiredTypeAM 午前中
	PharmacyDesiredDateDesiredTypeSpecified    = 2 // PharmacyDesiredDateDesiredTypeSpecified 時間指定（12時〜21時の1時間区切り）
	PharmacyDesiredDateDesiredTypeNotSpecified = 3 // PharmacyDesiredDateDesiredTypeNotSpecified 時間指定なし
)

// pharmacy_reserve_status_historyテーブルで使用されるステータスの定数
const (
	// status_cancel_type ステータスキャンセル種別
	PharmacyReserveStatusHistoryStatusCancelTypePatientCancelReserve  = 1 // 1=患者キャンセル（診療予約）：予約サイトで診療予約キャンセルに伴う服薬指導予約キャンセル
	PharmacyReserveStatusHistoryStatusCancelTypePatientCancelChange   = 2 // 2=患者キャンセル（薬局変更）：予約サイトで薬局変更に伴う服薬指導予約キャンセル
	PharmacyReserveStatusHistoryStatusCancelTypeClinicCancelReserve   = 3 // 3=クリニックキャンセル（診療予約）：クリニックシステムで診療予約キャンセルに伴う服薬指導予約キャンセル
	PharmacyReserveStatusHistoryStatusCancelTypePharmacyCancelReserve = 4 // 4=薬局キャンセル（服薬指導予約）：薬局システムで服薬指導予約キャンセル
)

// 薬の受け取り方
// reserve.prescription_receive_method
const (
	PrescriptionReceiveMethodTypeNotSpecify                = 0 // 処方箋をご自身で受け取る
	PrescriptionReceiveMethodTypeGMO24Pharmacy             = 1 // 薬局24
	PrescriptionReceiveMethodTypeCustomerSpecifiedPharmacy = 2 // 患者の指定した薬局

	PrescriptionReceiveMethodTypeNotSpecifyName                = "薬局受取（処方箋をご自身で受け取る）" // 処方箋をご自身で受け取る
	PrescriptionReceiveMethodTypeGMO24PharmacyName             = "配送受取"               // 薬局24
	PrescriptionReceiveMethodTypeCustomerSpecifiedPharmacyName = "薬局受取（処方箋を薬局に送信）"    // 患者の指定した薬局
)

// GetIVRUrl ... IVR URL
const GetIVRUrl = "%s/signup/ivr?key=%s"

// prescription_receptionテーブルで使用されるステータスの定数
const (
	PrescriptionReceptionStatusReceived = 1 // PrescriptionReceptionStatusReceived 受付ステータス：受付済み
	PrescriptionReceptionStatusCanceled = 2 // PrescriptionReceptionStatusCanceled 受付ステータス：キャンセル

	PrescriptionReceptionGenericDrugDesireWanted    = 1 // PrescriptionReceptionGenericDrugDesireWanted ジェネリック希望：希望する
	PrescriptionReceptionGenericDrugDesireNotWanted = 2 // PrescriptionReceptionGenericDrugDesireNotWanted ジェネリック希望：希望しない
	PrescriptionReceptionGenericDrugDesireAny       = 3 // PrescriptionReceptionGenericDrugDesireAny ジェネリック希望：お任せ

	PrescriptionReceptionRecordBringYes = 1 // PrescriptionReceptionRecordBringYes お薬手帳の持参：持参する
	PrescriptionReceptionRecordBringNo  = 2 // PrescriptionReceptionRecordBringNo お薬手帳の持参：持参しない

	PrescriptionReceptionPrintStatusNotPrinted = 1 // PrescriptionReceptionPrintStatusNotPrinted 印刷ステータス：未印刷
	PrescriptionReceptionPrintStatusPrinted    = 2 // PrescriptionReceptionPrintStatusPrinted 印刷ステータス：印刷済み

	PrescriptionReceptionSmsStatusNotSent = 1 // PrescriptionReceptionSmsStatusNotSent SMS送信ステータス：未送信
	PrescriptionReceptionSmsStatusSent    = 2 // PrescriptionReceptionSmsStatusSent SMS送信ステータス：送信済
)

// syuno_nyukin
const (
	SyunoNyukinNyukinStatusComplete = 1 // 完了
	SyunoNyukinNyukinStatusError    = 2 // 入金エラー
)

// syuno_seikyu
const (
	SyunoSeikyuNyukinKbnUnsettled = 0 // 未精算
	SyunoSeikyuNyukinKbnPartial   = 1 // 一部精算
	SyunoSeikyuNyukinKbnExempted  = 2 // 免除
	SyunoSeikyuNyukinKbnSettled   = 3 // 精算済
)

const (
	// FuriganaRegexp ...
	// ・全角カタカナ (ァ から ヴ まで)
	// ・全角長音記号（ー）
	// ・全角スペース
	// ・半角スペース
	FuriganaRegexp = "^[ァ-ヴー 　]+$"
)

const (
	GqlComplexityLimit = 1000 // gqlクエリの最大複雑度上限
	GqlDepthLimit      = 10   // gqlクエリの最大深さ上限
)

// BookingServiceName 予約サービス名
const BookingServiceName = "GMOクリニック・マップ"

const (
	MinLoginPasswordLength = 8
)

// GMO24Pharmacy情報
const (
	GMO24PharmacyName        = "薬局24 byGMO"
	GMO24PharmacyPhoneNumber = "050-2030-4981"
	GMO24PharmacyFaxNumber   = "03-6634-2240"
	GMO24PharmacyAddress     = "〒150-0031 東京都渋谷区桜丘町25-18 NT渋谷ビル2F"
)

// 支払い方法
const (
	PaymentMethodNotSpecify   = "ご登録クレジットカード"
	PaymentMethodCashOrOthers = "現金・その他"
)

const EmptyJSON = "{}"

// クリニックマップユーザーステータス
const (
	CustomerStatusRegistrationIncomplete = 1  // 登録情報未入力
	CustomerStatusPhoneIncomplete        = 2  // 電話番号確認未完了
	CustomerStatusActive                 = 3  // 登録完了
	CustomerStatusSuspended              = 4  // 利用停止
	CustomerStatusWithdrawn              = 99 // 退会済み
)

// FailedLoginCount 失敗したログイン回数
const FailedLoginCount = 5

// 薬局患者ファイルタイプ
const (
	PharmacyFileTypeMedicineNotebook = 1 // お薬手帳
	PharmacyFileTypePrescription     = 2 // 処方箋
)

// SMSLINK関連パラメータ
const (
	SmsContentMaxLength = 660 // SMSLINK文字数上限
)

// ラベル最大登録数
const LabelMax = 30

// UpdateStaffs で 更新対象から外す場合はこれをセットする
const IsNotUpdateStaffColumn = "omit"

const (
	IsActive = true // 有効
)

// MIMEタイプ
const (
	MimeTypePNG = "image/png" // PNG画像のMIMEタイプ
)

// calc_statusテーブルで使用されるステータスの定数
const (
	CalculateStatusNotDone    = 0 // 未済
	CalculateStatusInProgress = 1 // 計算中
	CalculateStatusError      = 8 // 異常終了
	CalculateStatusDone       = 9 // 終了
)

// calc_statusテーブルで使用される計算モードの定数
const (
	CalculateModeNormal     = 0 // 通常計算
	CalculateModeContinuous = 1 // 連続計算
	CalculateModeTrial      = 2 // 試算
)

// KeyPrefixSMS ... SMSのキーのPrefix
const KeyPrefixSMS = "sms_"

// KeyPrefixIVR ,,, IVRのキーのPrefix
const KeyPrefixIVR = "ivr_access_key_"

// DBのバッチサイズ
const (
	// デフォルトのバッチサイズ
	DefaultBatchSize = 1000
)

// PromptLogMaxCount プロンプトログの最大保存数
const PromptLogMaxCount = 10

// AWS Region constants
const (
	// AWSRegionUSEast1 ... バージニア州
	AWSRegionUSEast1 = "us-east-1"

	// AWSRegionAPNortheast1 ... 東京
	AWSRegionAPNortheast1 = "ap-northeast-1"
)

// カルテ使用状況ステータス hp_inf.karte_status
const (
	KarteStatusNoUse                               = 0 // カルテ利用なし
	KarteStatusInitialMasterDataCreationInProgress = 1 // 初期マスターデータ作成中
	KarteStatusInitialMasterDataCreationCompleted  = 2 // 初期マスターデータ作成済
	KarteStatusDemoInUse                           = 3 // デモ利用中
	KarteStatusRealInUse                           = 4 // リアル利用中
	KarteStatusDemoStopped                         = 5 // デモ停止中
	KarteStatusRealStopped                         = 6 // リアル停止中
)

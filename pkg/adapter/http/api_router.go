// Package http ...
package http

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/depth"
	"denkaru-server/pkg/adapter/graphql/directive"
	graph "denkaru-server/pkg/adapter/graphql/generated"
	"denkaru-server/pkg/adapter/graphql/resolver"
	myMiddleware "denkaru-server/pkg/adapter/middleware"
	"denkaru-server/pkg/adapter/ws/agent"
	"denkaru-server/pkg/config"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/wire"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/99designs/gqlgen/graphql"
	graphqlHandler "github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/extension"
	"github.com/99designs/gqlgen/graphql/handler/lru"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	sentryecho "github.com/getsentry/sentry-go/echo"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// InitRouter router初期化
func InitRouter(shutdownCtx context.Context) *echo.Echo {
	e := initEcho()

	routeRootPath(shutdownCtx, e)
	routePatientPath(shutdownCtx, e)
	routeAuthPath(e)
	routeAgentPath(e)
	routeMeetingsPath(e)
	routeDownloadClientCertificate(e)
	routeVerifyMail(e)
	routeInternalPath(shutdownCtx, e)
	routePharmacyPath(e)
	routeHealthCheckPath(e)

	return e
}

func initEcho() *echo.Echo {
	// 環境変数を取得
	allowOrigins := os.Getenv("URL_ALLOW_ORIGINS")
	// カンマ区切りの文字列をスライスに変換
	origins := strings.Split(allowOrigins, ",")

	e := echo.New()
	e.Use(
		middleware.BodyLimit("20M"), // 音声ファイルを送信するので大きめにとる
		middleware.Logger(),
		middleware.Recover(),
		middleware.CORSWithConfig(middleware.CORSConfig{
			AllowOrigins:     origins,
			AllowCredentials: true,
			AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization, constant.ClientCustomReferer, constant.ClientCaCnHeader},
			AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodOptions, http.MethodDelete},
		}),
		sentryecho.New(sentryecho.Options{}),
	)

	e.Use(myMiddleware.BindEchoContext)

	return e
}

/**
 * route / (root path)
 */
func routeRootPath(shutdownCtx context.Context, e *echo.Echo) {

	// address
	addressService := wire.InitializeAddressService(config.Db)

	// agent
	agentService := wire.InitializeAgentService(config.Db)

	// agree_service
	agreeService := wire.InitializeAgreeService(config.Db)

	// auditlog
	auditlogService := wire.InitializeAuditlogService(config.Db)

	// calendar
	calendar := wire.InitializeCalendarService(config.Db, config.OpenSearch, config.SQS)

	// client certificate
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)

	// comment
	commentService := wire.InitializeCommentService(config.Db, config.S3, config.S3PreSign)

	// examination
	examinationService := wire.InitializeExaminationService(config.Db)

	// examTimeSlot
	examTimeSlot := wire.InitializeExamTimeSlotService(config.Db)

	// freee
	freeeService := wire.InitializeFreeeService(config.Db)

	// hospital
	hospitalService := wire.InitializeHospitalUseCase(config.Db, config.Redis, config.SQS, config.SES, config.OpenSearch)

	// hospitalCheck
	hospitalStatusCheckService := wire.InitializeHospitalStatusCheckService(config.Db)

	// import external data hospital
	importDataService := wire.InitializeImportDataService(config.Db, config.S3)

	// label
	labelService := wire.InitializeLabelService(config.Db)

	// line
	lineService := wire.InitializeLineService(config.Db, config.Line, config.SQS)

	// mail
	mailService := wire.InitializeMailService(config.Db, config.SQS, config.SES)

	// meeting
	meetingService := wire.InitializeMeetingService(config.Db, config.Chime, config.Redis, config.OpenSearch, config.SQS, config.S3, config.S3PreSign)

	// survey
	surveyUsecase := wire.InitializeSurveyUsecase(config.Db, config.SQS, config.S3, config.S3PreSign, config.Redis)

	// message
	messageService := wire.InitializeMessageService(config.Db, config.Redis, config.OpenSearch, config.SQS, config.S3, config.S3PreSign)

	// patient
	patientService := wire.InitializePatientService(config.Db, config.S3, config.S3PreSign)

	// payment
	paymentService := wire.InitializePaymentService(config.Db, 90*time.Second) // timeout参考 https://docs.fincode.jp/develop_support/restriction

	// pharmacy
	pharmacyDeliveryService := wire.InitializePharmacyDeliveryService(config.Db)
	pharmacyHolidayService := wire.InitializePharmacyHolidayService(config.Db)
	pharmacyPatientFileService := wire.InitializePharmacyPatientFileService(config.Db, config.S3, config.S3PreSign)
	pharmacyReserveService := wire.InitializePharmacyReserveService(config.Db, config.SQS)
	pharmacyReserveDetailService := wire.InitializePharmacyReserveDetailService(config.Db)

	// portal_address
	portalAddressService := wire.InitializePortalAddressService(config.Db)

	// portal_hospital_notification
	portalHospitalNotification := wire.InitializePortalHospitalNotificationService(config.Db)

	// portalHospital
	portalHospitalService := wire.InitializePortalHospitalService(shutdownCtx, config.Db, config.S3PreSign, config.S3, config.OpenSearch, config.SQS)

	// portal_hospital_staff
	portalHospitalStaff := wire.InitializePortalHospitalStaffService(config.Db, config.S3PreSign, config.S3)

	// prescriptionReception
	prescriptionReceptionService := wire.InitializePrescriptionReceptionService(config.Db, config.S3, config.S3PreSign)

	// reservation
	reservation := wire.InitializeReservationService(config.Db, config.OpenSearch, config.SQS)

	// schema file
	schemaService := wire.InitializeSchemaService(config.Db, config.S3, config.S3PreSign)

	// session
	sessionService := wire.InitializeSessionService(os.Getenv("URL_AUTH_SERVER"))

	// signup
	signupService := wire.InitializeSignupService(config.Db, config.Redis)

	// sms
	smsService := wire.InitializeSMSService(config.Db, config.SQS, config.Redis)

	// specialist
	specialistService := wire.InitializeSpecialistService(config.Db)

	// staff
	staffService := wire.InitializeStaffService(config.Db, config.Redis)

	// surveyAnswerNoPatient
	surveyAnswerNoPatientService := wire.InitializeSurveyAnswerNoPatientService(config.Db, config.S3PreSign)

	// tag
	tagService := wire.InitializeTagService(config.Db)

	// task
	taskService := wire.InitializeTaskService(config.Db, config.S3, config.S3PreSign, config.Redis)

	// template document
	templateDocService := wire.InitializeTemplateDocService(config.Db, config.S3PreSign, config.Redis, config.S3)

	// treatmentCategory
	treatmentCategoryService := wire.InitializeTreatmentCategoryService(config.Db)

	// treatmentDepartment
	treatmentDepartment := wire.InitializeTreatmentDepartmentService(config.Db, config.OpenSearch, config.SQS)

	// zendesk
	zendeskService := wire.InitializeZendeskService(config.Db, config.Redis)

	// =======================================================================
	// demo
	// ocr
	ocrService := wire.InitializeOcrService(config.S3, config.S3PreSign, config.Redis)
	// ai
	aiService := wire.InitializeAIService(shutdownCtx, config.Db)
	// =======================================================================

	// =======================================================================
	// demo
	// fax
	faxService := wire.InitializeFaxService(config.Db, config.S3, config.S3PreSign, config.SQS)

	// message
	patientMessageService := wire.InitializePatientMessageService(config.Db, config.Redis, config.OpenSearch, config.SQS, config.S3, config.S3PreSign)
	// =======================================================================

	

	// graphql
	rslv := resolver.Resolver{
		AddressService:                    addressService,
		AgentService:                      agentService,
		AgreeService:                      agreeService,
		AIService:                         aiService,
		AuditlogService:                   auditlogService,
		CalendarService:                   calendar,
		ClientCertificateService:          clientCertificateService,
		CommentService:                    commentService,
		ExaminationService:                examinationService,
		ExamTimeSlotService:               examTimeSlot,
		FaxService:                        faxService,
		FreeeService:                      freeeService,
		HospitalService:                   hospitalService,
		ImportDataService:                 importDataService,
		LabelService:                      labelService,
		LineService:                       lineService,
		MailService:                       mailService,
		MeetingService:                    meetingService,
		MessageService:                    messageService,
		OcrService:                        ocrService,
		PatientService:                    patientService,
		PaymentService:                    paymentService,
		PharmacyPatientFileService:        pharmacyPatientFileService,
		PharmacyDeliveryService:           pharmacyDeliveryService,
		PharmacyHolidayService:            pharmacyHolidayService,
		PharmacyReserveDetailService:      pharmacyReserveDetailService,
		PharmacyReserveService:            pharmacyReserveService,
		PrescriptionReceptionService:      prescriptionReceptionService,
		PortalAddressService:              portalAddressService,
		PortalHospitalNotificationService: portalHospitalNotification,
		PortalHospitalService:             portalHospitalService,
		PortalHospitalStaffService:        portalHospitalStaff,
		ReservationService:                reservation,
		SchemaService:                     schemaService,
		SessionService:                    sessionService,
		SignupService:                     signupService,
		SMSService:                        smsService,
		SpecialistService:                 specialistService,
		StaffService:                      staffService,
		SurveyService:                     surveyUsecase,
		SurveyAnswerNoPatientService:      surveyAnswerNoPatientService,
		TagService:                        tagService,
		TaskService:                       taskService,
		TemplateDocService:                templateDocService,
		TreatmentCategoryService:          treatmentCategoryService,
		TreatmentDepartmentService:        treatmentDepartment,
		ZendeskService:                    zendeskService,
		HospitalStatusCheckService:        hospitalStatusCheckService,
		PatientMessageService:             patientMessageService,
	}

	// 顧客用APIのハンドラー
	handler := newDefaultServerWithoutIntrospection(
		graph.NewExecutableSchema(
			graph.Config{
				Resolvers: &rslv,
				Directives: directive.Directive(
					&directive.Handlers{
						AuthHandler: myMiddleware.NewAuthHandler(
							agreeService,
							clientCertificateService,
							hospitalService,
							sessionService,
							staffService,
							hospitalStatusCheckService),
						InternalAuthHandler: myMiddleware.NewInternalAuthHandler(),
					}),
			},
		),
	)
	// 前段のhasuraがInstrospectionクエリ使用するのでrootPathは無条件に有効化
	handler.Use(extension.Introspection{})

	// gql複雑度と深さチェック
	handler.Use(extension.FixedComplexityLimit(constant.GqlComplexityLimit))
	handler.Use(depth.FixedDepthLimit(constant.GqlDepthLimit))

	handler.AroundResponses(myMiddleware.TransactionHandler(config.Db))
	handler.AroundResponses(myMiddleware.Errorhandler())

	// 顧客用
	e.POST(constant.APIPathRoot, func(c echo.Context) error {
		handler.ServeHTTP(c.Response(), c.Request())
		return nil
	})

	// 本番環境ではplaygroundを閉じる
	// 開発環境でも脆弱性診断用に閉じれるようにした
	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		e.GET(constant.APIPathGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathRoot).ServeHTTP(c.Response(), c.Request())
			return nil
		})

		e.GET(constant.APIPathOperatorGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathOperator).ServeHTTP(c.Response(), c.Request())
			return nil
		})
	}

	// オペレータ用のPath
	// 顧客用のresolverをコピーして使いたいため、ここに配置する
	routeOperatorPath(e, &rslv)
}

/**
 * route /patient
 */
func routePatientPath(shutdownCtx context.Context, e *echo.Echo) {

	// staff
	sessionService := wire.InitializeSessionService(os.Getenv("URL_AUTH_SERVER"))

	// message
	patientMessageService := wire.InitializePatientMessageService(config.Db, config.Redis, config.OpenSearch, config.SQS, config.S3, config.S3PreSign)

	// patientPortalHospital
	patientPortalHospitalService := wire.InitializePatientPortalHospitalService(config.Db)

	// patient
	patientService := wire.InitializePatientService(config.Db, config.S3, config.S3PreSign)

	// mail
	mailService := wire.InitializeMailService(config.Db, config.SQS, config.SES)

	// ai
	aiService := wire.InitializeAIService(shutdownCtx, config.Db)

	// graphql
	rslvPatient := resolver.Resolver{
		PatientMessageService:        patientMessageService,
		PatientPortalHospitalService: patientPortalHospitalService,
		MailService:                  mailService,
		AIService:                    aiService,
		PatientService:               patientService,
	}

	handlerPatient := newDefaultServerWithoutIntrospection(
		graph.NewExecutableSchema(
			graph.Config{
				Resolvers: &rslvPatient,
				Directives: directive.Directive(
					&directive.Handlers{
						PatientAuthHandler: myMiddleware.NewPatientAuthHandler(sessionService),
					}),
			},
		),
	)

	// gql複雑度と深さチェック
	handlerPatient.Use(extension.FixedComplexityLimit(constant.GqlComplexityLimit))
	handlerPatient.Use(depth.FixedDepthLimit(constant.GqlDepthLimit))

	// Transactionのハンドリング
	handlerPatient.AroundResponses(myMiddleware.TransactionHandler(config.Db))
	handlerPatient.AroundResponses(myMiddleware.Errorhandler())

	e.POST(constant.APIPathPatient, func(c echo.Context) error {
		handlerPatient.ServeHTTP(c.Response(), c.Request())
		return nil
	})

	// 本番環境ではplaygroundを閉じる
	// 開発環境でも脆弱性診断用に閉じれるようにした
	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		e.GET(constant.APIPathPatientGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathPatient).ServeHTTP(c.Response(), c.Request())
			return nil
		})
	}
}

/**
 * route /auth
 */
func routeAuthPath(e *echo.Echo) {

	// auth
	authService := wire.InitializeAuthService(config.Redis)

	// graphql
	rslv := resolver.Resolver{
		AuthService: authService,
	}

	// internal無通信なので認証は不要
	handlerAuth := newDefaultServerWithoutIntrospection(
		graph.NewExecutableSchema(
			graph.Config{
				Resolvers: &rslv,
			},
		),
	)

	// gql複雑度と深さチェック
	handlerAuth.Use(extension.FixedComplexityLimit(constant.GqlComplexityLimit))
	handlerAuth.Use(depth.FixedDepthLimit(constant.GqlDepthLimit))

	// Transactionのハンドリング
	handlerAuth.AroundResponses(myMiddleware.Errorhandler())

	e.POST(constant.APIPathAuth, func(c echo.Context) error {
		handlerAuth.ServeHTTP(c.Response(), c.Request())
		return nil
	})

	// 本番環境ではplaygroundを閉じる
	// 開発環境でも脆弱性診断用に閉じれるようにした
	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		e.GET(constant.APIPathAuthGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathAuth).ServeHTTP(c.Response(), c.Request())
			return nil
		})
	}

	// RestAPI用のAuthハンドラーを初期化
	// client certificate
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)
	// staff
	sessionService := wire.InitializeSessionService(os.Getenv("URL_AUTH_SERVER"))
	// staff
	staffService := wire.InitializeStaffService(config.Db, config.Redis)
	// hospital
	hospitalService := wire.InitializeHospitalUseCase(config.Db, config.Redis, config.SQS, config.SES, config.OpenSearch)
	// agree
	agreeService := wire.InitializeAgreeService(config.Db)
	restAuthHandler := myMiddleware.NewRestAuthHandler(clientCertificateService, sessionService, staffService, hospitalService, agreeService)

	authPathGroup := e.Group(constant.APIPathAuth)
	authPathGroup.Any("/check", restAuthHandler.Auth(NewInternalAuth(staffService).Check))
	authPathGroup.Any("/agent/check", restAuthHandler.AuthAgent(NewInternalAuth(staffService).AuthAgent))
}

/**
 * route /agentws path
 */
func routeAgentPath(e *echo.Echo) {
	// agent
	agentService := wire.InitializeAgentService(config.Db)

	// agent ws
	agentHandler := agent.NewAgentHandler(agentService)

	e.GET(constant.APIPathAgentWebsocket,
		func(ctx echo.Context) error {
			return agentHandler.HandleAgentWebSocket(ctx)
		})
}

/**
 * route /meetings path
 */
func routeMeetingsPath(e *echo.Echo) {
	staffService := wire.InitializeStaffService(config.Db, config.Redis)
	sessionService := wire.InitializeSessionService(os.Getenv("URL_AUTH_SERVER"))
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)
	hospitalService := wire.InitializeHospitalUseCase(config.Db, config.Redis, config.SQS, config.SES, config.OpenSearch)
	meetingService := wire.InitializeMeetingService(config.Db, config.Chime, config.Redis, config.OpenSearch, config.SQS, config.S3, config.S3PreSign)
	agreeService := wire.InitializeAgreeService(config.Db)
	restAuthHandler := myMiddleware.NewRestAuthHandler(clientCertificateService, sessionService, staffService, hospitalService, agreeService)

	handler := NewMeetingHandler(meetingService, sessionService)
	meetingGroup := e.Group(constant.APIPathMeetings)
	meetingGroup.POST("/message", handler.PostMessage(), myMiddleware.RestTransactionHandler(config.Db))
	meetingGroup.POST("/subscribe", handler.SubscribeEventFromChime(), myMiddleware.RestTransactionHandler(config.Db))
	meetingGroup.POST("/subscribe/:id", handler.SubscribeNotify(), myMiddleware.RestTransactionHandler(config.Db))
	meetingGroup.GET("/:id", handler.GetMeeting(), restAuthHandler.Auth)
	meetingGroup.POST("/:id", handler.CreateMeeting(), restAuthHandler.Auth)
	meetingGroup.POST("/:id/join", handler.JoinMeeting(), restAuthHandler.Auth)
	meetingGroup.DELETE("/:id", handler.LeaveMeeting(), myMiddleware.RestTransactionHandler(config.Db), restAuthHandler.Auth)
}

func routeDownloadClientCertificate(e *echo.Echo) {
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)
	clientCertificategroup := e.Group(constant.APIPathClientCertificate)
	handler := NewClientCertificateHandler(clientCertificateService)
	clientCertificategroup.GET("*", handler.Download())
}

func routeVerifyMail(e *echo.Echo) {
	mailService := wire.InitializeMailService(config.Db, config.SQS, config.SES)
	handler := NewVerifyMailHandler(mailService)
	e.GET(constant.APIPathVerifyMail, handler.VerifyMail, myMiddleware.RestTransactionHandler(config.Db))
}

/**
 * route /operator path
 * オペレータ専用のサービスは顧客に表示したくないため、ここで定義する
 */
func routeOperatorPath(e *echo.Echo, customerRslv *resolver.Resolver) {
	// 明示的なコピー
	operatorRslv := *customerRslv

	// オペレータ用のサービスを設定
	operatorService := wire.InitializeOperatorService(config.CognitoIdp, config.Db)
	operatorRslv.OperatorService = operatorService

	// オペレーター用APIのハンドラー作成
	handlerOperator := newDefaultServerWithoutIntrospection(
		graph.NewExecutableSchema(
			graph.Config{
				Resolvers: &operatorRslv,
				Directives: directive.Directive(
					&directive.Handlers{
						AuthHandler: myMiddleware.NewOperatorAuthHandler(operatorService),
					}),
			},
		),
	)

	// gql複雑度と深さチェック
	handlerOperator.Use(extension.FixedComplexityLimit(constant.GqlComplexityLimit))
	handlerOperator.Use(depth.FixedDepthLimit(constant.GqlDepthLimit))

	handlerOperator.AroundResponses(myMiddleware.TransactionHandler(config.Db))
	handlerOperator.AroundResponses(myMiddleware.Errorhandler())

	// オペレーター用
	e.POST(constant.APIPathOperator, func(c echo.Context) error {
		handlerOperator.ServeHTTP(c.Response(), c.Request())
		return nil
	})

	// 本番環境ではplaygroundを閉じる
	// 開発環境でも脆弱性診断用に閉じれるようにした
	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		e.GET(constant.APIPathOperatorGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathOperator).ServeHTTP(c.Response(), c.Request())
			return nil
		})
	}
}

/**
 * route /internal
 * lambda/opesysなどから呼ばれることを想定
 */
func routeInternalPath(shutdownCtx context.Context, e *echo.Echo) {
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)
	handler := NewClientCertificateHandler(clientCertificateService)
	internalGroup := e.Group(constant.APIPathInternal, myMiddleware.RestTransactionHandler(config.Db))
	internalGroup.POST("/genCertificate", handler.Generate)
	

	// ai
	aiService := wire.InitializeAIService(shutdownCtx, config.Db)
	// クリニックマスターデータ
	mClinicMasterDataService := wire.InitializeMClinicMasterDataService(config.Db, config.PgxPool, config.S3)
	// graphql
	rslv := resolver.Resolver{
		AIService: aiService,
		MClinicMasterDataService:           mClinicMasterDataService,
	}

	// internal内通信なので認証は不要
	handlerInternal := newDefaultServerWithoutIntrospection(
		graph.NewExecutableSchema(
			graph.Config{
				Resolvers: &rslv,
			},
		),
	)

	// gql複雑度と深さチェック
	handlerInternal.Use(extension.FixedComplexityLimit(constant.GqlComplexityLimit))
	handlerInternal.Use(depth.FixedDepthLimit(constant.GqlDepthLimit))

	// Transactionのハンドリング
	handlerInternal.AroundResponses(myMiddleware.Errorhandler())
	handlerInternal.AroundResponses(myMiddleware.TransactionHandler(config.Db))

	e.POST(constant.APIPathInternal, func(c echo.Context) error {
		handlerInternal.ServeHTTP(c.Response(), c.Request())
		return nil
	})

	// 本番環境ではplaygroundを閉じる
	// 開発環境でも脆弱性診断用に閉じれるようにした
	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		e.GET(constant.APIPathInternalGraphql, func(c echo.Context) error {
			playground.Handler("GraphQL", constant.APIPathInternal).ServeHTTP(c.Response(), c.Request())
			return nil
		})
	}
}

/**
 * route /pharmacy
 */
func routePharmacyPath(e *echo.Echo) {
	// client certificate
	clientCertificateService := wire.InitializeClientCertificateService(config.Db)

	// staff
	sessionService := wire.InitializeSessionService(os.Getenv("URL_AUTH_SERVER"))

	// staff
	staffService := wire.InitializeStaffService(config.Db, config.Redis)

	// hospital
	hospitalService := wire.InitializeHospitalUseCase(config.Db, config.Redis, config.SQS, config.SES, config.OpenSearch)
	// agree
	agreeService := wire.InitializeAgreeService(config.Db)
	// RestAPI用のAuthハンドラーを初期化
	restAuthHandler := myMiddleware.NewRestAuthHandler(clientCertificateService, sessionService, staffService, hospitalService, agreeService)

	// DB Transactionハンドラーをmiddlewareとしてセット
	pharmacyGroup := e.Group(constant.APIPathPharmacy, myMiddleware.RestTransactionHandler(config.Db))

	pharmacyDeliveryService := wire.InitializePharmacyDeliveryService(config.Db)
	auditlogService := wire.InitializeAuditlogService(config.Db)
	mailService := wire.InitializeMailService(config.Db, config.SQS, config.SES)
	handler := NewPharmacyHandler(pharmacyDeliveryService, auditlogService, mailService)
	pharmacyGroup.GET("/deliveryCSV", handler.DownloadDeliveryCSV, restAuthHandler.Auth)
	pharmacyGroup.POST("/deliveryCSV", handler.UploadDeliveryCSV, restAuthHandler.Auth)
}

func routeHealthCheckPath(e *echo.Echo) {
	// health check
	dbHealthCheckService := wire.InitializeHealthCheckService(config.Db, config.Redis)
	etlService := wire.InitializeETLService(config.OpenSearch)
	healthCheckHandler := NewHealthCheckHandler(dbHealthCheckService, etlService)
	healthCheckGroup := e.Group(constant.APIPathHealthCheck)
	healthCheckGroup.GET("", healthCheckHandler.healthCheck())
}

// github.com/99designs/gqlgen/graphql/handlerのNewDefaultServerから"srv.Use(extension.Introspection{})"を制御可能にしたもの
func newDefaultServerWithoutIntrospection(es graphql.ExecutableSchema) *graphqlHandler.Server {
	srv := graphqlHandler.New(es)

	srv.AddTransport(transport.Websocket{
		KeepAlivePingInterval: 10 * time.Second,
	})
	srv.AddTransport(transport.Options{})
	srv.AddTransport(transport.GET{})
	srv.AddTransport(transport.POST{})
	srv.AddTransport(transport.MultipartForm{})

	srv.SetQueryCache(lru.New(1000))

	if !config.IsProduction() && !config.IsCloseGraphqlPlayground() {
		// 本番環境以外はIntrospection クエリを許可する
		// 脆弱性診断用にクエリを許可しない設定を追加
		// curlでも実行できないようにIsCloseGraphqlPlaygroundを追加
		srv.Use(extension.Introspection{})
	}

	srv.Use(extension.AutomaticPersistedQuery{
		Cache: lru.New(100),
	})

	return srv
}

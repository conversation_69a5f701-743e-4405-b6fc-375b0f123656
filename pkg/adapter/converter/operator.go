package converter

import (
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	customEntity "denkaru-server/pkg/repository/model/custom"
)

// ConvertToOperatorLoginInput OperatorLoginReqをentityモデルに変換する
func ConvertToOperatorLoginInput(m *gqlModel.OperatorLoginReq) *customEntity.OperatorLoginInput {
	return &customEntity.OperatorLoginInput{
		OperatorName:     m.OperatorName,
		OperatorPassword: m.OperatorPassword,
		HospitalID:       m.HospitalID,
	}
}

// ConvertToOperatorLoginRes OperatorLoginOutputをGraphQLのモデルに変換する
func ConvertToOperatorLoginRes(m *customEntity.OperatorLoginOutput) *gqlModel.OperatorLoginRes {
	return &gqlModel.OperatorLoginRes{
		ChallengeName: m.ChallengeName,
		SessionValue:  m.SessionValue,
		PharmacyFlg:   m.PharmacyFlg,
		KarteStatus:   int(m.KarteStatus),
	}
}

// ConvertToOperatorVerifyMFACodeInput OperatorVerifyMFACodeReqをentityモデルに変換する
func ConvertToOperatorVerifyMFACodeInput(m *gqlModel.OperatorVerifyMFACodeReq) *customEntity.OperatorVerifyMFACodeInput {
	return &customEntity.OperatorVerifyMFACodeInput{
		SessionValue: m.SessionValue,
		PinCode:      m.PinCode,
	}
}

// ConvertToOperatorVerifyMFACodeRes OperatorVerifyMFACodeOutputをGraphQLのモデルに変換する
func ConvertToOperatorVerifyMFACodeRes(m *customEntity.OperatorVerifyMFACodeOutput) *gqlModel.OperatorVerifyMFACodeRes {
	return &gqlModel.OperatorVerifyMFACodeRes{
		ChallengeName: m.ChallengeName,
		SessionValue:  m.SessionValue,
		PharmacyFlg:   m.PharmacyFlg,
	}
}

// ConvertToOperatorChangePasswordInput OperatorChangePasswordReqをentityモデルに変換する
func ConvertToOperatorChangePasswordInput(m *gqlModel.OperatorChangePasswordReq) *customEntity.OperatorChangePasswordInput {
	return &customEntity.OperatorChangePasswordInput{
		SessionValue: m.SessionValue,
		NewPassword:  m.NewPassword,
	}
}

// ConvertToOperatorChangePasswordRes OperatorChangePasswordOutputをGraphQLのモデルに変換する
func ConvertToOperatorChangePasswordRes(m *customEntity.OperatorChangePasswordOutput) *gqlModel.OperatorChangePasswordRes {
	return &gqlModel.OperatorChangePasswordRes{
		ChallengeName: m.ChallengeName,
		SessionValue:  m.SessionValue,
		PharmacyFlg:   m.PharmacyFlg,
	}
}

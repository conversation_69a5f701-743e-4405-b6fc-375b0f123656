package converter

import (
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/repository/model/entity"
)

func ConvertAddress(address []*entity.PostCodeMst) (res *model.SearchAddressByPostcodeRes) {
	result := make([]*model.PostCodeMstModel, 0, len(address))
	for _, m := range address {
		result = append(result, &model.PostCodeMstModel{
			ID:             m.ID,
			PostCd:         m.PostCd,
			PrefKana:       m.PrefKana,
			CityKana:       m.CityKana,
			PostalTermKana: m.PostalTermKana,
			PrefName:       m.PrefName,
			CityName:       m.CityName,
			Banti:          m.<PERSON>,
			IsDeleted:      m.<PERSON>,
			Address:        m.PrefName + m.CityName + m.<PERSON>,
		})
	}
	return &model.SearchAddressByPostcodeRes{
		TotalCount:        len(address),
		PostCodeMstModels: result,
	}
}

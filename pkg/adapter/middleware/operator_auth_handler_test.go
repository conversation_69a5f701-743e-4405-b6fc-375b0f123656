package middleware_test

import (
	"context"
	"denkaru-server/pkg/adapter/middleware"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mock_service "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/session"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/99designs/gqlgen/graphql"
	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/vektah/gqlparser/v2/ast"
	"go.uber.org/mock/gomock"
)

func Test_operatorAuthHandler_Auth(t *testing.T) {

	hospitalID := 1
	staffID := 2

	testCases := []struct {
		name               string
		cookies            []*http.Cookie
		ctx                context.Context
		expectedKey        string
		expectedJWKError   error
		hasPermission      bool
		expectedErrorFunc1 error
		isAllowedIP        bool
		expectedErrorFunc2 error
		expectedSession    *serviceModel.Session
		expectedError      *myerrors.DenkaruError
	}{
		{
			name: "正常系",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorKarteStatus, Value: strconv.Itoa(int(constant.KarteStatusRealInUse))},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedSession: &serviceModel.Session{
				HospitalID:   &hospitalID,
				StaffID:      &staffID,
				StaffUserID:  &staffID,
				IsOperator:   constant.StatusTrue,
				OperatorName: "test",
				KarteStatus:  util.NewPtr(constant.KarteStatusRealInUse),
			},
		},
		{
			name:          "異常系_echoのContextが無い",
			ctx:           context.Background(),
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("could not retrieve echo.Context")),
		},
		{
			name: "異常系_cookieにIdTokenが無い",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorIdToken, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_Claimsの抽出に失敗する",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorKarteStatus, Value: strconv.Itoa(constant.KarteStatusNoUse)},
			},
			expectedJWKError: fmt.Errorf("jwk error"),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("jwk error")),
		},
		{
			name: "異常系_hospitalIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hospitalIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorHospitalID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hasPermissionがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission")),
		},
		{
			name: "異常系_HasPermissionがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedErrorFunc1: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
		{
			name: "異常系_isAllowedIPがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP")),
		},
		{
			name: "異常系_IsAllowedIPForGroupがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission:      true,
			expectedErrorFunc2: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
		{
			name: "異常系_karteStatusが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorKarteStatus, Value: "not-a-number"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("strconv.Atoi: parsing \"not-a-number\": invalid syntax")),
		},
		{
			name: "異常系_karteStatusが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				// karteStatus cookie is missing
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorKarteStatus, fmt.Errorf("http: named cookie not present"))),
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			testee := middleware.NewOperatorAuthHandler(operatorService)

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			for _, cookie := range testCase.cookies {
				req.AddCookie(cookie)
			}
			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.Background()
			ctx = context.WithValue(ctx, constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}
			ctx = graphql.WithOperationContext(ctx, &graphql.OperationContext{
				Operation: &ast.OperationDefinition{
					Operation: ast.Query,
				},
			})

			// JWKのクライアントをMockに差し替える
			util.JWKClient = test_mock.NewJWKTestMock(testCase.expectedKey, testCase.expectedJWKError)

			operatorService.EXPECT().HasPermission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ ast.Operation, _ string, _ string, _ []string) (bool, error) {
					return testCase.hasPermission, testCase.expectedErrorFunc1
				}).AnyTimes()

			operatorService.EXPECT().
				IsAllowedIPForGroup(gomock.Any(), gomock.Any()).
				Return(testCase.isAllowedIP, testCase.expectedErrorFunc2).AnyTimes()

			newCtx, err := testee.Auth(ctx, nil)

			if err != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, testCase.expectedError.Code, denkaruError.Code)
				}
			}

			if newCtx != nil {
				sess, _ := session.GetSession(newCtx)
				assert.Equal(t, testCase.expectedSession, sess)
			}

		})
	}
}

func Test_operatorAuthHandler_AuthWebSocket(t *testing.T) {

	hospitalID := 1
	staffID := 2

	testCases := []struct {
		name               string
		cookies            []*http.Cookie
		ctx                context.Context
		expectedKey        string
		expectedJWKError   error
		hasPermission      bool
		expectedErrorFunc1 error
		isAllowedIP        bool
		expectedErrorFunc2 error
		expectedSession    *serviceModel.Session
		expectedError      *myerrors.DenkaruError
	}{
		{
			name: "正常系",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorKarteStatus, Value: strconv.Itoa(int(constant.KarteStatusRealInUse))},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedSession: &serviceModel.Session{
				HospitalID:   &hospitalID,
				StaffID:      &staffID,
				StaffUserID:  &staffID,
				IsOperator:   constant.StatusTrue,
				OperatorName: "test",
				KarteStatus:  util.NewPtr(constant.KarteStatusRealInUse),
			},
		},
		{
			name:          "異常系_echoのContextが無い",
			ctx:           context.Background(),
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("could not retrieve echo.Context")),
		},
		{
			name: "異常系_cookieにIdTokenが無い",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorIdToken, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_Claimsの抽出に失敗する",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedJWKError: fmt.Errorf("jwk error"),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("jwk error")),
		},
		{
			name: "異常系_hospitalIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hospitalIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorHospitalID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hasPermissionがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission")),
		},
		{
			name: "異常系_HasPermissionがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedErrorFunc1: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
		{
			name: "異常系_isAllowedIPがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP")),
		},
		{
			name: "異常系_IsAllowedIPForGroupがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission:      true,
			expectedErrorFunc2: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			testee := middleware.NewOperatorAuthHandler(operatorService)

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			for _, cookie := range testCase.cookies {
				req.AddCookie(cookie)
			}
			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.Background()
			ctx = context.WithValue(ctx, constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}
			ctx = graphql.WithOperationContext(ctx, &graphql.OperationContext{
				Operation: &ast.OperationDefinition{
					Operation: ast.Query,
				},
			})

			// JWKのクライアントをMockに差し替える
			util.JWKClient = test_mock.NewJWKTestMock(testCase.expectedKey, testCase.expectedJWKError)

			operatorService.EXPECT().HasPermission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ ast.Operation, _ string, _ string, _ []string) (bool, error) {
					return testCase.hasPermission, testCase.expectedErrorFunc1
				}).AnyTimes()

			operatorService.EXPECT().
				IsAllowedIPForGroup(gomock.Any(), gomock.Any()).
				Return(testCase.isAllowedIP, testCase.expectedErrorFunc2).AnyTimes()

			newCtx, err := testee.AuthWebSocket(ctx)

			if err != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, testCase.expectedError.Code, denkaruError.Code)
				}
			}

			if newCtx != nil {
				sess, _ := session.GetSession(newCtx)
				assert.Equal(t, testCase.expectedSession, sess)
			}

		})
	}
}

func Test_operatorAuthHandler_AuthAgree(t *testing.T) {

	hospitalID := 1
	staffID := 2

	testCases := []struct {
		name               string
		cookies            []*http.Cookie
		ctx                context.Context
		expectedKey        string
		expectedJWKError   error
		hasPermission      bool
		expectedErrorFunc1 error
		isAllowedIP        bool
		expectedErrorFunc2 error
		expectedSession    *serviceModel.Session
		expectedError      *myerrors.DenkaruError
	}{
		{
			name: "正常系",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorKarteStatus, Value: strconv.Itoa(int(constant.KarteStatusRealInUse))},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedSession: &serviceModel.Session{
				HospitalID:   &hospitalID,
				StaffID:      &staffID,
				StaffUserID:  &staffID,
				IsOperator:   constant.StatusTrue,
				OperatorName: "test",
				KarteStatus:  util.NewPtr(constant.KarteStatusRealInUse),
			},
		},
		{
			name:          "異常系_echoのContextが無い",
			ctx:           context.Background(),
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("could not retrieve echo.Context")),
		},
		{
			name: "異常系_cookieにIdTokenが無い",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorIdToken, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_Claimsの抽出に失敗する",
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedJWKError: fmt.Errorf("jwk error"),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("jwk error")),
		},
		{
			name: "異常系_hospitalIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが無い",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hospitalIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
				{Name: constant.DenkaruOperatorHospitalID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_staffIDが数字以外",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorStaffID, Value: "test"},
			},
			hasPermission: true,
			isAllowedIP:   true,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, fmt.Errorf("http: named cookie not present"))),
		},
		{
			name: "異常系_hasPermissionがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission")),
		},
		{
			name: "異常系_HasPermissionがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			expectedErrorFunc1: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
		{
			name: "異常系_isAllowedIPがfalse",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission: true,
			isAllowedIP:   false,
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP")),
		},
		{
			name: "異常系_IsAllowedIPForGroupがDBエラー",
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "dsjhfaw0e9fpui-0wmgnrihgnoierjdsagfpsodk",
					"alg": "RS256",
					"n": "iWCsDdpm5gNvrhpk7t1maGEvIdm4kJyXrbExkRGanDXrV2ti0yH1GBSQPg_rQqFTgGhQKU_p22yJeQauDdfUep0CLVCh631x4h7YGvA98gPFLevPd4vZ-rDctkhUIvF_b1psLVuk2Hntu2iKddoSaJK5NUiWh8rOXwH6KYSck35kWQ6U0RkNwQU-nRnVgzSz47jZU1Smb4xLytpDogeC1T0NKe9mnC3LvEOnIqCt_F_YWLlbl6hI8WEY06XRswuKihAMfeE33HRoDT2JICWzU2Ozoe9gTOAcrLTqX_C4LDlyRoKOQ_oCIWWGxPF9Mmv1yxZpx6Q2pWoxRxshDqpOfQ"
				}
			  ]
			}
			`,
			cookies: []*http.Cookie{
				{Name: constant.DenkaruOperatorIdToken, Value: "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ"},
				{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(hospitalID)},
				{Name: constant.DenkaruOperatorStaffID, Value: strconv.Itoa(staffID)},
			},
			hasPermission:      true,
			expectedErrorFunc2: fmt.Errorf("test"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("test")),
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			testee := middleware.NewOperatorAuthHandler(operatorService)

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			for _, cookie := range testCase.cookies {
				req.AddCookie(cookie)
			}
			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.Background()
			ctx = context.WithValue(ctx, constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}
			ctx = graphql.WithOperationContext(ctx, &graphql.OperationContext{
				Operation: &ast.OperationDefinition{
					Operation: ast.Query,
				},
			})

			// JWKのクライアントをMockに差し替える
			util.JWKClient = test_mock.NewJWKTestMock(testCase.expectedKey, testCase.expectedJWKError)

			operatorService.EXPECT().HasPermission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ ast.Operation, _ string, _ string, _ []string) (bool, error) {
					return testCase.hasPermission, testCase.expectedErrorFunc1
				}).AnyTimes()

			operatorService.EXPECT().
				IsAllowedIPForGroup(gomock.Any(), gomock.Any()).
				Return(testCase.isAllowedIP, testCase.expectedErrorFunc2).AnyTimes()

			newCtx, err := testee.AuthAgree(ctx)

			if err != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, testCase.expectedError.Code, denkaruError.Code)
				}
			}

			if newCtx != nil {
				sess, _ := session.GetSession(newCtx)
				assert.Equal(t, testCase.expectedSession, sess)
			}

		})
	}
}

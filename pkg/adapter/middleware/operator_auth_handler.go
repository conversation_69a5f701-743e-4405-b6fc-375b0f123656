package middleware

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/service"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util/session"
	"fmt"
	"strconv"

	"github.com/99designs/gqlgen/graphql"
	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/labstack/echo/v4"
)

type operatorAuthHandler struct {
	operatorService service.IOperatorService
}

func NewOperatorAuthHandler(operatorService service.IOperatorService) IAuthHandler {
	return &operatorAuthHandler{
		operatorService: operatorService,
	}
}

// Auth 代理ログイン用のセッション情報をCognitoを用いて認証する
func (h *operatorAuthHandler) Auth(ctx context.Context, _ *gqlModel.PermissionInput) (newCtx context.Context, err error) {
	// Step 1) Echoのコンテキストを取得
	c, err := EchoContextFromContext(ctx)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 2) CookieからIdトークンを取得
	idToken, err := session.GetCookie(c, constant.DenkaruOperatorIdToken)
	if err != nil {
		return
	}

	// Step 3) Cognitoトークンの検証
	claims, err := session.ExtractCognitoTokenClaims(ctx, idToken, false)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 4) 権限チェック
	oc := graphql.GetOperationContext(ctx)
	hasPermission, err := h.operatorService.HasPermission(oc.Operation.Operation, oc.OperationName, session.GetFunctionNameFromHeader(c), claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 権限が無い
	if !hasPermission {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission"))
		return
	}

	// グループに対して許可されたIPかどうか確認
	isAllowedIP, err := h.operatorService.IsAllowedIPForGroup(c, claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 許可されたIPではない場合
	if !isAllowedIP {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP"))
		return
	}

	// Step 5) CSオペレーター用Cookieからセッション情報を作成
	sess, err := genSessionFromOperatorCookies(c, claims)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	newCtx = context.WithValue(ctx, constant.ContextKeySession, sess)
	return
}

// AuthWebSocket 代理ログイン用のセッション情報をCognitoを用いて認証する(Websocket)
func (h *operatorAuthHandler) AuthWebSocket(ctx context.Context) (newCtx context.Context, err error) {
	// Step 1) Echoのコンテキストを取得
	c, err := EchoContextFromContext(ctx)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 2) CookieからIdトークンを取得
	idToken, err := session.GetCookie(c, constant.DenkaruOperatorIdToken)
	if err != nil {
		return
	}

	// Step 3) Cognitoトークンの検証
	claims, err := session.ExtractCognitoTokenClaims(ctx, idToken, false)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 4) 権限チェック
	oc := graphql.GetOperationContext(ctx)
	hasPermission, err := h.operatorService.HasPermission(oc.Operation.Operation, oc.OperationName, session.GetFunctionNameFromHeader(c), claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 権限が無い
	if !hasPermission {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission"))
		return
	}

	// グループに対して許可されたIPかどうか確認
	isAllowedIP, err := h.operatorService.IsAllowedIPForGroup(c, claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 許可されたIPではない場合
	if !isAllowedIP {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP"))
		return
	}

	// Step 5) CSオペレーター用Cookieからセッション情報を作成
	sess, err := genSessionFromOperatorCookies(c, claims)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	newCtx = context.WithValue(ctx, constant.ContextKeySession, sess)
	return
}

// AuthAgree 同意項目API用の認証チェック
// IAuthHandlerを満たすために実装されている
func (h *operatorAuthHandler) AuthAgree(ctx context.Context) (newCtx context.Context, err error) {
	// Step 1) Echoのコンテキストを取得
	c, err := EchoContextFromContext(ctx)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 2) CookieからIdトークンを取得
	idToken, err := session.GetCookie(c, constant.DenkaruOperatorIdToken)
	if err != nil {
		return
	}

	// Step 3) Cognitoトークンの検証
	claims, err := session.ExtractCognitoTokenClaims(ctx, idToken, false)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// Step 4) 権限チェック
	oc := graphql.GetOperationContext(ctx)
	hasPermission, err := h.operatorService.HasPermission(oc.Operation.Operation, oc.OperationName, session.GetFunctionNameFromHeader(c), claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 権限が無い
	if !hasPermission {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("no permission"))
		return
	}

	// グループに対して許可されたIPかどうか確認
	isAllowedIP, err := h.operatorService.IsAllowedIPForGroup(c, claims.CognitoGroups)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	// 許可されたIPではない場合
	if !isAllowedIP {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeNoPermission, fmt.Errorf("not allowed IP"))
		return
	}

	// Step 5) CSオペレーター用Cookieからセッション情報を作成
	sess, err := genSessionFromOperatorCookies(c, claims)
	if err != nil {
		err = myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, err)
		return
	}

	newCtx = context.WithValue(ctx, constant.ContextKeySession, sess)
	return
}

// オペレーター用Cookieの情報から、session情報を作成する
// ついでにCookieの有効期限も延長する
func genSessionFromOperatorCookies(echoCtx echo.Context, claims *model.CognitoClaims) (sess *model.Session, err error) {

	// staffIDを取得
	staffIDStr, err := session.GetCookie(echoCtx, constant.DenkaruOperatorStaffID)
	if err != nil {
		return
	}
	staffID, err := strconv.Atoi(staffIDStr)
	if err != nil {
		return
	}

	// hospitalIDを取得
	hospitalIDStr, err := session.GetCookie(echoCtx, constant.DenkaruOperatorHospitalID)
	if err != nil {
		return
	}
	hospitalID, err := strconv.Atoi(hospitalIDStr)
	if err != nil {
		return
	}

	// karteStatusを取得
	karteStatusStr, err := session.GetCookie(echoCtx, constant.DenkaruOperatorKarteStatus)
	if err != nil {
		return
	}

	karteStatus, err := strconv.Atoi(karteStatusStr)
	if err != nil {
		return
	}

	// session情報を作成
	sess = model.NewSession(&hospitalID, &staffID, &staffID, nil, nil, nil, &karteStatus)
	sess.IsOperator = constant.StatusTrue
	sess.OperatorName = claims.CognitoUsername

	// cookieの有効期限を延長
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorStaffID, staffIDStr, constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorHospitalID, hospitalIDStr, constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorKarteStatus, karteStatusStr, constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	return
}

input IssueTokenReq {
  staffID: Int
  hospitalID: Int
  staffUserID: Int
  pharmacyFlg: Boolean
  email: String
  portalCustomerID: Int
  tokenTTL: Int!
  refreshTokenTTL: Int!
  loginID: String
  karteStatus: Int
}

type IssueTokenRes {
  token: String!
  tokenExpiryTime: String!
  refreshToken: String!
  refreshTokenExpiryTime: String!
}

input RefreshTokenReq {
  refreshToken: String!
  tokenTTL: Int!
  refreshTokenTTL: Int!
  isWebSocket: Boolean
}

type RefreshTokenRes {
  token: String!
  tokenExpiryTime: String!
  refreshToken: String!
  refreshTokenExpiryTime: String!
}

input VerifyTokenReq {
  token: String!
}

type VerifyTokenRes {
  isValid: Boolean!
}

type DeleteTokenRes {
  isDeleted: Boolean!
  isSessionRemaining: Boolean!
}

extend type Mutation {
  issueToken(input: IssueTokenReq!): IssueTokenRes!
  refreshToken(input: RefreshTokenReq!): RefreshTokenRes!
  verifyToken(input: VerifyTokenReq!): VerifyTokenRes!
  deleteToken(token: String!): DeleteTokenRes!
}

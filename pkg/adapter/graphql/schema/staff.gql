input LoginReq {
  loginId: String!
  password: String!
}

type LoginRes {
  success: Int!
  isLoginIdInitialized: Int!
  isPasswordInitialized: Int!
  staffInfo: StaffInfo!
  hospitalId: Int!
  karteStatus: Int!
  pharmacyFlg: Boolean!
}

type LogoutRes {
  success: Int!
  isSessionRemaining: Boolean!
}

type StaffInfo {
  staffId: Int!
  staffName: String!
  staffKana: String!
  staffType: Int!
  hospitalID: Int!
  email: String
  managerKbn: Int!
  loginId: String!
  status: Int!
  medicalLicenseNo: String
  mayakuLicenseNo: String
  permissions: [StaffPermission!]!
}

input StaffPermissionInput {
  functionCd: String!
  permission: Int! @validation(format: "eq=0")
}

type StaffPermission {
  functionCd: String!
  permission: Int!
}

input GetStaffListReq {
  limit: Int @validation(format: "min=1")
  cursor: Int
  searchName: String
}

type GetStaffListRes {
  staffs: [StaffInfo]!
}

input CreateStaffReq {
  staffName: String! @validation(format: "min=1,max=40")
  staffKana: String! @validation(format: "kanaInput,min=1,max=40")
  staffType: Int! @validation(format: "eq=1|eq=13|eq=14|eq=15")
  managerKbn: Int! @validation(format: "eq=0|eq=7")
  loginId: String! @validation(format: "loginID,min=1,max=30")
  medicalLicenseNo: String @validation(format: "omitempty,max=6")
  mayakuLicenseNo: String @validation(format: "omitempty,max=7")
  permissions: [StaffPermissionInput!]!
}

type CreateStaffRes {
  staffId: Int!
  staffName: String!
  staffKana: String!
  staffType: Int!
  managerKbn: Int!
  loginId: String!
  password: String!
  medicalLicenseNo: String
  mayakuLicenseNo: String
  permissions: [StaffPermission!]!
}

input UpdateStaffReq {
  staffId: Int!
  staffName: String! @validation(format: "min=1,max=40") @label(value: "氏名")
  staffKana: String!
    @validation(format: "kanaInput,min=1,max=40")
    @label(value: "フリガナ")
  staffType: Int!
    @validation(format: "eq=1|eq=13|eq=14|eq=15")
    @label(value: "スタッフ種別")
  managerKbn: Int!
    @validation(format: "eq=0|eq=7|eq=10")
    @label(value: "権限区分")
  status: Int! @validation(format: "eq=0|eq=1") @label(value: "ステータス")
  passwordReset: Int!
    @validation(format: "eq=0|eq=1")
    @label(value: "パスワードリセット")
  medicalLicenseNo: String @validation(format: "omitempty,max=6")
  mayakuLicenseNo: String @validation(format: "omitempty,max=7")
  permissions: [StaffPermissionInput!]!
}

type UpdateStaffRes {
  staffId: Int!
  staffName: String!
  staffKana: String!
  staffType: Int!
  loginId: String!
  managerKbn: Int!
  password: String!
  status: Int!
  medicalLicenseNo: String
  mayakuLicenseNo: String
  permissions: [StaffPermission!]!
}

input ChangePasswordReq {
  oldPassword: String! @validation(format: "min=1,max=30")
  newPassword: String! @validation(format: "min=8,max=30")
}

type ChangePasswordRes {
  success: Int!
}

input LoginInitialReq {
  newLoginId: String! @validation(format: "min=1,max=30")
  newPassword: String! @validation(format: "min=8,max=30")
}

type LoginInitialRes {
  success: Int!
}

type StaffWithTaskCount {
  staffId: Int!
  staffName: String!
  taskCount: Int!
}

type GetStaffListWithTaskCountRes {
  staffs: [StaffWithTaskCount]!
  totalTaskCount: Int!
}

extend type Query {
  getStaffListWithTaskCount: GetStaffListWithTaskCountRes! @isAuthenticated
  getStaffList(input: GetStaffListReq): GetStaffListRes! @isAuthenticated
  getStaffListSetting(input: GetStaffListReq): GetStaffListRes!
    @isAuthenticated(permission: { isAdmin: true })
  getDoctorsInHospital: [StaffInfo!]! @isAuthenticated
  getSessionInfo: LoginRes! @isAuthenticated
}

extend type Mutation {
  login(input: LoginReq!): LoginRes!
  logout: LogoutRes! @isAuthenticated
  createStaff(input: CreateStaffReq!): CreateStaffRes!
    @isAuthenticated(permission: { isAdmin: true })
  updateStaff(input: UpdateStaffReq!): UpdateStaffRes!
    @isAuthenticated(permission: { isAdmin: true })
  changePassword(input: ChangePasswordReq!): ChangePasswordRes! @isAuthenticated
  loginInitial(input: LoginInitialReq!): LoginInitialRes! @isAuthenticated
}

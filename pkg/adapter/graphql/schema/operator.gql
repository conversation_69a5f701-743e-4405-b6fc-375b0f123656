extend type Mutation {
  operatorLogin(input: OperatorLoginReq): OperatorLoginRes!
  operatorVerifyMFACode(
    input: OperatorVerifyMFACodeReq
  ): OperatorVerifyMFACodeRes!
  operatorChangePassword(
    input: OperatorChangePasswordReq
  ): OperatorChangePasswordRes!
}

input OperatorLoginReq {
  operatorName: String!
  operatorPassword: String!
  hospitalId: Int! @validation(format: "min=1")
}

type OperatorLoginRes {
  challengeName: String!
  sessionValue: String
  pharmacyFlg: Boolean!
  karteStatus: Int!
}

input OperatorVerifyMFACodeReq {
  sessionValue: String!
  pinCode: String! @validation(format: "numeric,len=6")
}

type OperatorVerifyMFACodeRes {
  challengeName: String!
  sessionValue: String
  pharmacyFlg: Boolean!
}

input OperatorChangePasswordReq {
  newPassword: String!
  sessionValue: String!
}

type OperatorChangePasswordRes {
  challengeName: String!
  sessionValue: String
  pharmacyFlg: Boolean!
}

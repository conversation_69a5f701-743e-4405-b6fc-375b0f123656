// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type AIAssistPrompt struct {
	PromptID     int    `json:"promptId"`
	Name         string `json:"name"`
	Title        string `json:"title"`
	Prompt       string `json:"prompt"`
	CanEdit      bool   `json:"canEdit"`
	IsLatest     bool   `json:"isLatest"`
	OrderValue   int    `json:"orderValue"`
	IsActive     bool   `json:"isActive"`
	BasePromptID *int   `json:"basePromptId,omitempty"`
}

type AIChatHistoryRequest struct {
	CustomerID int `json:"customerId"`
}

type AIChatHistoryResponse struct {
	ContentID   int    `json:"contentId"`
	HistoryJSON string `json:"historyJson"`
}

type AIChatMessageRequest struct {
	ContentID int    `json:"contentId"`
	Message   string `json:"message"`
}

type AIChatMessageResponse struct {
	AiMessage string `json:"aiMessage"`
}

type AIReport struct {
	RawData    *string `json:"rawData,omitempty"`
	Latency    string  `json:"latency"`
	IsFinished bool    `json:"isFinished"`
}

type AddAuditlogInput struct {
	EventCd   string  `json:"eventCd"`
	PatientID *int    `json:"patientId,omitempty"`
	Hosoku    *string `json:"hosoku,omitempty"`
}

type AgentSettingConfig struct {
	HospitalID       int    `json:"hospitalID"`
	JobType          int    `json:"jobType"`
	FileType         int    `json:"fileType"`
	SettingName      string `json:"settingName"`
	IsEnabled        bool   `json:"isEnabled"`
	SharedFolderPath string `json:"sharedFolderPath"`
}

type AgentToken struct {
	Token string `json:"token"`
}

type Agreement struct {
	ReceiveNotifications bool `json:"receiveNotifications"`
	TermsPrivacyAgreed   bool `json:"termsPrivacyAgreed"`
}

type Appointment struct {
	LastAppointmentDate *time.Time `json:"lastAppointmentDate,omitempty"`
	NextAppointmentDate *time.Time `json:"nextAppointmentDate,omitempty"`
}

type AttachFilesItemForm struct {
	FileName string `json:"fileName"`
	S3key    string `json:"s3key"`
}

type AttachFilesItemFormByPatient struct {
	FileName string `json:"fileName"`
	S3key    string `json:"s3key"`
}

type Auditlog struct {
	LogID        int64     `json:"logId"`
	LogDate      time.Time `json:"logDate"`
	HpID         int       `json:"hpId"`
	HpName       string    `json:"hpName"`
	LoginID      string    `json:"loginId"`
	UserID       int       `json:"userId"`
	UserName     string    `json:"userName"`
	EventCd      string    `json:"eventCd"`
	EventName    string    `json:"eventName"`
	PtID         *int64    `json:"ptId,omitempty"`
	PtName       *string   `json:"ptName,omitempty"`
	SinDay       *int      `json:"sinDay,omitempty"`
	RaiinNo      *int64    `json:"raiinNo,omitempty"`
	Machine      *string   `json:"machine,omitempty"`
	Hosoku       *string   `json:"hosoku,omitempty"`
	Message      *string   `json:"message,omitempty"`
	IsOperator   int       `json:"isOperator"`
	OperatorName string    `json:"OperatorName"`
	PageType     string    `json:"pageType"`
}

type Calendar struct {
	CalendarID                         int                     `json:"calendarID"`
	CalendarNameSettingType            *int                    `json:"calendarNameSettingType,omitempty"`
	DoctorID                           *int                    `json:"doctorID,omitempty"`
	CalendarName                       *string                 `json:"calendarName,omitempty"`
	NumberOfDoctors                    *int                    `json:"numberOfDoctors,omitempty"`
	Label                              *string                 `json:"label,omitempty"`
	CalendarTreatMentIds               []*int                  `json:"calendarTreatMentIds,omitempty"`
	ReservationMethodType              *int                    `json:"reservationMethodType,omitempty"`
	ReservableSlotSettingType          *int                    `json:"reservableSlotSettingType,omitempty"`
	ReservableStartDays                *int                    `json:"reservableStartDays,omitempty"`
	ReservableStartTime                *string                 `json:"reservableStartTime,omitempty"`
	ReservableMinutesBeforeExamEndTime *int                    `json:"reservableMinutesBeforeExamEndTime,omitempty"`
	CalendarBasicSettings              []*CalendarBasicSetting `json:"calendarBasicSettings,omitempty"`
	CalendarTreatMents                 []*CalendarTreatment    `json:"calendarTreatMents,omitempty"`
	Doctor                             *StaffInfo              `json:"doctor,omitempty"`
	IsActive                           bool                    `json:"isActive"`
	CalendarTimeSlot                   *int                    `json:"calendarTimeSlot,omitempty"`
}

type CalendarBasicSetting struct {
	CalendarBasicSettingID int        `json:"calendarBasicSettingID"`
	CalendarID             int        `json:"calendarID"`
	StartDate              time.Time  `json:"startDate"`
	EndDate                *time.Time `json:"endDate,omitempty"`
	CreatedBy              string     `json:"createdBy"`
	UpdatedBy              string     `json:"updatedBy"`
	CreatedAt              time.Time  `json:"createdAt"`
	UpdatedAt              time.Time  `json:"updatedAt"`
	DaysOfWeek             []int      `json:"daysOfWeek"`
	WeeksOfMonth           []int      `json:"weeksOfMonth"`
	CloseOnHolidayFlag     bool       `json:"closeOnHolidayFlag"`
	StartTime              string     `json:"startTime"`
	ReservableSlot         *int       `json:"reservableSlot,omitempty"`
	StartWaitingNumber     *int       `json:"startWaitingNumber,omitempty"`
	EndTime                string     `json:"endTime"`
	Calendar               *Calendar  `json:"calendar,omitempty"`
}

type CalendarBasicSettingInput struct {
	CalendarBasicSettingID *int       `json:"calendarBasicSettingID,omitempty"`
	StartDate              time.Time  `json:"startDate"`
	EndDate                *time.Time `json:"endDate,omitempty"`
	StartTime              string     `json:"startTime"`
	EndTime                string     `json:"endTime"`
	CloseOnHolidayFlag     bool       `json:"closeOnHolidayFlag"`
	DaysOfWeek             []int      `json:"daysOfWeek"`
	WeeksOfMonth           []int      `json:"weeksOfMonth"`
	ReservableSlot         *int       `json:"reservableSlot,omitempty"`
	StartWaitingNumber     *int       `json:"startWaitingNumber,omitempty"`
}

type CalendarTreatment struct {
	CalendarTreatmentID   int                  `json:"calendarTreatmentID"`
	CalendarID            int                  `json:"calendarID"`
	TreatmentDepartmentID int                  `json:"treatmentDepartmentID"`
	TreatmentDepartment   *TreatmentDepartment `json:"treatmentDepartment,omitempty"`
	Calendar              *Calendar            `json:"calendar,omitempty"`
}

type CalendarWorkingTime struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

type CancelClinicPaymentInput struct {
	ReserveDetailID int `json:"reserveDetailId"`
}

type CancelPharmacyPaymentInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type CancelReservationByIDInput struct {
	ReserveID       int `json:"reserveId"`
	ReserveDetailID int `json:"reserveDetailId"`
	CalendarID      int `json:"calendarId"`
}

type CancelReservePaymentInput struct {
	PaymentClinicDetailID int64 `json:"paymentClinicDetailId"`
	StaffID               int   `json:"staffId"`
}

type CardInfo struct {
	CardNo     string `json:"card_no"`
	Brand      string `json:"brand"`
	Expire     string `json:"expire"`
	HolderName string `json:"holderName"`
}

type CategoryWithTaskCount struct {
	CategoryID   int    `json:"categoryId"`
	CategoryName string `json:"categoryName"`
	TaskCount    int    `json:"taskCount"`
}

type ChangeHospitalInfoInput struct {
	ClinicStatus *ClinicStatusInput `json:"clinicStatus"`
	Clinic       *ClinicInfoInput   `json:"clinic,omitempty"`
}

type ChangePasswordReq struct {
	OldPassword string `json:"oldPassword" validate:"min=1,max=30"`
	NewPassword string `json:"newPassword" validate:"min=8,max=30"`
}

type ChangePasswordRes struct {
	Success int `json:"success"`
}

type ChannelMember struct {
	MemberID   string      `json:"memberId"`
	IsPatient  int         `json:"isPatient"`
	HasUnread  int         `json:"hasUnread"`
	MemberInfo *MemberInfo `json:"memberInfo"`
}

type ChannelMemberByPatient struct {
	MemberID   string      `json:"memberId"`
	IsPatient  int         `json:"isPatient"`
	HasUnread  int         `json:"hasUnread"`
	MemberInfo *MemberInfo `json:"memberInfo"`
}

type CheckNormalCalculateDoneInput struct {
	PtID    int `json:"ptId"`
	SinDate int `json:"sinDate"`
}

type Claude3ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type Claude3ChatRequest struct {
	AnthropicVersion string                `json:"anthropic_version"`
	System           string                `json:"system"`
	MaxTokens        int                   `json:"max_tokens"`
	Messages         []*Claude3ChatMessage `json:"messages"`
}

type Claude3Content struct {
	Type   string         `json:"type"`
	Source *Claude3Source `json:"source,omitempty"`
	Text   *string        `json:"text,omitempty"`
}

type Claude3Message struct {
	Role    string            `json:"role"`
	Content []*Claude3Content `json:"content"`
}

type Claude3Request struct {
	AnthropicVersion string            `json:"anthropic_version"`
	System           string            `json:"system"`
	MaxTokens        int               `json:"max_tokens"`
	Messages         []*Claude3Message `json:"messages"`
}

type Claude3Response struct {
	Content []*Claude3Content `json:"content"`
}

type Claude3Source struct {
	Type      string `json:"type"`
	MediaType string `json:"media_type"`
	Data      string `json:"data"`
}

type ClaudeRequest struct {
	Prompt            string   `json:"prompt"`
	MaxTokensToSample int      `json:"max_tokens_to_sample"`
	Temperature       float64  `json:"temperature"`
	TopP              float64  `json:"top_p"`
	TopK              float64  `json:"top_k"`
	StopSequences     []string `json:"stop_sequences"`
	AnthropicVersion  string   `json:"anthropic_version"`
}

type ClaudeResponse struct {
	Completion string `json:"completion"`
}

type ClinicInfoInput struct {
	Name                   string  `json:"name"`
	MedicalInstitutionCode *string `json:"medicalInstitutionCode,omitempty"`
	PostCode               *string `json:"postCode,omitempty"`
	Address1               *string `json:"address1,omitempty"`
	Address2               *string `json:"address2,omitempty"`
	PhoneNumber            *string `json:"phoneNumber,omitempty"`
	FaxNumber              *string `json:"faxNumber,omitempty"`
	HomepageURL            *string `json:"homepageURL,omitempty"`
	PrefNo                 *int    `json:"prefNo,omitempty"`
	InsuranceCategory      *string `json:"insuranceCategory,omitempty"`
}

type ClinicInput struct {
	Name                   string  `json:"name"`
	MedicalInstitutionCode *string `json:"medicalInstitutionCode,omitempty"`
	PostCode               *string `json:"postCode,omitempty"`
	Address1               *string `json:"address1,omitempty"`
	Address2               *string `json:"address2,omitempty"`
	PhoneNumber            *string `json:"phoneNumber,omitempty"`
	HomepageURL            *string `json:"homepageURL,omitempty"`
	PrefNo                 *int    `json:"prefNo,omitempty"`
	InsuranceCategory      *string `json:"insuranceCategory,omitempty"`
}

type ClinicPayment struct {
	PaymentClinicDetailID   int64     `json:"paymentClinicDetailId"`
	ReserveDetailID         int       `json:"reserveDetailId"`
	PaymentStatus           int       `json:"paymentStatus"`
	PaymentType             int       `json:"paymentType"`
	BillingAmount           int       `json:"billingAmount"`
	DepositAmount           int       `json:"depositAmount"`
	PaymentDate             time.Time `json:"paymentDate"`
	ExamDate                time.Time `json:"examDate"`
	TreatmentCategoryName   string    `json:"treatmentCategoryName"`
	TreatmentDepartmentName string    `json:"treatmentDepartmentName"`
}

type ClinicReserve struct {
	ReserveID        int       `json:"reserveId"`
	ClinicName       string    `json:"clinicName"`
	ReserveStartDate time.Time `json:"reserveStartDate"`
	ReserveEndDate   time.Time `json:"reserveEndDate"`
	MeetingStatus    int       `json:"meetingStatus"`
}

type ClinicStatus struct {
	IsOpenClinic                  bool  `json:"isOpenClinic"`
	IsInsuranceMedicalInstitution *bool `json:"isInsuranceMedicalInstitution,omitempty"`
}

type ClinicStatusInput struct {
	IsOpenClinic                  bool  `json:"isOpenClinic"`
	IsInsuranceMedicalInstitution *bool `json:"isInsuranceMedicalInstitution,omitempty"`
}

type CommentFileInput struct {
	OriginalFileName string `json:"originalFileName"`
	UploadFileURL    string `json:"uploadFileUrl"`
	FileSize         int    `json:"fileSize"`
}

type CommentInput struct {
	TaskID      int    `json:"taskId"`
	ContentText string `json:"contentText"`
	ContentHTML string `json:"contentHtml"`
}

type CreateClientCertificateReq struct {
	Label string `json:"Label"`
}

type CreateClientCertificateRes struct {
	ClientCertificateID int    `json:"ClientCertificateID"`
	InstallPassword     string `json:"InstallPassword"`
	DownloadURL         string `json:"DownloadURL"`
	Label               string `json:"Label"`
	CommonName          string `json:"CommonName"`
	TokenExpireTime     string `json:"TokenExpireTime"`
}

type CreateClinicMasterDataReq struct {
	HpID int `json:"HpID"`
}

type CreateClinicMasterDataRes struct {
	Message string `json:"message"`
}

type CreateCommentInput struct {
	CommentInput     *CommentInput       `json:"commentInput"`
	CommentFileInput []*CommentFileInput `json:"commentFileInput,omitempty"`
	TaskInput        *TaskInput          `json:"taskInput,omitempty"`
}

type CreateCommentResponse struct {
	CommentID int `json:"commentId"`
}

type CreateLabelInput struct {
	Label       string `json:"label" validate:"max=10"`
	Description string `json:"description" validate:"max=20"`
	ColorCode   string `json:"colorCode"`
}

type CreatePatientInput struct {
	KanaName     string `json:"kanaName" validate:"kanaInput,max=200"`
	Name         string `json:"name" validate:"omitempty,max=200"`
	Gender       int    `json:"gender" validate:"omitempty,eq=1|eq=2"`
	Birthdate    int    `json:"birthdate" validate:"omitempty,birthdateInput"`
	PhoneNumber1 string `json:"phoneNumber1" validate:"omitempty,number"`
	PhoneNumber2 string `json:"phoneNumber2" validate:"omitempty,number"`
	Email        string `json:"email" validate:"omitempty,email,max=100"`
	HomePost     string `json:"homePost" validate:"omitempty,postCode"`
	HomeAddress1 string `json:"homeAddress1" validate:"omitempty,max=100"`
	HomeAddress2 string `json:"homeAddress2" validate:"omitempty,max=100"`
}

type CreatePatientRes struct {
	PatientID int `json:"patientId"`
}

type CreatePharmacyHolidayInput struct {
	HolidayStartDate time.Time `json:"holidayStartDate"`
	HolidayEndDate   time.Time `json:"holidayEndDate"`
}

type CreatePortalHospitalInput struct {
	PortalHospitalInput *PortalHospitalInput       `json:"portalHospitalInput,omitempty"`
	FilesInput          []*PortalHospitalFileInput `json:"filesInput,omitempty"`
	BusinessTimes       []*PortalBusinessTimeInput `json:"businessTimes,omitempty"`
}

type CreatePortalHospitalNotificationInput struct {
	HospitalNotificationInput *PortalHospitalNotificationInput `json:"hospitalNotificationInput,omitempty"`
}

type CreatePortalHospitalNotificationRes struct {
	HospitalNotificationID int `json:"hospitalNotificationId"`
}

type CreatePortalHospitalStaffInput struct {
	HospitalStaffInput *HospitalStaffInput             `json:"hospitalStaffInput,omitempty"`
	FilesInput         []*PortalHospitalStaffFileInput `json:"filesInput,omitempty"`
}

type CreatePortalStaffRes struct {
	PortalStaffID int `json:"portalStaffId"`
}

type CreatePromptInput struct {
	Prompt *PromptInput `json:"prompt"`
}

type CreateStaffReq struct {
	StaffName        string                  `json:"staffName" validate:"min=1,max=40"`
	StaffKana        string                  `json:"staffKana" validate:"kanaInput,min=1,max=40"`
	StaffType        int                     `json:"staffType" validate:"eq=1|eq=13|eq=14|eq=15"`
	ManagerKbn       int                     `json:"managerKbn" validate:"eq=0|eq=7"`
	LoginID          string                  `json:"loginId" validate:"loginID,min=1,max=30"`
	MedicalLicenseNo *string                 `json:"medicalLicenseNo,omitempty" validate:"omitempty,max=6"`
	MayakuLicenseNo  *string                 `json:"mayakuLicenseNo,omitempty" validate:"omitempty,max=7"`
	Permissions      []*StaffPermissionInput `json:"permissions"`
}

type CreateStaffRes struct {
	StaffID          int                `json:"staffId"`
	StaffName        string             `json:"staffName"`
	StaffKana        string             `json:"staffKana"`
	StaffType        int                `json:"staffType"`
	ManagerKbn       int                `json:"managerKbn"`
	LoginID          string             `json:"loginId"`
	Password         string             `json:"password"`
	MedicalLicenseNo *string            `json:"medicalLicenseNo,omitempty"`
	MayakuLicenseNo  *string            `json:"mayakuLicenseNo,omitempty"`
	Permissions      []*StaffPermission `json:"permissions"`
}

type CreateTaskCategoryInput struct {
	Name string `json:"name"`
}

type CreateTaskInput struct {
	TaskInput     *TaskInput       `json:"taskInput"`
	TaskFileInput []*TaskFileInput `json:"taskFileInput,omitempty"`
}

type CreateTaskRes struct {
	TaskID int `json:"taskId"`
}

type CreateTaskStatusInput struct {
	Name string `json:"name"`
}

type CreateZendeskSsoInput struct {
	FirstPage int `json:"firstPage"`
}

type DateFilterInput struct {
	DisplayDate time.Time `json:"displayDate"`
}

type DeleteCalendarInput struct {
	CalendarID int `json:"calendarId"`
}

type DeleteClientCertificateReq struct {
	ClientCertificateID int `json:"ClientCertificateID"`
}

type DeleteClientCertificateRes struct {
	Message string `json:"message"`
}

type DeleteCommentInput struct {
	CommentID int `json:"commentId"`
}

type DeleteCommentResponse struct {
	CommentID int `json:"commentId"`
}

type DeleteLabelInput struct {
	LabelID int `json:"labelID"`
}

type DeletePharmacyHolidayInput struct {
	PharmacyHolidayID int `json:"pharmacyHolidayId"`
}

type DeletePortalHospitalNotificationInput struct {
	HospitalNotificationID int `json:"hospitalNotificationId"`
}

type DeletePortalHospitalStaffInput struct {
	HospitalStaffID int `json:"hospitalStaffId"`
}

type DeletePromptInput struct {
	PromptID int `json:"promptId"`
}

type DeleteTaskCategoryInput struct {
	TaskCategoryID int `json:"taskCategoryId"`
}

type DeleteTaskInput struct {
	TaskID int `json:"taskId"`
}

type DeleteTaskRes struct {
	TaskID int `json:"taskId"`
}

type DeleteTaskStatusInput struct {
	TaskStatusID int `json:"taskStatusId"`
}

type DeleteTokenRes struct {
	IsDeleted          bool `json:"isDeleted"`
	IsSessionRemaining bool `json:"isSessionRemaining"`
}

type DesiredDate struct {
	PharmacyDesiredDateID int       `json:"pharmacyDesiredDateId"`
	DesiredType           int       `json:"desiredType"`
	DesiredDate           time.Time `json:"desiredDate"`
}

type DesiredDateInput struct {
	DesiredType int       `json:"desiredType" validate:"eq=1|eq=2|eq=3"`
	DesiredDate time.Time `json:"desiredDate"`
}

type DoctorInput struct {
	Name                           string `json:"name"`
	KanaName                       string `json:"kanaName"`
	MedicalProfessionalType        int    `json:"medicalProfessionalType" validate:"omitempty,eq=1|eq=2"`
	Gender                         int    `json:"gender" validate:"omitempty,eq=1|eq=2"`
	MedicalLicenseNumber           string `json:"medicalLicenseNumber"`
	MedicalLicenseRegistrationDate string `json:"medicalLicenseRegistrationDate"`
	Email                          string `json:"email"`
	PhoneNumber                    string `json:"phoneNumber"`
	ApplierCategory                int    `json:"applierCategory" validate:"omitempty,eq=1|eq=2"`
	IsDownloadPamphlet             bool   `json:"isDownloadPamphlet"`
}

type EditCommentInput struct {
	CommentID             int                 `json:"commentId"`
	CommentInput          *CommentInput       `json:"commentInput,omitempty"`
	TaskInput             *TaskInput          `json:"taskInput,omitempty"`
	AddedCommentFiles     []*CommentFileInput `json:"addedCommentFiles,omitempty"`
	DeletedCommentFileIds []int               `json:"deletedCommentFileIds,omitempty"`
}

type EditCommentResponse struct {
	CommentID int `json:"commentId"`
}

type EditPortalBusinessTimeInput struct {
	BusinessTimeID int     `json:"businessTimeId"`
	StartTime      string  `json:"startTime"`
	EndTime        string  `json:"endTime"`
	MonFlag        float64 `json:"monFlag"`
	TueFlag        float64 `json:"tueFlag"`
	WedFlag        float64 `json:"wedFlag"`
	ThuFlag        float64 `json:"thuFlag"`
	FriFlag        float64 `json:"friFlag"`
	SatFlag        float64 `json:"satFlag"`
	SunFlag        float64 `json:"sunFlag"`
}

type EditPortalHospitalNotificationInput struct {
	HospitalNotificationInput *PortalHospitalNotificationInput `json:"hospitalNotificationInput,omitempty"`
	HospitalNotificationID    int                              `json:"hospitalNotificationId"`
}

type EditPortalHospitalStaffInput struct {
	HospitalStaffID    int                             `json:"hospitalStaffId"`
	HospitalStaffInput *HospitalStaffInput             `json:"hospitalStaffInput,omitempty"`
	AddedFiles         []*PortalHospitalStaffFileInput `json:"addedFiles,omitempty"`
	DeletedFileIds     []int                           `json:"deletedFileIds,omitempty"`
}

type EditPromptInput struct {
	Prompt   *PromptInput `json:"prompt"`
	PromptID int          `json:"promptId"`
}

type EditTaskCategoryInput struct {
	TaskCategoryID int    `json:"taskCategoryId"`
	Name           string `json:"name"`
}

type EditTaskInput struct {
	TaskInput          *TaskInput       `json:"taskInput,omitempty"`
	AddedTaskFiles     []*TaskFileInput `json:"addedTaskFiles,omitempty"`
	DeletedTaskFileIds []int            `json:"deletedTaskFileIds,omitempty"`
	TaskID             int              `json:"taskId"`
}

type EditTaskRes struct {
	TaskID int `json:"taskId"`
}

type EditTaskStatusInput struct {
	TaskStatusID int    `json:"taskStatusId"`
	Name         string `json:"name"`
}

type EveryPatientMessageSetting struct {
	Sendable int `json:"sendable"`
}

type ExamTimeSlot struct {
	ExamTimeSlotID      int       `json:"examTimeSlotID"`
	ExamStartDate       time.Time `json:"examStartDate"`
	ExamEndDate         time.Time `json:"examEndDate"`
	CalendarID          int       `json:"calendarId"`
	Calendar            *Calendar `json:"calendar"`
	TreatmentType       int       `json:"treatmentType"`
	SlotLimitReserveNum int       `json:"slotLimitReserveNum"`
	IsSuspended         bool      `json:"isSuspended"`
	ReservableSlots     *int      `json:"reservableSlots,omitempty"`
	TotalReserveSlots   *int      `json:"totalReserveSlots,omitempty"`
}

type Examination struct {
	ExaminationID int    `json:"examinationId"`
	Name          string `json:"name"`
	Type          int    `json:"type"`
}

type ExecuteTemplateReq struct {
	TemplateDocID int `json:"templateDocId"`
	PatientID     int `json:"patientId"`
}

type FaxInfo struct {
	FaxID     int       `json:"faxId"`
	FileName  string    `json:"fileName"`
	S3Key     string    `json:"s3Key"`
	FileURL   string    `json:"fileUrl"`
	Direction int       `json:"direction"`
	From      string    `json:"from"`
	To        string    `json:"to"`
	Status    int       `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
}

type FinCodeTenantShopContract struct {
	StatusCode int    `json:"statusCode"`
	ShopName   string `json:"shopName"`
}

type FindPharmacyReserveStatusHistoriesInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type FindPharmacyReservesInput struct {
	DateFilter          *DateFilterInput    `json:"dateFilter"`
	KeywordFilter       *KeywordFilterInput `json:"keywordFilter"`
	StatusFilter        *StatusFilterInput  `json:"statusFilter"`
	Sort                *SortInput          `json:"sort"`
	ShowFutureTreatment *bool               `json:"showFutureTreatment,omitempty"`
}

type FindPrescriptionImagesInput struct {
	PrescriptionReceptionID int `json:"prescriptionReceptionId"`
}

type FindPrescriptionReceptionsInput struct {
	DateFilter   *PrescriptionReceptionDateFilterInput   `json:"dateFilter"`
	StatusFilter *PrescriptionReceptionStatusFilterInput `json:"statusFilter"`
	Paging       *PrescriptionReceptionPagingInput       `json:"paging"`
}

type GetCalendarInput struct {
	CalendarID int `json:"calendarId"`
}

type GetCalendarWorkingTimeInput struct {
	CalendarID int       `json:"calendarId"`
	StartDate  time.Time `json:"startDate"`
	EndDate    time.Time `json:"endDate"`
}

type GetCategoryListWithTaskCountRes struct {
	TaskCategories []*CategoryWithTaskCount `json:"taskCategories"`
	TotalCount     int                      `json:"totalCount"`
}

type GetCertificateFileRes struct {
	Portal []*PatientCustomerFile `json:"portal,omitempty"`
}

type GetClientCertificateRes struct {
	ClientCertificateID int     `json:"ClientCertificateID"`
	Label               string  `json:"Label"`
	CommonName          string  `json:"CommonName"`
	IssueDate           string  `json:"IssueDate"`
	ExpirationDate      string  `json:"ExpirationDate"`
	DownloadURL         string  `json:"DownloadURL"`
	TokenExpireTime     string  `json:"TokenExpireTime"`
	InstallPassword     *string `json:"InstallPassword,omitempty"`
}

type GetClinicPaymentListInput struct {
	PatientID int  `json:"patientId"`
	ExamYear  *int `json:"examYear,omitempty"`
	ExamMonth *int `json:"examMonth,omitempty"`
}

type GetCommentFileURLInput struct {
	CommentFileID int `json:"commentFileId"`
}

type GetCommentFileURLRes struct {
	URL string `json:"url"`
}

type GetCommentUploadFileURLInput struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
}

type GetCommentUploadFileURLRes struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
	URL                   string `json:"url"`
}

type GetCommentsInput struct {
	TaskID    *int `json:"taskId,omitempty"`
	CommentID *int `json:"commentId,omitempty"`
}

type GetExamTimeSlotByConditionsInput struct {
	CalendarID            int        `json:"calendarId"`
	TreatmentDepartmentID *int       `json:"treatmentDepartmentId,omitempty"`
	ExamEndDate           *time.Time `json:"examEndDate,omitempty"`
	ExamStartDate         *time.Time `json:"examStartDate,omitempty"`
}

type GetFaxFileUploadReq struct {
	FileName string `json:"fileName"`
}

type GetFaxFileUploadRes struct {
	S3Key string `json:"s3Key"`
	URL   string `json:"url"`
}

type GetFaxListReq struct {
	Direction int `json:"direction"`
}

type GetFinCodeTenantShopContractInput struct {
	ShopID string `json:"shopId"`
}

type GetHospitalInfoByHomepageURLInput struct {
	HomepageURL string `json:"homepageUrl"`
	Prompt      string `json:"prompt"`
}

type GetHospitalInfoByHomepageURLRes struct {
	Description string `json:"description"`
}

type GetInsuranceFileRes struct {
	Portal *PatientCustomerFile `json:"portal,omitempty"`
}

type GetMessagesRes struct {
	Messages   []*Message  `json:"messages"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

type GetMessagesResByPatient struct {
	Messages   []*MessageByPatient  `json:"messages"`
	Pagination *PaginationByPatient `json:"pagination,omitempty"`
}

type GetPatientMemoRes struct {
	MemoHTML string `json:"memoHtml"`
}

type GetPatientRes struct {
	PatientID        int64            `json:"patientId"`
	PatientName      string           `json:"patientName"`
	PatientNameKana  string           `json:"patientNameKana"`
	Gender           int              `json:"gender"`
	Birthdate        *time.Time       `json:"birthdate,omitempty"`
	PhoneNumber1     string           `json:"phoneNumber1"`
	PhoneNumber2     string           `json:"phoneNumber2"`
	Email            string           `json:"email"`
	HomePost         string           `json:"homePost"`
	HomeAddress1     string           `json:"homeAddress1"`
	HomeAddress2     string           `json:"homeAddress2"`
	PortalCustomerID *int             `json:"portalCustomerId,omitempty"`
	PortalBasicInfo  *PortalBasicInfo `json:"portalBasicInfo,omitempty"`
	Appointment      *Appointment     `json:"appointment"`
	PtNum            int64            `json:"ptNum"`
}

type GetPatientsRes struct {
	Patients []*Patient `json:"patients,omitempty"`
	Count    int        `json:"count"`
}

type GetPharmacyBusinessDaysInput struct {
	BaseDate time.Time `json:"baseDate"`
	Count    int       `json:"count"`
}

type GetPharmacyHolidayByIDInput struct {
	PharmacyHolidayID int `json:"pharmacyHolidayId"`
}

type GetPharmacyPaymentDetailHistoryInput struct {
	PaymentPharmacyDetailHistoryID int64 `json:"paymentPharmacyDetailHistoryId"`
}

type GetPharmacyPaymentHistoriesInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type GetPharmacyPaymentInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type GetPharmacyPaymentRes struct {
	PaymentPharmacyDetailID int64      `json:"paymentPharmacyDetailId"`
	PaymentStatus           int        `json:"paymentStatus"`
	PaymentType             int        `json:"paymentType"`
	MedicationCost          int        `json:"medicationCost"`
	DeliveryFee             int        `json:"deliveryFee"`
	ErrorMessage            string     `json:"errorMessage"`
	FirstSuccessDate        *time.Time `json:"firstSuccessDate,omitempty"`
}

type GetPharmacyReserveContactInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type GetPharmacyReserveInsuranceInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type GetPortalHospitalFileURLInput struct {
	FileID int `json:"fileId"`
}

type GetPortalHospitalFileURLRes struct {
	URL string `json:"url"`
}

type GetPortalHospitalNotificationRes struct {
	HospitalNotification *PortalHospitalNotificationOutput `json:"hospitalNotification,omitempty"`
}

type GetPortalHospitalNotificationsRes struct {
	HospitalNotifications []*PortalHospitalNotificationOutput `json:"hospitalNotifications,omitempty"`
	Count                 int                                 `json:"count"`
}

type GetPortalHospitalRes struct {
	PortalHospital *PortalHospital `json:"portalHospital,omitempty"`
}

type GetPortalHospitalStaffRes struct {
	PortalHospitalStaff *PortalHospitalStaff `json:"portalHospitalStaff,omitempty"`
}

type GetPortalHospitalStaffUploadFileURLInput struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
}

type GetPortalHospitalStaffUploadFileURLRes struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
	URL                   string `json:"url"`
}

type GetPortalHospitalUploadFileURLInput struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
}

type GetPortalHospitalUploadFileURLRes struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
	URL                   string `json:"url"`
}

type GetPortalStaffFileURLInput struct {
	FileID int `json:"fileId"`
}

type GetPortalStaffFileURLRes struct {
	URL string `json:"url"`
}

type GetRequestReservePaymentRes struct {
	PaymentClinicDetailID int64   `json:"paymentClinicDetailId"`
	ErrorCode             *string `json:"errorCode,omitempty"`
	UserMessage           *string `json:"userMessage,omitempty"`
}

type GetReservationDetailByIDInput struct {
	ReserveDetailID     int   `json:"reserveDetailId"`
	IsLoadCancelReserve *bool `json:"isLoadCancelReserve,omitempty"`
}

type GetReservationDetailsByConditions struct {
	CalendarID            *int       `json:"calendarId,omitempty"`
	TreatmentDepartmentID *int       `json:"treatmentDepartmentId,omitempty"`
	TreatmentType         *int       `json:"treatmentType,omitempty"`
	ReserveType           *int       `json:"reserveType,omitempty"`
	PatientID             *int       `json:"patientId,omitempty"`
	FromDate              *time.Time `json:"fromDate,omitempty"`
	ToDate                *time.Time `json:"toDate,omitempty"`
	IsLoadCancelReserve   *bool      `json:"isLoadCancelReserve,omitempty"`
	IsLoadRaiinInfo       *bool      `json:"isLoadRaiinInfo,omitempty"`
}

type GetStaffListReq struct {
	Limit      *int    `json:"limit,omitempty" validate:"min=1"`
	Cursor     *int    `json:"cursor,omitempty"`
	SearchName *string `json:"searchName,omitempty"`
}

type GetStaffListRes struct {
	Staffs []*StaffInfo `json:"staffs"`
}

type GetStaffListWithTaskCountRes struct {
	Staffs         []*StaffWithTaskCount `json:"staffs"`
	TotalTaskCount int                   `json:"totalTaskCount"`
}

type GetSurveysReq struct {
	Keyword string  `json:"keyword"`
	Limit   *int    `json:"limit,omitempty"`
	Cursor  *int64  `json:"cursor,omitempty"`
	Date    *string `json:"date,omitempty"`
}

type GetTaskCategoryWithCountInput struct {
	ResponsibleStaffID *int `json:"responsibleStaffId,omitempty"`
}

type GetTaskFileURLInput struct {
	TaskFileID int `json:"taskFileId"`
}

type GetTaskFileURLRes struct {
	URL string `json:"url"`
}

type GetTaskRes struct {
	Task            *Task      `json:"task,omitempty"`
	HistoryPatients []*Patient `json:"historyPatients,omitempty"`
}

type GetTaskStatusesWithTaskCountInput struct {
	ResponsibleStaffID *int `json:"responsibleStaffId,omitempty"`
	TaskCategoryID     *int `json:"taskCategoryId,omitempty"`
}

type GetTaskStatusesWithTaskCountRes struct {
	TaskStatuses   []*TaskStatusWithTaskCount `json:"taskStatuses"`
	TotalTaskCount int                        `json:"totalTaskCount"`
}

type GetTaskUploadFileURLInput struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
}

type GetTaskUploadFileURLRes struct {
	FileNameWithExtension string `json:"fileNameWithExtension"`
	URL                   string `json:"url"`
}

type GetTasksInput struct {
	Limit              *int          `json:"limit,omitempty"`
	CursorID           *int64        `json:"cursorId,omitempty"`
	CursorDate         *string       `json:"cursorDate,omitempty"`
	ResponsibleStaffID *int          `json:"responsibleStaffId,omitempty"`
	CategoryID         *int          `json:"categoryId,omitempty"`
	StatusID           *int          `json:"statusId,omitempty"`
	Sort               *TaskSortType `json:"sort,omitempty"`
	Order              *SortOrder    `json:"order,omitempty"`
	PatientID          *int          `json:"patientId,omitempty"`
}

type GetTasksRes struct {
	Tasks []*Task `json:"tasks,omitempty"`
}

type GetTreatmentDepartmentsByConditions struct {
	Limit                     *int   `json:"limit,omitempty"`
	Cursor                    *int64 `json:"cursor,omitempty"`
	PortalPublicStatus        *int   `json:"portalPublicStatus,omitempty"`
	TreatmentDepartmentStatus *int   `json:"treatmentDepartmentStatus,omitempty"`
}

type GetZendeskSsoResponse struct {
	Token    string `json:"token"`
	Endpoint string `json:"endpoint"`
	ReturnTo string `json:"returnTo"`
}

type HasPharmacyReserveInRangeInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type Hospital struct {
	IsOpenClinic            bool   `json:"isOpenClinic"`
	InsuredBranchCode       string `json:"insuredBranchCode"`
	Name                    string `json:"name"`
	PostCode                string `json:"postCode"`
	Address                 string `json:"address"`
	Telephone               string `json:"telephone"`
	Email                   string `json:"email"`
	Status                  int    `json:"status"`
	RousaiInsuredBranchCode string `json:"rousaiInsuredBranchCode"`
	ReceName                string `json:"receName"`
	KaisetuName             string `json:"kaisetuName"`
}

type HospitalInfo struct {
	HospitalName string `json:"hospitalName"`
}

type HospitalInput struct {
	InsuredBranchCode       string  `json:"insuredBranchCode"`
	Name                    string  `json:"name"`
	PostCode                string  `json:"postCode"`
	Address                 string  `json:"address"`
	Telephone               string  `json:"telephone"`
	Email                   string  `json:"email"`
	Fax                     *string `json:"fax,omitempty"`
	RousaiInsuredBranchCode string  `json:"rousaiInsuredBranchCode"`
	ReceName                string  `json:"receName"`
	KaisetuName             string  `json:"kaisetuName"`
}

type HospitalStaffInput struct {
	Name             string  `json:"name" validate:"max=30"`
	ExperienceDetail *string `json:"experienceDetail,omitempty" validate:"max=1000"`
	Description      *string `json:"description,omitempty" validate:"max=1000"`
	SpecialistDetail *string `json:"specialistDetail,omitempty" validate:"max=1000"`
	IsDirector       bool    `json:"isDirector"`
}

type HpFincodeInfo struct {
	HpTenantShopID                   string `json:"hpTenantShopId"`
	PlatformID                       int    `json:"platformId"`
	IsFincodeRelation                bool   `json:"isFincodeRelation"`
	IsHtFincodeRelation              bool   `json:"isHtFincodeRelation"`
	IsFincodeRelationAvailableStatus bool   `json:"isFincodeRelationAvailableStatus"`
	IsInsuranceMedicalInstitution    bool   `json:"isInsuranceMedicalInstitution"`
}

type ImportHospitalData struct {
	Name                *string                `json:"name,omitempty"`
	Telephone           *string                `json:"telephone,omitempty"`
	Address1            *string                `json:"address1,omitempty"`
	Address2            *string                `json:"address2,omitempty"`
	PostCode            *string                `json:"postCode,omitempty"`
	HolidayDetail       *string                `json:"holidayDetail,omitempty"`
	Homepage            *string                `json:"homepage,omitempty"`
	BusinessTimes       []*OSBusinessTime      `json:"businessTimes,omitempty"`
	CarparkDetail       *string                `json:"carparkDetail,omitempty"`
	Description         *string                `json:"description,omitempty"`
	DescriptionTitle    *string                `json:"descriptionTitle,omitempty"`
	HospitalID          *int                   `json:"hospitalId,omitempty"`
	ScuelID             *string                `json:"scuelId,omitempty"`
	IsActive            *bool                  `json:"isActive,omitempty"`
	IsCarpark           *bool                  `json:"isCarpark,omitempty"`
	PaymentDetails      *string                `json:"paymentDetails,omitempty"`
	DirectorName        *string                `json:"directorName,omitempty"`
	Stations            []*OSStation           `json:"stations,omitempty"`
	Tags                []*OSTag               `json:"tags,omitempty"`
	Examinations        []*OSExamination       `json:"examinations,omitempty"`
	Specialists         []*OSSpecialist        `json:"specialists,omitempty"`
	TreatmentCategories []*OSTreatmentCategory `json:"treatmentCategories,omitempty"`
	Payments            []*ImportPayment       `json:"payments,omitempty"`
}

type ImportPayment struct {
	PaymentTypeName *string `json:"paymentTypeName,omitempty"`
}

type IsPaymentMethodRegisteredForPatientInput struct {
	PatientID int `json:"patientId"`
}

type IsPharmacyHoliday struct {
	Date      time.Time `json:"date"`
	IsHoliday bool      `json:"isHoliday"`
}

type IssueTokenReq struct {
	StaffID          *int    `json:"staffID,omitempty"`
	HospitalID       *int    `json:"hospitalID,omitempty"`
	StaffUserID      *int    `json:"staffUserID,omitempty"`
	PharmacyFlg      *bool   `json:"pharmacyFlg,omitempty"`
	Email            *string `json:"email,omitempty"`
	PortalCustomerID *int    `json:"portalCustomerID,omitempty"`
	TokenTTL         int     `json:"tokenTTL"`
	RefreshTokenTTL  int     `json:"refreshTokenTTL"`
	LoginID          *string `json:"loginID,omitempty"`
	KarteStatus      *int    `json:"karteStatus,omitempty"`
}

type IssueTokenRes struct {
	Token                  string `json:"token"`
	TokenExpiryTime        string `json:"tokenExpiryTime"`
	RefreshToken           string `json:"refreshToken"`
	RefreshTokenExpiryTime string `json:"refreshTokenExpiryTime"`
}

type Item struct {
	ItemID    string `json:"itemId"`
	Src       string `json:"src"`
	ItemName  string `json:"itemName"`
	IsDeleted int    `json:"isDeleted"`
}

type ItemByPatient struct {
	ItemID    string `json:"itemId"`
	Src       string `json:"src"`
	ItemName  string `json:"itemName"`
	IsDeleted int    `json:"isDeleted"`
}

type JSONObject struct {
	OldContent            *string    `json:"oldContent,omitempty"`
	NewContent            *string    `json:"newContent,omitempty"`
	OldStatus             *int       `json:"oldStatus,omitempty"`
	NewStatus             *int       `json:"newStatus,omitempty"`
	OldExpiredAt          *time.Time `json:"oldExpiredAt,omitempty"`
	NewExpiredAt          *time.Time `json:"newExpiredAt,omitempty"`
	OldTitle              *string    `json:"oldTitle,omitempty"`
	NewTitle              *string    `json:"newTitle,omitempty"`
	OldDetail             *string    `json:"oldDetail,omitempty"`
	NewDetail             *string    `json:"newDetail,omitempty"`
	OldPatientID          *int       `json:"oldPatientId,omitempty"`
	NewPatientID          *int       `json:"newPatientId,omitempty"`
	OldResponsibleStaffID *int       `json:"oldResponsibleStaffId,omitempty"`
	NewResponsibleStaffID *int       `json:"newResponsibleStaffId,omitempty"`
}

type KeywordFilterInput struct {
	Customer *string `json:"customer,omitempty"`
	Patient  *string `json:"patient,omitempty"`
	Clinic   *string `json:"clinic,omitempty"`
}

type LLMModelResponse struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type Label struct {
	LabelID     int    `json:"labelID"`
	Label       string `json:"label"`
	Description string `json:"description"`
	ColorCode   string `json:"colorCode"`
	SortOrder   int    `json:"sortOrder"`
}

type LoginInitialReq struct {
	NewLoginID  string `json:"newLoginId" validate:"min=1,max=30"`
	NewPassword string `json:"newPassword" validate:"min=8,max=30"`
}

type LoginInitialRes struct {
	Success int `json:"success"`
}

type LoginReq struct {
	LoginID  string `json:"loginId"`
	Password string `json:"password"`
}

type LoginRes struct {
	Success               int        `json:"success"`
	IsLoginIDInitialized  int        `json:"isLoginIdInitialized"`
	IsPasswordInitialized int        `json:"isPasswordInitialized"`
	StaffInfo             *StaffInfo `json:"staffInfo"`
	HospitalID            int        `json:"hospitalId"`
	KarteStatus           int        `json:"karteStatus"`
	PharmacyFlg           bool       `json:"pharmacyFlg"`
}

type LogoutRes struct {
	Success            int  `json:"success"`
	IsSessionRemaining bool `json:"isSessionRemaining"`
}

type MailDeliverySettingsReq struct {
	Email            string `json:"email" validate:"email"`
	AllowLogin       bool   `json:"allowLogin"`
	AllowReservation bool   `json:"allowReservation"`
	AllowNewMessage  bool   `json:"allowNewMessage"`
	AllowTask        bool   `json:"allowTask"`
}

type MailDeliverySettingsRes struct {
	StaffID          int    `json:"staffId"`
	Email            string `json:"email"`
	AllowLogin       bool   `json:"allowLogin"`
	AllowReservation bool   `json:"allowReservation"`
	AllowNewMessage  bool   `json:"allowNewMessage"`
	AllowTask        bool   `json:"allowTask"`
}

type Meeting struct {
	ChimeMeetingID    *string          `json:"chimeMeetingId,omitempty"`
	MeetingID         int              `json:"meetingId"`
	Patient           *Patient         `json:"patient,omitempty"`
	PatientID         int              `json:"patientId"`
	ReserveID         *int             `json:"reserveId,omitempty"`
	Reservation       *Reservation     `json:"reservation,omitempty"`
	PharmacyReserveID *int             `json:"pharmacyReserveId,omitempty"`
	PharmacyReserve   *PharmacyReserve `json:"pharmacyReserve,omitempty"`
	Status            int              `json:"status"`
	IsBothJoined      int              `json:"isBothJoined"`
}

type MemberInfo struct {
	MemberName string `json:"memberName"`
	Status     int    `json:"status"`
	StaffType  int    `json:"staffType"`
	ManagerKbn int    `json:"managerKbn"`
}

type MemberInfoByPatient struct {
	MemberName string `json:"memberName"`
	Role       string `json:"role"`
}

type Message struct {
	MessageID        string      `json:"messageId"`
	ChannelID        int         `json:"channelId"`
	MessageType      MessageType `json:"messageType"`
	Items            []*Item     `json:"items"`
	Content          string      `json:"content"`
	PostedMember     string      `json:"postedMember"`
	PostedMemberInfo *MemberInfo `json:"postedMemberInfo"`
	IsPatient        int         `json:"isPatient"`
	CreatedAt        string      `json:"createdAt"`
	UpdatedAt        string      `json:"updatedAt"`
	HospitalID       int         `json:"hospitalId"`
	IsDeleted        int         `json:"isDeleted"`
}

type MessageByPatient struct {
	MessageID    string      `json:"messageId"`
	ChannelID    int         `json:"channelId"`
	MessageType  MessageType `json:"messageType"`
	Items        []*Item     `json:"items"`
	CreatedAt    string      `json:"createdAt"`
	Content      string      `json:"content"`
	PostedMember string      `json:"postedMember"`
	IsPatient    int         `json:"isPatient"`
	UpdatedAt    string      `json:"updatedAt"`
	HospitalID   int         `json:"hospitalId"`
	IsDeleted    int         `json:"isDeleted"`
}

type MessageURLRes struct {
	FileName string `json:"fileName"`
	S3key    string `json:"s3key"`
	URL      string `json:"url"`
}

type MessageURLResByPatient struct {
	FileName string `json:"fileName"`
	S3key    string `json:"s3key"`
	URL      string `json:"url"`
}

type MessagesUploadURLByPatient struct {
	ChannelID int       `json:"channelId" validate:"min=1"`
	FileNames []*string `json:"fileNames"`
}

type Mutation struct {
}

type NotificationCreateStaffInfo struct {
	StaffID   int    `json:"staffId"`
	StaffName string `json:"staffName"`
}

type NotifyChannel struct {
	ChannelID             string     `json:"channelId"`
	ChannelName           string     `json:"channelName"`
	PatientID             string     `json:"patientId"`
	ChannelType           int        `json:"channelType"`
	HospitalID            int        `json:"hospitalId"`
	IsUnread              int        `json:"isUnread"`
	Sendable              int        `json:"sendable"`
	NotifyTo              *int       `json:"notifyTo,omitempty"`
	LatestPostedMessageAt *time.Time `json:"latestPostedMessageAt,omitempty"`
}

type NotifyMessage struct {
	EventType  string         `json:"eventType"`
	NotifyType string         `json:"notifyType"`
	Message    *Message       `json:"message"`
	Channel    *NotifyChannel `json:"channel"`
}

type NotifyMessageToPatient struct {
	EventType  string            `json:"eventType"`
	NotifyType string            `json:"notifyType"`
	Message    *MessageByPatient `json:"message"`
}

type OSBusinessTime struct {
	StartTime *string `json:"startTime,omitempty"`
	EndTime   *string `json:"endTime,omitempty"`
	MonFlag   *int    `json:"monFlag,omitempty"`
	TueFlag   *int    `json:"tueFlag,omitempty"`
	WedFlag   *int    `json:"wedFlag,omitempty"`
	ThuFlag   *int    `json:"thuFlag,omitempty"`
	FriFlag   *int    `json:"friFlag,omitempty"`
	SatFlag   *int    `json:"satFlag,omitempty"`
	SunFlag   *int    `json:"sunFlag,omitempty"`
}

type OSExamination struct {
	ExaminationID int     `json:"examinationId"`
	Name          *string `json:"name,omitempty"`
}

type OSSpecialist struct {
	SpecialistID int     `json:"specialistId"`
	Name         *string `json:"name,omitempty"`
}

type OSStation struct {
	StationDetail *OSStationDetail `json:"stationDetail,omitempty"`
	StationID     *string          `json:"stationId,omitempty"`
}

type OSStationDetail struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}

type OSTag struct {
	Name  *string `json:"name,omitempty"`
	TagID int     `json:"tagId"`
}

type OSTreatmentCategory struct {
	Name                *string `json:"name,omitempty"`
	Description         *string `json:"description,omitempty"`
	TreatmentCategoryID int     `json:"treatmentCategoryId"`
}

type OperatorChangePasswordReq struct {
	NewPassword  string `json:"newPassword"`
	SessionValue string `json:"sessionValue"`
}

type OperatorChangePasswordRes struct {
	ChallengeName string  `json:"challengeName"`
	SessionValue  *string `json:"sessionValue,omitempty"`
	PharmacyFlg   bool    `json:"pharmacyFlg"`
}

type OperatorLoginReq struct {
	OperatorName     string `json:"operatorName"`
	OperatorPassword string `json:"operatorPassword"`
	HospitalID       int    `json:"hospitalId" validate:"min=1"`
}

type OperatorLoginRes struct {
	ChallengeName string  `json:"challengeName"`
	SessionValue  *string `json:"sessionValue,omitempty"`
	PharmacyFlg   bool    `json:"pharmacyFlg"`
	KarteStatus   int     `json:"karteStatus"`
}

type OperatorVerifyMFACodeReq struct {
	SessionValue string `json:"sessionValue"`
	PinCode      string `json:"pinCode" validate:"numeric,len=6"`
}

type OperatorVerifyMFACodeRes struct {
	ChallengeName string  `json:"challengeName"`
	SessionValue  *string `json:"sessionValue,omitempty"`
	PharmacyFlg   bool    `json:"pharmacyFlg"`
}

type Pagination struct {
	StartMessageCreatedTime string `json:"startMessageCreatedTime"`
	EndMessageCreatedTime   string `json:"endMessageCreatedTime"`
	CurrentPage             int    `json:"currentPage"`
	LastPage                int    `json:"lastPage"`
	Num                     int    `json:"num"`
	HasPreviousMore         int    `json:"hasPreviousMore"`
	HasNextMore             int    `json:"hasNextMore"`
}

type PaginationByPatient struct {
	StartMessageCreatedTime string `json:"startMessageCreatedTime"`
	EndMessageCreatedTime   string `json:"endMessageCreatedTime"`
	CurrentPage             int    `json:"currentPage"`
	LastPage                int    `json:"lastPage"`
	Num                     int    `json:"num"`
	HasPreviousMore         int    `json:"hasPreviousMore"`
	HasNextMore             int    `json:"hasNextMore"`
}

type Patient struct {
	PatientID                 int        `json:"patientID"`
	PatientName               *string    `json:"patientName,omitempty"`
	PatientNameKana           *string    `json:"patientNameKana,omitempty"`
	Gender                    *int       `json:"gender,omitempty"`
	Birthdate                 *time.Time `json:"birthdate,omitempty"`
	LastAppointmentDate       *time.Time `json:"lastAppointmentDate,omitempty"`
	NextAppointmentDate       *time.Time `json:"nextAppointmentDate,omitempty"`
	LastAppointmentDepartment *string    `json:"lastAppointmentDepartment,omitempty"`
	NextAppointmentDepartment *string    `json:"nextAppointmentDepartment,omitempty"`
	PhoneNumber1              *string    `json:"phoneNumber1,omitempty"`
	PhoneNumber2              *string    `json:"phoneNumber2,omitempty"`
	Email                     *string    `json:"email,omitempty"`
	PortalCustomerID          *int       `json:"portalCustomerId,omitempty"`
	PatientNumber             *int       `json:"patientNumber,omitempty"`
	HomePost                  *string    `json:"homePost,omitempty"`
	HomeAddress1              *string    `json:"homeAddress1,omitempty"`
	HomeAddress2              *string    `json:"homeAddress2,omitempty"`
}

type PatientChannel struct {
	ChannelID             string     `json:"channelId"`
	ChannelName           string     `json:"channelName"`
	PatientID             string     `json:"patientId"`
	CreatedBy             string     `json:"createdBy"`
	UpdatedBy             string     `json:"updatedBy"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             time.Time  `json:"updatedAt"`
	HospitalID            int        `json:"hospitalId"`
	IsUnread              int        `json:"isUnread"`
	Sendable              int        `json:"sendable"`
	LatestPostedMessageAt *time.Time `json:"latestPostedMessageAt,omitempty"`
	IsDeleted             int        `json:"isDeleted"`
}

type PatientChannelByPatient struct {
	HospitalID            int           `json:"hospitalId"`
	ChannelID             string        `json:"channelId"`
	PatientID             string        `json:"patientID"`
	HospitalInfo          *HospitalInfo `json:"hospitalInfo"`
	PatientSendable       int           `json:"patientSendable"`
	IsUnread              int           `json:"isUnread"`
	LatestPostedMessageAt *time.Time    `json:"latestPostedMessageAt,omitempty"`
}

type PatientCustomerFile struct {
	FileID           int       `json:"fileId"`
	OriginalFileName string    `json:"originalFileName"`
	S3Key            string    `json:"s3Key"`
	CreatedAt        time.Time `json:"createdAt"`
	Size             int       `json:"size"`
	URL              string    `json:"url"`
}

type PatientMemoInput struct {
	PatientID int64  `json:"patientId"`
	MemoHTML  string `json:"memoHtml"`
}

type PatientSearchInput struct {
	SearchKeyword  string `json:"searchKeyword"`
	Birthdate      *int   `json:"birthdate,omitempty"`
	HasAppointment *bool  `json:"hasAppointment,omitempty"`
	Limit          *int   `json:"limit,omitempty"`
	Cursor         *int64 `json:"cursor,omitempty"`
	PatientIds     []int  `json:"patientIds,omitempty"`
}

type PatientSearchReq struct {
	PatientID *string `json:"patientID,omitempty"`
	WholeName *string `json:"wholeName,omitempty"`
	BirthDate *string `json:"birthDate,omitempty"`
}

type PatientSearchRes struct {
	PatientID       string  `json:"patientID"`
	WholeName       string  `json:"wholeName"`
	WholeNameInKana string  `json:"wholeNameInKana"`
	BirthDate       string  `json:"birthDate"`
	Age             int     `json:"age"`
	Gender          string  `json:"gender"`
	LastVisitDate   *string `json:"lastVisitDate,omitempty"`
	NextVisitDate   *string `json:"nextVisitDate,omitempty"`
}

type PaymentDetail struct {
	PaymentClinicDetailID int64 `json:"paymentClinicDetailId"`
	PaymentStatus         int   `json:"paymentStatus"`
	PaymentType           int   `json:"paymentType"`
	BillingAmount         int   `json:"billingAmount"`
}

type PermissionInput struct {
	IsAdmin    bool   `json:"isAdmin"`
	FunctionCd string `json:"functionCd"`
}

type PharmacyDeliveryAddress struct {
	PharmacyDeliveryAddressID int     `json:"pharmacyDeliveryAddressId"`
	PharmacyReserveID         int     `json:"pharmacyReserveId"`
	DeliveryAddressID         int     `json:"deliveryAddressId"`
	Address1                  string  `json:"address1"`
	Address2                  *string `json:"address2,omitempty"`
	PostCode                  string  `json:"postCode"`
	PhoneNumber               string  `json:"phoneNumber"`
}

type PharmacyDeliveryHistories struct {
	PharmacyReserveID     int       `json:"pharmacyReserveId"`
	DeliveryAt            time.Time `json:"deliveryAt"`
	DeliveryInquiryNumber string    `json:"deliveryInquiryNumber"`
}

type PharmacyDeliveryHistory struct {
	PharmacyReserveID     int       `json:"pharmacyReserveId"`
	DeliveryAt            time.Time `json:"deliveryAt"`
	DeliveryName          string    `json:"deliveryName"`
	DeliveryPostCode      string    `json:"deliveryPostCode"`
	DeliveryAddress       string    `json:"deliveryAddress"`
	DeliveryPhoneNumber   string    `json:"deliveryPhoneNumber"`
	DeliveryInquiryNumber string    `json:"deliveryInquiryNumber"`
}

type PharmacyHoliday struct {
	PharmacyHolidayID int       `json:"pharmacyHolidayId"`
	HospitalID        int       `json:"hospitalId"`
	HolidayStartDate  time.Time `json:"holidayStartDate"`
	HolidayEndDate    time.Time `json:"holidayEndDate"`
	CreatedAt         time.Time `json:"createdAt"`
	CreatedBy         string    `json:"createdBy"`
	UpdatedAt         time.Time `json:"updatedAt"`
	UpdatedBy         string    `json:"updatedBy"`
}

type PharmacyMeeting struct {
	MeetingID int `json:"meetingId"`
	Status    int `json:"status"`
}

type PharmacyPatient struct {
	PtID             int       `json:"ptId"`
	PtNum            int       `json:"ptNum"`
	KanaName         *string   `json:"kanaName,omitempty"`
	Name             *string   `json:"name,omitempty"`
	Sex              int       `json:"sex"`
	Birthday         time.Time `json:"birthday"`
	PortalCustomerID *int      `json:"portalCustomerId,omitempty"`
}

type PharmacyPatientFile struct {
	PharmacyPatientFileID   int       `json:"pharmacyPatientFileID"`
	PharmacyReserveDetailID int       `json:"pharmacyReserveDetailID"`
	PatientID               int       `json:"patientID"`
	OriginalFileName        string    `json:"originalFileName"`
	S3Key                   string    `json:"s3Key"`
	CreatedAt               time.Time `json:"createdAt"`
	URL                     string    `json:"URL"`
	Type                    int       `json:"type"`
	MimeType                *string   `json:"mimeType,omitempty"`
}

type PharmacyPaymentDetailHistory struct {
	PaymentPharmacyDetailHistoryID int64     `json:"paymentPharmacyDetailHistoryId"`
	PaymentDate                    time.Time `json:"paymentDate"`
	PaymentStatus                  int       `json:"paymentStatus"`
	ActionType                     int       `json:"actionType"`
	PaymentType                    int       `json:"paymentType"`
	MedicationCost                 int       `json:"medicationCost"`
	DeliveryFee                    int       `json:"deliveryFee"`
	PreviousMedicationCost         int       `json:"previousMedicationCost"`
	PreviousDeliveryFee            int       `json:"previousDeliveryFee"`
	ErrorMessage                   string    `json:"errorMessage"`
	CardInfo                       *CardInfo `json:"cardInfo,omitempty"`
}

type PharmacyPaymentHistory struct {
	PaymentPharmacyDetailHistoryID int64     `json:"paymentPharmacyDetailHistoryId"`
	PaymentDate                    time.Time `json:"paymentDate"`
	PaymentStatus                  int       `json:"paymentStatus"`
	ActionType                     int       `json:"actionType"`
	PaymentType                    int       `json:"paymentType"`
	TotalAmount                    int       `json:"totalAmount"`
}

type PharmacyReserve struct {
	PharmacyReserveID       int                      `json:"pharmacyReserveId"`
	Patient                 *PharmacyPatient         `json:"patient"`
	Customer                *PortalCustomer          `json:"customer"`
	DesiredDateStatus       int                      `json:"desiredDateStatus"`
	DesiredDate             []*DesiredDate           `json:"desiredDate"`
	ReserveUpdateDate       time.Time                `json:"reserveUpdateDate"`
	Reserve                 *ClinicReserve           `json:"reserve,omitempty"`
	SmsStatus               int                      `json:"smsStatus"`
	VideocallStatus         int                      `json:"videocallStatus"`
	Meeting                 *PharmacyMeeting         `json:"meeting,omitempty"`
	PostalServiceType       int                      `json:"postalServiceType"`
	CSVStatus               int                      `json:"csvStatus"`
	PharmacistStatus        int                      `json:"pharmacistStatus"`
	Pharmacist              *PharmacyStaff           `json:"pharmacist,omitempty"`
	PharmacyReserveDetails  []*PharmacyReserveDetail `json:"pharmacyReserveDetails"`
	PharmacyDeliveryAddress *PharmacyDeliveryAddress `json:"pharmacyDeliveryAddress"`
}

type PharmacyReserveContact struct {
	Customer        *PharmacyReserveContactCustomer        `json:"customer"`
	Clinic          *PharmacyReserveContactClinic          `json:"clinic"`
	DeliveryAddress *PharmacyReserveContactDeliveryAddress `json:"deliveryAddress"`
}

type PharmacyReserveContactClinic struct {
	HospitalID int    `json:"hospital_id"`
	Name       string `json:"name"`
	Telephone  string `json:"telephone"`
	Fax        string `json:"fax"`
	Email      string `json:"email"`
	PostCode   string `json:"post_code"`
	Address    string `json:"address"`
}

type PharmacyReserveContactCustomer struct {
	CustomerID int        `json:"customerId"`
	KanaName   *string    `json:"kanaName,omitempty"`
	Name       *string    `json:"name,omitempty"`
	Gender     *int       `json:"gender,omitempty"`
	Birthday   *time.Time `json:"birthday,omitempty"`
	Telephone  *string    `json:"telephone,omitempty"`
	Email      string     `json:"email"`
	PostCode   string     `json:"post_code"`
	Address    string     `json:"address"`
}

type PharmacyReserveContactDeliveryAddress struct {
	Name        string `json:"name"`
	PostCode    string `json:"post_code"`
	Address     string `json:"address"`
	PhoneNumber string `json:"phone_number"`
}

type PharmacyReserveDetail struct {
	PharmacyReserveDetailID    int              `json:"pharmacyReserveDetailId"`
	Patient                    *PharmacyPatient `json:"patient"`
	Customer                   *PortalCustomer  `json:"customer"`
	PrescriptionType           int              `json:"prescriptionType"`
	Status                     int              `json:"status"`
	GuidanceStatus             int              `json:"guidanceStatus"`
	PaymentStatus              int              `json:"paymentStatus"`
	HasPrescriptionFiles       bool             `json:"hasPrescriptionFiles"`
	UsesElectronicPrescription *bool            `json:"usesElectronicPrescription,omitempty"`
	HasElectronicPrescription  *bool            `json:"hasElectronicPrescription,omitempty"`
	RedemptionNumber           *string          `json:"redemptionNumber,omitempty"`
	PrescriptionDeliveryMethod *int             `json:"prescriptionDeliveryMethod,omitempty"`
}

type PharmacyReserveDetailSurveyInfo struct {
	BasicAnswerJSON          *string    `json:"basicAnswerJson,omitempty"`
	PharmacyAnswerJSON       *string    `json:"pharmacyAnswerJson,omitempty"`
	CurrentMedications       string     `json:"currentMedications"`
	SurveyAnswerDate         *time.Time `json:"surveyAnswerDate,omitempty"`
	HasMedicationFiles       bool       `json:"hasMedicationFiles"`
	MedicationFormAnswerDate *time.Time `json:"medicationFormAnswerDate,omitempty"`
	HasPrescriptionRecord    bool       `json:"hasPrescriptionRecord"`
	GenericDrugDesire        *int       `json:"genericDrugDesire,omitempty"`
}

type PharmacyReserveInsurance struct {
	InsuranceImageURL           *string   `json:"insuranceImageUrl,omitempty"`
	MedicalCertificateImageUrls []*string `json:"medicalCertificateImageUrls"`
}

type PharmacyReserveStatusHistory struct {
	PharmacyReserveStatusHistoryID int       `json:"pharmacyReserveStatusHistoryId"`
	PharmacyReserveDetailID        int       `json:"pharmacyReserveDetailId"`
	Status                         *int      `json:"status,omitempty"`
	StatusCancelType               *int      `json:"statusCancelType,omitempty"`
	GuidanceStatus                 *int      `json:"guidanceStatus,omitempty"`
	PharmacistID                   *int      `json:"pharmacistId,omitempty"`
	PharmacistName                 *string   `json:"pharmacistName,omitempty"`
	PharmacistMemo                 string    `json:"pharmacistMemo"`
	CreatedAt                      time.Time `json:"createdAt"`
}

type PharmacyStaff struct {
	UserID  int    `json:"userId"`
	Name    string `json:"name"`
	LoginID string `json:"loginId"`
}

type PortalAddress struct {
	Postcode       string `json:"postcode"`
	PrefectureID   int    `json:"prefectureId"`
	PrefectureName string `json:"prefectureName"`
	CityName       string `json:"cityName"`
	CityID         int    `json:"cityId"`
	Banti          string `json:"banti"`
}

type PortalBasicInfo struct {
	Name         string     `json:"name"`
	NameKana     string     `json:"nameKana"`
	Gender       *int       `json:"gender,omitempty"`
	Birthdate    *time.Time `json:"birthdate,omitempty"`
	HomePost     *string    `json:"homePost,omitempty"`
	HomeAddress1 *string    `json:"homeAddress1,omitempty"`
	HomeAddress2 *string    `json:"homeAddress2,omitempty"`
	PhoneNumber1 string     `json:"phoneNumber1"`
	Email        string     `json:"email"`
}

type PortalBusinessTime struct {
	BusinessTimeID int     `json:"businessTimeId"`
	StartTime      string  `json:"startTime"`
	EndTime        string  `json:"endTime"`
	MonFlag        float64 `json:"monFlag"`
	TueFlag        float64 `json:"tueFlag"`
	WedFlag        float64 `json:"wedFlag"`
	ThuFlag        float64 `json:"thuFlag"`
	FriFlag        float64 `json:"friFlag"`
	SatFlag        float64 `json:"satFlag"`
	SunFlag        float64 `json:"sunFlag"`
}

type PortalBusinessTimeInput struct {
	StartTime string  `json:"startTime"`
	EndTime   string  `json:"endTime"`
	MonFlag   float64 `json:"monFlag"`
	TueFlag   float64 `json:"tueFlag"`
	WedFlag   float64 `json:"wedFlag"`
	ThuFlag   float64 `json:"thuFlag"`
	FriFlag   float64 `json:"friFlag"`
	SatFlag   float64 `json:"satFlag"`
	SunFlag   float64 `json:"sunFlag"`
}

type PortalCustomer struct {
	CustomerID int        `json:"customerId"`
	KanaName   *string    `json:"kanaName,omitempty"`
	Name       *string    `json:"name,omitempty"`
	Gender     *int       `json:"gender,omitempty"`
	Birthday   *time.Time `json:"birthday,omitempty"`
	Telephone  *string    `json:"telephone,omitempty"`
}

type PortalCustomerPharmacy struct {
	PortalCustomerPharmacyID int     `json:"portalCustomerPharmacyId"`
	ReserveID                int     `json:"reserveId"`
	CustomerID               int     `json:"customerId"`
	PharmacyName             string  `json:"pharmacyName"`
	PharmacyStoreName        *string `json:"pharmacyStoreName,omitempty"`
	FaxNumber                string  `json:"faxNumber"`
	Address1                 string  `json:"address1"`
	Address2                 *string `json:"address2,omitempty"`
	PostCode                 string  `json:"postCode"`
	PhoneNumber              string  `json:"phoneNumber"`
}

type PortalHospital struct {
	HospitalID          int                          `json:"hospitalId"`
	Name                string                       `json:"name"`
	PostCode            string                       `json:"postCode"`
	Telephone           string                       `json:"telephone"`
	IsCarpark           bool                         `json:"isCarpark"`
	PaymentDetails      string                       `json:"paymentDetails"`
	CarparkDetail       *string                      `json:"carparkDetail,omitempty"`
	DescriptionTitle    string                       `json:"descriptionTitle"`
	Description         *string                      `json:"description,omitempty"`
	IsActive            bool                         `json:"isActive"`
	BusinessTimeDetail  *string                      `json:"businessTimeDetail,omitempty"`
	DirectorName        *string                      `json:"directorName,omitempty"`
	TimelineDescription *string                      `json:"timelineDescription,omitempty"`
	HolidayDetail       *string                      `json:"holidayDetail,omitempty"`
	Address1            string                       `json:"address1"`
	Address2            *string                      `json:"address2,omitempty"`
	AccessDetail        *string                      `json:"accessDetail,omitempty"`
	HomePage            *string                      `json:"homePage,omitempty"`
	MailAddress         *string                      `json:"mailAddress,omitempty"`
	BusinessTimes       []*PortalBusinessTime        `json:"businessTimes"`
	HospitalStations    []*PortalHospitalStation     `json:"hospitalStations,omitempty"`
	Tags                []*PortalHospitalTag         `json:"tags"`
	Examinations        []*PortalHospitalExamination `json:"examinations"`
	Files               []*PortalHospitalFile        `json:"files,omitempty"`
	Specialists         []*PortalHospitalSpecialist  `json:"specialists"`
}

type PortalHospitalExamination struct {
	ExaminationID int `json:"examinationId"`
	Type          int `json:"type"`
}

type PortalHospitalFile struct {
	FileID           *int       `json:"fileId,omitempty"`
	OriginalFileName string     `json:"originalFileName"`
	S3Key            string     `json:"s3Key"`
	CreatedAt        *time.Time `json:"createdAt,omitempty"`
}

type PortalHospitalFileInput struct {
	OriginalFileName string `json:"originalFileName"`
	UploadFileURL    string `json:"uploadFileUrl"`
}

type PortalHospitalInput struct {
	Name                string                        `json:"name" validate:"max=200"`
	PostCode            string                        `json:"postCode"`
	Telephone           string                        `json:"telephone"`
	IsCarpark           bool                          `json:"isCarpark"`
	PaymentDetails      string                        `json:"paymentDetails" validate:"max=4000"`
	CarparkDetail       *string                       `json:"carparkDetail,omitempty" validate:"omitempty,max=4000"`
	DescriptionTitle    string                        `json:"descriptionTitle" validate:"max=1000"`
	Description         *string                       `json:"description,omitempty" validate:"omitempty,max=4000"`
	IsActive            bool                          `json:"isActive"`
	BusinessTimeDetail  *string                       `json:"businessTimeDetail,omitempty" validate:"omitempty,max=2000"`
	DirectorName        *string                       `json:"directorName,omitempty" validate:"omitempty,max=40"`
	MailAddress         *string                       `json:"mailAddress,omitempty" validate:"omitempty,max=255"`
	TimelineDescription *string                       `json:"timelineDescription,omitempty" validate:"omitempty,max=2000"`
	HolidayDetail       *string                       `json:"holidayDetail,omitempty" validate:"omitempty,max=200"`
	AccessDetail        *string                       `json:"accessDetail,omitempty" validate:"omitempty,max=4000"`
	HomePage            *string                       `json:"homePage,omitempty" validate:"omitempty,max=2000"`
	Address1            string                        `json:"address1"`
	Address2            *string                       `json:"address2,omitempty" validate:"omitempty,max=2000"`
	TagIds              []int                         `json:"tagIds,omitempty"`
	ExaminationIds      []int                         `json:"examinationIds,omitempty"`
	HospitalStations    []*PortalHospitalStationInput `json:"hospitalStations,omitempty"`
	SpecialistIds       []int                         `json:"specialistIds,omitempty"`
}

type PortalHospitalNotification struct {
	HospitalNotificationID int        `json:"hospitalNotificationId"`
	Title                  string     `json:"title"`
	Description            string     `json:"description"`
	Status                 int        `json:"status"`
	StartDate              *time.Time `json:"startDate,omitempty"`
	EndDate                *time.Time `json:"endDate,omitempty"`
	CreatedTime            *string    `json:"createdTime,omitempty"`
}

type PortalHospitalNotificationInput struct {
	Title       string     `json:"title" validate:"max=50"`
	Description string     `json:"description"`
	Status      int        `json:"status"`
	StartDate   *time.Time `json:"startDate,omitempty"`
	EndDate     *time.Time `json:"endDate,omitempty"`
}

type PortalHospitalNotificationOutput struct {
	HospitalNotificationInfo        *PortalHospitalNotification  `json:"hospitalNotificationInfo"`
	HospitalNotificationCreateStaff *NotificationCreateStaffInfo `json:"hospitalNotificationCreateStaff,omitempty"`
}

type PortalHospitalSpecialist struct {
	SpecialistID int `json:"specialistId"`
}

type PortalHospitalStaff struct {
	HospitalStaffID  int                        `json:"hospitalStaffId"`
	Name             string                     `json:"name"`
	Description      *string                    `json:"description,omitempty"`
	Order            int                        `json:"order"`
	ExperienceDetail *string                    `json:"experienceDetail,omitempty"`
	SpecialistDetail *string                    `json:"specialistDetail,omitempty"`
	IsDirector       bool                       `json:"isDirector"`
	Files            []*PortalHospitalStaffFile `json:"files,omitempty"`
}

type PortalHospitalStaffFile struct {
	FileID           *int       `json:"fileId,omitempty"`
	OriginalFileName string     `json:"originalFileName"`
	S3Key            string     `json:"s3Key"`
	CreatedAt        *time.Time `json:"createdAt,omitempty"`
}

type PortalHospitalStaffFileInput struct {
	OriginalFileName string `json:"originalFileName"`
	UploadFileURL    string `json:"uploadFileUrl"`
}

type PortalHospitalStation struct {
	StationID     int    `json:"stationId"`
	StationName   string `json:"stationName"`
	WalkingMinute int    `json:"walkingMinute"`
}

type PortalHospitalStationInput struct {
	StationID     int `json:"stationId"`
	WalkingMinute int `json:"walkingMinute"`
}

type PortalHospitalTag struct {
	TagID int `json:"tagId"`
}

type PortalMasterRailLine struct {
	RaillineID        int                          `json:"raillineId"`
	Name              string                       `json:"name"`
	RaillineCompanyID int                          `json:"raillineCompanyId"`
	RaillineCompany   *PortalMasterRailLineCompany `json:"raillineCompany"`
}

type PortalMasterRailLineCompany struct {
	RaillineCompanyID int    `json:"raillineCompanyId"`
	Name              string `json:"name"`
	Type              int    `json:"type"`
}

type PortalMasterStation struct {
	StationID  int                   `json:"stationId"`
	CityID     int                   `json:"cityId"`
	RaillineID int                   `json:"raillineId"`
	Name       string                `json:"name"`
	Railline   *PortalMasterRailLine `json:"railline"`
}

type PortalPrefecture struct {
	Name         string `json:"name"`
	PrefectureID int    `json:"prefectureId"`
}

type PostCodeMstModel struct {
	ID             int64  `json:"id"`
	PostCd         string `json:"postCd"`
	PrefKana       string `json:"prefKana"`
	CityKana       string `json:"cityKana"`
	PostalTermKana string `json:"postalTermKana"`
	PrefName       string `json:"prefName"`
	CityName       string `json:"cityName"`
	Banti          string `json:"banti"`
	IsDeleted      int    `json:"isDeleted"`
	Address        string `json:"address"`
}

type Prefecture struct {
	Name         string `json:"name"`
	PrefectureID int    `json:"prefectureId"`
}

type PrescriptionImage struct {
	PrescriptionImageID int     `json:"prescriptionImageId"`
	ImageURL            string  `json:"imageUrl"`
	MimeType            *string `json:"mimeType,omitempty"`
}

type PrescriptionReception struct {
	PrescriptionReceptionID int       `json:"prescriptionReceptionId"`
	HpID                    int       `json:"hpId"`
	ReceptionTimestamp      time.Time `json:"receptionTimestamp"`
	ReceptionStatus         int       `json:"receptionStatus"`
	PhoneNumber             string    `json:"phoneNumber"`
	GenericDrugDesire       int       `json:"genericDrugDesire"`
	PrescriptionRecordBring int       `json:"prescriptionRecordBring"`
	OtherRequest            *string   `json:"otherRequest,omitempty"`
	PrintStatus             int       `json:"printStatus"`
	SmsStatus               int       `json:"smsStatus"`
	Memo                    *string   `json:"memo,omitempty"`
	IsNew                   bool      `json:"isNew"`
}

type PrescriptionReceptionDateFilterInput struct {
	DisplayDate time.Time `json:"displayDate"`
}

type PrescriptionReceptionPagingInput struct {
	Limit    *int   `json:"limit,omitempty"`
	CursorID *int64 `json:"cursorId,omitempty"`
}

type PrescriptionReceptionStatusFilterInput struct {
	ReceptionStatus *int `json:"receptionStatus,omitempty" validate:"omitempty,eq=1|eq=2"`
	SmsStatus       *int `json:"smsStatus,omitempty" validate:"omitempty,eq=1|eq=2"`
}

type PromptInput struct {
	Name         string `json:"name"`
	Title        string `json:"title"`
	Prompt       string `json:"prompt"`
	CanEdit      bool   `json:"canEdit"`
	IsLatest     bool   `json:"isLatest"`
	IsActive     bool   `json:"isActive"`
	OrderValue   int    `json:"orderValue"`
	IsDeleted    int    `json:"isDeleted"`
	BasePromptID *int   `json:"basePromptId,omitempty"`
}

type PromptSetting struct {
	PromptName string `json:"prompt_name"`
	LlmModel   int    `json:"llm_model"`
	Prompt     string `json:"prompt"`
}

type Query struct {
}

type RaiinInfo struct {
	RaiinNo      int    `json:"raiinNo"`
	PtID         int    `json:"ptId"`
	SinDate      int    `json:"sinDate"`
	Status       int    `json:"status"`
	SinStartTime string `json:"sinStartTime"`
	SinEndTime   string `json:"sinEndTime"`
}

type ReceptionModel struct {
	HpID                  int    `json:"hpId"`
	PtID                  int    `json:"ptId"`
	SinDate               int    `json:"sinDate"`
	YoyakuID              int    `json:"yoyakuId"`
	YoyakuTime            string `json:"yoyakuTime"`
	YoyakuEndTime         string `json:"yoyakuEndTime"`
	TreatmentDepartmentID int    `json:"treatmentDepartmentId"`
	ReserveDetailID       int    `json:"reserveDetailId"`
	TantoID               int    `json:"tantoId"`
}

type RefreshTokenReq struct {
	RefreshToken    string `json:"refreshToken"`
	TokenTTL        int    `json:"tokenTTL"`
	RefreshTokenTTL int    `json:"refreshTokenTTL"`
	IsWebSocket     *bool  `json:"isWebSocket,omitempty"`
}

type RefreshTokenRes struct {
	Token                  string `json:"token"`
	TokenExpiryTime        string `json:"tokenExpiryTime"`
	RefreshToken           string `json:"refreshToken"`
	RefreshTokenExpiryTime string `json:"refreshTokenExpiryTime"`
}

type RegistFincodeShopIDInput struct {
	ShopID string `json:"shopId"`
}

type RegisterRaiinInput struct {
	ReceptionModel *ReceptionModel `json:"receptionModel"`
	RaiinComment   string          `json:"raiinComment"`
}

type RelationConditionOfTreatmentDepartment struct {
	CalendarTreatments     []*CalendarTreatment        `json:"calendarTreatments,omitempty"`
	ReserveDetails         []*ReservationDetail        `json:"reserveDetails,omitempty"`
	ReserveDetailHistories []*ReservationDetailHistory `json:"reserveDetailHistories,omitempty"`
}

type RequestClinicPaymentInput struct {
	ReserveDetailID int `json:"reserveDetailId"`
	PaymentType     int `json:"paymentType" validate:"eq=1|eq=2"`
	Amount          int `json:"amount" validate:"min=0"`
}

type RequestPaymentRes struct {
	IsSuccess bool `json:"isSuccess"`
}

type RequestPharmacyPaymentInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
	PaymentType             int `json:"paymentType" validate:"eq=1|eq=2"`
	MedicationCost          int `json:"medicationCost" validate:"min=0"`
	DeliveryFee             int `json:"deliveryFee" validate:"min=0"`
}

type RequestReservePaymentInput struct {
	HpID            int `json:"hpId"`
	ReserveDetailID int `json:"reserveDetailId"`
	PaymentType     int `json:"paymentType" validate:"eq=0|eq=1|eq=2"`
	Amount          int `json:"amount" validate:"min=0"`
	StaffID         int `json:"staffId"`
}

type RequireAgreeInto struct {
	AgreementID                  int       `json:"agreementID"`
	PreRequireAgreeStartDateTime time.Time `json:"preRequireAgreeStartDateTime"`
	RequireAgreeStartDateTime    time.Time `json:"requireAgreeStartDateTime"`
	Title                        string    `json:"title"`
	Document                     string    `json:"document"`
}

type Reservation struct {
	ReserveID                 int                     `json:"reserveId"`
	PatientID                 *int                    `json:"patientId,omitempty"`
	Patient                   *Patient                `json:"patient,omitempty"`
	PrescriptionReceiveMethod int                     `json:"prescriptionReceiveMethod"`
	ReservationDetails        []*ReservationDetail    `json:"reservationDetails,omitempty"`
	PharmacyReserve           *PharmacyReserve        `json:"pharmacyReserve,omitempty"`
	PortalCustomerPharmacy    *PortalCustomerPharmacy `json:"portalCustomerPharmacy,omitempty"`
	Meeting                   *Meeting                `json:"meeting,omitempty"`
}

type ReservationCalendarCreateInput struct {
	CalendarNameSettingType            *int                         `json:"calendarNameSettingType,omitempty"`
	DoctorID                           *int                         `json:"doctorID,omitempty"`
	CalendarName                       *string                      `json:"calendarName,omitempty"`
	NumberOfDoctors                    *int                         `json:"numberOfDoctors,omitempty"`
	Label                              *string                      `json:"label,omitempty" validate:"omitempty,max=10"`
	CalendarTreatMentIds               []*int                       `json:"calendarTreatMentIds,omitempty"`
	ReservationMethodType              *int                         `json:"reservationMethodType,omitempty"`
	ReservableSlotSettingType          *int                         `json:"reservableSlotSettingType,omitempty"`
	ReservableStartDays                *int                         `json:"reservableStartDays,omitempty"`
	ReservableStartTime                *string                      `json:"reservableStartTime,omitempty"`
	ReservableMinutesBeforeExamEndTime *int                         `json:"reservableMinutesBeforeExamEndTime,omitempty"`
	CalendarBasicSettings              []*CalendarBasicSettingInput `json:"calendarBasicSettings"`
	CalendarTimeSlot                   *int                         `json:"calendarTimeSlot,omitempty"`
}

type ReservationCreateInput struct {
	CalendarID             int     `json:"calendarId"`
	CalendarTreatmentID    int     `json:"calendarTreatmentId"`
	ExamTimeSlotID         int     `json:"examTimeSlotId"`
	TreatmentType          *int    `json:"treatmentType,omitempty" validate:"omitempty,eq=0|eq=1"`
	ReserveType            *int    `json:"reserveType,omitempty" validate:"omitempty,eq=0|eq=1"`
	PatientID              *int    `json:"patientId,omitempty"`
	Memo                   *string `json:"memo,omitempty"`
	IsSuspendedReservation bool    `json:"isSuspendedReservation"`
}

type ReservationCreateRes struct {
	ReserveID          int                 `json:"reserveId"`
	ReserveDetailID    int                 `json:"reserveDetailId"`
	RegisterRaiinInput *RegisterRaiinInput `json:"registerRaiinInput"`
}

type ReservationDetail struct {
	ReserveDetailID            int                         `json:"reserveDetailId"`
	ReserveID                  int                         `json:"reserveId"`
	PatientID                  *int                        `json:"patientId,omitempty"`
	Patient                    *Patient                    `json:"patient,omitempty"`
	Status                     int                         `json:"status"`
	TreatmentType              *int                        `json:"treatmentType,omitempty"`
	ReserveType                *int                        `json:"reserveType,omitempty"`
	QueueID                    *int                        `json:"queueId,omitempty"`
	CalendarTreatmentID        int                         `json:"calendarTreatmentId"`
	CalendarTreatment          *CalendarTreatment          `json:"calendarTreatment"`
	Memo                       *string                     `json:"memo,omitempty"`
	FincodeCustomerID          *string                     `json:"fincodeCustomerId,omitempty"`
	PaymentCardID              *string                     `json:"paymentCardId,omitempty"`
	PaymentStatus              int                         `json:"paymentStatus"`
	ReservationDetailHistories []*ReservationDetailHistory `json:"reservationDetailHistories,omitempty"`
	Reservation                *Reservation                `json:"reservation"`
	PaymentDetail              *PaymentDetail              `json:"paymentDetail,omitempty"`
	ExamTimeSlotID             int                         `json:"examTimeSlotId"`
	ExamTimeSlot               *ExamTimeSlot               `json:"examTimeSlot"`
	UpdatedAt                  time.Time                   `json:"updatedAt"`
	IsSuspendedReservation     bool                        `json:"isSuspendedReservation"`
	IsSurveyAnswered           bool                        `json:"isSurveyAnswered"`
	PharmacyReserveDetail      *PharmacyReserveDetail      `json:"pharmacyReserveDetail,omitempty"`
	RaiinInfo                  *RaiinInfo                  `json:"raiinInfo,omitempty"`
}

type ReservationDetailHistory struct {
	ReserveDetailHistoryID int                `json:"reserveDetailHistoryId"`
	ReserveDetailID        int                `json:"reserveDetailId"`
	CalendarTreatmentID    int                `json:"calendarTreatmentId"`
	CalendarTreatment      *CalendarTreatment `json:"calendarTreatment"`
	Status                 int                `json:"status"`
	TreatmentType          *int               `json:"treatmentType,omitempty"`
	ReserveType            *int               `json:"reserveType,omitempty"`
	Memo                   *string            `json:"memo,omitempty"`
	CreatedAt              time.Time          `json:"createdAt"`
	ExamTimeSlotID         int                `json:"examTimeSlotId"`
	ExamTimeSlot           *ExamTimeSlot      `json:"examTimeSlot"`
}

type ReservationUpdateInput struct {
	CalendarID          int     `json:"calendarId"`
	CalendarTreatmentID int     `json:"calendarTreatmentId"`
	ExamTimeSlotID      int     `json:"examTimeSlotId"`
	TreatmentType       *int    `json:"treatmentType,omitempty" validate:"omitempty,eq=0|eq=1"`
	ReserveType         *int    `json:"reserveType,omitempty" validate:"omitempty,eq=0|eq=1"`
	Memo                *string `json:"memo,omitempty"`
	Status              *int    `json:"status,omitempty" validate:"omitempty,eq=0|eq=1|eq=2|eq=3|eq=4"`
}

type ReservationUpdateRes struct {
	ReserveID       int `json:"reserveId"`
	ReserveDetailID int `json:"reserveDetailId"`
	CalendarID      int `json:"calendarId"`
}

type SaveAgentConfigInput struct {
	HospitalID       int    `json:"hospitalID"`
	JobType          int    `json:"jobType"`
	FileType         int    `json:"fileType"`
	IsEnabled        bool   `json:"isEnabled"`
	SharedFolderPath string `json:"sharedFolderPath"`
}

type SchemaResponse struct {
	SchemaID   int    `json:"schemaId"`
	HospitalID int    `json:"hospitalId"`
	Name       string `json:"name"`
	FileName   string `json:"fileName"`
	SortOrder  int    `json:"sortOrder"`
	S3Url      string `json:"s3Url"`
}

type SchemaURLRes struct {
	FileName string `json:"fileName"`
	S3key    string `json:"s3key"`
	URL      string `json:"url"`
}

type SearchAddressByPostcodeRes struct {
	TotalCount        int                 `json:"totalCount"`
	PostCodeMstModels []*PostCodeMstModel `json:"postCodeMstModels"`
}

type SendFaxReq struct {
	To       string `json:"to"`
	FileName string `json:"fileName"`
	S3Key    string `json:"s3Key"`
}

type SendFaxRes struct {
	FaxID  int `json:"faxId"`
	Status int `json:"status"`
}

type SetMailDeliverySettingsRes struct {
	Success            int `json:"success"`
	IsVerificationSent int `json:"isVerificationSent"`
}

type SortInput struct {
	PharmacyDesiredDate       *SortOrder `json:"pharmacyDesiredDate,omitempty"`
	PharmacyReserveUpdateDate *SortOrder `json:"pharmacyReserveUpdateDate,omitempty"`
	ReserveTime               *SortOrder `json:"reserveTime,omitempty"`
}

type SortPortalHospitalStaffsInput struct {
	StaffIds []int `json:"staffIds"`
}

type SortPromptInput struct {
	PromptID      int `json:"promptId"`
	OldOrderValue int `json:"oldOrderValue"`
	NewOrderValue int `json:"newOrderValue"`
}

type SortTaskCategoriesInput struct {
	TaskCategoryIds []int `json:"taskCategoryIds"`
}

type SortTaskStatusesInput struct {
	TaskStatusIds []int `json:"taskStatusIds"`
}

type SortTreatmentDepartmentInput struct {
	TreatmentDepartmentIds []int `json:"treatmentDepartmentIds"`
}

type Specialist struct {
	SpecialistID int    `json:"specialistId"`
	Name         string `json:"name"`
}

type Staff struct {
	StaffID   int    `json:"staffId"`
	StaffName string `json:"staffName"`
}

type StaffChannel struct {
	ChannelID             string     `json:"channelId"`
	ChannelName           string     `json:"channelName"`
	ChannelType           int        `json:"channelType"`
	CreatedBy             string     `json:"createdBy"`
	UpdatedBy             string     `json:"updatedBy"`
	CreatedAt             time.Time  `json:"createdAt"`
	UpdatedAt             time.Time  `json:"updatedAt"`
	HospitalID            int        `json:"hospitalId"`
	IsUnread              int        `json:"isUnread"`
	LatestPostedMessageAt *time.Time `json:"latestPostedMessageAt,omitempty"`
}

type StaffInfo struct {
	StaffID          int                `json:"staffId"`
	StaffName        string             `json:"staffName"`
	StaffKana        string             `json:"staffKana"`
	StaffType        int                `json:"staffType"`
	HospitalID       int                `json:"hospitalID"`
	Email            *string            `json:"email,omitempty"`
	ManagerKbn       int                `json:"managerKbn"`
	LoginID          string             `json:"loginId"`
	Status           int                `json:"status"`
	MedicalLicenseNo *string            `json:"medicalLicenseNo,omitempty"`
	MayakuLicenseNo  *string            `json:"mayakuLicenseNo,omitempty"`
	Permissions      []*StaffPermission `json:"permissions"`
}

type StaffPermission struct {
	FunctionCd string `json:"functionCd"`
	Permission int    `json:"permission"`
}

type StaffPermissionInput struct {
	FunctionCd string `json:"functionCd"`
	Permission int    `json:"permission" validate:"eq=0"`
}

type StaffWithTaskCount struct {
	StaffID   int    `json:"staffId"`
	StaffName string `json:"staffName"`
	TaskCount int    `json:"taskCount"`
}

type StatusFilterInput struct {
	Status           *int `json:"status,omitempty" validate:"omitempty,eq=1|eq=2|eq=3|eq=4|eq=5|eq=6|eq=7"`
	PharmacistStatus *int `json:"pharmacistStatus,omitempty" validate:"omitempty,eq=1|eq=2"`
	GuidanceStatus   *int `json:"guidanceStatus,omitempty" validate:"omitempty,eq=1|eq=2|eq=3"`
	PaymentStatus    *int `json:"paymentStatus,omitempty" validate:"omitempty,eq=1|eq=2|eq=3"`
	CSVStatus        *int `json:"csvStatus,omitempty" validate:"omitempty,eq=1|eq=2|eq=3"`
}

type StopBillingClinicPaymentInput struct {
	ReserveDetailID int `json:"reserveDetailId"`
}

type StopBillingPharmacyPaymentInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailId"`
}

type Subscription struct {
}

type SurveyAnswer struct {
	SurveyAnswerID   int       `json:"surveyAnswerId"`
	TreatmentTitle   *string   `json:"treatmentTitle,omitempty"`
	TreatmentType    *int      `json:"treatmentType,omitempty"`
	BasicAnswerJSON  string    `json:"basicAnswerJson"`
	CustomAnswerJSON string    `json:"customAnswerJson"`
	CreatedAt        time.Time `json:"createdAt"`
	ExamStartDate    time.Time `json:"examStartDate"`
	ReserveDetailID  int       `json:"reserveDetailId"`
}

type SurveyAnswerNoPatient struct {
	SurveyAnswerNoPatientID *int64                       `json:"surveyAnswerNoPatientId,omitempty"`
	HospitalID              *int                         `json:"hospitalId,omitempty"`
	SurveyID                *int                         `json:"surveyId,omitempty"`
	SurveyName              *string                      `json:"surveyName,omitempty"`
	KanaName                *string                      `json:"kanaName,omitempty"`
	Name                    *string                      `json:"name,omitempty"`
	Birthday                *int                         `json:"birthday,omitempty"`
	CreatedBy               *string                      `json:"createdBy,omitempty"`
	UpdatedBy               *string                      `json:"updatedBy,omitempty"`
	CreatedAt               *time.Time                   `json:"createdAt,omitempty"`
	UpdatedAt               *time.Time                   `json:"updatedAt,omitempty"`
	IsDeleted               *int                         `json:"isDeleted,omitempty"`
	Files                   []*SurveyAnswerNoPatientFile `json:"files,omitempty"`
}

type SurveyAnswerNoPatientCreateReq struct {
	SurveyAnswer *string                           `json:"surveyAnswer,omitempty"`
	KanaName     *string                           `json:"kanaName,omitempty"`
	Name         *string                           `json:"name,omitempty"`
	Birthday     int                               `json:"birthday"`
	Secret       string                            `json:"secret"`
	Files        []*SurveyAnswerNoPatientFileInput `json:"files,omitempty"`
}

type SurveyAnswerNoPatientDetail struct {
	SurveyAnswerNoPatientID *int64                                      `json:"surveyAnswerNoPatientId,omitempty"`
	HospitalID              *int                                        `json:"hospitalId,omitempty"`
	SurveyID                *int                                        `json:"surveyId,omitempty"`
	SurveyName              *string                                     `json:"surveyName,omitempty"`
	SurveyAnswer            *string                                     `json:"surveyAnswer,omitempty"`
	KanaName                *string                                     `json:"kanaName,omitempty"`
	Name                    *string                                     `json:"name,omitempty"`
	Birthday                *int                                        `json:"birthday,omitempty"`
	CreatedBy               *string                                     `json:"createdBy,omitempty"`
	UpdatedBy               *string                                     `json:"updatedBy,omitempty"`
	CreatedAt               *time.Time                                  `json:"createdAt,omitempty"`
	UpdatedAt               *time.Time                                  `json:"updatedAt,omitempty"`
	IsDeleted               *int                                        `json:"isDeleted,omitempty"`
	Files                   []*SurveyAnswerNoPatientFileWithDownloadURL `json:"files,omitempty"`
}

type SurveyAnswerNoPatientFile struct {
	FileName string `json:"fileName"`
	S3Key    string `json:"s3Key"`
}

type SurveyAnswerNoPatientFileInput struct {
	FileName string `json:"fileName"`
	S3Key    string `json:"s3Key"`
}

type SurveyAnswerNoPatientFileWithDownloadURL struct {
	FileName    string `json:"fileName"`
	DownloadURL string `json:"downloadUrl"`
	S3Key       string `json:"s3Key"`
}

type SurveyAnswerNoPatientUploadFileURL struct {
	FileName  string `json:"fileName"`
	UploadURL string `json:"uploadUrl"`
	S3Key     string `json:"s3Key"`
}

type SurveyAnswerNoPatientUploadFileURLsReq struct {
	Secret    string   `json:"secret"`
	FileNames []string `json:"fileNames"`
}

type SurveyCreateReq struct {
	FQuesJSON string `json:"fQuesJson"`
	Name      string `json:"name"`
}

type SurveyDateRangeReq struct {
	HospitalID int    `json:"hospitalId"`
	StartDate  string `json:"startDate"`
	EndDate    string `json:"endDate"`
}

type SurveyInformationReq struct {
	SurveyID  int    `json:"surveyId"`
	FQuesJSON string `json:"fQuesJson"`
	Name      string `json:"name"`
}

type SurveyPDFReq struct {
	HospitalID int `json:"hospitalId"`
	SurveyID   int `json:"surveyId"`
}

type SurveyPDFURLRes struct {
	PDFURL string `json:"pdfURL"`
}

type SurveyPDFZipReq struct {
	SurveyIds []int `json:"surveyIds"`
}

type SurveyPDFsRes struct {
	SurveyID   int    `json:"surveyId"`
	Date       string `json:"date"`
	Department string `json:"department"`
	Physician  string `json:"physician"`
}

type SurveyRes struct {
	SurveyID         int       `json:"surveyId"`
	FQuesJSON        string    `json:"fQuesJson"`
	Name             string    `json:"name"`
	CreatedDate      time.Time `json:"createdDate"`
	LastModifiedDate time.Time `json:"lastModifiedDate"`
	Secret           *string   `json:"secret,omitempty"`
	ClinicName       *string   `json:"clinicName,omitempty"`
	IsDeleted        int       `json:"isDeleted"`
	CreatedAt        time.Time `json:"createdAt"`
}

type SurveyTemplate struct {
	SurveyTemplateID int    `json:"surveyTemplateId"`
	FQuesJSON        string `json:"fQuesJson"`
	Name             string `json:"name"`
}

type SuspendReservationInput struct {
	CalendarID          int       `json:"calendarId"`
	CalendarTreatmentID *int      `json:"calendarTreatmentId,omitempty"`
	ExamEndDate         time.Time `json:"examEndDate"`
	ExamStartDate       time.Time `json:"examStartDate"`
}

type Tag struct {
	TagID     int    `json:"tagId"`
	Name      string `json:"name"`
	SortOrder int    `json:"sortOrder"`
}

type Task struct {
	TaskID             int            `json:"taskId"`
	CreatedDate        time.Time      `json:"createdDate"`
	CategoryID         int            `json:"categoryId"`
	Category           *TaskCategory  `json:"category"`
	Title              string         `json:"title"`
	DetailText         *string        `json:"detailText,omitempty"`
	DetailHTML         *string        `json:"detailHtml,omitempty"`
	CreatedStaff       *Staff         `json:"createdStaff,omitempty"`
	ResponsibleStaffID *int           `json:"responsibleStaffId,omitempty"`
	ResponsibleStaff   *Staff         `json:"responsibleStaff,omitempty"`
	StatusID           int            `json:"statusId"`
	Status             *TaskStatus    `json:"status"`
	ExpiredAt          *time.Time     `json:"expiredAt,omitempty"`
	PatientID          *int           `json:"patientId,omitempty"`
	Patient            *Patient       `json:"patient,omitempty"`
	HospitalID         int            `json:"hospitalId"`
	IsAutoCreated      bool           `json:"isAutoCreated"`
	TaskFiles          []*TaskFile    `json:"taskFiles,omitempty"`
	TaskHistories      []*TaskHistory `json:"taskHistories,omitempty"`
	Comments           []*TaskComment `json:"comments,omitempty"`
}

type TaskCategory struct {
	ID         int    `json:"id"`
	HospitalID int    `json:"hospitalId"`
	Name       string `json:"name"`
	Order      int    `json:"order"`
}

type TaskComment struct {
	ID             int                `json:"id"`
	TaskID         int                `json:"taskId"`
	ContentText    *string            `json:"contentText,omitempty"`
	ContentHTML    *string            `json:"contentHtml,omitempty"`
	CreatedStaffID int                `json:"createdStaffId"`
	CreatedStaff   *Staff             `json:"createdStaff"`
	CreatedAt      *time.Time         `json:"createdAt,omitempty"`
	IsEdited       bool               `json:"isEdited"`
	CommentFiles   []*TaskCommentFile `json:"commentFiles"`
	TaskChanged    string             `json:"taskChanged"`
}

type TaskCommentFile struct {
	ID               int        `json:"id"`
	TaskCommentID    int        `json:"taskCommentId"`
	OriginalFileName string     `json:"originalFileName"`
	S3Key            string     `json:"s3Key"`
	CreatedAt        *time.Time `json:"createdAt,omitempty"`
	Size             int        `json:"size"`
}

type TaskFile struct {
	TaskFileID       *int       `json:"taskFileId,omitempty"`
	OriginalFileName string     `json:"originalFileName"`
	S3Key            string     `json:"s3Key"`
	CreatedAt        *time.Time `json:"createdAt,omitempty"`
	Size             int        `json:"size"`
}

type TaskFileInput struct {
	OriginalFileName string `json:"originalFileName"`
	UploadFileURL    string `json:"uploadFileUrl"`
	FileSize         int    `json:"fileSize"`
}

type TaskHistory struct {
	ID          int        `json:"id"`
	TaskID      int        `json:"taskId"`
	History     string     `json:"history"`
	CreatedAt   *time.Time `json:"createdAt,omitempty"`
	EditedStaff *Staff     `json:"editedStaff,omitempty"`
}

type TaskInput struct {
	Title              *string `json:"title,omitempty"`
	CategoryID         *int    `json:"categoryId,omitempty"`
	ResponsibleStaffID *int    `json:"responsibleStaffId,omitempty"`
	DetailHTML         *string `json:"detailHtml,omitempty"`
	DetailText         *string `json:"detailText,omitempty"`
	StatusID           *int    `json:"statusId,omitempty"`
	PatientID          *int    `json:"patientId,omitempty"`
	ExpiredAt          *string `json:"expiredAt,omitempty"`
	IsAutoCreated      *bool   `json:"isAutoCreated,omitempty"`
}

type TaskStatus struct {
	ID         int    `json:"id"`
	HospitalID int    `json:"hospitalId"`
	Name       string `json:"name"`
	Color      string `json:"color"`
	Order      int    `json:"order"`
}

type TaskStatusWithTaskCount struct {
	StatusID   int    `json:"statusId"`
	StatusName string `json:"statusName"`
	TaskCount  int    `json:"taskCount"`
}

type TemplateDocInfo struct {
	TemplateDocID int       `json:"templateDocId"`
	DisplayName   string    `json:"displayName"`
	FileName      string    `json:"fileName"`
	S3Key         string    `json:"s3Key"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	IsDeleted     bool      `json:"isDeleted"`
}

type TemplateDocInput struct {
	TemplateDocID *int       `json:"templateDocId,omitempty"`
	DisplayName   string     `json:"displayName" validate:"max=255"`
	FileName      string     `json:"fileName" validate:"templateDocExtension,max=255"`
	S3Key         *string    `json:"s3Key,omitempty"`
	CreatedAt     *time.Time `json:"createdAt,omitempty"`
	UpdatedAt     *time.Time `json:"updatedAt,omitempty"`
	IsDeleted     *bool      `json:"isDeleted,omitempty"`
}

type TemplateDocListRes struct {
	Templates []*TemplateDocInfo `json:"templates,omitempty"`
}

type TemplateDocParam struct {
	ItemName         string `json:"itemName"`
	ParameterName    string `json:"parameterName"`
	ReflectedContent string `json:"reflected_content"`
}

type TranscribeResult struct {
	Transcription  string  `json:"transcription"`
	ProcessingTime float64 `json:"processing_time"`
}

type TreatmentCategory struct {
	TreatmentCategoryID int    `json:"treatmentCategoryId"`
	Name                string `json:"name"`
	GroupType           int    `json:"groupType"`
}

type TreatmentDepartment struct {
	TreatmentDepartmentID       int                  `json:"treatmentDepartmentId"`
	CreatedAt                   string               `json:"createdAt"`
	CreatedBy                   string               `json:"createdBy"`
	UpdatedAt                   string               `json:"updatedAt"`
	UpdatedBy                   string               `json:"updatedBy"`
	HospitalID                  int                  `json:"hospitalId"`
	TreatmentCategoryID         int                  `json:"treatmentCategoryId"`
	Title                       string               `json:"title"`
	Description                 string               `json:"description"`
	Note                        *string              `json:"note,omitempty"`
	TreatmentType               int                  `json:"treatmentType"`
	TreatmentMethod             int                  `json:"treatmentMethod"`
	FirstConsultationTime       int                  `json:"firstConsultationTime"`
	NextConsultationTime        int                  `json:"nextConsultationTime"`
	FirstMedicalInterviewFormID *int                 `json:"firstMedicalInterviewFormId,omitempty"`
	NextMedicalInterviewFormID  *int                 `json:"nextMedicalInterviewFormId,omitempty"`
	TreatmentDepartmentStatus   int                  `json:"treatmentDepartmentStatus"`
	PortalPublicStatus          int                  `json:"portalPublicStatus"`
	Order                       int                  `json:"order"`
	SpecialNote                 *string              `json:"specialNote,omitempty"`
	FeeListNote                 *string              `json:"feeListNote,omitempty"`
	TreatmentFeeList            []*TreatmentFee      `json:"treatmentFeeList,omitempty"`
	TreatmentCategory           *TreatmentCategory   `json:"treatmentCategory,omitempty"`
	CalendarTreatments          []*CalendarTreatment `json:"calendarTreatments,omitempty"`
	IsDeleted                   int                  `json:"isDeleted"`
}

type TreatmentDepartmentCreateReq struct {
	TreatmentCategoryID         int                        `json:"treatmentCategoryId"`
	Title                       string                     `json:"title" validate:"max=30"`
	Description                 string                     `json:"description" validate:"max=1000"`
	Note                        *string                    `json:"note,omitempty" validate:"omitempty,max=1000"`
	TreatmentType               int                        `json:"treatmentType"`
	TreatmentMethod             int                        `json:"treatmentMethod"`
	FirstConsultationTime       int                        `json:"firstConsultationTime"`
	NextConsultationTime        int                        `json:"nextConsultationTime"`
	FirstMedicalInterviewFormID *int                       `json:"firstMedicalInterviewFormId,omitempty"`
	NextMedicalInterviewFormID  *int                       `json:"nextMedicalInterviewFormId,omitempty"`
	TreatmentDepartmentStatus   int                        `json:"treatmentDepartmentStatus"`
	SpecialNote                 *string                    `json:"specialNote,omitempty" validate:"omitempty,max=1000"`
	FeeListNote                 *string                    `json:"feeListNote,omitempty" validate:"omitempty,max=1000"`
	TreatmentFeeList            []*TreatmentFeeCreateInput `json:"treatmentFeeList"`
	PortalPublicStatus          int                        `json:"portalPublicStatus"`
}

type TreatmentDepartmentRes struct {
	TreatmentDepartmentID       int     `json:"treatmentDepartmentId"`
	HospitalID                  int     `json:"hospitalId"`
	TreatmentCategoryID         int     `json:"treatmentCategoryId"`
	Title                       string  `json:"title"`
	Description                 string  `json:"description"`
	Note                        *string `json:"note,omitempty"`
	TreatmentType               int     `json:"treatmentType"`
	TreatmentMethod             int     `json:"treatmentMethod"`
	FirstConsultationTime       int     `json:"firstConsultationTime"`
	NextConsultationTime        int     `json:"nextConsultationTime"`
	FirstMedicalInterviewFormID *int    `json:"firstMedicalInterviewFormId,omitempty"`
	NextMedicalInterviewFormID  *int    `json:"nextMedicalInterviewFormId,omitempty"`
	TreatmentDepartmentStatus   int     `json:"treatmentDepartmentStatus"`
	PortalPublicStatus          int     `json:"portalPublicStatus"`
	SpecialNote                 *string `json:"specialNote,omitempty"`
	FeeListNote                 *string `json:"feeListNote,omitempty"`
	Order                       int     `json:"order"`
}

type TreatmentDepartmentUpdateReq struct {
	TreatmentDepartmentID       int                        `json:"treatmentDepartmentId"`
	TreatmentCategoryID         int                        `json:"treatmentCategoryId"`
	Title                       string                     `json:"title" validate:"max=30"`
	Description                 string                     `json:"description" validate:"max=1000"`
	Note                        *string                    `json:"note,omitempty" validate:"omitempty,max=1000"`
	TreatmentType               int                        `json:"treatmentType"`
	TreatmentMethod             int                        `json:"treatmentMethod"`
	FirstConsultationTime       int                        `json:"firstConsultationTime"`
	NextConsultationTime        int                        `json:"nextConsultationTime"`
	FirstMedicalInterviewFormID *int                       `json:"firstMedicalInterviewFormId,omitempty"`
	NextMedicalInterviewFormID  *int                       `json:"nextMedicalInterviewFormId,omitempty"`
	TreatmentDepartmentStatus   int                        `json:"treatmentDepartmentStatus"`
	SpecialNote                 *string                    `json:"specialNote,omitempty" validate:"omitempty,max=1000"`
	FeeListNote                 *string                    `json:"feeListNote,omitempty" validate:"omitempty,max=1000"`
	TreatmentFeeList            []*TreatmentFeeUpdateInput `json:"treatmentFeeList"`
	PortalPublicStatus          int                        `json:"portalPublicStatus"`
}

type TreatmentFee struct {
	FeeID                 int    `json:"feeId"`
	TreatmentDepartmentID int    `json:"treatmentDepartmentId"`
	Title                 string `json:"title"`
	MinFee                *int   `json:"minFee,omitempty"`
	MaxFee                *int   `json:"maxFee,omitempty"`
	IsPriceRange          bool   `json:"isPriceRange"`
}

type TreatmentFeeCreateInput struct {
	Title        string `json:"title"`
	MinFee       *int   `json:"minFee,omitempty"`
	MaxFee       *int   `json:"maxFee,omitempty"`
	IsPriceRange bool   `json:"isPriceRange"`
}

type TreatmentFeeUpdateInput struct {
	FeeID        *int   `json:"feeId,omitempty"`
	Title        string `json:"title"`
	MinFee       *int   `json:"minFee,omitempty"`
	MaxFee       *int   `json:"maxFee,omitempty"`
	IsPriceRange bool   `json:"isPriceRange"`
}

type UpdateHospitalStatusToOpenSearchInput struct {
	HpID         int              `json:"hpID"`
	ActionStatus ActionStatusType `json:"actionStatus"`
}

type UpdateHpStatusIVRInput struct {
	Key    string `json:"key"`
	Status int    `json:"status"`
}

type UpdateLabelInput struct {
	LabelID     int    `json:"labelID"`
	Label       string `json:"label" validate:"max=10"`
	Description string `json:"description" validate:"max=20"`
	ColorCode   string `json:"colorCode"`
}

type UpdateMeetingRequest struct {
	MeetingID      int     `json:"meetingId"`
	Status         int     `json:"status" validate:"eq=0|eq=1|eq=2|eq=3|eq=4|eq=5"`
	ChimeMeetingID *string `json:"chimeMeetingId,omitempty"`
}

type UpdatePatientInput struct {
	PatientID    int64  `json:"patientId"`
	KanaName     string `json:"kanaName" validate:"omitempty,kanaInput,max=200"`
	Name         string `json:"name" validate:"omitempty,max=200"`
	Gender       int    `json:"gender" validate:"omitempty,eq=1|eq=2"`
	Birthdate    int    `json:"birthdate" validate:"omitempty,birthdateInput"`
	PhoneNumber1 string `json:"phoneNumber1" validate:"omitempty,number"`
	PhoneNumber2 string `json:"phoneNumber2" validate:"omitempty,number"`
	Email        string `json:"email" validate:"omitempty,email,max=100"`
	HomePost     string `json:"homePost" validate:"omitempty,postCode"`
	HomeAddress1 string `json:"homeAddress1" validate:"omitempty,max=100"`
	HomeAddress2 string `json:"homeAddress2" validate:"omitempty,max=100"`
}

type UpdatePatientRes struct {
	PatientID    int64      `json:"patientId"`
	KanaName     string     `json:"kanaName"`
	Name         string     `json:"name"`
	Gender       int        `json:"gender"`
	Birthdate    *time.Time `json:"birthdate,omitempty"`
	PhoneNumber1 string     `json:"phoneNumber1"`
	PhoneNumber2 string     `json:"phoneNumber2"`
	Email        string     `json:"email"`
	HomePost     string     `json:"homePost"`
	HomeAddress1 string     `json:"homeAddress1"`
	HomeAddress2 string     `json:"homeAddress2"`
}

type UpdatePharmacyHolidayInput struct {
	PharmacyHolidayID int       `json:"pharmacyHolidayId"`
	HolidayStartDate  time.Time `json:"holidayStartDate"`
	HolidayEndDate    time.Time `json:"holidayEndDate"`
}

type UpdatePharmacyReserveCSVStatusInput struct {
	PharmacyReserveID int `json:"pharmacyReserveId"`
	CSVStatus         int `json:"csvStatus"`
}

type UpdatePharmacyReserveDesiredDateInput struct {
	PharmacyReserveID int                 `json:"pharmacyReserveId"`
	DesiredDates      []*DesiredDateInput `json:"desiredDates"`
}

type UpdatePharmacyReserveDetailGuidanceStatusByPharmacyReserveInput struct {
	PharmacyReserveID int     `json:"pharmacyReserveId"`
	GuidanceStatus    int     `json:"guidanceStatus" validate:"eq=1|eq=2|eq=3"`
	PharmacistMemo    *string `json:"pharmacist_memo,omitempty"`
}

type UpdatePharmacyReserveDetailGuidanceStatusInput struct {
	PharmacyReserveDetailID int     `json:"pharmacyReserveDetailId"`
	GuidanceStatus          int     `json:"guidanceStatus" validate:"eq=1|eq=2|eq=3"`
	PharmacistMemo          *string `json:"pharmacist_memo,omitempty"`
}

type UpdatePharmacyReserveDetailStatusByPharmacyReserveInput struct {
	PharmacyReserveID int  `json:"pharmacyReserveId"`
	Status            int  `json:"status" validate:"eq=1|eq=2|eq=3|eq=4|eq=5|eq=6|eq=7"`
	StatusCancelType  *int `json:"status_cancel_type,omitempty" validate:"omitempty,eq=1|eq=2|eq=3|eq=4"`
}

type UpdatePharmacyReserveDetailStatusInput struct {
	PharmacyReserveDetailID int  `json:"pharmacyReserveDetailId"`
	Status                  int  `json:"status" validate:"eq=1|eq=2|eq=3|eq=4|eq=5|eq=6|eq=7"`
	StatusCancelType        *int `json:"status_cancel_type,omitempty" validate:"omitempty,eq=1|eq=2|eq=3|eq=4"`
}

type UpdatePharmacyReservePostalServiceTypeInput struct {
	PharmacyReserveID int `json:"pharmacyReserveId"`
	PostalServiceType int `json:"postalServiceType" validate:"eq=1|eq=2"`
}

type UpdatePortalHospitalInput struct {
	AddedBusinessTimes     []*PortalBusinessTimeInput     `json:"addedBusinessTimes,omitempty"`
	EditedBusinessTimes    []*EditPortalBusinessTimeInput `json:"editedBusinessTimes,omitempty"`
	DeletedBusinessTimeIds []int                          `json:"deletedBusinessTimeIds,omitempty"`
	PortalHospitalInput    *PortalHospitalInput           `json:"portalHospitalInput,omitempty"`
	AddedFiles             []*PortalHospitalFileInput     `json:"addedFiles,omitempty"`
	DeletedFileIds         []int                          `json:"deletedFileIds,omitempty"`
}

type UpdatePrescriptionReceptionMemoInput struct {
	PrescriptionReceptionID int    `json:"prescriptionReceptionId"`
	Memo                    string `json:"memo" validate:"max=20"`
}

type UpdatePrescriptionReceptionPrintStatusInput struct {
	PrescriptionReceptionID int `json:"prescriptionReceptionId"`
	PrintStatus             int `json:"printStatus" validate:"omitempty,eq=1|eq=2"`
}

type UpdatePrescriptionReceptionStatusInput struct {
	PrescriptionReceptionID int `json:"prescriptionReceptionId"`
	ReceptionStatus         int `json:"receptionStatus" validate:"omitempty,eq=1|eq=2"`
}

type UpdateReservePaymentInput struct {
	PaymentClinicDetailID int64 `json:"paymentClinicDetailId"`
	PaymentType           int   `json:"paymentType" validate:"eq=1|eq=2"`
	Amount                int   `json:"amount" validate:"min=0"`
	StaffID               int   `json:"staffId"`
}

type UpdateStaffReq struct {
	StaffID          int                     `json:"staffId"`
	StaffName        string                  `json:"staffName" validate:"min=1,max=40" label:"氏名"`
	StaffKana        string                  `json:"staffKana" validate:"kanaInput,min=1,max=40" label:"フリガナ"`
	StaffType        int                     `json:"staffType" validate:"eq=1|eq=13|eq=14|eq=15" label:"スタッフ種別"`
	ManagerKbn       int                     `json:"managerKbn" validate:"eq=0|eq=7|eq=10" label:"権限区分"`
	Status           int                     `json:"status" validate:"eq=0|eq=1" label:"ステータス"`
	PasswordReset    int                     `json:"passwordReset" validate:"eq=0|eq=1" label:"パスワードリセット"`
	MedicalLicenseNo *string                 `json:"medicalLicenseNo,omitempty" validate:"omitempty,max=6"`
	MayakuLicenseNo  *string                 `json:"mayakuLicenseNo,omitempty" validate:"omitempty,max=7"`
	Permissions      []*StaffPermissionInput `json:"permissions"`
}

type UpdateStaffRes struct {
	StaffID          int                `json:"staffId"`
	StaffName        string             `json:"staffName"`
	StaffKana        string             `json:"staffKana"`
	StaffType        int                `json:"staffType"`
	LoginID          string             `json:"loginId"`
	ManagerKbn       int                `json:"managerKbn"`
	Password         string             `json:"password"`
	Status           int                `json:"status"`
	MedicalLicenseNo *string            `json:"medicalLicenseNo,omitempty"`
	MayakuLicenseNo  *string            `json:"mayakuLicenseNo,omitempty"`
	Permissions      []*StaffPermission `json:"permissions"`
}

type VerifyTokenReq struct {
	Token string `json:"token"`
}

type VerifyTokenRes struct {
	IsValid bool `json:"isValid"`
}

type Yakkyoku24Info struct {
	Name       string `json:"name"`
	PostCode   string `json:"postCode"`
	Telephone1 string `json:"telephone1"`
	Telephone2 string `json:"telephone2"`
	FaxNumber  string `json:"faxNumber"`
	Address1   string `json:"address1"`
	Address2   string `json:"address2"`
}

type AddChannelMembersReq struct {
	ChannelID int       `json:"channelId" validate:"min=1"`
	Members   []*string `json:"members"`
}

type AgreeReq struct {
	AgreementIDs []int `json:"agreementIDs"`
}

type AllowEveryPatientSendReq struct {
	Sendable int `json:"sendable" validate:"eq=0|eq=1"`
}

type AllowPatientSendReq struct {
	PatientID string `json:"patientId"`
	Sendable  int    `json:"sendable" validate:"eq=0|eq=1"`
}

type AttachFilesForm struct {
	ChannelID       int                    `json:"channelId" validate:"min=1"`
	MessageType     MessageType            `json:"messageType"`
	AttachFilesItem []*AttachFilesItemForm `json:"attachFilesItem,omitempty"`
}

type AttachFilesFormByPatient struct {
	ChannelID       int                             `json:"channelId" validate:"min=1"`
	MessageType     MessageTypeByPatient            `json:"messageType"`
	AttachFilesItem []*AttachFilesItemFormByPatient `json:"attachFilesItem,omitempty"`
}

type AuthenticateSignupUserInput struct {
	SmsKey string `json:"smsKey"`
}

type AuthenticateSignupUserRes struct {
	Status string `json:"status"`
}

type ClinicCodeInput struct {
	ClinicCode   string `json:"clinicCode"`
	SuggestLimit int    `json:"suggestLimit"`
}

type ClinicCodeSuggestion struct {
	ClinicCode string `json:"clinicCode"`
}

type CreatePatientChannelReq struct {
	PatientID string `json:"patientId"`
}

type CreateSchemaForm struct {
	S3Url      string `json:"s3Url"`
	FileName   string `json:"fileName"`
	SchemaName string `json:"schemaName" validate:"min=1,max=10"`
}

type CreateStaffChannelReq struct {
	ChannelName string    `json:"channelName" validate:"max=50"`
	ChannelType int       `json:"channelType" validate:"channelType"`
	Members     []*string `json:"members"`
}

type DeleteChannelMembersReq struct {
	ChannelID int    `json:"channelId" validate:"min=1"`
	Member    string `json:"member"`
}

type DeleteChannelReq struct {
	ChannelID int `json:"channelId" validate:"min=1"`
}

type DeleteIVRKeyInput struct {
	HpID int `json:"hpId"`
}

type DeleteMessageFileForm struct {
	ItemID string `json:"itemId"`
}

type DeleteMessageReq struct {
	MessageID string `json:"messageId"`
}

type DeleteSchemaForm struct {
	SchemaID int `json:"schemaId"`
}

type EditChannelReq struct {
	ChannelID   int    `json:"channelId" validate:"min=1"`
	ChannelName string `json:"channelName" validate:"max=50"`
}

type EditMessageReq struct {
	MessageID string `json:"messageId"`
	Content   string `json:"content" validate:"min=1,max=300"`
}

type FindSignupClinicInfoInput struct {
	PrefectureCode    string `json:"prefectureCode"`
	InsuranceCategory string `json:"insuranceCategory"`
	ClinicCode        string `json:"clinicCode"`
}

type GenIVRKeyInput struct {
	HpID int `json:"hpId"`
}

type GenIVRKeyRes struct {
	Key string `json:"key"`
}

type GetChannelMembersReq struct {
	ChannelID int `json:"channelId" validate:"min=1"`
}

type GetIVRKeyInput struct {
	HpID int `json:"hpId"`
}

type GetIVRKeyRes struct {
	IvrKey string `json:"ivrKey"`
}

type GetMessagesReq struct {
	ChannelID                 int     `json:"channelId" validate:"min=1"`
	StartCursorMsgCreatedTime *string `json:"startCursorMsgCreatedTime,omitempty"`
	EndCursorMsgCreatedTime   *string `json:"endCursorMsgCreatedTime,omitempty"`
	Num                       int     `json:"num" validate:"min=1,max=100"`
}

type GetMessagesReqByPatient struct {
	ChannelID                 int     `json:"channelId" validate:"min=1"`
	StartCursorMsgCreatedTime *string `json:"startCursorMsgCreatedTime,omitempty"`
	EndCursorMsgCreatedTime   *string `json:"endCursorMsgCreatedTime,omitempty"`
	Num                       int     `json:"num" validate:"min=1,max=100"`
}

type GetPharmacyPatientFilesInput struct {
	PharmacyReserveDetailID int `json:"pharmacyReserveDetailID"`
}

type GetSMSTemplateInput struct {
	SmsCode string `json:"smsCode"`
}

type MarkAsReadForPatientChannelReq struct {
	ChannelID int `json:"channelId" validate:"min=1"`
}

type MarkAsReadForPatientChannelReqByPatient struct {
	ChannelID int `json:"channelId" validate:"min=1"`
}

type MarkAsReadForStaffChannelReq struct {
	ChannelID int `json:"channelId" validate:"min=1"`
}

type MessageForm struct {
	ChannelID int    `json:"channelId" validate:"min=1"`
	Content   string `json:"content" validate:"min=1,max=300"`
}

type MessageFormByPatient struct {
	ChannelID int    `json:"channelId" validate:"min=1"`
	Content   string `json:"content" validate:"min=1,max=300"`
}

type NotifyRes struct {
	Success int `json:"success"`
}

type OcrFileUploadRequest struct {
	FileName string `json:"fileName"`
}

type OcrFileUploadResponse struct {
	S3Key string `json:"s3Key"`
	URL   string `json:"url"`
}

type OcrRequest struct {
	Engine    EnginType `json:"engine"`
	FileS3Key string    `json:"fileS3Key"`
	Prompt    *string   `json:"prompt,omitempty"`
}

type OcrResponse struct {
	Result string  `json:"result"`
	Error  *string `json:"error,omitempty"`
}

type PharmacyDeliveryHistoriesInput struct {
	PharmacyReserveID int `json:"pharmacyReserveId"`
}

type PharmacyDeliveryHistoryInput struct {
	PharmacyReserveID     int    `json:"pharmacyReserveId"`
	DeliveryInquiryNumber string `json:"deliveryInquiryNumber"`
}

type PrefectureCode struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type SearchInChannel struct {
	ChannelID  int        `json:"channelId"`
	SearchWord string     `json:"searchWord" validate:"min=1,max=50"`
	Page       int        `json:"page" validate:"min=1"`
	Num        int        `json:"num" validate:"min=1,max=20"`
	SortOrder  *SortOrder `json:"sortOrder,omitempty"`
}

type SearchInChannelByPatient struct {
	ChannelID  int    `json:"channelId"`
	SearchWord string `json:"searchWord" validate:"min=1,max=50"`
	Page       int    `json:"page" validate:"min=1"`
	Num        int    `json:"num" validate:"min=1,max=20"`
}

type SearchInMessagesDownloadURL struct {
	ChannelID int       `json:"channelId"`
	S3Keys    []*string `json:"s3Keys"`
}

type SendMailForReserveReq struct {
	ReserveIds       []string `json:"reserveIds"`
	TemplateMailCode string   `json:"templateMailCode"`
}

type SendMailReq struct {
	StaffID          *int    `json:"staffId,omitempty"`
	TemplateMailCode string  `json:"templateMailCode"`
	AdditionalParam  *string `json:"additionalParam,omitempty"`
}

type SendMailRes struct {
	Success int `json:"success"`
}

type SendSMSForPharmacyReserveReq struct {
	SmsCode           string  `json:"smsCode"`
	SmsContent        *string `json:"smsContent,omitempty"`
	PharmacyReserveID int     `json:"pharmacyReserveID"`
}

type SendSMSForSignupReq struct {
	SignUpUserID int    `json:"signUpUserID"`
	SmsCode      string `json:"smsCode"`
}

type SendSMSPrescriptionReceptionInput struct {
	SmsCode                 string  `json:"smsCode"`
	SmsContent              *string `json:"smsContent,omitempty"`
	PrescriptionReceptionID int     `json:"prescriptionReceptionId"`
}

type SendSMSSForReserveReq struct {
	ReserveIDs []string `json:"reserveIDs"`
	SmsCode    string   `json:"smsCode"`
}

type SignupClinicInfoRes struct {
	ClinicName        string `json:"clinicName"`
	PostCode          string `json:"postCode"`
	Address           string `json:"address"`
	PhoneNumber       string `json:"phoneNumber"`
	AdministratorName string `json:"administratorName"`
}

type SignupInput struct {
	ClinicStatus *ClinicStatus `json:"clinicStatus"`
	Doctor       *DoctorInput  `json:"doctor"`
	Clinic       *ClinicInput  `json:"clinic,omitempty"`
	Agreement    *Agreement    `json:"agreement"`
	Aid          *string       `json:"aid,omitempty"`
	Bid          *string       `json:"bid,omitempty"`
	Cid          *string       `json:"cid,omitempty"`
}

type SignupRes struct {
	UserID int `json:"userId"`
}

type SmsOtherTemplateRes struct {
	SmsCode         string `json:"smsCode"`
	SmsTemplateBody string `json:"smsTemplateBody"`
}

type SpUploadFile struct {
	Data string `json:"data"`
}

type UpdateLabelItemPositionInput struct {
	LabelIDs []int `json:"labelIDs"`
}

type UpdateSchemaForm struct {
	SchemaID   int    `json:"schemaId"`
	SchemaName string `json:"schemaName" validate:"min=1,max=10"`
}

type UpdateSchemaItemPositionInput struct {
	SchemaIds []int `json:"schemaIds"`
}

type ActionStatusType string

const (
	ActionStatusTypeCancel    ActionStatusType = "cancel"
	ActionStatusTypeSuspend   ActionStatusType = "suspend"
	ActionStatusTypeUnsuspend ActionStatusType = "unsuspend"
)

var AllActionStatusType = []ActionStatusType{
	ActionStatusTypeCancel,
	ActionStatusTypeSuspend,
	ActionStatusTypeUnsuspend,
}

func (e ActionStatusType) IsValid() bool {
	switch e {
	case ActionStatusTypeCancel, ActionStatusTypeSuspend, ActionStatusTypeUnsuspend:
		return true
	}
	return false
}

func (e ActionStatusType) String() string {
	return string(e)
}

func (e *ActionStatusType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ActionStatusType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ActionStatusType", str)
	}
	return nil
}

func (e ActionStatusType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type APIType string

const (
	APITypeAmiVoice      APIType = "AmiVoice"
	APITypeGcpStt        APIType = "GcpStt"
	APITypeFasterWhisper APIType = "FasterWhisper"
	APITypeKotobaWhisper APIType = "KotobaWhisper"
)

var AllAPIType = []APIType{
	APITypeAmiVoice,
	APITypeGcpStt,
	APITypeFasterWhisper,
	APITypeKotobaWhisper,
}

func (e APIType) IsValid() bool {
	switch e {
	case APITypeAmiVoice, APITypeGcpStt, APITypeFasterWhisper, APITypeKotobaWhisper:
		return true
	}
	return false
}

func (e APIType) String() string {
	return string(e)
}

func (e *APIType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = APIType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ApiType", str)
	}
	return nil
}

func (e APIType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type MessageType string

const (
	MessageTypeText  MessageType = "text"
	MessageTypeImage MessageType = "image"
	MessageTypeFile  MessageType = "file"
)

var AllMessageType = []MessageType{
	MessageTypeText,
	MessageTypeImage,
	MessageTypeFile,
}

func (e MessageType) IsValid() bool {
	switch e {
	case MessageTypeText, MessageTypeImage, MessageTypeFile:
		return true
	}
	return false
}

func (e MessageType) String() string {
	return string(e)
}

func (e *MessageType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = MessageType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid MessageType", str)
	}
	return nil
}

func (e MessageType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type MessageTypeByPatient string

const (
	MessageTypeByPatientText  MessageTypeByPatient = "text"
	MessageTypeByPatientImage MessageTypeByPatient = "image"
	MessageTypeByPatientFile  MessageTypeByPatient = "file"
)

var AllMessageTypeByPatient = []MessageTypeByPatient{
	MessageTypeByPatientText,
	MessageTypeByPatientImage,
	MessageTypeByPatientFile,
}

func (e MessageTypeByPatient) IsValid() bool {
	switch e {
	case MessageTypeByPatientText, MessageTypeByPatientImage, MessageTypeByPatientFile:
		return true
	}
	return false
}

func (e MessageTypeByPatient) String() string {
	return string(e)
}

func (e *MessageTypeByPatient) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = MessageTypeByPatient(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid MessageTypeByPatient", str)
	}
	return nil
}

func (e MessageTypeByPatient) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type SortOrder string

const (
	SortOrderAscend  SortOrder = "ascend"
	SortOrderDescend SortOrder = "descend"
)

var AllSortOrder = []SortOrder{
	SortOrderAscend,
	SortOrderDescend,
}

func (e SortOrder) IsValid() bool {
	switch e {
	case SortOrderAscend, SortOrderDescend:
		return true
	}
	return false
}

func (e SortOrder) String() string {
	return string(e)
}

func (e *SortOrder) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SortOrder(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SortOrder", str)
	}
	return nil
}

func (e SortOrder) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TaskSortType string

const (
	TaskSortTypeCreatedDate TaskSortType = "createdDate"
	TaskSortTypeExpiredAt   TaskSortType = "expiredAt"
)

var AllTaskSortType = []TaskSortType{
	TaskSortTypeCreatedDate,
	TaskSortTypeExpiredAt,
}

func (e TaskSortType) IsValid() bool {
	switch e {
	case TaskSortTypeCreatedDate, TaskSortTypeExpiredAt:
		return true
	}
	return false
}

func (e TaskSortType) String() string {
	return string(e)
}

func (e *TaskSortType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskSortType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskSortType", str)
	}
	return nil
}

func (e TaskSortType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type EnginType string

const (
	EnginTypeGoogleCloudVisionAPI        EnginType = "GoogleCloudVisionApi"
	EnginTypeAWSBedrockClaude3Sonnet     EnginType = "AWSBedrockClaude3Sonnet"
	EnginTypeAWSBedrockClaude3_5Sonnet   EnginType = "AWSBedrockClaude3_5Sonnet"
	EnginTypeAWSBedrockClaude3_5SonnetV2 EnginType = "AWSBedrockClaude3_5Sonnet_v2"
	EnginTypeAWSBedrockClaude3_5Haiku    EnginType = "AWSBedrockClaude3_5Haiku"
)

var AllEnginType = []EnginType{
	EnginTypeGoogleCloudVisionAPI,
	EnginTypeAWSBedrockClaude3Sonnet,
	EnginTypeAWSBedrockClaude3_5Sonnet,
	EnginTypeAWSBedrockClaude3_5SonnetV2,
	EnginTypeAWSBedrockClaude3_5Haiku,
}

func (e EnginType) IsValid() bool {
	switch e {
	case EnginTypeGoogleCloudVisionAPI, EnginTypeAWSBedrockClaude3Sonnet, EnginTypeAWSBedrockClaude3_5Sonnet, EnginTypeAWSBedrockClaude3_5SonnetV2, EnginTypeAWSBedrockClaude3_5Haiku:
		return true
	}
	return false
}

func (e EnginType) String() string {
	return string(e)
}

func (e *EnginType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = EnginType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid enginType", str)
	}
	return nil
}

func (e EnginType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/converter"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util"
	"strconv"
	"unsafe"
)

// Reserve is the resolver for the reserve field.
func (r *mutationResolver) Reserve(ctx context.Context, input model.ReservationCreateInput) (*model.ReservationCreateRes, error) {
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return nil, err
	}

	reservationDetailToCreate := converter.ConvertGqlReservationDetailToEntityModel(sess.StaffID, sess.HospitalID, &input)

	result, err := r.ReservationService.Reserve(ctx, *sess.HospitalID, input.CalendarID, reservationDetailToCreate)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOカレンダー：登録
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoCalenderCreate,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	if input.PatientID != nil {
		// 患者ID指定がある場合
		auditlog.PtID = (*int64)(unsafe.Pointer(input.PatientID))
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	// 停止予約場合、メール配信をスキップする。
	if input.PatientID != nil {
		// メール配信：新規予約のお知らせ：対象クリニックの全スタッフにメール送信
		reservation := r.MailService.GetReservationForMail(
			ctx, serviceModel.ReservationForMailInput{
				HospitalId:            *sess.HospitalID,
				CalendarID:            input.CalendarID,
				TreatmentDepartmentID: result.CalendarTreatment.TreatmentDepartmentID,
				ReserveID:             result.ReserveDetailID,
				ExamTimeSlotID:        input.ExamTimeSlotID,
				TreatmentType:         input.TreatmentType,
				ReserveType:           input.ReserveType,
				Memo:                  input.Memo,
			}, nil)
		if err := r.MailService.RegisterMailForClinicReserve(ctx, *sess.HospitalID, reservation, constant.TemplateMailCodeNewReservation); err != nil {
			return nil, err
		}

		// カスタマーにお知らせメールを送信します
		reserveInfo, err := r.ReservationService.GetReservationInfoByReserveDetailID(ctx, result.ReserveDetailID)
		if err != nil {
			return nil, err
		}

		var templateCode string
		if reserveInfo.ReservationType == constant.ReservationTypeInPerson {
			templateCode = constant.TemplateMailCodePortalNewReservationInPerson
		} else {
			templateCode = constant.TemplateMailCodePortalNewReservationOnline
		}

		err = r.MailService.RegisterMailForCustomer(ctx, reserveInfo, templateCode)
		if err != nil {
			return nil, err
		}
	}

	// 来院情報の登録の入力に詳細予約を変換する
	registerRaiinInput, err := converter.ConvertReservationDetailToRegisterRaiinInput(result)
	if err != nil {
		return nil, err
	}

	return &model.ReservationCreateRes{
		ReserveID:          result.ReserveID,
		ReserveDetailID:    result.ReserveDetailID,
		RegisterRaiinInput: registerRaiinInput,
	}, nil
}

// SuspendReservation is the resolver for the suspendReservation field.
func (r *mutationResolver) SuspendReservation(ctx context.Context, input model.SuspendReservationInput) (bool, error) {
	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return false, err
	}

	err = r.ReservationService.SuspendReservation(ctx, *sess.StaffID, *sess.HospitalID, input.CalendarID, input)
	if err != nil {
		return false, err
	}

	// 監査ログ：GMOカレンダー：登録
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoCalenderCreate,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}

	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return false, err
	}

	return true, nil
}

// UpdateReservation is the resolver for the updateReservation field.
func (r *mutationResolver) UpdateReservation(ctx context.Context, reserveID int, reserveDetailID int, calendarID int, input model.ReservationUpdateInput, resendMessage *bool) (*model.ReservationUpdateRes, error) {
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return nil, err
	}

	// メール配信：予約変更のお知らせ：予約変更前情報の取得
	beforeReservation, err := r.ReservationService.GetReservationDetailByID(ctx, reserveDetailID, *sess.HospitalID, nil)
	if err != nil {
		return nil, err
	}

	reservationDetailUpdateRequest, specificDetails := converter.ConvertGqlReservationDetailToUpdateEntityModel(sess.StaffID, &input, *beforeReservation)

	afterReservationDetail, err := r.ReservationService.UpdateReservation(ctx, reserveID, reserveDetailID, *sess.HospitalID, reservationDetailUpdateRequest, specificDetails, sess.StaffID)
	if err != nil {
		return nil, err
	}

	// カルテ利用ステータスの確認（カルテ利用中の場合）
	if sess.KarteStatus != nil && *sess.KarteStatus == constant.KarteStatusRealInUse {
		// 来院情報の更新
		err = r.ReservationService.UpdateRaiinInfo(ctx, afterReservationDetail)
		if err != nil {
			return nil, err
		}
	}

	// 監査ログ：GMOカレンダー：編集
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoCalenderEdit,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	if afterReservationDetail.PatientID != nil {
		// 患者ID指定がある場合
		auditlog.PtID = (*int64)(unsafe.Pointer(afterReservationDetail.PatientID))
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	var shouldSendStaffMail, shouldSendPatientMail = r.ReservationService.CheckShouldSendMailWhenUpdateReservation(input, beforeReservation)
	// 停止予約場合、メール配信をスキップする。
	if afterReservationDetail.PatientID != nil {
		if (beforeReservation.Status == int(constant.Reserved) && afterReservationDetail.Status == int(constant.AppointmentStarted)) || (resendMessage != nil && *resendMessage) {
			// 予約のステータスが予約済み -> 診察開始になった時にSMS or LINEを送信する(LINE優先)
			reserveDtlIDs := []string{strconv.Itoa(reserveDetailID)}
			err = r.SMSService.RequestForReserve(ctx, reserveDtlIDs, constant.SMSCodeNotifyToPatientJustBefore)
			if err != nil {
				return nil, err
			}
		} else {
			// それ以外は更新メールをクリニックスタッフと患者へ送信

			// メール配信：予約変更のお知らせ：対象クリニックの全スタッフにメール送信
			if shouldSendStaffMail {
				reservation := r.MailService.GetReservationForMail(
					ctx, serviceModel.ReservationForMailInput{
						HospitalId:            *sess.HospitalID,
						CalendarID:            input.CalendarID,
						TreatmentDepartmentID: afterReservationDetail.CalendarTreatment.TreatmentDepartmentID,
						ReserveID:             reserveDetailID,
						ExamTimeSlotID:        input.ExamTimeSlotID,
						TreatmentType:         input.TreatmentType,
						ReserveType:           input.ReserveType,
						Memo:                  input.Memo,
					}, nil)
				if err := r.MailService.RegisterMailForClinicReserve(ctx, *sess.HospitalID, reservation, constant.TemplateMailCodeUpdateReservation); err != nil {
					return nil, err
				}
			}

			// カスタマーにお知らせメールを送信します
			if shouldSendPatientMail {
				reserveInfo, err := r.ReservationService.GetReservationInfoByReserveDetailID(ctx, reserveDetailID)
				if err != nil {
					return nil, err
				}

				var templateCode string
				if reserveInfo.ReservationType == constant.ReservationTypeInPerson {
					templateCode = constant.TemplateMailCodePortalUpdateReservationInPerson
				} else {
					templateCode = constant.TemplateMailCodePortalUpdateReservationOnline
				}

				err = r.MailService.RegisterMailForCustomer(ctx, reserveInfo, templateCode)
				if err != nil {
					return nil, err
				}
			}
		}

	}

	return &model.ReservationUpdateRes{
		ReserveID:       reserveID,
		ReserveDetailID: reserveDetailID,
		CalendarID:      input.CalendarID,
	}, nil
}

// UpdateTreatmentStatusToCompleted is the resolver for the updateTreatmentStatusToCompleted field.
func (r *mutationResolver) UpdateTreatmentStatusToCompleted(ctx context.Context, reserveDetailIds []int) (bool, error) {
	_, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return false, err
	}

	err = r.ReservationService.UpdateTreatmentStatusToCompleted(ctx, reserveDetailIds)
	if err != nil {
		return false, err
	}

	return true, nil
}

// CancelReservation is the resolver for the cancelReservation field.
func (r *mutationResolver) CancelReservation(ctx context.Context, input model.CancelReservationByIDInput) (bool, error) {
	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return false, err
	}

	// メール配信：予約キャンセルのお知らせ：予約削除前情報の取得
	beforeReservation, err := r.ReservationService.GetReservationDetailByID(ctx, input.ReserveDetailID, *sess.HospitalID, nil)
	if err != nil {
		return false, err
	}

	// 予約キャンセル前に、情報を取得
	reserveInfo, err := r.ReservationService.GetReservationInfoByReserveDetailID(ctx, input.ReserveDetailID)
	if err != nil {
		return false, err
	}

	result, err := r.ReservationService.CancelReservation(ctx, *sess.StaffID, input.ReserveID, input.ReserveDetailID, input.CalendarID)
	if err != nil {
		return false, err
	}

	// 薬局予約情報がある場合、キャンセル日時を服薬指導希望日として設定する
	var pharmacyReserveDetail = beforeReservation.PharmacyReserveDetail
	if pharmacyReserveDetail != nil {
		err = r.PharmacyReserveService.UpdateDesiredDateForStatusCancel(ctx, *sess.StaffID, &pharmacyReserveDetail.PharmacyReserveDetailID, model.UpdatePharmacyReserveDesiredDateInput{
			PharmacyReserveID: pharmacyReserveDetail.PharmacyReserveID,
			DesiredDates: []*model.DesiredDateInput{
				{
					DesiredType: constant.PharmacyDesiredDateDesiredTypeSpecified,
					DesiredDate: util.Time.JST(),
				},
			},
		})
		if err != nil {
			return false, err
		}
	}

	// カルテ利用ステータスの確認（カルテ利用中の場合）
	if sess.KarteStatus != nil && *sess.KarteStatus == constant.KarteStatusRealInUse {
		// 来院情報の削除
		err = r.ReservationService.DeleteRaiinInfo(ctx, beforeReservation)
		if err != nil {
			return false, err
		}
	}

	// 監査ログ：GMOカレンダー：削除
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoCalenderDelete,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	if beforeReservation.PatientID != nil {
		// 患者ID指定がある場合
		auditlog.PtID = (*int64)(unsafe.Pointer(beforeReservation.PatientID))
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return false, err
	}

	// 停止予約場合、メール配信をスキップする。
	if beforeReservation.PatientID != nil {
		// メール配信：予約キャンセルのお知らせ：対象クリニックの全スタッフにメール送信
		reservation := r.MailService.GetReservationForMail(
			ctx, serviceModel.ReservationForMailInput{
				HospitalId:            *sess.HospitalID,
				CalendarID:            input.CalendarID,
				TreatmentDepartmentID: beforeReservation.CalendarTreatment.TreatmentDepartmentID,
				ReserveID:             input.ReserveDetailID,
				ExamTimeSlotID:        beforeReservation.ExamTimeSlot.ExamTimeSlotID,
				TreatmentType:         beforeReservation.ReserveDetail.TreatmentType,
				ReserveType:           beforeReservation.ReserveDetail.ReserveType,
				Memo:                  beforeReservation.Memo,
			}, nil)
		if err := r.MailService.RegisterMailForClinicReserve(ctx, *sess.HospitalID, reservation, constant.TemplateMailCodeCancelReservation); err != nil {
			return false, err
		}

		// カスタマーにお知らせメールを送信します
		err = r.MailService.RegisterMailForCustomer(ctx, reserveInfo, constant.TemplateMailCodePortalCancelReservation)
		if err != nil {
			return false, err
		}

		// 薬局24宛にメールを送信
		if beforeReservation.Reserve != nil &&
			beforeReservation.Reserve.PrescriptionReceiveMethod == constant.PrescriptionReceiveMethodTypeGMO24Pharmacy &&
			beforeReservation.PharmacyReserveDetail != nil {
			err = r.MailService.RegisterMailToPharmacy(ctx, sess.StaffID,
				constant.TemplateMailCodeToPharmacyForCancelReserve,
				map[string]string{},
			)
			if err != nil {
				return false, err
			}
		}
	}

	return result, nil
}

// GetReservationDetailByID is the resolver for the getReservationDetailByID field.
func (r *queryResolver) GetReservationDetailByID(ctx context.Context, input model.GetReservationDetailByIDInput) (*model.ReservationDetail, error) {
	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return nil, err
	}

	result, err := r.ReservationService.GetReservationDetailByID(ctx, input.ReserveDetailID, *sess.HospitalID, input.IsLoadCancelReserve)
	if err != nil {
		return nil, err
	}

	return converter.ConvertReservationDetailToGqlModel(result), nil
}

// GetReservationDetailsByConditions is the resolver for the getReservationDetailsByConditions field.
func (r *queryResolver) GetReservationDetailsByConditions(ctx context.Context, input model.GetReservationDetailsByConditions) ([]*model.ReservationDetail, error) {
	sess, err := r.SessionService.GetSessionWithContext(ctx)
	if err != nil {
		return nil, err
	}

	result, err := r.ReservationService.GetReservationDetailsByConditions(ctx, sess.HospitalID, nil, nil, util.NewPtr(true), input)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOカレンダー：一覧閲覧
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoCalenderView,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	return converter.ConvertReservationDetailsToGqlModel(result), nil
}

package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/converter"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/middleware"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/util/session"
	"strconv"
	"time"
)

// OperatorLogin is the resolver for the operatorLogin field.
func (r *mutationResolver) OperatorLogin(ctx context.Context, input *model.OperatorLoginReq) (*model.OperatorLoginRes, error) {
	// リクエストパラメーターのバリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	// 監査ログ：GMOログイン
	auditlog := &model.Auditlog{
		HpID:         input.HospitalID,
		EventCd:      constant.EventCdGmoLogin,
		IsOperator:   constant.StatusTrue,
		OperatorName: input.OperatorName,
	}
	err := r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	// ログイン
	output, err := r.OperatorService.Login(ctx, converter.ConvertToOperatorLoginInput(input))
	if err != nil {
		return nil, err
	}

	// hospitalIDとstaffIDとoperatorNameをCookieに設定
	echoCtx, err := middleware.EchoContextFromContext(ctx)
	if err != nil {
		return nil, err
	}
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorHospitalID, strconv.Itoa(input.HospitalID), constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorStaffID, strconv.Itoa(output.OwnerStaffID), constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorKarteStatus, strconv.Itoa(int(output.KarteStatus)), constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))

	// IdTokenがある時とない時で、Cookieに入れる内容が変わる
	if output.IdToken != nil {
		session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorIdToken, *output.IdToken, time.Until(output.IdTokenExpireTime), session.GetTopLevelDomainFromOrigin(echoCtx))
	} else {
		session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorName, input.OperatorName, constant.DenkaruOperatorValuesTTL, session.GetTopLevelDomainFromOrigin(echoCtx))
	}

	return converter.ConvertToOperatorLoginRes(output), nil
}

// OperatorVerifyMFACode is the resolver for the operatorVerifyMFACode field.
func (r *mutationResolver) OperatorVerifyMFACode(ctx context.Context, input *model.OperatorVerifyMFACodeReq) (*model.OperatorVerifyMFACodeRes, error) {
	// リクエストパラメーターのバリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	// CookieからoperatorNameを取得
	echoCtx, err := middleware.EchoContextFromContext(ctx)
	if err != nil {
		return nil, err
	}
	operatorName, err := session.GetCookie(echoCtx, constant.DenkaruOperatorName)
	if err != nil {
		return nil, err
	}

	// hospitalIDを取得
	hospitalIDStr, err := session.GetCookie(echoCtx, constant.DenkaruOperatorHospitalID)
	if err != nil {
		return nil, err
	}
	hospitalID, err := strconv.Atoi(hospitalIDStr)
	if err != nil {
		return nil, err
	}

	// パラメーターの作成
	param := converter.ConvertToOperatorVerifyMFACodeInput(input)
	param.OperatorName = operatorName

	// MFAコードを検証する
	output, err := r.OperatorService.VerifyMFACode(ctx, hospitalID, param)
	if err != nil {
		return nil, err
	}

	// IdTokenをCookieに設定
	session.SetCookieWithDomain(echoCtx, constant.DenkaruOperatorIdToken, output.IdToken, time.Until(output.IdTokenExpireTime), session.GetTopLevelDomainFromOrigin(echoCtx))

	// operatorNameは不要になったので、削除
	session.ClearCookie(echoCtx, constant.DenkaruOperatorName)

	return converter.ConvertToOperatorVerifyMFACodeRes(output), nil
}

// OperatorChangePassword is the resolver for the operatorChangePassword field.
func (r *mutationResolver) OperatorChangePassword(ctx context.Context, input *model.OperatorChangePasswordReq) (*model.OperatorChangePasswordRes, error) {
	// リクエストパラメーターのバリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	// CookieからoperatorNameを取得
	echoCtx, err := middleware.EchoContextFromContext(ctx)
	if err != nil {
		return nil, err
	}
	operatorName, err := session.GetCookie(echoCtx, constant.DenkaruOperatorName)
	if err != nil {
		return nil, err
	}

	// hospitalIDを取得
	hospitalIDStr, err := session.GetCookie(echoCtx, constant.DenkaruOperatorHospitalID)
	if err != nil {
		return nil, err
	}
	hospitalID, err := strconv.Atoi(hospitalIDStr)
	if err != nil {
		return nil, err
	}

	param := converter.ConvertToOperatorChangePasswordInput(input)
	param.OperatorName = operatorName

	// パスワード変更
	output, err := r.OperatorService.ChangePassword(ctx, hospitalID, param)
	if err != nil {
		return nil, err
	}

	return converter.ConvertToOperatorChangePasswordRes(output), nil
}

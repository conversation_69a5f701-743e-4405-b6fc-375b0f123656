package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util/array"
)

// CreatePortalHospitalStaff is the resolver for the createPortalHospitalStaff field.
func (r *mutationResolver) CreatePortalHospitalStaff(ctx context.Context, input model.CreatePortalHospitalStaffInput) (*model.CreatePortalStaffRes, error) {
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	res, err := r.PortalHospitalStaffService.CreatePortalHospitalStaff(ctx, input)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// EditPortalHospitalStaff is the resolver for the editPortalHospitalStaff field.
func (r *mutationResolver) EditPortalHospitalStaff(ctx context.Context, input model.EditPortalHospitalStaffInput) (bool, error) {
	if err := model.Validate(input); err != nil {
		return false, err
	}

	err := r.PortalHospitalStaffService.EditPortalHospitalStaff(ctx, input)
	if err != nil {
		return false, err
	}

	return true, nil
}

// DeletePortalHospitalStaff is the resolver for the deletePortalHospitalStaff field.
func (r *mutationResolver) DeletePortalHospitalStaff(ctx context.Context, input model.DeletePortalHospitalStaffInput) (bool, error) {
	err := r.PortalHospitalStaffService.DeletePortalHospitalStaff(ctx, input)
	if err != nil {
		return false, err
	}

	return true, nil
}

// SortPortalHospitalStaffs is the resolver for the sortPortalHospitalStaffs field.
func (r *mutationResolver) SortPortalHospitalStaffs(ctx context.Context, input model.SortPortalHospitalStaffsInput) (bool, error) {
	err := r.PortalHospitalStaffService.SortPortalHospitalStaffs(ctx, &input)
	if err != nil {
		return false, err
	}

	return true, nil
}

// GetPortalHospitalStaffs is the resolver for the getPortalHospitalStaffs field.
func (r *queryResolver) GetPortalHospitalStaffs(ctx context.Context) ([]*model.PortalHospitalStaff, error) {
	staffs, err := r.PortalHospitalStaffService.GetPortalHospitalStaffs(ctx)
	if err != nil {
		return nil, err
	}

	gqlStaffs := array.Map(staffs, func(staff *entity.PortalHospitalStaff) *model.PortalHospitalStaff {
		return &model.PortalHospitalStaff{
			HospitalStaffID:  staff.HospitalStaffID,
			Name:             staff.Name,
			Description:      &staff.Description,
			Order:            staff.Order_,
			ExperienceDetail: &staff.ExperienceDetail,
			SpecialistDetail: &staff.SpecialistDetail,
			IsDirector:       staff.IsDirector,
			Files:            nil, // Files will be loaded separately if needed
		}
	})

	return gqlStaffs, nil
}

// GetPortalHospitalStaff is the resolver for the getPortalHospitalStaff field.
func (r *queryResolver) GetPortalHospitalStaff(ctx context.Context, hospitalStaffID int) (*model.GetPortalHospitalStaffRes, error) {
	staff, err := r.PortalHospitalStaffService.GetPortalHospitalStaff(ctx, hospitalStaffID)
	if err != nil {
		return nil, err
	}

	return staff, nil
}

// GetPortalHospitalStaffUploadFileUrls is the resolver for the getPortalHospitalStaffUploadFileUrls field.
func (r *queryResolver) GetPortalHospitalStaffUploadFileUrls(ctx context.Context, hospitalStaffID int, input []*model.GetPortalHospitalStaffUploadFileURLInput) ([]*model.GetPortalHospitalStaffUploadFileURLRes, error) {
	result, err := r.PortalHospitalStaffService.GetPortalHospitalStaffUploadFileUrls(ctx, hospitalStaffID, input)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetPortalStaffFileURL is the resolver for the getPortalStaffFileUrl field.
func (r *queryResolver) GetPortalStaffFileURL(ctx context.Context, input model.GetPortalStaffFileURLInput) (*model.GetPortalStaffFileURLRes, error) {
	url, err := r.PortalHospitalStaffService.GetPortalStaffFileURL(ctx, input)
	if err != nil {
		return nil, err
	}

	return &model.GetPortalStaffFileURLRes{
		URL: *url,
	}, nil
}

package resolver_test

import (
	"context"
	"denkaru-server/pkg/adapter/converter"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/custom"
	mock_service "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_OperatorLogin(t *testing.T) {
	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	testCases := []struct {
		name               string
		input              *model.OperatorLoginReq
		ctx                context.Context
		expectedResFunc1   *custom.OperatorLoginOutput
		expectedErrorFunc1 error
		expectedRes        *model.OperatorLoginRes
		expectedError      error
	}{
		{
			name: "正常系_IdTokenなし",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: &custom.OperatorLoginOutput{
				ChallengeName: "SMS_MFA",
				SessionValue:  util.NewPtr("test_session_value"),
				OwnerStaffID:  staffID1,
				KarteStatus:   constant.KarteStatusInitialMasterDataCreationInProgress,
			},
			expectedRes: &model.OperatorLoginRes{
				ChallengeName: "SMS_MFA",
				SessionValue:  util.NewPtr("test_session_value"),
				PharmacyFlg:   false,
				KarteStatus:   constant.KarteStatusInitialMasterDataCreationInProgress,
			},
		},
		{
			name: "正常系_IdTokenあり",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: &custom.OperatorLoginOutput{
				IdToken:           util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
				IdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
				OwnerStaffID:      staffID1,
				KarteStatus:       constant.KarteStatusInitialMasterDataCreationCompleted,
			},
			expectedRes: &model.OperatorLoginRes{
				ChallengeName: "",
				SessionValue:  nil,
				PharmacyFlg:   false,
				KarteStatus:   constant.KarteStatusInitialMasterDataCreationCompleted,
			},
		},
		{
			name: "正常系_IdTokenなし 薬局",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: &custom.OperatorLoginOutput{
				ChallengeName: "SMS_MFA",
				SessionValue:  util.NewPtr("test_session_value"),
				OwnerStaffID:  staffID1,
				PharmacyFlg:   true,
				KarteStatus:   constant.KarteStatusDemoInUse,
			},
			expectedRes: &model.OperatorLoginRes{
				ChallengeName: "SMS_MFA",
				SessionValue:  util.NewPtr("test_session_value"),
				PharmacyFlg:   true,
				KarteStatus:   constant.KarteStatusDemoInUse,
			},
		},
		{
			name: "正常系_IdTokenあり 薬局",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: &custom.OperatorLoginOutput{
				IdToken:           util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
				IdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
				OwnerStaffID:      staffID1,
				PharmacyFlg:       true,
				KarteStatus:       constant.KarteStatusRealInUse,
			},
			expectedRes: &model.OperatorLoginRes{
				ChallengeName: "",
				SessionValue:  nil,
				PharmacyFlg:   true,
				KarteStatus:   constant.KarteStatusRealInUse,
			},
		},
		{
			name: "異常系_hospitalIDが0",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       0,
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New(""), "HospitalID"),
		},
		{
			name: "異常系_Loginに失敗",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedErrorFunc1: fmt.Errorf("test error"),
			expectedError:      fmt.Errorf("test error"),
		},
		{
			name: "異常系_echo.Contextが無い",
			input: &model.OperatorLoginReq{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			ctx: context.Background(),
			expectedResFunc1: &custom.OperatorLoginOutput{
				ChallengeName: "SMS_MFA",
				SessionValue:  util.NewPtr("test_session_value"),
				OwnerStaffID:  staffID1,
			},
			expectedError: fmt.Errorf("could not retrieve echo.Context"),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			auditlogService := mock_service.NewMockIAuditlogService(ctrl)
			rslv := resolver.Resolver{
				OperatorService: operatorService,
				AuditlogService: auditlogService,
			}

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.WithValue(context.Background(), constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}

			auditlogService.EXPECT().
				AddAuditlog(ctx, gomock.Any()).
				Return(nil).
				AnyTimes()

			operatorService.EXPECT().
				Login(ctx, converter.ConvertToOperatorLoginInput(testCase.input)).
				DoAndReturn(func(_ context.Context, _ *custom.OperatorLoginInput) (*custom.OperatorLoginOutput, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			res, err := rslv.Mutation().OperatorLogin(ctx, testCase.input)
			if err != nil {
				var denkaruExpectedError *myerrors.DenkaruError
				var denkaruActualErrors *myerrors.DenkaruErrors
				if errors.As(testCase.expectedError, &denkaruExpectedError) && errors.As(err, &denkaruActualErrors) {
					assert.Equal(t, denkaruExpectedError.ToGQLError(ctx).Extensions, denkaruActualErrors.ToGQLError(ctx)[0].Extensions)
					return
				}
				assert.Equal(t, testCase.expectedError, err)
			}

			assert.Equal(t, testCase.expectedRes, res)
			for _, cookie := range echoCtx.Response().Header().Values(echo.HeaderSetCookie) {
				switch {
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorHospitalID, testCase.input.HospitalID)):
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorStaffID, testCase.expectedResFunc1.OwnerStaffID)):
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorName, testCase.input.OperatorName)):
				case testCase.expectedResFunc1.IdToken != nil && strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorIdToken, *testCase.expectedResFunc1.IdToken)):
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorKarteStatus, testCase.expectedResFunc1.KarteStatus)):
				default:
					t.Fail()
				}

			}

		})
	}

}

func Test_OperatorVerifyMFACode(t *testing.T) {
	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	testCases := []struct {
		name               string
		operatorName       string
		hospitalID         int
		input              *model.OperatorVerifyMFACodeReq
		ctx                context.Context
		expectedResFunc1   *custom.OperatorVerifyMFACodeOutput
		expectedErrorFunc1 error
		expectedRes        *model.OperatorVerifyMFACodeRes
		expectedError      error
	}{
		{
			name:         "正常系",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName:     "TEST",
				SessionValue:      util.NewPtr("test_session_value_2"),
				IdToken:           "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ",
				IdTokenExpireTime: time.Date(2500, 3, 1, 1, 23, 45, 0, time.UTC),
				PharmacyFlg:       false,
			},
			expectedRes: &model.OperatorVerifyMFACodeRes{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   false,
			},
		},
		{
			name:         "正常系 薬局",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName:     "TEST",
				SessionValue:      util.NewPtr("test_session_value_2"),
				IdToken:           "eyJraWQiOiJkc2poZmF3MGU5ZnB1aS0wd21nbnJpaGdub2llcmpkc2FnZnBzb2RrIiwiYWxnIjoiUlMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QuJbCPBHUdwBvkygvwnS3NmUYRi9tcauk85DOG5NYD8WBGhB2N-3Hz6edfzCI6sV5nU6uBSA83ibVJoiEN-PXiKFDHeaSdp0-hqZocpbCqULz8MIzZY1kZAY323UqJNsmFLML5SACPtkr-GubEat0Pfe7JmkfrimR_WmfEYrtMMU6fYfLAdkSE1QiWbCn3IfcSvmRBBRIYo0ITEv1-lNobxReEUcndzHcJW71BL38_lWi6LxWH-czK7qlz1fh4cNGHYf8tdu4GZNEwuuSGL1XlS4PSk4ZMbzdwrAZxmFdkdXd99t_tdi5o0EV22PavQJZpu9Pjc3BFdorMcfl3bixQ",
				IdTokenExpireTime: time.Date(2500, 3, 1, 1, 23, 45, 0, time.UTC),
				PharmacyFlg:       true,
			},
			expectedRes: &model.OperatorVerifyMFACodeRes{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   true,
			},
		},
		{
			name:         "異常系_PinCodeが5文字",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "01234",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New(""), "PinCode"),
		},
		{
			name:         "異常系_PinCodeが7文字",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "0123456",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New(""), "PinCode"),
		},
		{
			name:         "異常系_echo.Contextが無い",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			ctx:           context.Background(),
			expectedError: fmt.Errorf("could not retrieve echo.Context"),
		},
		{
			name:       "異常系_CookieにDenkaruOperatorNameが無い",
			hospitalID: hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorName, errors.New("http: named cookie not present"))),
		},
		{
			name:         "異常系_CookieにDenkaruOperatorHospitalIDが無い",
			operatorName: "test-user",
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, errors.New("http: named cookie not present"))),
		},
		{
			name:         "異常系_MFA認証失敗",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorVerifyMFACodeReq{
				PinCode:      "012345",
				SessionValue: "test_session_value_1",
			},
			expectedErrorFunc1: errors.New("test"),
			expectedError:      errors.New("test"),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			rslv := resolver.Resolver{
				OperatorService: operatorService,
			}

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			if len(testCase.operatorName) > 0 {
				req.AddCookie(&http.Cookie{Name: constant.DenkaruOperatorName, Value: testCase.operatorName})
			}
			if testCase.hospitalID > 0 {
				req.AddCookie(&http.Cookie{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(testCase.hospitalID)})
			}
			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.WithValue(context.Background(), constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}

			param := converter.ConvertToOperatorVerifyMFACodeInput(testCase.input)
			param.OperatorName = testCase.operatorName
			operatorService.EXPECT().VerifyMFACode(ctx, testCase.hospitalID, param).
				DoAndReturn(func(_ context.Context, _ int, _ *custom.OperatorVerifyMFACodeInput) (*custom.OperatorVerifyMFACodeOutput, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			res, err := rslv.Mutation().OperatorVerifyMFACode(ctx, testCase.input)
			if err != nil {
				var denkaruExpectedError *myerrors.DenkaruError
				var denkaruActualErrors *myerrors.DenkaruErrors
				if errors.As(testCase.expectedError, &denkaruExpectedError) && errors.As(err, &denkaruActualErrors) {
					assert.Equal(t, denkaruExpectedError.ToGQLError(ctx).Extensions, denkaruActualErrors.ToGQLError(ctx)[0].Extensions)
					return
				}
				assert.Equal(t, testCase.expectedError, err)
			}

			assert.Equal(t, testCase.expectedRes, res)
			for _, cookie := range echoCtx.Response().Header().Values(echo.HeaderSetCookie) {
				switch {
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorIdToken, testCase.expectedResFunc1.IdToken)):
				case strings.Contains(cookie, fmt.Sprintf("%v=%v", constant.DenkaruOperatorName, "")):
				default:
					t.Fail()
				}
			}

		})
	}
}

func Test_OperatorChangePassword(t *testing.T) {
	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	testCases := []struct {
		name               string
		operatorName       string
		hospitalID         int
		input              *model.OperatorChangePasswordReq
		ctx                context.Context
		expectedResFunc1   *custom.OperatorChangePasswordOutput
		expectedErrorFunc1 error
		expectedRes        *model.OperatorChangePasswordRes
		expectedError      error
	}{
		{
			name:         "正常系",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "Test123-",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   false,
			},
			expectedRes: &model.OperatorChangePasswordRes{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   false,
			},
		},
		{
			name:         "正常系 薬局",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "Test123-",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   true,
			},
			expectedRes: &model.OperatorChangePasswordRes{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_2"),
				PharmacyFlg:   true,
			},
		},
		{
			name:         "異常系_echo.Contextが無い",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			ctx:           context.Background(),
			expectedError: fmt.Errorf("could not retrieve echo.Context"),
		},
		{
			name: "異常系_CookieにDenkaruOperatorNameが無い",
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorName, errors.New("http: named cookie not present"))),
		},
		{
			name:         "異常系_CookieにDenkaruOperatorHospitalIDが無い",
			operatorName: "test-user",
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedError: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("cookie '%s' not found: %w", constant.DenkaruOperatorHospitalID, errors.New("http: named cookie not present"))),
		},
		{
			name:         "異常系_パスワード変更失敗",
			operatorName: "test-user",
			hospitalID:   hospitalID1,
			input: &model.OperatorChangePasswordReq{
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedErrorFunc1: errors.New("test"),
			expectedError:      errors.New("test"),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorService := mock_service.NewMockIOperatorService(ctrl)
			rslv := resolver.Resolver{
				OperatorService: operatorService,
			}

			// echo.Contextのセットアップ
			e := echo.New()
			req, _ := http.NewRequest(http.MethodGet, "", nil)
			if len(testCase.operatorName) > 0 {
				req.AddCookie(&http.Cookie{Name: constant.DenkaruOperatorName, Value: testCase.operatorName})
			}
			if testCase.hospitalID > 0 {
				req.AddCookie(&http.Cookie{Name: constant.DenkaruOperatorHospitalID, Value: strconv.Itoa(testCase.hospitalID)})
			}

			echoCtx := e.NewContext(req, httptest.NewRecorder())
			ctx := context.WithValue(context.Background(), constant.ContextKeyEcho, echoCtx)
			if testCase.ctx != nil {
				ctx = testCase.ctx
			}

			param := converter.ConvertToOperatorChangePasswordInput(testCase.input)
			param.OperatorName = testCase.operatorName
			operatorService.EXPECT().ChangePassword(ctx, testCase.hospitalID, param).
				DoAndReturn(func(_ context.Context, _ int, _ *custom.OperatorChangePasswordInput) (*custom.OperatorChangePasswordOutput, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			res, err := rslv.Mutation().OperatorChangePassword(ctx, testCase.input)
			if err != nil {
				var denkaruExpectedError *myerrors.DenkaruError
				var denkaruActualErrors *myerrors.DenkaruErrors
				if errors.As(testCase.expectedError, &denkaruExpectedError) && errors.As(err, &denkaruActualErrors) {
					assert.Equal(t, denkaruExpectedError.ToGQLError(ctx).Extensions, denkaruActualErrors.ToGQLError(ctx)[0].Extensions)
					return
				}
				assert.Equal(t, testCase.expectedError, err)
			}
			assert.Equal(t, testCase.expectedRes, res)

		})
	}
}

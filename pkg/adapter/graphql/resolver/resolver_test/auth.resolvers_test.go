package resolver

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	serviceModel "denkaru-server/pkg/service/model"
	mockService "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_mutationResolver_IssueToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	now := time.Now()

	tests := []struct {
		name           string
		input          model.IssueTokenReq
		mockServiceRes *model.IssueTokenRes
		issueTokenErr  error
		want           *model.IssueTokenRes
		wantErr        error
	}{
		{
			name:  "正常系",
			input: model.IssueTokenReq{},
			mockServiceRes: &model.IssueTokenRes{
				Token:                  "aaaaa",
				TokenExpiryTime:        now.Add(time.Hour).Format(time.RFC3339Nano), // 例: 1時間後の時刻を設定
				RefreshToken:           "bbbbb",
				RefreshTokenExpiryTime: now.Add(8 * time.Hour).Format(time.RFC3339Nano), // 例: 8時間後の時刻を設定
			},
			want: &model.IssueTokenRes{
				Token:                  "aaaaa",
				TokenExpiryTime:        now.Add(time.Hour).Format(time.RFC3339Nano), // 例: 1時間後の時刻を設定
				RefreshToken:           "bbbbb",
				RefreshTokenExpiryTime: now.Add(8 * time.Hour).Format(time.RFC3339Nano), // 例: 8時間後の時刻を設定
			},
			wantErr: nil,
		},
		{
			name: "正常系 with KarteStatus",
			input: model.IssueTokenReq{
				KarteStatus: util.NewPtr(1),
			},
			mockServiceRes: &model.IssueTokenRes{
				Token:                  "aaaaa",
				TokenExpiryTime:        now.Add(time.Hour).Format(time.RFC3339Nano),
				RefreshToken:           "bbbbb",
				RefreshTokenExpiryTime: now.Add(8 * time.Hour).Format(time.RFC3339Nano),
			},
			want: &model.IssueTokenRes{
				Token:                  "aaaaa",
				TokenExpiryTime:        now.Add(time.Hour).Format(time.RFC3339Nano),
				RefreshToken:           "bbbbb",
				RefreshTokenExpiryTime: now.Add(8 * time.Hour).Format(time.RFC3339Nano),
			},
			wantErr: nil,
		},
		{
			name:           "IssueToken 失敗",
			input:          model.IssueTokenReq{},
			mockServiceRes: &model.IssueTokenRes{},
			issueTokenErr:  fmt.Errorf("issue token failed"),
			wantErr:        fmt.Errorf("issue token failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authService := mockService.NewMockIAuthService(ctrl)
			rslv := resolver.Resolver{
				AuthService: authService,
			}

			authService.EXPECT().IssueToken(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, sess *serviceModel.Session, _, _ int, _ bool) (token, tokenExpire, refreshToken, refreshTokenExpire string, err error) {
				// Verify KarteStatus is correctly passed from input to session
				if tt.input.KarteStatus != nil {
					assert.NotNil(t, sess.KarteStatus)
					assert.Equal(t, *tt.input.KarteStatus, *sess.KarteStatus)
				}
				return tt.mockServiceRes.Token, tt.mockServiceRes.TokenExpiryTime, tt.mockServiceRes.RefreshToken, tt.mockServiceRes.RefreshTokenExpiryTime, tt.issueTokenErr
			}).AnyTimes()

			got, err := rslv.Mutation().IssueToken(ctx, tt.input)

			if tt.wantErr != nil {
				assert.Equal(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, got.Token, tt.want.Token)
				assert.Equal(t, got.TokenExpiryTime, tt.want.TokenExpiryTime)
				assert.Equal(t, got.RefreshToken, tt.want.RefreshToken)
				assert.Equal(t, got.RefreshTokenExpiryTime, tt.want.RefreshTokenExpiryTime)
			}
		})
	}
}

func Test_mutationResolver_RefreshToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	type args struct {
		input model.RefreshTokenReq
	}
	tests := []struct {
		name                 string
		args                 args
		tokenResponse        string
		refreshTokenResponse string
		refreshSessionErr    error
		want                 *model.RefreshTokenRes
		wantErr              error
	}{
		{
			name: "正常系",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiYmJiYmJiYiIsIm5iZiI6MzMzMzMzMzMzfQ.tJMsOP2Uw7LqmRWvw5Akk5fUZvkHLxTFPMFrouMB66g",
				},
			},
			tokenResponse:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
			refreshTokenResponse: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
			want: &model.RefreshTokenRes{
				Token:                  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
				TokenExpiryTime:        "",
				RefreshToken:           "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
				RefreshTokenExpiryTime: "",
			},
			wantErr: nil,
		},
		{
			name: "正常系 isWebSocket:true",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiYmJiYmJiYiIsIm5iZiI6MzMzMzMzMzMzfQ.tJMsOP2Uw7LqmRWvw5Akk5fUZvkHLxTFPMFrouMB66g",
					IsWebSocket:  util.NewPtr(true),
				},
			},
			tokenResponse:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
			refreshTokenResponse: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
			want: &model.RefreshTokenRes{
				Token:                  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
				TokenExpiryTime:        "",
				RefreshToken:           "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
				RefreshTokenExpiryTime: "",
			},
			wantErr: nil,
		},
		{
			name: "正常系 isWebSocket:false",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiYmJiYmJiYiIsIm5iZiI6MzMzMzMzMzMzfQ.tJMsOP2Uw7LqmRWvw5Akk5fUZvkHLxTFPMFrouMB66g",
					IsWebSocket:  util.NewPtr(false),
				},
			},
			tokenResponse:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
			refreshTokenResponse: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
			want: &model.RefreshTokenRes{
				Token:                  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImhvc3BpdGFsSUQiOjIsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiY2NjY2NjYyIsIm5iZiI6MzMzMzMzMzMzLCJzdGFmZklkIjoxfQ.7MBIDxFVNPPuFGdHUTIB89OeynZSzEqFdVX0VyR5scM",
				TokenExpiryTime:        "",
				RefreshToken:           "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
				RefreshTokenExpiryTime: "",
			},
			wantErr: nil,
		},
		{
			name: "リフレッシュトークン不正",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "fugafuga",
				},
			},
			wantErr: fmt.Errorf("token contains an invalid number of segments"),
		},
		{
			name: "リフレッシュトークンにjtiなし",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwibmJmIjozMzMzMzMzMzN9.T3QCR55lloKniKm87e10-gn89oJd3UPVyUxH4Ebn6To",
				},
			},
			wantErr: fmt.Errorf("invalid refresh token"),
		},
		{
			name: "RefreshSessionエラー",
			args: args{
				input: model.RefreshTokenReq{
					RefreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjExMTExMTExMTEsImlhdCI6MjIyMjIyMjIyMiwianRpIjoiZGRkZGRkZCIsIm5iZiI6MzMzMzMzMzMzfQ.gTviGixJy_W-4ln0bqvvczfWT_yHqp_skr7TfFlOY14",
				},
			},
			refreshSessionErr: fmt.Errorf("refreshSession Error"),
			wantErr:           fmt.Errorf("refreshSession Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authService := mockService.NewMockIAuthService(ctrl)
			rslv := resolver.Resolver{
				AuthService: authService,
			}

			authService.EXPECT().RefreshToken(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string, _, _ int, isWebSocket bool) (token, tokenExpire, refreshToken, refreshTokenExpire string, err error) {
				if tt.args.input.IsWebSocket != nil {
					assert.Equal(t, *tt.args.input.IsWebSocket, isWebSocket)
				} else {
					assert.Equal(t, false, isWebSocket)
				}
				return tt.tokenResponse, "", tt.refreshTokenResponse, "", tt.refreshSessionErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().RefreshToken(ctx, tt.args.input)

			if tt.wantErr == nil {
				assert.NoError(t, gotErr)
				assert.Equal(t, fmt.Sprint(tt.want), fmt.Sprint(gotRes))
			} else {
				assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
			}
		})
	}
}

func Test_mutationResolver_DeleteToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx   context.Context
		token string
	}
	tests := []struct {
		name                          string
		args                          args
		deleteTokenErr                error
		deleteTokenIsSessionRemaining bool
		wantErr                       error
		isSessionRemaining            bool
	}{
		{
			name: "正常系_残セッションあり",
			args: args{
				token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
			},
			deleteTokenErr:                nil,
			deleteTokenIsSessionRemaining: true,
			wantErr:                       nil,
			isSessionRemaining:            true,
		},
		{
			name: "正常系_残セッションなし",
			args: args{
				token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
			},
			deleteTokenErr:                nil,
			deleteTokenIsSessionRemaining: false,
			wantErr:                       nil,
			isSessionRemaining:            false,
		},
		{
			name: "token不正",
			args: args{
				token: "",
			},
			deleteTokenErr: nil,
			wantErr:        fmt.Errorf("JWT token is empty"),
		},
		{
			name: "DeleteToken失敗",
			args: args{
				token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
			},
			deleteTokenErr: fmt.Errorf("delete token failed"),
			wantErr:        fmt.Errorf("delete token failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authService := mockService.NewMockIAuthService(ctrl)
			rslv := resolver.Resolver{
				AuthService: authService,
			}

			authService.EXPECT().DeleteToken(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string, _ *serviceModel.Session) (isSessionRemaining bool, err error) {
				return tt.deleteTokenIsSessionRemaining, tt.deleteTokenErr
			}).AnyTimes()

			got, err := rslv.Mutation().DeleteToken(tt.args.ctx, tt.args.token)

			if tt.wantErr != nil {
				assert.Equal(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, got.IsDeleted, true)
			}
		})
	}
}

func Test_mutationResolver_VerifyToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx   context.Context
		input model.VerifyTokenReq
	}
	tests := []struct {
		name           string
		args           args
		verifyTokenErr error
		wantErr        error
	}{
		{
			name: "正常系",
			args: args{
				input: model.VerifyTokenReq{
					Token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
				},
			},
			verifyTokenErr: nil,
			wantErr:        nil,
		},
		{
			name: "token不正",
			args: args{
				input: model.VerifyTokenReq{
					Token: "",
				},
			},
			verifyTokenErr: nil,
			wantErr:        fmt.Errorf("JWT token is empty"),
		},
		{
			name: "VerifyToken失敗",
			args: args{
				input: model.VerifyTokenReq{
					Token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
				},
			},
			verifyTokenErr: fmt.Errorf("verify token failed"),
			wantErr:        fmt.Errorf("verify token failed"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			authService := mockService.NewMockIAuthService(ctrl)
			rslv := resolver.Resolver{
				AuthService: authService,
			}

			authService.EXPECT().VerifyToken(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (ok bool, err error) {
				return true, tt.verifyTokenErr
			}).AnyTimes()

			got, err := rslv.Mutation().VerifyToken(tt.args.ctx, tt.args.input)

			if tt.wantErr != nil {
				assert.Equal(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, got.IsValid, true)
			}
		})
	}
}

package resolver

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/adapter/middleware"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mockService "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func Test_mutationResolver_Login(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	now := time.Now()
	clientCrtGMOHealthcare := constant.DomainGMOHealthcare + "-1-2222"
	clientCrtGMOHealthtech := constant.DomainGMOHealthtech + "-1-2222"

	mockHospitalID := 2
	mockPharmacyFlg := false

	type args struct {
		ctx        context.Context
		input      model.LoginReq
		clientCaCn string
	}
	type want struct {
		res                    *model.LoginRes
		token                  string
		refreshToken           string
		tokenExpireTime        time.Time
		refreshTokenExpireTime time.Time
	}
	tests := []struct {
		name                        string
		args                        args
		staffInfo                   *entity.UserMst
		hpInf                       *custom.HpInf
		authenticateErr             error
		token                       string
		refreshToken                string
		tokenExpireTime             time.Time
		refreshTokenExpireTime      time.Time
		createSessionErr            error
		addAuditLogErr              error
		registerMailErr             error
		mockHospitalID              *int
		mockPharmacyFlg             *bool
		isClientCertificateValidErr error
		staffPermissionErr          error
		mockEnv                     map[string]string
		want                        want
		wantToken                   string
		wantErr                     error
	}{
		{
			name: "正常系_healthcare",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
				KarteStatus: constant.KarteStatusRealInUse,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			token:                       "aaaaa",
			tokenExpireTime:             now.Add(time.Hour).In(util.JSTLocation), // 例: 1時間後の時刻を設定
			refreshToken:                "bbbbb",
			refreshTokenExpireTime:      now.Add(8 * time.Hour).In(util.JSTLocation), // 例: 8時間後の時刻を設定
			want: want{
				res: &model.LoginRes{
					Success:               constant.StatusTrue,
					IsPasswordInitialized: 0,
					HospitalID:            2,
					PharmacyFlg:           false,
					KarteStatus:           constant.KarteStatusRealInUse,
					StaffInfo: &model.StaffInfo{
						StaffID: 1,
					},
				},
				token:                  "aaaaa",
				tokenExpireTime:        now.Add(time.Hour).In(util.JSTLocation),
				refreshToken:           "bbbbb",
				refreshTokenExpireTime: now.Add(8 * time.Hour).In(util.JSTLocation),
			},
			wantErr: nil,
		},
		{
			name: "正常系_healthtech",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthtech,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
				KarteStatus: constant.KarteStatusNoUse,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			token:                       "aaaaa",
			tokenExpireTime:             now.Add(time.Hour).In(util.JSTLocation), // 例: 1時間後の時刻を設定
			refreshToken:                "bbbbb",
			refreshTokenExpireTime:      now.Add(8 * time.Hour).In(util.JSTLocation), // 例: 8時間後の時刻を設定
			want: want{
				res: &model.LoginRes{
					Success:               constant.StatusTrue,
					IsPasswordInitialized: 0,
					HospitalID:            2,
					PharmacyFlg:           false,
					KarteStatus:           constant.KarteStatusNoUse,
					StaffInfo: &model.StaffInfo{
						StaffID: 1,
					},
				},
				token:                  "aaaaa",
				tokenExpireTime:        now.Add(time.Hour).In(util.JSTLocation),
				refreshToken:           "bbbbb",
				refreshTokenExpireTime: now.Add(8 * time.Hour).In(util.JSTLocation),
			},
			wantErr: nil,
		},
		{
			name: "EchoContext不正",
			args: args{
				ctx: nil,
			},
			wantErr: fmt.Errorf("could not retrieve echo.Context\nno convert context"),
		},
		{
			name: "クライアント証明書不正(フォーマット)",
			args: args{
				ctx:        context.Background(),
				clientCaCn: constant.DomainGMOHealthcare + "_1_2222",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeCertInvalid, fmt.Errorf("invalid client ca common name")),
		},
		{
			name: "クライアント証明書不正(数値)",
			args: args{
				ctx:        context.Background(),
				clientCaCn: constant.DomainGMOHealthcare + "-hogehoge-2222",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeCertInvalid, fmt.Errorf("invalid client ca common name")),
		},
		{
			name: "クライアント証明書不正(DBデータなし)",
			args: args{
				ctx:        context.Background(),
				clientCaCn: clientCrtGMOHealthcare,
			},
			isClientCertificateValidErr: fmt.Errorf("ClientCertificate not found"),
			wantErr:                     fmt.Errorf("ClientCertificate not found"),
		},
		{
			name: "Authenticateエラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			createSessionErr:            fmt.Errorf("createSession Error"),
			authenticateErr:             fmt.Errorf("authenticate Error"),
			wantErr:                     fmt.Errorf("authenticate Error"),
		},
		{
			name: "CreateSessionエラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			createSessionErr:            fmt.Errorf("createSession Error"),
			wantErr:                     fmt.Errorf("createSession Error"),
		},
		{
			name: "AddAuditlogエラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			token:                       "aaaaa",
			tokenExpireTime:             now.Add(time.Hour).In(util.JSTLocation),
			refreshToken:                "bbbbb",
			refreshTokenExpireTime:      now.Add(8 * time.Hour).In(util.JSTLocation),
			addAuditLogErr:              fmt.Errorf("addAuditLog Error"),
			wantErr:                     fmt.Errorf("addAuditLog Error"),
		},
		{
			name: "異常系 環境変数：VULNERABILITY_TEST_LOGIN_USER エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("unexpected end of JSON input")),
		},
		{
			name: "異常系 環境変数：VULNERABILITY_TEST_LOGIN_USER hospitalID 変換 エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "[{\"id\":\"hogehoge\",\"password\":\"fugafuga\",\"hospitalId\":\"id\"}]",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("strconv.Atoi: parsing \"id\": invalid syntax")),
		},
		{
			name: "異常系 GetClientCertificateID取得エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: "",
			},
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "[{\"id\":\"testUser\",\"password\":\"hogehoge\"}]",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeCertInvalid, fmt.Errorf("invalid client ca common name")),
		},
		{
			name: "異常系 一般ユーザー クライアント証明書エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "[{\"id\":\"testUser\",\"password\":\"hogehoge\"}]",
			},
			isClientCertificateValidErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("検索対象のレコードが存在しません。"), "証明書")),
			wantErr:                     errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("検索対象のレコードが存在しません。"), "証明書")),
		},
		{
			name: "異常系 認証エラー ロックなし",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			mockHospitalID: &mockHospitalID,
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "[{\"id\":\"testUser\",\"password\":\"hogehoge\"}]",
			},
			authenticateErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeLoginFailed, fmt.Errorf("password is invalid loginID %v", "hogehoge"))),
			wantErr:         errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeLoginFailed, fmt.Errorf("password is invalid loginID %v", "hogehoge"))),
		},
		{
			name: "異常系 認証エラー ロックあり メール送信失敗",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			mockHospitalID: &mockHospitalID,
			mockEnv: map[string]string{
				"ENV_APP":                       "dev",
				"IS_SKIP_CLIENT_CERT_VERIFY":    "true",
				"VULNERABILITY_TEST_LOGIN_USER": "[{\"id\":\"testUser\",\"password\":\"hogehoge\"}]",
			},
			authenticateErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeAccountLocked, fmt.Errorf("loginID %v is locked", "hogehoge"))),
			registerMailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrMissingWhereClause)),
			wantErr:         errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrMissingWhereClause)),
		},
		{
			name: "異常系 スタッフ権限取得エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			token:                       "aaaaa",
			tokenExpireTime:             now.Add(time.Hour).In(util.JSTLocation), // 例: 1時間後の時刻を設定
			refreshToken:                "bbbbb",
			refreshTokenExpireTime:      now.Add(8 * time.Hour).In(util.JSTLocation), // 例: 8時間後の時刻を設定
			want: want{
				res: &model.LoginRes{
					Success:               constant.StatusTrue,
					IsPasswordInitialized: 0,
					HospitalID:            2,
					PharmacyFlg:           false,
					StaffInfo: &model.StaffInfo{
						StaffID: 1,
					},
				},
				token:                  "aaaaa",
				tokenExpireTime:        now.Add(time.Hour).In(util.JSTLocation),
				refreshToken:           "bbbbb",
				refreshTokenExpireTime: now.Add(8 * time.Hour).In(util.JSTLocation),
			},
			staffPermissionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidField)),
			wantErr:            errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidField)),
		},
		{
			name: "異常系 ログインのお知らせメール送信エラー",
			args: args{
				ctx: context.Background(),
				input: model.LoginReq{
					LoginID:  "hogehoge",
					Password: "fugafuga",
				},
				clientCaCn: clientCrtGMOHealthcare,
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 2,
			},
			hpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			mockHospitalID:              &mockHospitalID,
			mockPharmacyFlg:             &mockPharmacyFlg,
			isClientCertificateValidErr: nil,
			token:                       "aaaaa",
			tokenExpireTime:             now.Add(time.Hour).In(util.JSTLocation), // 例: 1時間後の時刻を設定
			refreshToken:                "bbbbb",
			refreshTokenExpireTime:      now.Add(8 * time.Hour).In(util.JSTLocation), // 例: 8時間後の時刻を設定
			want: want{
				res: &model.LoginRes{
					Success:               constant.StatusTrue,
					IsPasswordInitialized: 0,
					HospitalID:            2,
					PharmacyFlg:           false,
					StaffInfo: &model.StaffInfo{
						StaffID: 1,
					},
				},
				token:                  "aaaaa",
				tokenExpireTime:        now.Add(time.Hour).In(util.JSTLocation),
				refreshToken:           "bbbbb",
				refreshTokenExpireTime: now.Add(8 * time.Hour).In(util.JSTLocation),
			},
			registerMailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidValueOfLength)),
			wantErr:         errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidValueOfLength)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for k, v := range tt.mockEnv {
				_ = os.Setenv(k, v)
			}
			defer func() {
				for k := range tt.mockEnv {
					_ = os.Setenv(k, "")
				}
			}()

			staffService := mockService.NewMockIStaffService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			clientCertificateService := mockService.NewMockIClientCertificateService(ctrl)
			rslv := resolver.Resolver{
				StaffService:             staffService,
				SessionService:           sessionService,
				AuditlogService:          auditlogService,
				MailService:              mailService,
				ClientCertificateService: clientCertificateService,
			}

			staffService.EXPECT().Authenticate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ int, _, _ string) (staff *entity.UserMst, hpInf *custom.HpInf, err error) {
				return tt.staffInfo, tt.hpInf, tt.authenticateErr
			}).AnyTimes()
			sessionService.EXPECT().CreateSession(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *serviceModel.Session) (token, refreshToken string, tokenExpire, refreshTokenExpire time.Time, err error) {
				return tt.token, tt.refreshToken, tt.tokenExpireTime, tt.refreshTokenExpireTime, tt.createSessionErr // refreshTokenExpireTimeを使用
			}).AnyTimes()
			staffService.EXPECT().GetStaffPermission(gomock.Any(), gomock.Any()).DoAndReturn(func(_ int, _ int) ([]entity.UserPermission, error) {
				return []entity.UserPermission{}, tt.staffPermissionErr
			}).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.Auditlog) (err error) {
				return tt.addAuditLogErr
			}).AnyTimes()
			mailService.EXPECT().RegisterMail(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.SendMailReq, _ bool) (err error) {
				return tt.registerMailErr
			}).AnyTimes()
			clientCertificateService.EXPECT().IsClientCertificateValid(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ int) (*int, error) {
				return tt.mockHospitalID, tt.isClientCertificateValidErr
			}).AnyTimes()

			e := echo.New()
			req, err := http.NewRequest(http.MethodGet, "/", nil)
			if err != nil {
				t.Fatal("Failed to create request:", err)
			}
			req.Header.Set(constant.ClientCaCnHeader, tt.args.clientCaCn)
			rec := httptest.NewRecorder()
			ec := e.NewContext(req, rec)

			ctx := context.Background()
			if tt.args.ctx != nil {
				ctx = context.WithValue(tt.args.ctx, constant.ContextKeyEcho, ec)
			}

			gotRes, gotErr := rslv.Mutation().Login(ctx, tt.args.input)

			if tt.wantErr == nil {
				assert.NoError(t, gotErr)
				assert.Equal(t, fmt.Sprint(tt.want.res.Success), fmt.Sprint(gotRes.Success))
				assert.Equal(t, fmt.Sprint(tt.want.res.IsPasswordInitialized), fmt.Sprint(gotRes.IsPasswordInitialized))
				assert.Equal(t, fmt.Sprint(tt.want.res.StaffInfo.StaffID), fmt.Sprint(gotRes.StaffInfo.StaffID))
				assert.Equal(t, fmt.Sprint(tt.want.res.HospitalID), fmt.Sprint(gotRes.HospitalID))
				assert.Equal(t, fmt.Sprint(tt.want.res.PharmacyFlg), fmt.Sprint(gotRes.PharmacyFlg))

				_, err = middleware.EchoContextFromContext(ctx)
				t.Log(err)
				cookies := rec.Result().Cookies()

				for _, c := range cookies {
					switch c.Name {
					case constant.DenkaruAccessToken:
						assert.Equal(t, tt.want.token, c.Value)
						assert.Less(t, tt.want.tokenExpireTime.Sub(c.Expires), time.Second) // 誤差1秒以内
					case constant.DenkaruRefreshToken:
						assert.Equal(t, tt.want.refreshToken, c.Value)
						assert.Less(t, tt.want.refreshTokenExpireTime.Sub(c.Expires), time.Second) // 誤差1秒以内
					default:
						t.Log(c.Value)
					}
				}
			} else {
				assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
			}
		})
	}
}

func Test_mutationResolver_Logout(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name                            string
		args                            args
		cookieValue                     string
		deleteSessionErr                error
		deleteSessionIsSessionRemaining bool
		addAuditLogErr                  error
		want                            *model.LogoutRes
		wantErrContains                 string
		isSessionRemaining              bool
	}{
		{
			name: "正常系_残セッションあり",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:                     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg", // 有効なJWTトークンをシミュレート
			deleteSessionIsSessionRemaining: true,
			want: &model.LogoutRes{
				Success:            constant.StatusTrue,
				IsSessionRemaining: true,
			},
		},
		{
			name: "正常系_残セッションなし",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:                     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg", // 有効なJWTトークンをシミュレート
			deleteSessionIsSessionRemaining: false,
			want: &model.LogoutRes{
				Success:            constant.StatusTrue,
				IsSessionRemaining: false,
			},
		},
		{
			name: "EchoContext不正",
			args: args{
				ctx: nil,
			},
			wantErrContains: "could not retrieve echo.Context",
		},
		{
			name: "cookieからJWTトークン取得失敗",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:     "",
			wantErrContains: "could not retrieve jwt token",
		},
		{
			name: "JWTトークン不正",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:     "invaild_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg", // 無効なJWTトークンをシミュレート
			wantErrContains: "could not extract session from token",
		},
		{
			name: "DeleteSessionエラー",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
			deleteSessionErr: fmt.Errorf("deleteSession Error"),
			wantErrContains:  "could not delete session",
		},
		{
			name: "AddAuditlogエラー",
			args: args{
				ctx: context.Background(),
			},
			cookieValue:     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk3MDI4Njk0ODQsImhvc3BpdGFsSUQiOjIsImlhdCI6MTcwMjg2NTg4NCwianRpIjoiM2RiOGU4OTgtNWZhOC00NmVmLWE3MDMtYmNlMDU5Mzk3OTViIiwibmJmIjoxNzAyODY1ODg0LCJzdGFmZklkIjoxfQ.I2S1iWVCHRAiZMiF7u3sDa_L034TWlhK5AgwVS1ryyg",
			addAuditLogErr:  fmt.Errorf("addAuditLog Error"),
			wantErrContains: "could not add audit log",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionService := mockService.NewMockISessionService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			rslv := resolver.Resolver{
				SessionService:  sessionService,
				AuditlogService: auditlogService,
			}

			e := echo.New()
			req := httptest.NewRequest(http.MethodPost, "/", nil)
			rec := httptest.NewRecorder()

			if tt.cookieValue != "" {
				req.AddCookie(&http.Cookie{
					Name:  constant.DenkaruAccessToken,
					Value: tt.cookieValue,
				})
			}

			ec := e.NewContext(req, rec)
			ctx := context.Background()
			if tt.args.ctx != nil {
				ctx = context.WithValue(tt.args.ctx, constant.ContextKeyEcho, ec)
			}

			sessionService.EXPECT().DeleteSession(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (isSessionRemaining bool, err error) {
				return tt.deleteSessionIsSessionRemaining, tt.deleteSessionErr
			}).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.Auditlog) (err error) {
				return tt.addAuditLogErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().Logout(ctx)

			if tt.wantErrContains != "" {
				assert.ErrorContains(t, gotErr, tt.wantErrContains)
			} else {
				assert.NoError(t, gotErr)
				assert.Equal(t, tt.want, gotRes)
			}
		})
	}
}

func Test_mutationQuery_GetStaffListWithTaskCount(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1)}
	ctx := test_mock.GetTestContextWithSession(s)

	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name             string
		args             args
		cookieValue      string
		mockHospitalID   *int
		staffList        []*custom.Staff
		staffListTaskCnt int
		getStaffListErr  error
		want             *model.GetStaffListWithTaskCountRes
		wantErr          error
	}{
		{
			name: "正常系 スタッフ一覧取得",
			args: args{
				ctx: ctx,
			},
			staffListTaskCnt: 1,
			staffList: []*custom.Staff{
				{
					StaffID:   1,
					StaffName: "スタッフ",
					TaskCount: 1,
				},
				{
					StaffID:   2,
					StaffName: "スタッフ2",
					TaskCount: 4,
				},
			},
			want: &model.GetStaffListWithTaskCountRes{
				Staffs: []*model.StaffWithTaskCount{
					{
						StaffID:   1,
						StaffName: "スタッフ",
						TaskCount: 1,
					},
					{
						StaffID:   2,
						StaffName: "スタッフ2",
						TaskCount: 4,
					},
				},
				TotalTaskCount: 5,
			},
		},
		{
			name: "異常系 セッション取得 error",
			args: args{
				ctx: test_mock.GetTestContextWithSession(nil),
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系 hospitalID is nil",
			args: args{
				ctx: test_mock.GetTestContextWithSession(&serviceModel.Session{}),
			},
			getStaffListErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("hospital is nil")),
			wantErr:         myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("hospital is nil")),
		},
		{
			name: "異常系 GetStaffListWithTaskCount error",
			args: args{
				ctx: ctx,
			},
			getStaffListErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidDB)),
			wantErr:         errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrInvalidDB)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				StaffService: staffService,
			}

			staffService.EXPECT().GetStaffListWithTaskCount(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, _ int) ([]*custom.Staff, int, error) {
				return tt.staffList, tt.staffListTaskCnt, tt.getStaffListErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Query().GetStaffListWithTaskCount(tt.args.ctx)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(gotErr, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				} else {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				}

			} else {
				for i, v := range tt.want.Staffs {
					assert.Equal(t, v.StaffID, gotRes.Staffs[i].StaffID)
				}
			}
		})
	}
}

func Test_mutationQuery_GetStaffList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1)}
	ctx := test_mock.GetTestContextWithSession(s)

	type args struct {
		ctx   context.Context
		input *model.GetStaffListReq
	}
	tests := []struct {
		name        string
		args        args
		cookieValue string

		staffList       *custom.StaffList
		getStaffListErr error
		want            *model.GetStaffListRes
		wantErr         error
	}{
		{
			name: "正常系 スタッフ一覧取得",
			args: args{
				ctx:   ctx,
				input: &model.GetStaffListReq{Limit: util.NewPtr(1), Cursor: util.NewPtr(1), SearchName: util.NewPtr("aaaa")},
			},
			staffList: &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}},
			want:      &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
		{
			name: "正常系 スタッフ一覧取得エラー",
			args: args{
				ctx:   ctx,
				input: &model.GetStaffListReq{Limit: util.NewPtr(1), Cursor: util.NewPtr(1), SearchName: util.NewPtr("aaaa")},
			},
			staffList:       &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}},
			getStaffListErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("find staff list error")),
			wantErr:         myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("find staff list error")),
			want:            &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
		{
			name: "正常系 GetStaffListReq is nil",
			args: args{
				ctx:   ctx,
				input: nil,
			},
			staffList: &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}},
			want:      &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
		{
			name: "正常系 validation error",
			args: args{
				ctx:   ctx,
				input: &model.GetStaffListReq{Limit: util.NewPtr(0), Cursor: util.NewPtr(1), SearchName: util.NewPtr("aaaa")},
			},
			staffList: &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}},
			want:      nil,
			wantErr:   myerrors.NewDenkaruErrors(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("Key: 'GetStaffListReq.Limit' Error:Field validation for 'Limit' failed on the 'min' tag"), "Limit")),
		},
		{
			name: "異常系 セッション取得 error",
			args: args{
				ctx:   test_mock.GetTestContextWithSession(nil),
				input: nil,
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				StaffService: staffService,
			}

			staffService.EXPECT().GetStaffList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ int, _ int, _ *int, _ string) (*custom.StaffList, error) {
				return tt.staffList, tt.getStaffListErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Query().GetStaffList(tt.args.ctx, tt.args.input)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(gotErr, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				} else {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				}

			} else {
				for i, v := range tt.want.Staffs {
					assert.Equal(t, v.StaffID, gotRes.Staffs[i].StaffID)
				}
			}
		})
	}
}

func Test_mutationQuery_GetStaffListSetting(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s := &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1)}
	ctx := test_mock.GetTestContextWithSession(s)

	type args struct {
		ctx   context.Context
		input *model.GetStaffListReq
	}
	tests := []struct {
		name        string
		args        args
		cookieValue string

		staffList       *custom.StaffList
		getStaffListErr error
		want            *model.GetStaffListRes
		wantErr         error
	}{
		{
			name: "正常系 スタッフ一覧取得",
			args: args{
				ctx:   ctx,
				input: &model.GetStaffListReq{Limit: util.NewPtr(1), Cursor: util.NewPtr(1), SearchName: util.NewPtr("aaaa")},
			},
			staffList: &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}},
			want:      &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				StaffService: staffService,
			}

			staffService.EXPECT().GetStaffList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ int, _ int, _ *int, _ string) (*custom.StaffList, error) {
				return tt.staffList, tt.getStaffListErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Query().GetStaffListSetting(tt.args.ctx, tt.args.input)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(gotErr, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				} else {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				}

			} else {
				for i, v := range tt.want.Staffs {
					assert.Equal(t, v.StaffID, gotRes.Staffs[i].StaffID)
				}
			}
		})
	}
}

func Test_mutationQuery_CreateStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess  *serviceModel.Session
		input model.CreateStaffReq
	}
	tests := []struct {
		name              string
		args              args
		staffMstRes       *entity.UserMst
		password          string
		createStaffErr    error
		changePasswordErr error
		auditlogErr       error
		wantRes           *model.CreateStaffRes
		wantErr           error
	}{
		{
			name: "正常系",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.CreateStaffReq{
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					LoginID:    "Tanaka01",
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			staffMstRes: &entity.UserMst{
				ID:               99,
				Name:             "田中一郎",
				KanaName:         "タナカイチロウ",
				JobCd:            constant.JobCodePharmacistOffice,
				ManagerKbn:       constant.ManagerKbnAdmin,
				LoginID:          "Tanaka01",
				MedicalLicenseNo: "L01234",
				MayakuLicenseNo:  "MO12345",
			},
			password: "password12345",
			wantRes: &model.CreateStaffRes{
				StaffID:          99,
				StaffName:        "田中一郎",
				StaffKana:        "タナカイチロウ",
				StaffType:        constant.JobCodePharmacistOffice,
				ManagerKbn:       constant.ManagerKbnAdmin,
				LoginID:          "Tanaka01",
				Password:         "password12345",
				MedicalLicenseNo: util.NewPtr("L01234"),
				MayakuLicenseNo:  util.NewPtr("MO12345"),
				Permissions: []*model.StaffPermission{
					{
						FunctionCd: "1",
						Permission: constant.PermissionWrite,
					},
				},
			},
		},
		{
			name: "異常系、セッションエラー",
			args: args{
				sess:  nil,
				input: model.CreateStaffReq{},
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、スタッフ情報バリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.CreateStaffReq{
					StaffName:  "",
					StaffKana:  "",
					StaffType:  0,
					ManagerKbn: 1,
					LoginID:    "あ",
				},
			},
			wantErr: fmt.Errorf("[" +
				"{Code:INVALID_PARAMETER,UserMessage:StaffNameを正しく入力してください,ErrMessage:Key: 'CreateStaffReq.StaffName' Error:Field validation for 'StaffName' failed on the 'min' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:StaffKanaを正しく入力してください,ErrMessage:Key: 'CreateStaffReq.StaffKana' Error:Field validation for 'StaffKana' failed on the 'kanaInput' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:StaffTypeを正しく入力してください,ErrMessage:Key: 'CreateStaffReq.StaffType' Error:Field validation for 'StaffType' failed on the 'eq=1|eq=13|eq=14|eq=15' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:ManagerKbnを正しく入力してください,ErrMessage:Key: 'CreateStaffReq.ManagerKbn' Error:Field validation for 'ManagerKbn' failed on the 'eq=0|eq=7' tag}" +
				"{Code:INVALID_LOGIN_ID,UserMessage:ログインIDには英数字を使用してください,ErrMessage:Key: 'CreateStaffReq.LoginID' Error:Field validation for 'LoginID' failed on the 'loginID' tag}" +
				"]"),
		},
		{
			name: "異常系、Permissionバリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.CreateStaffReq{
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					LoginID:    "Tanaka01",
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionDisable,
						},
					},
				},
			},
			wantErr: fmt.Errorf("[{Code:INVALID_PARAMETER,UserMessage:Permissionを正しく入力してください,ErrMessage:Key: 'StaffPermissionInput.Permission' Error:Field validation for 'Permission' failed on the 'eq' tag}]"),
		},
		{
			name: "異常系、StaffTypeバリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(true)},
				input: model.CreateStaffReq{
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					LoginID:    "Tanaka01",
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			wantErr: fmt.Errorf("Code:INVALID_PARAMETER,UserMessage:StaffTypeを正しく入力してください,ErrMessage:薬局服薬指導システムのStaffTypeは薬剤師(本店),薬剤師(在宅)のみ"),
		},
		{
			name: "異常系、CreateStaffエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.CreateStaffReq{
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					LoginID:    "Tanaka01",
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			createStaffErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodePermissionInvalidFunctionCd, fmt.Errorf("include invalid functionCd"))),
			wantErr:        errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodePermissionInvalidFunctionCd, fmt.Errorf("include invalid functionCd"))),
		},
		{
			name: "異常系、AddAuditlogエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.CreateStaffReq{
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					LoginID:    "Tanaka01",
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			auditlogErr: fmt.Errorf("AddAuditlog Error"),
			wantErr:     fmt.Errorf("Failed to add audit log.: AddAuditlog Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)

			rslv := resolver.Resolver{
				StaffService:    staffService,
				AuditlogService: auditlogService,
			}

			staffService.EXPECT().CreateStaff(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *entity.UserMst, _ []*entity.UserPermission) (*entity.UserMst, string, error) {
				return tt.staffMstRes, tt.password, tt.createStaffErr
			}).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.Auditlog) error {
				return tt.auditlogErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().CreateStaff(ctx, tt.args.input)
			if gotErr != nil {
				assert.Nil(t, gotRes)
				assert.EqualError(t, gotErr, tt.wantErr.Error())
				return
			}
			assert.NoError(t, gotErr)
			assert.NotNil(t, gotRes)
			assert.Equal(t, tt.wantRes, gotRes)
		})
	}
}

func Test_mutationQuery_UpdateStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess  *serviceModel.Session
		input model.UpdateStaffReq
	}
	tests := []struct {
		name              string
		args              args
		staffMstRes       *entity.UserMst
		password          string
		createStaffErr    error
		changePasswordErr error
		auditlogErr       error
		wantRes           *model.UpdateStaffRes
		wantErr           error
	}{
		{
			name: "正常系",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			staffMstRes: &entity.UserMst{
				ID:               99,
				Name:             "田中一郎",
				KanaName:         "タナカイチロウ",
				JobCd:            constant.JobCodePharmacistOffice,
				ManagerKbn:       constant.ManagerKbnAdmin,
				LoginID:          "Tanaka01",
				MedicalLicenseNo: "L01234",
				MayakuLicenseNo:  "MO12345",
			},
			password: "password12345",
			wantRes: &model.UpdateStaffRes{
				StaffID:          99,
				StaffName:        "田中一郎",
				StaffKana:        "タナカイチロウ",
				StaffType:        constant.JobCodePharmacistOffice,
				ManagerKbn:       constant.ManagerKbnAdmin,
				LoginID:          "Tanaka01",
				Password:         "password12345",
				MedicalLicenseNo: util.NewPtr("L01234"),
				MayakuLicenseNo:  util.NewPtr("MO12345"),
				Permissions: []*model.StaffPermission{
					{
						FunctionCd: "1",
						Permission: constant.PermissionWrite,
					},
				},
			},
		},
		{
			name: "異常系、セッションエラー",
			args: args{
				sess:  nil,
				input: model.UpdateStaffReq{},
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、スタッフ情報バリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "",
					StaffKana:  "",
					StaffType:  0,
					ManagerKbn: 1,
				},
			},
			wantErr: fmt.Errorf("[" +
				"{Code:INVALID_PARAMETER,UserMessage:氏名を正しく入力してください,ErrMessage:Key: 'UpdateStaffReq.氏名' Error:Field validation for '氏名' failed on the 'min' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:フリガナを正しく入力してください,ErrMessage:Key: 'UpdateStaffReq.フリガナ' Error:Field validation for 'フリガナ' failed on the 'kanaInput' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:スタッフ種別を正しく入力してください,ErrMessage:Key: 'UpdateStaffReq.スタッフ種別' Error:Field validation for 'スタッフ種別' failed on the 'eq=1|eq=13|eq=14|eq=15' tag}" +
				"{Code:INVALID_PARAMETER,UserMessage:権限区分を正しく入力してください,ErrMessage:Key: 'UpdateStaffReq.権限区分' Error:Field validation for '権限区分' failed on the 'eq=0|eq=7|eq=10' tag}" +
				"]"),
		},
		{
			name: "異常系、Permissionバリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionDisable,
						},
					},
				},
			},
			wantErr: fmt.Errorf("[{Code:INVALID_PARAMETER,UserMessage:Permissionを正しく入力してください,ErrMessage:Key: 'StaffPermissionInput.Permission' Error:Field validation for 'Permission' failed on the 'eq' tag}]"),
		},
		{
			name: "異常系、StaffTypeバリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(true)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			wantErr: fmt.Errorf("Code:INVALID_PARAMETER,UserMessage:StaffTypeを正しく入力してください,ErrMessage:薬局服薬指導システムのStaffTypeは薬剤師(本店),薬剤師(在宅)のみ"),
		},
		{
			name: "異常系、自ID無効化エラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    1,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
					Status: 0,
				},
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeStaffNotAlloweStatus, fmt.Errorf("cannot change status self staffID: %v ", 1)),
		},
		{
			name: "異常系、CreateStaffエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
				},
			},
			createStaffErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodePermissionInvalidFunctionCd, fmt.Errorf("include invalid functionCd"))),
			wantErr:        errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodePermissionInvalidFunctionCd, fmt.Errorf("include invalid functionCd"))),
		},
		{
			name: "異常系、AddAuditlogエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.UpdateStaffReq{
					StaffID:    2,
					StaffName:  "田中一郎",
					StaffKana:  "タナカイチロウ",
					StaffType:  constant.JobCodeDoctor,
					ManagerKbn: constant.ManagerKbnAdmin,
					Permissions: []*model.StaffPermissionInput{
						{
							FunctionCd: "1",
							Permission: constant.PermissionWrite,
						},
					},
					PasswordReset: 1,
				},
			},
			auditlogErr: fmt.Errorf("AddAuditlog Error"),
			wantErr:     fmt.Errorf("Failed to add audit log.: AddAuditlog Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)

			rslv := resolver.Resolver{
				StaffService:    staffService,
				AuditlogService: auditlogService,
			}

			staffService.EXPECT().UpdateStaff(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *entity.UserMst, _ []*entity.UserPermission) (*entity.UserMst, string, error) {
				return tt.staffMstRes, tt.password, tt.createStaffErr
			}).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.Auditlog) error {
				return tt.auditlogErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().UpdateStaff(ctx, tt.args.input)
			if gotErr != nil {
				assert.Nil(t, gotRes)
				assert.EqualError(t, gotErr, tt.wantErr.Error())
				return
			}
			assert.NoError(t, gotErr)
			assert.NotNil(t, gotRes)
			assert.Equal(t, tt.wantRes, gotRes)
		})
	}
}

func Test_mutationQuery_ChangePassword(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess  *serviceModel.Session
		input model.ChangePasswordReq
	}
	tests := []struct {
		name string
		args args

		changePasswordErr error
		auditlogErr       error
		mailErr           error
		wantErr           error
	}{
		{
			name: "正常系",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new12345",
				},
			},
		},
		{
			name: "異常系、セッションエラー",
			args: args{
				sess: nil,
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new123",
				},
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、バリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new123",
				},
			},
			wantErr: fmt.Errorf("[{Code:INVALID_PARAMETER,UserMessage:NewPasswordを正しく入力してください,ErrMessage:Key: 'ChangePasswordReq.NewPassword' Error:Field validation for 'NewPassword' failed on the 'min' tag}]"),
		},
		{
			name: "異常系、ChangePasswordエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new12345",
				},
			},
			changePasswordErr: fmt.Errorf("ChangePassword Error"),
			wantErr:           fmt.Errorf("ChangePassword Error"),
		},
		{
			name: "異常系、AddAuditlogエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new12345",
				},
			},
			auditlogErr: fmt.Errorf("AddAuditlog Error"),
			wantErr:     fmt.Errorf("Failed to add audit log.: AddAuditlog Error"),
		},
		{
			name: "異常系、ChangePasswordエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.ChangePasswordReq{
					OldPassword: "old12345",
					NewPassword: "new12345",
				},
			},
			mailErr: fmt.Errorf("RegisterMail Error"),
			wantErr: fmt.Errorf("RegisterMail Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)

			rslv := resolver.Resolver{
				StaffService:    staffService,
				AuditlogService: auditlogService,
				MailService:     mailService,
			}

			staffService.EXPECT().ChangePassword(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ int, _, _ string) error {
				return tt.changePasswordErr
			}).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.Auditlog) error {
				return tt.auditlogErr
			}).AnyTimes()
			mailService.EXPECT().RegisterMail(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.SendMailReq, _ bool) error {
				return tt.mailErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().ChangePassword(ctx, tt.args.input)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
			} else {
				assert.NoError(t, gotErr)
				assert.Equal(t, gotRes.Success, 1)
			}
		})
	}
}

func Test_mutationQuery_LoginInitial(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess  *serviceModel.Session
		input model.LoginInitialReq
	}
	tests := []struct {
		name string
		args args

		loginInitialErr error
		mailErr         error
		wantErr         error
	}{
		{
			name: "正常系",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.LoginInitialReq{
					NewLoginID:  "login1234",
					NewPassword: "new12345",
				},
			},
		},
		{
			name: "異常系、セッションエラー",
			args: args{
				sess: nil,
				input: model.LoginInitialReq{
					NewLoginID:  "login1234",
					NewPassword: "new123",
				},
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、バリデーションエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.LoginInitialReq{
					NewLoginID:  "login1234",
					NewPassword: "1",
				},
			},
			wantErr: fmt.Errorf("[{Code:INVALID_PARAMETER,UserMessage:NewPasswordを正しく入力してください,ErrMessage:Key: 'LoginInitialReq.NewPassword' Error:Field validation for 'NewPassword' failed on the 'min' tag}]"),
		},
		{
			name: "異常系、LoginInitialエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.LoginInitialReq{
					NewLoginID:  "login1234",
					NewPassword: "new12345",
				},
			},
			loginInitialErr: fmt.Errorf("LoginInitial Error"),
			wantErr:         fmt.Errorf("LoginInitial Error"),
		},
		{
			name: "異常系、RegisterMailエラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
				input: model.LoginInitialReq{
					NewLoginID:  "login1234",
					NewPassword: "new12345",
				},
			},
			mailErr: fmt.Errorf("RegisterMail Error"),
			wantErr: fmt.Errorf("RegisterMail Error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)

			rslv := resolver.Resolver{
				StaffService: staffService,
				MailService:  mailService,
			}

			staffService.EXPECT().LoginInitial(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ int, _, _ string) error {
				return tt.loginInitialErr
			}).AnyTimes()
			mailService.EXPECT().RegisterMail(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.SendMailReq, _ bool) error {
				return tt.mailErr
			}).AnyTimes()

			gotRes, gotErr := rslv.Mutation().LoginInitial(ctx, tt.args.input)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
			} else {
				assert.NoError(t, gotErr)
				assert.Equal(t, gotRes.Success, 1)
			}
		})
	}
}

func Test_mutationQuery_GetDoctorsInHospital(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess *serviceModel.Session
	}
	tests := []struct {
		name        string
		args        args
		cookieValue string

		staffList            []*entity.UserMst
		getDoctorsInHospital error
		want                 *model.GetStaffListRes
		wantErr              error
	}{
		{
			name: "正常系 スタッフ一覧取得",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
			},
			staffList: []*entity.UserMst{{ID: 1}},
			want:      &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
		{
			name: "正常系 スタッフ一覧取得エラー",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1), PharmacyFlg: util.NewPtr(false)},
			},
			staffList:            []*entity.UserMst{{ID: 1}},
			getDoctorsInHospital: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("find staff list error")),
			wantErr:              myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("find staff list error")),
			want:                 &model.GetStaffListRes{Staffs: []*model.StaffInfo{{StaffID: 1}}},
		},
		{
			name: "異常系、セッションエラー",
			args: args{
				sess: nil,
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				StaffService: staffService,
			}

			staffService.EXPECT().GetDoctorsInHospital(gomock.Any()).DoAndReturn(func(_ int) ([]*entity.UserMst, error) {
				return tt.staffList, tt.getDoctorsInHospital
			}).AnyTimes()

			gotRes, gotErr := rslv.Query().GetDoctorsInHospital(ctx)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(gotErr, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				} else {
					assert.Equal(t, tt.wantErr.Error(), gotErr.Error())
				}

			} else {
				for i, v := range tt.want.Staffs {
					assert.Equal(t, v.StaffID, gotRes[i].StaffID)
				}
			}
		})
	}
}

func Test_mutationQuery_GetSessionInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		sess *serviceModel.Session
	}
	tests := []struct {
		name                     string
		args                     args
		staffInfo                *entity.UserMst
		permissions              []*entity.UserPermission
		checkStaffInfoErr        error
		hospitalStatusCheckErr   error
		hospitalStatusCheckHpInf *custom.HpInf
		want                     *model.LoginRes
		wantErr                  error
	}{
		{
			name: "正常系",
			args: args{
				sess: &serviceModel.Session{HospitalID: util.NewPtr(1), StaffID: util.NewPtr(1)},
			},
			staffInfo: &entity.UserMst{
				ID:   1,
				HpID: 1,
				Name: "Test Staff",
			},
			permissions: []*entity.UserPermission{},
			hospitalStatusCheckHpInf: &custom.HpInf{
				PharmacyFlg: false,
			},
			want: &model.LoginRes{
				Success:               constant.StatusTrue,
				IsLoginIDInitialized:  0,
				IsPasswordInitialized: 0,
				HospitalID:            1,
				StaffInfo: &model.StaffInfo{
					StaffID:     1,
					StaffName:   "Test Staff",
					StaffKana:   "",
					StaffType:   0,
					ManagerKbn:  0,
					Email:       nil,
					LoginID:     "",
					Status:      0,
					Permissions: []*model.StaffPermission{},
				},
				PharmacyFlg: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.args.sess)

			staffService := mockService.NewMockIStaffService(ctrl)
			hospitalStatusCheckService := mockService.NewMockIHospitalStatusCheckService(ctrl)

			rslv := resolver.Resolver{
				StaffService:               staffService,
				HospitalStatusCheckService: hospitalStatusCheckService,
			}

			staffService.EXPECT().CheckStaffInfo(gomock.Any()).Return(tt.staffInfo, tt.permissions, tt.checkStaffInfoErr).AnyTimes()
			hospitalStatusCheckService.EXPECT().ClinicStatusCheck(gomock.Any(), gomock.Any()).Return(true, tt.hospitalStatusCheckHpInf, tt.hospitalStatusCheckErr).AnyTimes()

			gotRes, gotErr := rslv.Query().GetSessionInfo(ctx)
			if tt.wantErr != nil {
				assert.NotEmpty(t, gotErr)
			} else {
				assert.NoError(t, gotErr)
				assert.Equal(t, tt.want.StaffInfo.Permissions, gotRes.StaffInfo.Permissions)
			}
		})
	}
}

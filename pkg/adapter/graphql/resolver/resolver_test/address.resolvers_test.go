package resolver_test

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/entity"
	mockService "denkaru-server/pkg/test_mock/service"
	"errors"
	"fmt"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_queryResolver_SearchAddressByPostcode(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                    string
		getAddressByPostcodeRes []*entity.PostCodeMst
		want                    *model.SearchAddressByPostcodeRes
		wantErr                 error
	}{
		{
			name: "正常系、データあり",
			getAddressByPostcodeRes: []*entity.PostCodeMst{
				{
					PostCd:         "1000001",
					PrefKana:       "ﾄｳｷｮｳﾄ",
					CityKana:       "ﾁﾖﾀﾞｸ",
					PostalTermKana: "ﾁﾖﾀﾞ",
					PrefName:       "東京都",
					CityName:       "千代田区",
					Banti:          "千代田",
					ID:             15313,
				},
			},
			want: &model.SearchAddressByPostcodeRes{
				TotalCount: 1,
				PostCodeMstModels: []*model.PostCodeMstModel{
					{
						ID:             15313,
						PostCd:         "1000001",
						PrefKana:       "ﾄｳｷｮｳﾄ",
						CityKana:       "ﾁﾖﾀﾞｸ",
						PostalTermKana: "ﾁﾖﾀﾞ",
						PrefName:       "東京都",
						CityName:       "千代田区",
						Banti:          "千代田",
						Address:        "東京都千代田区千代田",
					},
				},
			},
			wantErr: nil,
		},
		{
			name:                    "正常系、データなし",
			getAddressByPostcodeRes: nil,
			want: &model.SearchAddressByPostcodeRes{
				TotalCount:        0,
				PostCodeMstModels: nil,
			},
			wantErr: nil,
		},
		{
			name:                    "異常系",
			getAddressByPostcodeRes: nil,
			want:                    nil,
			wantErr:                 errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の住所は見つかりません"), "住所")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addressService := mockService.NewMockIAddressService(ctrl)
			rslv := resolver.Resolver{
				AddressService: addressService,
			}

			addressService.EXPECT().GetAddressByPostcode(gomock.Any()).DoAndReturn(func(_ string) ([]*entity.PostCodeMst, error) {
				if tt.wantErr != nil {
					return nil, tt.wantErr
				}
				return tt.getAddressByPostcodeRes, nil
			}).AnyTimes()

			got, err := rslv.Query().SearchAddressByPostcode(ctx, "")

			if err != nil || tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.Equal(t, got.TotalCount, tt.want.TotalCount)
			assert.Equal(t, got.PostCodeMstModels, tt.want.PostCodeMstModels)
		})
	}
}

func Test_queryResolver_GetPrefectures(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name              string
		getPrefecturesRes []*entity.PortalMPrefecture
		want              []*model.Prefecture
		wantErr           error
	}{
		{
			name: "正常系、データあり",
			getPrefecturesRes: []*entity.PortalMPrefecture{
				{
					PrefectureID: 1,
					Name:         "北海道",
				},
			},
			want: []*model.Prefecture{
				{
					Name:         "北海道",
					PrefectureID: 1,
				},
			},
			wantErr: nil,
		},
		{
			name:              "正常系、データなし",
			getPrefecturesRes: nil,
			want:              nil,
			wantErr:           nil,
		},
		{
			name:              "異常系",
			getPrefecturesRes: nil,
			wantErr:           errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の住所は見つかりません"), "住所")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			addressService := mockService.NewMockIAddressService(ctrl)
			rslv := resolver.Resolver{
				AddressService: addressService,
			}

			addressService.EXPECT().GetPrefectures(gomock.Any()).DoAndReturn(func(_ context.Context) ([]*entity.PortalMPrefecture, error) {
				if tt.wantErr != nil {
					return nil, tt.wantErr
				}
				return tt.getPrefecturesRes, nil
			}).AnyTimes()

			got, err := rslv.Query().GetPrefectures(ctx)

			if err != nil || tt.wantErr != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, got, tt.want)
		})
	}
}

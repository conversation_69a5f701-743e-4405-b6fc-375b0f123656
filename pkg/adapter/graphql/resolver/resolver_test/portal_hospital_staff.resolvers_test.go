package resolver_test

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/myerrors"
	mock_service "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_mutationResolver_CreatePortalHospitalStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                         string
		createPortalHospitalStaffRes *model.CreatePortalStaffRes
		createPortalHospitalStaffErr error
		want                         *model.CreatePortalStaffRes
		wantErr                      error
	}{
		{
			name: "正常系",
			createPortalHospitalStaffRes: &model.CreatePortalStaffRes{
				PortalStaffID: 1,
			},
			want: &model.CreatePortalStaffRes{
				PortalStaffID: 1,
			},
			wantErr: nil,
		},
		{
			name:                         "異常系 CreatePortalHospitalStaffエラー",
			createPortalHospitalStaffErr: errors.New("hospitalId is nil"),
			want:                         nil,
			wantErr:                      errors.New("hospitalId is nil"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().CreatePortalHospitalStaff(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ model.CreatePortalHospitalStaffInput) (*model.CreatePortalStaffRes, error) {
				return tt.createPortalHospitalStaffRes, tt.createPortalHospitalStaffErr
			}).AnyTimes()

			got, err := rslv.Mutation().CreatePortalHospitalStaff(ctx, model.CreatePortalHospitalStaffInput{})

			if err != nil {
				assert.Nil(t, got)
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_mutationResolver_EditPortalHospitalStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                       string
		editPortalHospitalStaffErr error
		want                       bool
		wantErr                    error
	}{
		{
			name:    "正常系",
			want:    true,
			wantErr: nil,
		},
		{
			name:                       "異常系 EditPortalHospitalStaffエラー",
			editPortalHospitalStaffErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ")),
			want:                       false,
			wantErr:                    errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().EditPortalHospitalStaff(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ model.EditPortalHospitalStaffInput) error {
				return tt.editPortalHospitalStaffErr
			}).AnyTimes()

			got, err := rslv.Mutation().EditPortalHospitalStaff(ctx, model.EditPortalHospitalStaffInput{})

			if err != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_mutationResolver_DeletePortalHospitalStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                         string
		deletePortalHospitalStaffErr error
		want                         bool
		wantErr                      error
	}{
		{
			name:    "正常系",
			want:    true,
			wantErr: nil,
		},
		{
			name:                         "異常系",
			deletePortalHospitalStaffErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ")),
			want:                         false,
			wantErr:                      errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().DeletePortalHospitalStaff(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ model.DeletePortalHospitalStaffInput) error {
				return tt.deletePortalHospitalStaffErr
			}).AnyTimes()

			got, err := rslv.Mutation().DeletePortalHospitalStaff(ctx, model.DeletePortalHospitalStaffInput{})

			if err != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_mutationResolver_SortPortalHospitalStaffs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                        string
		sortPortalHospitalStaffsErr error
		want                        bool
		wantErr                     error
	}{
		{
			name:    "正常系",
			want:    true,
			wantErr: nil,
		},
		{
			name:                        "異常系 SortPortalHospitalStaffsエラー",
			sortPortalHospitalStaffsErr: errors.New("staff order not found"),
			want:                        false,
			wantErr:                     errors.New("staff order not found"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().SortPortalHospitalStaffs(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ *model.SortPortalHospitalStaffsInput) error {
				return tt.sortPortalHospitalStaffsErr
			}).AnyTimes()

			got, err := rslv.Mutation().SortPortalHospitalStaffs(ctx, model.SortPortalHospitalStaffsInput{})

			if err != nil {
				assert.EqualError(t, err, tt.wantErr.Error())
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_queryResolver_GetPortalHospitalStaffs(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                       string
		getPortalHospitalStaffsRes []*model.PortalHospitalStaff
		getPortalHospitalStaffsErr error
		want                       []*model.PortalHospitalStaff
		wantErr                    error
	}{
		{
			name: "正常系、1件",
			getPortalHospitalStaffsRes: []*model.PortalHospitalStaff{
				{
					HospitalStaffID:  staffID1,
					Name:             "テストユーザー",
					Description:      util.NewPtr("aaaaa"),
					Order:            1,
					ExperienceDetail: util.NewPtr("経験詳細"),
					SpecialistDetail: util.NewPtr("専門詳細"),
					IsDirector:       false,
					Files:            nil,
				},
			},
			want: []*model.PortalHospitalStaff{
				{
					HospitalStaffID:  staffID1,
					Name:             "テストユーザー",
					Description:      util.NewPtr("aaaaa"),
					Order:            1,
					ExperienceDetail: util.NewPtr("経験詳細"),
					SpecialistDetail: util.NewPtr("専門詳細"),
					IsDirector:       false,
					Files:            nil,
				},
			},
			wantErr: nil,
		},
		{
			name:                       "異常系 GetPortalHospitalStaffsエラー",
			getPortalHospitalStaffsErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")))),
			wantErr:                    myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session"))),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().GetPortalHospitalStaffs(gomock.Any()).DoAndReturn(func(_ context.Context) ([]*model.PortalHospitalStaff, error) {
				return tt.getPortalHospitalStaffsRes, tt.getPortalHospitalStaffsErr
			}).AnyTimes()

			got, err := rslv.Query().GetPortalHospitalStaffs(ctx)

			if err != nil {
				assert.Nil(t, got)
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_queryResolver_GetPortalHospitalStaff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                      string
		getPortalHospitalStaffRes *model.GetPortalHospitalStaffRes
		getPortalHospitalStaffErr error
		want                      *model.GetPortalHospitalStaffRes
		wantErr                   error
	}{
		{
			name: "正常系",
			getPortalHospitalStaffRes: &model.GetPortalHospitalStaffRes{
				PortalHospitalStaff: &model.PortalHospitalStaff{
					HospitalStaffID: staffID1,
					Name:            "テストユーザー",
					Description:     util.NewPtr("hogehoge"),
					Order:           2,
				},
			},
			want: &model.GetPortalHospitalStaffRes{
				PortalHospitalStaff: &model.PortalHospitalStaff{
					HospitalStaffID: staffID1,
					Name:            "テストユーザー",
					Description:     util.NewPtr("hogehoge"),
					Order:           2,
				},
			},
			wantErr: nil,
		},
		{
			name:                      "異常系 GetPortalHospitalStaffエラー",
			getPortalHospitalStaffErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")))),
			wantErr:                   myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session"))),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().GetPortalHospitalStaff(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ int) (*model.GetPortalHospitalStaffRes, error) {
				return tt.getPortalHospitalStaffRes, tt.getPortalHospitalStaffErr
			}).AnyTimes()

			got, err := rslv.Query().GetPortalHospitalStaff(ctx, 1)

			if err != nil {
				assert.Nil(t, got)
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.want.PortalHospitalStaff, got.PortalHospitalStaff)
		})
	}
}

func Test_queryResolver_GetPortalHospitalStaffUploadFileUrls(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                                    string
		getPortalHospitalStaffUploadFileUrlsRes []*model.GetPortalHospitalStaffUploadFileURLRes
		getPortalHospitalStaffUploadFileUrlsErr error
		want                                    []*model.GetPortalHospitalStaffUploadFileURLRes
		wantErr                                 error
	}{
		{
			name: "正常系、1件",
			getPortalHospitalStaffUploadFileUrlsRes: []*model.GetPortalHospitalStaffUploadFileURLRes{
				{
					FileNameWithExtension: "exel",
					URL:                   "<EMAIL>",
				},
			},
			want: []*model.GetPortalHospitalStaffUploadFileURLRes{
				{
					FileNameWithExtension: "exel",
					URL:                   "<EMAIL>",
				},
			},
			wantErr: nil,
		},
		{
			name:                                    "異常系 GetPortalHospitalStaffUploadFileUrlsエラー",
			getPortalHospitalStaffUploadFileUrlsErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")))),
			wantErr:                                 myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session"))),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().GetPortalHospitalStaffUploadFileUrls(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ int, _ []*model.GetPortalHospitalStaffUploadFileURLInput) ([]*model.GetPortalHospitalStaffUploadFileURLRes, error) {
				return tt.getPortalHospitalStaffUploadFileUrlsRes, tt.getPortalHospitalStaffUploadFileUrlsErr
			}).AnyTimes()

			got, err := rslv.Query().GetPortalHospitalStaffUploadFileUrls(ctx, staffID1, []*model.GetPortalHospitalStaffUploadFileURLInput{})

			if err != nil {
				assert.Nil(t, got)
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, got, tt.want)
		})
	}
}

func Test_queryResolver_GetPortalStaffFileURL(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	tests := []struct {
		name                     string
		getPortalStaffFileURLRes *string
		getPortalStaffFileURLErr error
		want                     *model.GetPortalStaffFileURLRes
		wantErr                  error
	}{
		{
			name:                     "正常系",
			getPortalStaffFileURLRes: util.NewPtr("<EMAIL>"),
			want: &model.GetPortalStaffFileURLRes{
				URL: "<EMAIL>",
			},
			wantErr: nil,
		},
		{
			name:                     "異常系 GetPortalHospitalStaffUploadFileUrlsエラー",
			getPortalStaffFileURLErr: errors.New("hospitalID or staffID is nil"),
			wantErr:                  errors.New("hospitalID or staffID is nil"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			portalHospitalStaffService := mock_service.NewMockIPortalHospitalStaffService(ctrl)
			rslv := &resolver.Resolver{
				PortalHospitalStaffService: portalHospitalStaffService,
			}

			portalHospitalStaffService.EXPECT().GetPortalStaffFileURL(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ model.GetPortalStaffFileURLInput) (url *string, err error) {
				return tt.getPortalStaffFileURLRes, tt.getPortalStaffFileURLErr
			}).AnyTimes()

			got, err := rslv.Query().GetPortalStaffFileURL(ctx, model.GetPortalStaffFileURLInput{})

			if err != nil {
				assert.Nil(t, got)
				assert.EqualError(t, err, tt.wantErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, got, tt.want)
		})
	}
}

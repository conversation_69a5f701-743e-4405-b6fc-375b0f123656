package resolver

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mockService "denkaru-server/pkg/test_mock/service"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func Test_mutationResolver_Reserve(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes         *serviceModel.Session
		sessionErr         error
		reserveRes         *custom.ReserveDetail
		reserveErr         error
		patientServiceRes  *entity.PtInf
		patientServiceErr  error
		getMailRes         serviceModel.ReservationForMailOutput
		regMailClinicErr   error
		regMailCustomerErr error
		reserveDetailIDRes *custom.ReserveDetailInfo
		reserveDetailIDErr error
		auditErr           error
	}

	tests := []struct {
		name            string
		want            want
		reserveDetailID int
		input           model.ReservationCreateInput
		expectedRes     *model.ReservationCreateRes
		expectedErr     error
	}{
		{
			name:            "正常系",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					Patient: &custom.PtInf{
						PtInf: entity.PtInf{
							HpID: 1,
						},
					},
					ExamTimeSlot: custom.ExamTimeSlot{
						ExamTimeSlot: entity.ExamTimeSlot{
							ExamStartDate: time.Date(2025, 3, 31, 19, 0, 0, 0, util.JSTLocation),
							ExamEndDate:   time.Date(2025, 3, 31, 19, 30, 0, 0, util.JSTLocation),
						},
						Calendar: custom.Calendar{
							Calendar: entity.Calendar{
								DoctorID: util.NewPtr(5),
							},
						},
					},
					Memo: util.NewPtr("Test memo"),
				},

				getMailRes: serviceModel.ReservationForMailOutput{
					ExamTimePeriod:       "ExamTimePeriod",
					TreatmentAndReserve:  "TreatmentAndReserve",
					TreatmentDepartment:  "TreatmentDepartment",
					CalendarLabel:        "CalendarLabel",
					ReservationDetailURL: "ReservationDetailURL",
					Memo:                 "",
					ReservationChanges:   "",
				},
				reserveDetailIDRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        0,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
				Memo:                   util.NewPtr("Test memo"),
			},
			expectedRes: &model.ReservationCreateRes{
				ReserveID:       1,
				ReserveDetailID: 2,
				RegisterRaiinInput: &model.RegisterRaiinInput{
					RaiinComment: "Test memo",
					ReceptionModel: &model.ReceptionModel{
						HpID:                  1,
						PtID:                  3,
						SinDate:               20250331,
						YoyakuID:              1,
						YoyakuTime:            "190000",
						YoyakuEndTime:         "193000",
						TreatmentDepartmentID: 1,
						ReserveDetailID:       2,
						TantoID:               5,
					},
				},
			},
		},
		{
			name:            "正常系 予約停止",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					Patient: &custom.PtInf{
						PtInf: entity.PtInf{
							HpID: 1,
						},
					},
					ExamTimeSlot: custom.ExamTimeSlot{
						ExamTimeSlot: entity.ExamTimeSlot{
							ExamStartDate: time.Date(2025, 3, 31, 19, 0, 0, 0, util.JSTLocation),
							ExamEndDate:   time.Date(2025, 3, 31, 19, 30, 0, 0, util.JSTLocation),
						},
						Calendar: custom.Calendar{
							Calendar: entity.Calendar{
								DoctorID: util.NewPtr(5),
							},
						},
					},
					Memo: util.NewPtr(""),
				},
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(constant.ReservationTypeInPerson),
				TreatmentType:          util.NewPtr(1),
				PatientID:              nil,
				IsSuspendedReservation: false,
			},
			expectedRes: &model.ReservationCreateRes{
				ReserveID:       1,
				ReserveDetailID: 2,
				RegisterRaiinInput: &model.RegisterRaiinInput{
					RaiinComment: "",
					ReceptionModel: &model.ReceptionModel{
						HpID:                  1,
						PtID:                  3,
						SinDate:               20250331,
						YoyakuID:              1,
						YoyakuTime:            "190000",
						YoyakuEndTime:         "193000",
						TreatmentDepartmentID: 1,
						ReserveDetailID:       2,
						TantoID:               5,
					},
				},
			},
		},
		{
			name:            "正常系 Validation Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getMailRes: serviceModel.ReservationForMailOutput{
					ExamTimePeriod:       "ExamTimePeriod",
					TreatmentAndReserve:  "TreatmentAndReserve",
					TreatmentDepartment:  "TreatmentDepartment",
					CalendarLabel:        "CalendarLabel",
					ReservationDetailURL: "ReservationDetailURL",
					Memo:                 "",
					ReservationChanges:   "",
				},
				reserveDetailIDRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        0,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(999),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruErrors(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("Key: 'ReservationCreateInput.TreatmentType' Error:Field validation for 'TreatmentType' failed on the 'eq=0|eq=1' tag"), "TreatmentType"))),
		},
		{
			name:            "異常系 GetSessionWithContext Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: nil,
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
				reserveRes: &custom.ReserveDetail{},
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name:            "異常系 Reserve Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeNonExistentPatient, fmt.Errorf("指定の患者は存在しません"))),
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeNonExistentPatient, fmt.Errorf("指定の患者は存在しません"))),
		},
		{
			name:            "異常系 AddAuditlog Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				auditErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
		},
		{
			name:            "異常系 RegisterMailForClinicReserve Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				regMailClinicErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "DummyTemplate"), "MAILコード")),
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "DummyTemplate"), "MAILコード")),
		},
		{
			name:            "異常系 GetReservationInfoByReserveDetailID Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				reserveDetailIDErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
		},
		{
			name:            "異常系 RegisterMailForCustomer Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getMailRes: serviceModel.ReservationForMailOutput{
					ExamTimePeriod:       "ExamTimePeriod",
					TreatmentAndReserve:  "TreatmentAndReserve",
					TreatmentDepartment:  "TreatmentDepartment",
					CalendarLabel:        "CalendarLabel",
					ReservationDetailURL: "ReservationDetailURL",
					Memo:                 "",
					ReservationChanges:   "",
				},
				reserveDetailIDRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        1,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
				regMailCustomerErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "DummyTemplate"), "メール")),
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "DummyTemplate"), "メール")),
		},
		{
			name:            "異常系 ConvertReservationDetailToRegisterRaiinInput Error",
			reserveDetailID: 2,
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 2,
						PatientID:       util.NewPtr(3),
					},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					Patient: nil,
				},
				getMailRes: serviceModel.ReservationForMailOutput{},
				reserveDetailIDRes: &custom.ReserveDetailInfo{
					ReservationType: constant.ReservationTypeInPerson,
				},
			},
			input: model.ReservationCreateInput{
				CalendarID:             1,
				CalendarTreatmentID:    1,
				ExamTimeSlotID:         1,
				ReserveType:            util.NewPtr(1),
				TreatmentType:          util.NewPtr(1),
				PatientID:              util.NewPtr(1),
				IsSuspendedReservation: false,
			},
			expectedErr: fmt.Errorf("patient is nil"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			patientService := mockService.NewMockIPatientService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				AuditlogService:    auditlogService,
				MailService:        mailService,
				SessionService:     sessionService,
				StaffService:       staffService,
				PatientService:     patientService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).AnyTimes().
				Return(tt.want.sessionRes, tt.want.sessionErr)
			reservationService.EXPECT().Reserve(gomock.Any(), hospitalID, tt.input.CalendarID, gomock.Any()).
				Return(tt.want.reserveRes, tt.want.reserveErr).AnyTimes()
			patientService.EXPECT().GetPatientByID(gomock.Any(), hospitalID).
				Return(tt.want.patientServiceRes, tt.want.patientServiceErr).AnyTimes()
			auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).AnyTimes().
				Return(tt.want.auditErr)
			mailService.EXPECT().GetReservationForMail(ctx, gomock.Any(), nil).AnyTimes().
				Return(tt.want.getMailRes)
			mailService.EXPECT().
				RegisterMailForClinicReserve(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ context.Context, _ int, _ serviceModel.ReservationForMailOutput, _ string) error {
					return tt.want.regMailClinicErr
				}).AnyTimes()
			reservationService.EXPECT().GetReservationInfoByReserveDetailID(ctx, tt.reserveDetailID).AnyTimes().Return(tt.want.reserveDetailIDRes, tt.want.reserveDetailIDErr)
			mailService.EXPECT().RegisterMailForCustomer(ctx, gomock.Any(), gomock.Any()).AnyTimes().Return(tt.want.regMailCustomerErr)

			res, err := rslv.Mutation().Reserve(ctx, tt.input)

			if err != nil {
				assert.Nil(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedRes, res)
		})
	}
}

func Test_mutationResolver_SuspendReservation(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes      *serviceModel.Session
		sessionErr      error
		sessionCall     int
		reservationErr  error
		reservationCall int
		auditErr        error
		auditCall       int
	}

	tests := []struct {
		name        string
		want        want
		input       model.SuspendReservationInput
		expectedRes bool
		expectedErr error
	}{
		{
			name: "正常系",
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:   &hospitalID,
					StaffID:      &staffID,
					PharmacyFlg:  util.NewPtr(false),
					IsOperator:   1,
					OperatorName: "オペレーター太郎",
				},
				sessionCall:     1,
				reservationCall: 1,
				auditCall:       1,
			},
			input: model.SuspendReservationInput{
				CalendarID: 10,
			},
			expectedRes: true,
		},
		{
			name: "異常系 SessionServiceエラー",
			want: want{
				sessionRes:      nil,
				sessionErr:      errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
				sessionCall:     1,
				reservationCall: 0,
				auditCall:       0,
			},
			input:       model.SuspendReservationInput{},
			expectedRes: false,
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name: "異常系 ReservationServiceエラー",
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:   &hospitalID,
					StaffID:      &staffID,
					PharmacyFlg:  util.NewPtr(false),
					IsOperator:   1,
					OperatorName: "オペレーター太郎",
				},
				reservationErr:  errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeCalendarNotFound, fmt.Errorf("予約カレンダーが見つかりません"))),
				sessionCall:     1,
				reservationCall: 1,
				auditCall:       0,
			},
			input:       model.SuspendReservationInput{},
			expectedRes: false,
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeCalendarNotFound, fmt.Errorf("予約カレンダーが見つかりません"))),
		},
		{
			name: "異常系 AuditlogServiceエラー",
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:   &hospitalID,
					StaffID:      &staffID,
					PharmacyFlg:  util.NewPtr(false),
					IsOperator:   1,
					OperatorName: "オペレーター太郎",
				},
				auditErr:        errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
				sessionCall:     1,
				reservationCall: 1,
				auditCall:       1,
			},
			input:       model.SuspendReservationInput{},
			expectedRes: false,
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				AuditlogService:    auditlogService,
				MailService:        mailService,
				SessionService:     sessionService,
				StaffService:       staffService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).
				Return(tt.want.sessionRes, tt.want.sessionErr).Times(tt.want.sessionCall)
			reservationService.EXPECT().SuspendReservation(
				ctx, gomock.Any(), gomock.Any(), tt.input.CalendarID, tt.input).Return(tt.want.reservationErr).Times(tt.want.reservationCall)
			auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).Return(tt.want.auditErr).Times(tt.want.auditCall)

			res, err := rslv.Mutation().SuspendReservation(ctx, tt.input)
			if err != nil {
				assert.False(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.True(t, res)
		})
	}
}

func Test_mutationResolver_UpdateReserve01(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes          *serviceModel.Session
		sessionErr          error
		reserveDetailRes    *custom.ReserveDetail
		reserveDetailErr    error
		updReserveRes       *custom.ReserveDetail
		updReserveErr       error
		reqReserveErr       error
		auditErr            error
		getReserveMailRes   serviceModel.ReservationForMailOutput
		regMailClinicErr    error
		getReserveDetailRes *custom.ReserveDetailInfo
		getReserveDetailErr error
		regMailCustomerErr  error
		updateRaiinErr      error
	}

	tests := []struct {
		name            string
		reserveID       int
		reserveDetailID int
		calendarID      int
		hospitalID      int
		input           model.ReservationUpdateInput
		resendMessage   *bool
		want            want
		expectedRes     *model.ReservationUpdateRes
		expectedErr     error
	}{
		{
			name:            "正常系",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        0,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			expectedRes: &model.ReservationUpdateRes{
				ReserveID:       1,
				ReserveDetailID: 1,
				CalendarID:      1,
			},
		},
		{
			name:            "正常系",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			resendMessage: util.NewPtr(true),
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentCompleted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        constant.ReservationTypeInPerson,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			expectedRes: &model.ReservationUpdateRes{
				ReserveID:       1,
				ReserveDetailID: 1,
				CalendarID:      1,
			},
		},
		{
			name:            "正常系 Bad Input",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(999),
				ReserveType:         util.NewPtr(999),
				Status:              util.NewPtr(999),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
			},
			expectedErr: errors.Join(myerrors.NewDenkaruErrors(
				myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("Key: 'ReservationUpdateInput.TreatmentType' Error:Field validation for 'TreatmentType' failed on the 'eq=0|eq=1' tag"), "TreatmentType"),
				myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("Key: 'ReservationUpdateInput.ReserveType' Error:Field validation for 'ReserveType' failed on the 'eq=0|eq=1' tag"), "ReserveType"),
				myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("Key: 'ReservationUpdateInput.Status' Error:Field validation for 'Status' failed on the 'eq=0|eq=1|eq=2|eq=3|eq=4' tag"), "Status")),
			),
		},
		{
			name:            "異常系 GetSessionWithContext エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name:            "異常系 GetReservationDetailByID エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeReserveNotFound, errors.New("予約が見つかりません。再確認してください。"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeReserveNotFound, errors.New("予約が見つかりません。再確認してください。"))),
		},
		{
			name:            "異常系 UpdateReservation エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			resendMessage: util.NewPtr(false),
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeCalendarUnableToChangeReserveTypeFromInPersonToOnline, fmt.Errorf("診療方式を対面からオンラインに切り替えることはできません"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeCalendarUnableToChangeReserveTypeFromInPersonToOnline, fmt.Errorf("診療方式を対面からオンラインに切り替えることはできません"))),
		},
		{
			name:            "異常系 AddAuditlog エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				auditErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
		},
		{
			name:            "異常系 RequestForReserve エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				reqReserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid sms_code [%s]", "SMS-001"), "SMSコード")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid sms_code [%s]", "SMS-001"), "SMSコード")),
		},
		{
			name:            "異常系 RegisterMailForClinicReserve エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveMailRes: serviceModel.ReservationForMailOutput{},
				regMailClinicErr:  errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "templateCode"), "MAILコード")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "templateCode"), "MAILコード")),
		},
		{
			name:            "異常系 GetReservationInfoByReserveDetailID エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveMailRes:   serviceModel.ReservationForMailOutput{},
				getReserveDetailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
		},
		{
			name:            "異常系 RegisterMailForCustomer エラー",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveMailRes: serviceModel.ReservationForMailOutput{},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					ReservationType: constant.ReservationTypeOnline,
				},
				regMailCustomerErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "ErrorTemplate"), "メール")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "ErrorTemplate"), "メール")),
		},
		{
			name:            "異常系：UpdateRaiinInfo error",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
					KarteStatus: util.NewPtr(constant.KarteStatusRealInUse),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        0,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
				updateRaiinErr: errors.New("UpdateRaiinInfo error"),
			},
			expectedErr: errors.New("UpdateRaiinInfo error"),
		},
		{
			name:            "正常系 カルテ使用中",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
					KarteStatus: util.NewPtr(constant.KarteStatusRealInUse),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{
						PharmacyReserveDetailID: 1,
						PharmacyReserveID:       1,
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        constant.ReservationTypeInPerson,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			expectedRes: &model.ReservationUpdateRes{
				ReserveID:       1,
				ReserveDetailID: 1,
				CalendarID:      1,
			},
		},
		{
			name:            "正常系 カルテ未使用",
			reserveID:       1,
			reserveDetailID: 1,
			calendarID:      1,
			hospitalID:      1,
			input: model.ReservationUpdateInput{
				CalendarID:          1,
				CalendarTreatmentID: 1,
				ExamTimeSlotID:      1,
				TreatmentType:       util.NewPtr(1),
				ReserveType:         util.NewPtr(1),
				Status:              util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
					KarteStatus: util.NewPtr(constant.KarteStatusNoUse),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				updReserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        0,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
			},
			expectedRes: &model.ReservationUpdateRes{
				ReserveID:       1,
				ReserveDetailID: 1,
				CalendarID:      1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			lineService := mockService.NewMockILineService(ctrl)
			smsService := mockService.NewMockISMSService(ctrl)
			staffService := mockService.NewMockIStaffService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				AuditlogService:    auditlogService,
				MailService:        mailService,
				SessionService:     sessionService,
				LineService:        lineService,
				SMSService:         smsService,
				StaffService:       staffService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).AnyTimes().Return(tt.want.sessionRes, tt.want.sessionErr)
			reservationService.EXPECT().GetReservationDetailByID(ctx, 1, hospitalID, gomock.Any()).AnyTimes().Return(tt.want.reserveDetailRes, tt.want.reserveDetailErr)
			reservationService.EXPECT().UpdateReservation(gomock.Any(), tt.reserveID, tt.reserveDetailID, hospitalID, gomock.Any(), nil, &staffID).Return(tt.want.updReserveRes, tt.want.updReserveErr).AnyTimes()

			// カルテ利用ステータスによる条件分岐のテスト
			if tt.want.sessionRes != nil && tt.want.sessionRes.KarteStatus != nil && *tt.want.sessionRes.KarteStatus == constant.KarteStatusRealInUse {
				reservationService.EXPECT().UpdateRaiinInfo(ctx, gomock.Any()).Return(tt.want.updateRaiinErr).AnyTimes()
			} else {
				// カルテ未使用の場合はUpdateRaiinInfoは呼ばれない
				reservationService.EXPECT().UpdateRaiinInfo(ctx, gomock.Any()).Times(0)
			}

			auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).AnyTimes().Return(tt.want.auditErr)
			mailService.EXPECT().GetReservationForMail(ctx, gomock.Any(), gomock.Any()).AnyTimes().Return(tt.want.getReserveMailRes)
			mailService.EXPECT().
				RegisterMailForClinicReserve(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ context.Context, _ int, _ serviceModel.ReservationForMailOutput, _ string) error {
					return tt.want.regMailClinicErr
				}).AnyTimes()
			reservationService.EXPECT().GetReservationInfoByReserveDetailID(ctx, tt.reserveID).Return(tt.want.getReserveDetailRes, tt.want.getReserveDetailErr).AnyTimes()
			mailService.EXPECT().RegisterMailForCustomer(ctx, gomock.Any(), gomock.Any()).Return(tt.want.regMailCustomerErr).AnyTimes()
			smsService.EXPECT().RequestForReserve(ctx, gomock.Any(), "NotifyToPatientJustBefore").Return(tt.want.reqReserveErr).AnyTimes()
			reservationService.EXPECT().CheckShouldSendMailWhenUpdateReservation(tt.input, tt.want.reserveDetailRes).Return(true, true).AnyTimes()

			res, err := rslv.Mutation().UpdateReservation(ctx, tt.reserveID, tt.reserveID, tt.calendarID, tt.input, tt.resendMessage)

			if err != nil {
				assert.Nil(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.NotNil(t, res)
			assert.Equal(t, tt.expectedRes, res)
		})
	}
}

func Test_mutationResolver_CancelReserve01(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes          *serviceModel.Session
		sessionErr          error
		reserveDetailRes    *custom.ReserveDetail
		reserveDetailErr    error
		getReserveDetailRes *custom.ReserveDetailInfo
		getReserveDetailErr error
		cancelReserveRes    bool
		cancelReserveErr    error
		updDesiredRErr      error
		auditErr            error
		getReserveMailRes   serviceModel.ReservationForMailOutput
		regMailClinicErr    error
		regMailCustomerErr  error
		regRaiinErr         error
		regMailPharmacyErr  error
	}

	tests := []struct {
		name        string
		input       model.CancelReservationByIDInput
		want        want
		expectedRes bool
		expectedErr error
	}{
		{
			name: "正常系",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{
						PharmacyReserveDetailID: 1,
						PharmacyReserveID:       1,
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        constant.ReservationTypeInPerson,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
				cancelReserveRes: true,
			},
			expectedRes: true,
		},
		{
			name: "異常系 GetSessionWithContext エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name: "異常系 GetReservationDetailByID エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeReserveNotFound, errors.New("予約が見つかりません。再確認してください。"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeReserveNotFound, errors.New("予約が見つかりません。再確認してください。"))),
		},
		{
			name: "異常系 GetReservationInfoByReserveDetailID エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				getReserveDetailErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の予約情報は見つかりません"), "予約情報")),
		},
		{
			name: "異常系 CancelReservation エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				cancelReserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("%vのReserveDetailIDで予約情報の取得に失敗しました", 999))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("%vのReserveDetailIDで予約情報の取得に失敗しました", 999))),
		},
		{
			name: "異常系 UpdateDesiredDateForStatusCancel エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{
						PharmacyReserveDetailID: 1,
						PharmacyReserveID:       1,
					},
				},
				updDesiredRErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の薬局予約は見つかりません"), "薬局予約")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の薬局予約は見つかりません"), "薬局予約")),
		},
		{
			name: "異常系 AddAuditlog エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       nil,
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				auditErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
		},
		{
			name: "異常系 RegisterMailForClinicReserve エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveMailRes: serviceModel.ReservationForMailOutput{},
				regMailClinicErr:  errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "templateCode"), "MAILコード")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("invalid mail code [%v]", "templateCode"), "MAILコード")),
		},
		{
			name: "異常系 RegisterMailForCustomer エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
				},
				getReserveMailRes: serviceModel.ReservationForMailOutput{},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					ReservationType: constant.ReservationTypeOnline,
				},
				regMailCustomerErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "ErrorTemplate"), "メール")),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のテンプレートメールが見つかりません。templateCode:%v", "ErrorTemplate"), "メール")),
		},
		{
			name: "異常系 RegisterMailToPharmacy エラー",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          int(constant.AppointmentStarted),
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					Reserve: &custom.Reserve{
						Reserve: entity.Reserve{
							PrescriptionReceiveMethod: constant.PrescriptionReceiveMethodTypeGMO24Pharmacy,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{},
				},
				getReserveMailRes: serviceModel.ReservationForMailOutput{},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					ReservationType: constant.ReservationTypeOnline,
				},
				regMailPharmacyErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("invalid values type [%#v]", map[string]string{}))),
			},
			expectedErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("invalid values type [%#v]", map[string]string{})),
		},
		{
			name: "正常系 カルテ使用中",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
					KarteStatus: util.NewPtr(constant.KarteStatusRealInUse),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{
						PharmacyReserveDetailID: 1,
						PharmacyReserveID:       1,
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        constant.ReservationTypeInPerson,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
				cancelReserveRes: true,
			},
			expectedRes: true,
		},
		{
			name: "正常系 カルテ未使用",
			input: model.CancelReservationByIDInput{
				CalendarID:      1,
				ReserveID:       1,
				ReserveDetailID: 1,
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
					KarteStatus: util.NewPtr(constant.KarteStatusNoUse),
				},
				reserveDetailRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						TreatmentType:   util.NewPtr(1),
						ReserveType:     util.NewPtr(1),
						Status:          0,
						QueueID:         util.NewPtr(1),
						PatientID:       util.NewPtr(1),
					},
					ExamTimeSlot: custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{
						CalendarTreatment: entity.CalendarTreatment{
							TreatmentDepartmentID: 1,
						},
					},
					PharmacyReserveDetail: &entity.PharmacyReserveDetail{
						PharmacyReserveDetailID: 1,
						PharmacyReserveID:       1,
					},
				},
				getReserveDetailRes: &custom.ReserveDetailInfo{
					IsReceiveNotifications: true,
					ExamStartDate:          time.Now(),
					ExamEndDate:            time.Now().Add(30 * time.Minute),
					ReservationID:          1,
					ReservationDetailID:    1,
					ReservationType:        constant.ReservationTypeInPerson,
					ClinicName:             "test",
					ClinicAddress:          "北海道",
					ClinicPhoneNumber:      "12345679",
				},
				cancelReserveRes: true,
			},
			expectedRes: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			pharmacyReserveService := mockService.NewMockIPharmacyReserveService(ctrl)

			rslv := resolver.Resolver{
				ReservationService:     reservationService,
				AuditlogService:        auditlogService,
				MailService:            mailService,
				SessionService:         sessionService,
				PharmacyReserveService: pharmacyReserveService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).AnyTimes().Return(tt.want.sessionRes, tt.want.sessionErr)
			reservationService.EXPECT().GetReservationDetailByID(ctx, 1, hospitalID, gomock.Any()).AnyTimes().Return(tt.want.reserveDetailRes, tt.want.reserveDetailErr)
			reservationService.EXPECT().CancelReservation(gomock.Any(), staffID, tt.input.ReserveID, tt.input.ReserveDetailID, gomock.Any()).
				Return(tt.want.cancelReserveRes, tt.want.cancelReserveErr).AnyTimes()

			// カルテ利用ステータスによる条件分岐のテスト
			if tt.want.sessionRes != nil && tt.want.sessionRes.KarteStatus != nil && *tt.want.sessionRes.KarteStatus == constant.KarteStatusRealInUse {
				reservationService.EXPECT().DeleteRaiinInfo(gomock.Any(), gomock.Any()).Return(tt.want.regRaiinErr).AnyTimes()
			} else {
				// カルテ未使用の場合はDeleteRaiinInfoは呼ばれない
				reservationService.EXPECT().DeleteRaiinInfo(gomock.Any(), gomock.Any()).Times(0)
			}

			auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).AnyTimes().Return(tt.want.auditErr)
			mailService.EXPECT().GetReservationForMail(ctx, gomock.Any(), gomock.Any()).AnyTimes().Return(tt.want.getReserveMailRes)
			mailService.EXPECT().
				RegisterMailForClinicReserve(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ context.Context, _ int, _ serviceModel.ReservationForMailOutput, _ string) error {
					return tt.want.regMailClinicErr
				}).AnyTimes()
			reservationService.EXPECT().GetReservationInfoByReserveDetailID(ctx, tt.input.ReserveDetailID).Return(tt.want.getReserveDetailRes, tt.want.getReserveDetailErr).AnyTimes()
			mailService.EXPECT().RegisterMailForCustomer(ctx, gomock.Any(), gomock.Any()).Return(tt.want.regMailCustomerErr).AnyTimes()
			mailService.EXPECT().RegisterMailToPharmacy(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.want.regMailPharmacyErr).AnyTimes()
			pharmacyReserveService.EXPECT().UpdateDesiredDateForStatusCancel(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.want.updDesiredRErr).AnyTimes()

			res, err := rslv.Mutation().CancelReservation(ctx, tt.input)

			if err != nil {
				assert.False(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.True(t, res)
		})
	}
}

func Test_mutationResolver_CancelReserve(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	reservationService := mockService.NewMockIReservationService(ctrl)
	auditlogService := mockService.NewMockIAuditlogService(ctrl)
	mailService := mockService.NewMockIMailService(ctrl)
	sessionService := mockService.NewMockISessionService(ctrl)
	staffService := mockService.NewMockIStaffService(ctrl)
	pharmacyReserveService := mockService.NewMockIPharmacyReserveService(ctrl)

	rslv := resolver.Resolver{
		ReservationService:     reservationService,
		AuditlogService:        auditlogService,
		MailService:            mailService,
		SessionService:         sessionService,
		StaffService:           staffService,
		PharmacyReserveService: pharmacyReserveService,
	}

	s := &serviceModel.Session{
		HospitalID:  &hospitalID,
		StaffID:     &staffID,
		PharmacyFlg: util.NewPtr(false),
	}
	ctx := test_mock.GetTestContextWithSession(s)
	sessionService.EXPECT().
		GetSessionWithContext(ctx).
		Return(s, nil)

	treatmentType := 1
	reserveType := 1
	reservation := custom.ReserveDetail{
		ExamTimeSlot:      custom.ExamTimeSlot{},
		CalendarTreatment: &custom.CalendarTreatment{},
	}

	reservation.ReserveID = 1
	reservation.ReserveDetailID = 1
	reservation.CalendarTreatment.TreatmentDepartmentID = 1
	reservation.TreatmentType = &treatmentType
	reservation.ReserveType = &reserveType
	reservation.PatientID = util.NewPtr(1)
	reservationInfo := &custom.ReserveDetailInfo{
		IsReceiveNotifications: true,
		ExamStartDate:          time.Now(),
		ExamEndDate:            time.Now().Add(30 * time.Minute),
		ReservationID:          1,
		ReservationDetailID:    1,
		ReservationType:        0,
		ClinicName:             "test",
		ClinicAddress:          "北海道",
		ClinicPhoneNumber:      "12345679",
	}
	reservation.PharmacyReserveDetail = &entity.PharmacyReserveDetail{
		PharmacyReserveDetailID: 1,
		PharmacyReserveID:       1,
	}

	reservationService.EXPECT().GetReservationDetailByID(ctx, 1, hospitalID, gomock.Any()).AnyTimes().Return(&reservation, nil)
	reservationService.EXPECT().CancelReservation(gomock.Any(), reservation.ReserveID, reservation.ReserveDetailID, gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
	reservationService.EXPECT().DeleteRaiinInfo(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).AnyTimes().Return(nil)
	mailService.EXPECT().GetReservationForMail(ctx, gomock.Any(), gomock.Any()).AnyTimes().Return(serviceModel.ReservationForMailOutput{
		ExamTimePeriod:       "ExamTimePeriod",
		TreatmentAndReserve:  "TreatmentAndReserve",
		TreatmentDepartment:  "TreatmentDepartment",
		CalendarLabel:        "CalendarLabel",
		ReservationDetailURL: "ReservationDetailURL",
		Memo:                 "",
		ReservationChanges:   "",
	})
	mailService.EXPECT().
		RegisterMailForClinicReserve(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, _ int, _ serviceModel.ReservationForMailOutput, _ string) error {
			return nil
		}).AnyTimes()
	reservationService.EXPECT().
		GetReservationInfoByReserveDetailID(ctx, reservation.ReserveID).Return(reservationInfo, nil)
	mailService.EXPECT().RegisterMailForCustomer(ctx, gomock.Any(), gomock.Any()).Return(nil)
	mailService.EXPECT().RegisterMailToPharmacy(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	staffService.EXPECT().
		GetStaffList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(_, _ int, _ *int, _ string) (*custom.StaffList, error) {
			return &custom.StaffList{StaffList: []custom.StaffPermission{{UserMst: entity.UserMst{ID: 1}}}}, nil
		}).AnyTimes()

	pharmacyReserveService.EXPECT().UpdateDesiredDateForStatusCancel(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	input := model.CancelReservationByIDInput{
		CalendarID:      1,
		ReserveID:       1,
		ReserveDetailID: 1,
	}

	gotRes, gotErr := rslv.Mutation().CancelReservation(ctx, input)

	assert.Equal(t, nil, gotErr)
	assert.Equal(t, true, gotRes)
}

func Test_queryResolver_GetReservationDetailByID(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes *serviceModel.Session
		sessionErr error
		reserveRes *custom.ReserveDetail
		reserveErr error
	}

	tests := []struct {
		name        string
		input       model.GetReservationDetailByIDInput
		want        want
		expectedRes *model.ReservationDetail
		expectedErr error
	}{
		{
			name: "正常系",
			input: model.GetReservationDetailByIDInput{
				ReserveDetailID:     1,
				IsLoadCancelReserve: util.NewPtr(true),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: &custom.ReserveDetail{
					ReserveDetail: entity.ReserveDetail{
						ReserveID:       1,
						ReserveDetailID: 1,
						ExamTimeSlotID:  1,
						PatientID:       util.NewPtr(1),
						Status:          0,
						IsSuspended:     false,
					},
					Reserve:           &custom.Reserve{},
					ExamTimeSlot:      custom.ExamTimeSlot{},
					CalendarTreatment: &custom.CalendarTreatment{},
				},
			},
			expectedRes: &model.ReservationDetail{
				ReserveDetailID:   1,
				ReserveID:         1,
				ReserveType:       nil,
				TreatmentType:     nil,
				Memo:              nil,
				QueueID:           nil,
				ExamTimeSlotID:    1,
				FincodeCustomerID: util.NewPtr(""),
				PaymentCardID:     util.NewPtr(""),
				PaymentStatus:     0,
				Status:            0,
				UpdatedAt:         time.Time{},
				CalendarTreatment: &model.CalendarTreatment{
					TreatmentDepartment: &model.TreatmentDepartment{
						Note:               util.NewPtr(""),
						SpecialNote:        util.NewPtr(""),
						FeeListNote:        util.NewPtr(""),
						TreatmentFeeList:   []*model.TreatmentFee{},
						TreatmentCategory:  &model.TreatmentCategory{},
						CalendarTreatments: []*model.CalendarTreatment{},
					},
				},
				IsSuspendedReservation: false,
				IsSurveyAnswered:       false,
				ExamTimeSlot: &model.ExamTimeSlot{
					Calendar: &model.Calendar{},
				},
				ReservationDetailHistories: []*model.ReservationDetailHistory{},
				Reservation:                &model.Reservation{},
			},
		},
		{
			name:  "異常系 GetSessionWithContext エラー",
			input: model.GetReservationDetailByIDInput{},
			want: want{
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name: "異常系 GetReservationDetailByID エラー",
			input: model.GetReservationDetailByIDInput{
				ReserveDetailID:     1,
				IsLoadCancelReserve: util.NewPtr(true),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				SessionService:     sessionService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).AnyTimes().Return(tt.want.sessionRes, tt.want.sessionErr)
			reservationService.EXPECT().GetReservationDetailByID(
				ctx, 1, hospitalID, gomock.Any()).AnyTimes().Return(tt.want.reserveRes, tt.want.reserveErr)

			res, err := rslv.Query().GetReservationDetailByID(ctx, tt.input)

			if err != nil {
				assert.Nil(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedRes, res)
		})
	}
}

func Test_queryResolver_GetReservationsByConditions(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes *serviceModel.Session
		sessionErr error
		reserveRes []*custom.ReserveDetail
		reserveErr error
		auditErr   error
	}

	tests := []struct {
		name        string
		input       model.GetReservationDetailsByConditions
		want        want
		expectedRes []*model.ReservationDetail
		expectedErr error
	}{
		{
			name: "正常系",
			input: model.GetReservationDetailsByConditions{
				PatientID:       util.NewPtr(1),
				IsLoadRaiinInfo: util.NewPtr(true),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveRes: []*custom.ReserveDetail{
					{
						ReserveDetail: entity.ReserveDetail{
							ReserveID:       1,
							ReserveDetailID: 1,
							ExamTimeSlotID:  1,
							PatientID:       util.NewPtr(1),
							Status:          0,
							IsSuspended:     false,
						},
						ExamTimeSlot:      custom.ExamTimeSlot{},
						CalendarTreatment: &custom.CalendarTreatment{},
						RaiinInfo: &entity.RaiinInf{
							RaiinNo:      1,
							SinDate:      1,
							SinStartTime: "102921",
							SinEndTime:   "102938",
							PtID:         1,
							Status:       1,
						},
					},
				},
			},
			expectedRes: []*model.ReservationDetail{
				{
					ReserveDetailID:   1,
					ReserveID:         1,
					ReserveType:       nil,
					TreatmentType:     nil,
					Memo:              nil,
					QueueID:           nil,
					ExamTimeSlotID:    1,
					FincodeCustomerID: util.NewPtr(""),
					PaymentCardID:     util.NewPtr(""),
					PaymentStatus:     0,
					Status:            0,
					UpdatedAt:         time.Time{},
					CalendarTreatment: &model.CalendarTreatment{
						TreatmentDepartment: &model.TreatmentDepartment{
							Note:               util.NewPtr(""),
							SpecialNote:        util.NewPtr(""),
							FeeListNote:        util.NewPtr(""),
							TreatmentFeeList:   []*model.TreatmentFee{},
							TreatmentCategory:  &model.TreatmentCategory{},
							CalendarTreatments: []*model.CalendarTreatment{},
						},
					},
					IsSuspendedReservation: false,
					IsSurveyAnswered:       false,
					ExamTimeSlot: &model.ExamTimeSlot{
						Calendar: &model.Calendar{},
					},
					ReservationDetailHistories: []*model.ReservationDetailHistory{},
					RaiinInfo: &model.RaiinInfo{
						RaiinNo:      1,
						SinDate:      1,
						SinStartTime: "102921",
						SinEndTime:   "102938",
						PtID:         1,
						Status:       1,
					},
				},
			},
		},
		{
			name: "異常系 GetSessionWithContext エラー",
			input: model.GetReservationDetailsByConditions{
				PatientID: util.NewPtr(1),
			},
			want: want{
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name: "異常系 GetReservationDetailsByConditions エラー",
			input: model.GetReservationDetailsByConditions{
				PatientID: util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				reserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrRecordNotFound)),
		},
		{
			name: "異常系 AddAuditlog エラー",
			input: model.GetReservationDetailsByConditions{
				PatientID: util.NewPtr(1),
			},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID:  &hospitalID,
					StaffID:     &staffID,
					PharmacyFlg: util.NewPtr(false),
				},
				auditErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, gorm.ErrDuplicatedKey)),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				SessionService:     sessionService,
				AuditlogService:    auditlogService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).AnyTimes().Return(tt.want.sessionRes, tt.want.sessionErr)
			reservationService.EXPECT().GetReservationDetailsByConditions(
				ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.want.reserveRes, tt.want.reserveErr)
			auditlogService.EXPECT().AddAuditlog(ctx, gomock.Any()).AnyTimes().Return(tt.want.auditErr)

			res, err := rslv.Query().GetReservationDetailsByConditions(ctx, tt.input)

			if err != nil {
				assert.Nil(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedRes, res)
		})
	}
}

func Test_mutationResolver_UpdateTreatmentStatusToCompleted(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type want struct {
		sessionRes *serviceModel.Session
		sessionErr error
		reserveErr error
	}

	tests := []struct {
		name        string
		input       []int
		want        want
		expectedRes bool
		expectedErr error
	}{
		{
			name:  "正常系",
			input: []int{1, 2, 3},
			want: want{
				sessionRes: &serviceModel.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			expectedRes: true,
		},
		{
			name:  "異常系 GetSessionWithContextエラー",
			input: []int{1, 2, 3},
			want: want{
				sessionErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no session error"))),
		},
		{
			name:  "異常系 UpdateTreatmentStatusToCompletedエラー",
			input: []int{1, 2, 3},
			want: want{
				reserveErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("診療完了の設定に失敗しました"))),
			},
			expectedErr: errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("診療完了の設定に失敗しました"))),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := test_mock.GetTestContextWithSession(tt.want.sessionRes)

			reservationService := mockService.NewMockIReservationService(ctrl)
			auditlogService := mockService.NewMockIAuditlogService(ctrl)
			mailService := mockService.NewMockIMailService(ctrl)
			sessionService := mockService.NewMockISessionService(ctrl)

			rslv := resolver.Resolver{
				ReservationService: reservationService,
				AuditlogService:    auditlogService,
				MailService:        mailService,
				SessionService:     sessionService,
			}

			sessionService.EXPECT().GetSessionWithContext(ctx).Return(nil, tt.want.sessionErr)
			reservationService.EXPECT().UpdateTreatmentStatusToCompleted(ctx, gomock.Any()).AnyTimes().
				Return(tt.want.reserveErr)

			res, err := rslv.Mutation().UpdateTreatmentStatusToCompleted(ctx, tt.input)

			if err != nil {
				assert.False(t, res)
				assert.EqualError(t, err, tt.expectedErr.Error())
				return
			}
			assert.NoError(t, err)
			assert.True(t, res)

		})
	}
}

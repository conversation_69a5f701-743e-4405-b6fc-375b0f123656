package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/array"
	"denkaru-server/pkg/util/session"
)

// CreateSurvey is the resolver for the createSurvey field.
func (r *mutationResolver) CreateSurvey(ctx context.Context, input model.SurveyCreateReq) (*model.SurveyRes, error) {
	survey, err := r.SurveyService.CreateSurvey(ctx, input)

	if err != nil {
		return nil, err
	}

	return &model.SurveyRes{
		SurveyID:  int(survey.SurveyID),
		FQuesJSON: survey.FQuesJSON,
		Name:      survey.Name,
	}, nil
}

// DeleteSurveyByID is the resolver for the deleteSurveyById field.
func (r *mutationResolver) DeleteSurveyByID(ctx context.Context, id int) (*model.SurveyRes, error) {
	err := r.SurveyService.RemoveSurvey(id)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// UpdateSurvey is the resolver for the updateSurvey field.
func (r *mutationResolver) UpdateSurvey(ctx context.Context, input model.SurveyInformationReq) (*model.SurveyRes, error) {
	survey, err := r.SurveyService.UpdateSurvey(ctx, input)

	if err != nil {
		return nil, err
	}

	return &model.SurveyRes{
		SurveyID:  survey.SurveyID,
		FQuesJSON: survey.FQuesJSON,
		Name:      survey.Name,
	}, nil
}

// GetSurveys is the resolver for the getSurveyList field.
func (r *queryResolver) GetSurveys(ctx context.Context, input *model.GetSurveysReq) ([]*model.SurveyRes, error) {
	surveys, err := r.SurveyService.FindSurveys(ctx, input)

	if err != nil {
		return nil, err
	}

	result := array.Map(surveys, func(survey *custom.Survey) *model.SurveyRes {
		return &model.SurveyRes{
			SurveyID:         survey.SurveyID,
			Name:             survey.Name,
			FQuesJSON:        survey.FQuesJSON,
			CreatedDate:      survey.CreatedAt,
			LastModifiedDate: survey.UpdatedAt,
			Secret:           &survey.Secret,
			IsDeleted:        survey.IsDeleted,
			CreatedAt:        survey.CreatedAt,
		}
	})

	return result, nil
}

// GetSurveysWithoutPermission is the resolver for the getSurveysWithoutPermission field.
func (r *queryResolver) GetSurveysWithoutPermission(ctx context.Context, input *model.GetSurveysReq) ([]*model.SurveyRes, error) {
	return r.GetSurveys(ctx, input)
}

// GetSurveyByID is the resolver for the getSurveyById field.
func (r *queryResolver) GetSurveyByID(ctx context.Context, id int) (*model.SurveyRes, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, err
	}

	survey, err := r.SurveyService.FindSurveyByID(id, *hospitalID)

	if err != nil {
		return nil, err
	}

	return &model.SurveyRes{
		SurveyID:         int(survey.SurveyID),
		FQuesJSON:        survey.FQuesJSON,
		Name:             survey.Name,
		CreatedDate:      survey.CreatedAt,
		LastModifiedDate: survey.UpdatedAt,
	}, nil
}

// GetSurveyTemplates is the resolver for the getSurveyTemplates field.
func (r *queryResolver) GetSurveyTemplates(ctx context.Context) ([]*model.SurveyTemplate, error) {
	templates, err := r.SurveyService.FindSurveyTemplates()
	if err != nil {
		return nil, err
	}

	result := array.Map(templates, func(template *entity.MTemplateSurvey) *model.SurveyTemplate {
		return &model.SurveyTemplate{
			SurveyTemplateID: template.SurveyTemplateID,
			Name:             template.Name,
			FQuesJSON:        template.FQuesJSON,
		}
	})

	return result, nil
}

// GetSurveyAnswers is the resolver for the getSurveyAnswers field.
func (r *queryResolver) GetSurveyAnswers(ctx context.Context, patientID int) ([]*model.SurveyAnswer, error) {
	// セッション情報の取得
	sess, err := session.GetSession(ctx)
	if err != nil {
		return nil, err
	}

	surveyAnswers, err := r.SurveyService.FindSurveyAnswerByPatientID(ctx, *sess.HospitalID, patientID)
	if err != nil {
		return nil, err
	}

	gqlRes := make([]*model.SurveyAnswer, 0, len(surveyAnswers))
	for _, sa := range surveyAnswers {
		answer := &model.SurveyAnswer{
			SurveyAnswerID:   sa.SurveyAnswerID,
			TreatmentTitle:   &sa.TreatmentTitle,
			TreatmentType:    sa.TreatmentType,
			BasicAnswerJSON:  sa.CommonSurvey,
			CustomAnswerJSON: sa.SurveyAnswer.SurveyAnswer,
			CreatedAt:        sa.CreatedAt,
			ExamStartDate:    sa.ExamStartDate,
			ReserveDetailID:  *sa.ReserveDetailID,
		}
		gqlRes = append(gqlRes, answer)
	}

	// 監査ログ：GMO患者ページ：閲覧
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoPatientPageView,
		PtID:         util.NewPtr(int64(patientID)),
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	return gqlRes, nil
}

// DownloadSurveyPDF is the resolver for the downloadSurveyPDF field.
func (r *queryResolver) DownloadSurveyPDF(ctx context.Context, input *model.SurveyPDFReq) (res *model.SurveyPDFURLRes, err error) {
	url, err := r.SurveyService.GetSurveyPDF(ctx, input)
	if err != nil {
		return
	}

	res = &model.SurveyPDFURLRes{
		PDFURL: url,
	}

	return
}

// DownloadSurveyPDFZipByDateRange is the resolver for the downloadSurveyPDFZipByDateRange field.
func (r *queryResolver) DownloadSurveyPDFZipByDateRange(ctx context.Context, input *model.SurveyDateRangeReq) (res *model.SurveyPDFURLRes, err error) {
	url, err := r.SurveyService.GetSurveyPDFZipByDateRange(ctx, input)
	if err != nil {
		return
	}

	res = &model.SurveyPDFURLRes{
		PDFURL: url,
	}

	return
}

// DownloadSurveyPDFZipByIds is the resolver for the downloadSurveyPDFZipByIds field.
func (r *queryResolver) DownloadSurveyPDFZipByIds(ctx context.Context, input *model.SurveyPDFZipReq) (res *model.SurveyPDFURLRes, err error) {
	url, err := r.SurveyService.GetSurveyPDFZipByIDs(ctx, input)
	if err != nil {
		return
	}

	res = &model.SurveyPDFURLRes{
		PDFURL: url,
	}

	return
}

// GetSurveyPDFsByDateRange is the resolver for the getSurveyPDFsByDateRange field.
func (r *queryResolver) GetSurveyPDFsByDateRange(ctx context.Context, input *model.SurveyDateRangeReq) (res *model.SurveyPDFsRes, err error) {
	return r.SurveyService.GetSurveyPDFsByDateRange(ctx, input)
}

// GetSurveyBySecret is the resolver for the GetSurveyBySecret field.
func (r *queryResolver) GetSurveyBySecret(ctx context.Context, secret string) (*model.SurveyRes, error) {
	survey, err := r.SurveyService.GetSurveyBySecret(ctx, secret)
	if err != nil {
		return nil, err
	}
	if survey == nil {
		return nil, nil
	}
	return &model.SurveyRes{
		SurveyID:         survey.SurveyID,
		Name:             survey.Name,
		FQuesJSON:        survey.FQuesJSON,
		CreatedDate:      survey.CreatedAt,
		LastModifiedDate: survey.UpdatedAt,
		Secret:           &survey.Secret,
		ClinicName:       survey.ClinicName,
	}, nil
}

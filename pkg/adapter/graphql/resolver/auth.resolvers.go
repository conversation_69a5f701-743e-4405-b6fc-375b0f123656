package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util/session"
	"fmt"
)

// IssueToken is the resolver for the issueToken field.
func (r *mutationResolver) IssueToken(ctx context.Context, input model.IssueTokenReq) (*model.IssueTokenRes, error) {
	sess := serviceModel.NewSession(input.HospitalID, input.StaffID, input.StaffUserID, input.PharmacyFlg, input.Email, input.PortalCustomerID, input.KarteStatus)
	token, tokenExpire, refreshToken, refreshTokenExpire, err := r.AuthService.IssueToken(ctx, sess, input.TokenTTL, input.RefreshTokenTTL, false)
	if err != nil {
		return nil, err
	}

	return &model.IssueTokenRes{
		Token:                  token,
		TokenExpiryTime:        tokenExpire,
		RefreshToken:           refreshToken,
		RefreshTokenExpiryTime: refreshTokenExpire,
	}, nil
}

// RefreshToken is the resolver for the refreshToken field.
func (r *mutationResolver) RefreshToken(ctx context.Context, input model.RefreshTokenReq) (res *model.RefreshTokenRes, err error) {
	// JWTトークンから情報を取得
	_, refreshClaims, err := session.ExtractJWTToken(input.RefreshToken)
	if err != nil {
		return
	}

	refreshTokenID, ok := refreshClaims["jti"]
	if !ok {
		err = fmt.Errorf("invalid refresh token")
		return
	}

	// IsWebSocket未指定の場合は、WwbSocket経由以外とする
	isWebSocket := false
	if input.IsWebSocket != nil {
		isWebSocket = *input.IsWebSocket
	}

	// セッションリフレッシュ
	res = new(model.RefreshTokenRes)
	res.Token, res.TokenExpiryTime, res.RefreshToken, res.RefreshTokenExpiryTime, err = r.AuthService.RefreshToken(ctx, refreshTokenID.(string), input.TokenTTL, input.RefreshTokenTTL, isWebSocket)
	if err != nil {
		return nil, err
	}

	return
}

// VerifyToken is the resolver for the verifyToken field.
func (r *mutationResolver) VerifyToken(ctx context.Context, input model.VerifyTokenReq) (*model.VerifyTokenRes, error) {
	_, claims, err := session.ExtractJWTToken(input.Token)
	if err != nil {
		return nil, err
	}

	ok, err := r.AuthService.VerifyToken(ctx, claims["jti"].(string))
	if err != nil {
		return nil, err
	}

	return &model.VerifyTokenRes{
		IsValid: ok,
	}, nil
}

// DeleteToken is the resolver for the deleteToken field.
func (r *mutationResolver) DeleteToken(ctx context.Context, token string) (*model.DeleteTokenRes, error) {
	s, claims, err := session.ExtractJWTToken(token)
	if err != nil {
		return nil, err
	}

	isSessionRemaining, err := r.AuthService.DeleteToken(ctx, claims["jti"].(string), s)
	if err != nil {
		return nil, err
	}

	return &model.DeleteTokenRes{
		IsDeleted:          true,
		IsSessionRemaining: isSessionRemaining,
	}, nil
}

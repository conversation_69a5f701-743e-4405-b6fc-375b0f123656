package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/adapter/converter"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/adapter/middleware"
	"denkaru-server/pkg/config"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/session"
	"errors"
	"fmt"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
)

// Login is the resolver for the login field.
func (r *mutationResolver) Login(ctx context.Context, input model.LoginReq) (*model.LoginRes, error) {
	c, err := middleware.EchoContextFromContext(ctx)
	if err != nil {
		return nil, errors.Join(err, fmt.Errorf("no convert context"))
	}
	var hospitalID *int

	if !config.IsProduction() && config.IsSkipClientCertVerify() {
		// 脆弱性診断など、クライアント証明書の検証をスキップしたい場合の分岐処理
		// 本番環境ではないことを担保する
		// hospitalIDを取得する必要があるため、環境変数に設定した値から取得する（本来はnginxで付与される）
		loginUsers, err := util.GetLoginUserForVulnerabilityTest()
		if err != nil {
			return nil, err
		}

		hospitalID, err = util.GetHospitalIDForVulnerabilityTestLoginUser(input, loginUsers)
		if err != nil {
			return nil, err
		}

		// hospitalIDがnilの場合は脆弱性診断用ユーザーではないため、通常通りクライアント証明書を検証する
		if hospitalID == nil {
			clientCertificateID, err := session.GetClientCertificateID(c)
			if err != nil {
				return nil, err
			}

			// クライアント証明書の有効性をチェック
			hospitalID, err = r.ClientCertificateService.IsClientCertificateValid(ctx, *clientCertificateID)
			if err != nil {
				return nil, err
			}
		}
	} else {
		// クライアント証明書の検証をスキップしない
		// todo ユーザー登録処理いれるときにinputのバリデーションも合わせていれる
		clientCertificateID, err := session.GetClientCertificateID(c)
		if err != nil {
			return nil, err
		}
		// クライアント証明書の有効性をチェック
		hospitalID, err = r.ClientCertificateService.IsClientCertificateValid(ctx, *clientCertificateID)
		if err != nil {
			return nil, err
		}
	}

	// 認証
	staff, hpInf, err := r.StaffService.Authenticate(ctx, *hospitalID, input.LoginID, input.Password)
	if err != nil {
		myErr := new(myerrors.DenkaruError)
		if errors.As(err, &myErr) {
			if definitions.DenkaruCodeAccountLocked.CodeEq(myErr.Code) {
				// メール配信：アカウントがロックされました
				mailReq := &model.SendMailReq{
					StaffID:          &staff.ID,
					TemplateMailCode: constant.TemplateMailCodeLockedAccount,
				}
				err := r.MailService.RegisterMail(ctx, mailReq, hpInf.PharmacyFlg)
				if err != nil {
					return nil, err
				}
			}
		}
		return nil, err
	}

	// スタッフ用セッション作成
	sess := serviceModel.NewSession(&staff.HpID, &staff.ID, &staff.UserID, &hpInf.PharmacyFlg, nil, nil, util.NewPtr(int(hpInf.KarteStatus)))
	accessToken, refreshToken, tokenExpireTime, refreshExpireTime, err := r.SessionService.CreateSession(ctx, sess)
	if err != nil {
		return nil, err
	}

	// 現在時刻との差分を計算してDurationを取得
	expireDuration := time.Until(tokenExpireTime)

	// 現在時刻との差分を計算してDurationを取得
	refreshExpireDuration := time.Until(refreshExpireTime)

	// クッキー設定
	session.SetCookie(c, session.GenCookieNameWithAccessSource(c, constant.DenkaruAccessToken), accessToken, expireDuration)          // アクセストークンの有効期限を設定
	session.SetCookie(c, session.GenCookieNameWithAccessSource(c, constant.DenkaruRefreshToken), refreshToken, refreshExpireDuration) // リフレッシュトークンの有効期限を設定

	// スタッフ権限取得
	staffPermission, err := r.StaffService.GetStaffPermission(staff.HpID, staff.ID)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOログイン
	auditlog := &model.Auditlog{
		HpID:         staff.HpID,
		UserID:       staff.ID,
		EventCd:      constant.EventCdGmoLogin,
		IsOperator:   constant.StatusFalse,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, err
	}

	// メール配信：ログインのお知らせ
	mailReq := &model.SendMailReq{
		StaffID:          &staff.ID,
		TemplateMailCode: constant.TemplateMailCodeLogin,
	}
	err = r.MailService.RegisterMail(ctx, mailReq, hpInf.PharmacyFlg)
	if err != nil {
		return nil, err
	}

	return &model.LoginRes{
		Success:               constant.StatusTrue,
		IsLoginIDInitialized:  staff.IsInitLoginID,
		IsPasswordInitialized: staff.IsInitPassword,
		HospitalID:            staff.HpID,
		KarteStatus:           int(hpInf.KarteStatus),
		StaffInfo: &model.StaffInfo{
			StaffID:     staff.ID,
			StaffName:   staff.Name,
			StaffKana:   staff.KanaName,
			StaffType:   staff.JobCd,
			ManagerKbn:  staff.ManagerKbn,
			Email:       &staff.Email,
			LoginID:     staff.LoginID,
			Status:      staff.Status,
			Permissions: converter.ConvertStaffPermission(staffPermission),
		},
		PharmacyFlg: hpInf.PharmacyFlg,
	}, nil
}

// Logout is the resolver for the logout field.
func (r *mutationResolver) Logout(ctx context.Context) (*model.LogoutRes, error) {
	c, err := middleware.EchoContextFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("could not convert context: %w", err)
	}

	defer func() {
		// 以下の処理でエラーが発生してもCookieをクリアする
		session.ClearDenkaruCookies(c)
	}()

	// HTTP only cookieからJWTトークンを取得
	jwtToken, err := session.GetCookie(c, session.GenCookieNameWithAccessSource(c, constant.DenkaruAccessToken))
	if err != nil {
		return nil, fmt.Errorf("could not retrieve jwt token: %w", err)
	}

	// JWTトークンから情報を取得
	sess, _, err := session.ExtractJWTToken(jwtToken)
	if err != nil {
		return nil, fmt.Errorf("could not extract session from token: %w", err)
	}

	// セッション削除
	isSessionRemaining, err := r.SessionService.DeleteSession(ctx, jwtToken)
	if err != nil {
		return nil, fmt.Errorf("could not delete session: %w", err)
	}

	// 監査ログ：GMOログアウト
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoLogout,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, fmt.Errorf("could not add audit log: %w", err)
	}

	return &model.LogoutRes{
		Success:            constant.StatusTrue,
		IsSessionRemaining: isSessionRemaining,
	}, nil
}

// CreateStaff is the resolver for the createStaff field.
func (r *mutationResolver) CreateStaff(ctx context.Context, input model.CreateStaffReq) (*model.CreateStaffRes, error) {
	sess, err := session.GetSession(ctx)

	if err != nil {
		return nil, err
	}

	// バリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}
	for _, p := range input.Permissions {
		if err := model.Validate(p); err != nil {
			return nil, err
		}
	}

	// バリデーション：システム毎のStaffType
	err = model.ValidateStaffType(*sess.PharmacyFlg, input.StaffType)
	if err != nil {
		return nil, err
	}

	// スタッフ作成
	createStaff := converter.ConvertCreateStaff(*sess.HospitalID, *sess.StaffID, input)
	userPermission := converter.ConvertUserPermission(*sess.HospitalID, *sess.StaffID, input.Permissions)
	resStaff, password, err := r.StaffService.CreateStaff(ctx, createStaff, userPermission)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOアカウント管理：アカウントの追加
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoAccountAdd,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, fmt.Errorf("Failed to add audit log.: %w", err)
	}

	return converter.ConvertCreateStaffRes(resStaff, userPermission, password), err
}

// UpdateStaff is the resolver for the updateStaff field.
func (r *mutationResolver) UpdateStaff(ctx context.Context, input model.UpdateStaffReq) (*model.UpdateStaffRes, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return nil, err
	}

	// バリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}
	for _, p := range input.Permissions {
		if err := model.Validate(p); err != nil {
			return nil, err
		}
	}

	// バリデーション：システム毎のStaffType
	err = model.ValidateStaffType(*sess.PharmacyFlg, input.StaffType)
	if err != nil {
		return nil, err
	}

	// 変更対象が自分の場合は無効化できない
	if *sess.StaffID == input.StaffID && input.Status == constant.StatusFalse {
		return nil, myerrors.NewDenkaruError(definitions.DenkaruCodeStaffNotAlloweStatus, fmt.Errorf("cannot change status self staffID: %v ", input.StaffID))
	}

	updateStaff := converter.ConvertUpdateStaff(*sess.HospitalID, *sess.StaffID, input)
	userPermission := converter.ConvertUserPermission(*sess.HospitalID, *sess.StaffID, input.Permissions)

	resEntity, password, err := r.StaffService.UpdateStaff(ctx, updateStaff, userPermission)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOアカウント管理：アカウントの更新
	eventCd := constant.EventCdGmoAccountEdit
	if input.PasswordReset == 1 {
		// GMOアカウント管理：パスワードリセット
		eventCd = constant.EventCdGmoAccountPasswordReset
	} else if input.Status == constant.StatusFalse {
		// GMOアカウント管理：アカウントの無効
		eventCd = constant.EventCdGmoAccountInvalid
	}
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      eventCd,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, fmt.Errorf("Failed to add audit log.: %w", err)
	}

	return converter.ConvertUpdateStaffRes(resEntity, userPermission, password), err
}

// ChangePassword is the resolver for the changePassword field.
func (r *mutationResolver) ChangePassword(ctx context.Context, input model.ChangePasswordReq) (*model.ChangePasswordRes, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return nil, err
	}

	// バリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	err = r.StaffService.ChangePassword(ctx, *sess.HospitalID, *sess.StaffID, input.OldPassword, input.NewPassword)
	if err != nil {
		return nil, err
	}

	// 監査ログ：GMOアカウント管理：パスワード変更
	auditlog := &model.Auditlog{
		HpID:         *sess.HospitalID,
		UserID:       *sess.StaffID,
		EventCd:      constant.EventCdGmoAccountPasswordEdit,
		IsOperator:   sess.IsOperator,
		OperatorName: sess.OperatorName,
	}
	err = r.AuditlogService.AddAuditlog(ctx, auditlog)
	if err != nil {
		return nil, fmt.Errorf("Failed to add audit log.: %w", err)
	}

	// メール配信：パスワード変更完了のお知らせ
	mailReq := &model.SendMailReq{
		StaffID:          sess.StaffID,
		TemplateMailCode: constant.TemplateMailCodeChangedPassword,
	}
	err = r.MailService.RegisterMail(ctx, mailReq, *sess.PharmacyFlg)
	if err != nil {
		return nil, err
	}

	return &model.ChangePasswordRes{Success: 1}, nil
}

// LoginInitial is the resolver for the loginInitial field.
func (r *mutationResolver) LoginInitial(ctx context.Context, input model.LoginInitialReq) (*model.LoginInitialRes, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return nil, err
	}

	// バリデーション
	if err := model.Validate(input); err != nil {
		return nil, err
	}

	err = r.StaffService.LoginInitial(ctx, *sess.HospitalID, *sess.StaffID, input.NewLoginID, input.NewPassword)
	if err != nil {
		return nil, err
	}

	// メール配信：初期設定完了のお知らせ
	mailReq := &model.SendMailReq{
		StaffID:          sess.StaffID,
		TemplateMailCode: constant.TemplateMailCodeInitialSetting,
	}
	err = r.MailService.RegisterMail(ctx, mailReq, *sess.PharmacyFlg)
	if err != nil {
		return nil, err
	}

	return &model.LoginInitialRes{Success: 1}, nil
}

// GetStaffListWithTaskCount is the resolver for the getStaffListWithTaskCount field.
func (r *queryResolver) GetStaffListWithTaskCount(ctx context.Context) (*model.GetStaffListWithTaskCountRes, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, err
	}

	if hospitalID == nil {
		return nil, myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("hospital is nil"))
	}
	staffs, totalTaskCount, err := r.StaffService.GetStaffListWithTaskCount(ctx, *hospitalID)

	if err != nil {
		return nil, err
	}

	gqlStaffs := make([]*model.StaffWithTaskCount, 0, len(staffs))
	for _, staff := range staffs {
		gqlStaffs = append(gqlStaffs, &model.StaffWithTaskCount{
			StaffID:   staff.StaffID,
			StaffName: staff.StaffName,
			TaskCount: staff.TaskCount,
		})
	}

	return &model.GetStaffListWithTaskCountRes{Staffs: gqlStaffs, TotalTaskCount: totalTaskCount}, nil
}

// GetStaffList is the resolver for the getStaffList field.
func (r *queryResolver) GetStaffList(ctx context.Context, input *model.GetStaffListReq) (*model.GetStaffListRes, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, err

	}

	if input == nil {
		input = &model.GetStaffListReq{}
	}

	if input.Limit == nil {
		input.Limit = util.NewPtr(constant.DefaultPageLimit)
	}

	if input.SearchName == nil {
		input.SearchName = util.NewPtr("")
	}

	if err := model.Validate(input); err != nil {
		return nil, err
	}

	staffs, err := r.StaffService.GetStaffList(*hospitalID, *input.Limit, input.Cursor, *input.SearchName)
	if err != nil {
		return nil, err
	}

	return converter.ConvertGetStaffRes(staffs), nil
}

// GetStaffListSetting is the resolver for the getStaffListSetting field.
func (r *queryResolver) GetStaffListSetting(ctx context.Context, input *model.GetStaffListReq) (*model.GetStaffListRes, error) {
	return r.GetStaffList(ctx, input)
}

// GetDoctorsInHospital is the resolver for the getDoctorsInHospital field.
func (r *queryResolver) GetDoctorsInHospital(ctx context.Context) ([]*model.StaffInfo, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return nil, err
	}

	result, err := r.StaffService.GetDoctorsInHospital(*sess.HospitalID)

	if err != nil {
		return nil, err
	}

	staffsRes := make([]*model.StaffInfo, 0)
	for _, v := range result {
		staffsRes = append(staffsRes, &model.StaffInfo{
			StaffID:   v.ID,
			StaffName: v.Name,
			StaffKana: v.KanaName,
			StaffType: v.JobCd,
			Email:     &v.Email,
		})
	}

	return staffsRes, nil
}

// GetSessionInfo is the resolver for the getSessionInfo field.
func (r *queryResolver) GetSessionInfo(ctx context.Context) (*model.LoginRes, error) {
	staff, permissions, err := r.StaffService.CheckStaffInfo(ctx)
	if err != nil {
		return nil, err
	}

	_, hpInf, err := r.HospitalStatusCheckService.ClinicStatusCheck(ctx, staff.HpID)
	if err != nil {
		return nil, err
	}

	return &model.LoginRes{
		Success:               constant.StatusTrue,
		IsLoginIDInitialized:  staff.IsInitLoginID,
		IsPasswordInitialized: staff.IsInitPassword,
		HospitalID:            staff.HpID,
		StaffInfo: &model.StaffInfo{
			StaffID:     staff.ID,
			StaffName:   staff.Name,
			StaffKana:   staff.KanaName,
			StaffType:   staff.JobCd,
			ManagerKbn:  staff.ManagerKbn,
			Email:       &staff.Email,
			LoginID:     staff.LoginID,
			Status:      staff.Status,
			Permissions: converter.ConvertStaffPermission(permissions),
		},
		PharmacyFlg: hpInf.PharmacyFlg,
	}, nil
}

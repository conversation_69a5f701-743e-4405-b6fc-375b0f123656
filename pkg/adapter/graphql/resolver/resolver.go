package resolver

import (
	"denkaru-server/pkg/service"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

// Resolver リゾルバー
type Resolver struct {
	AgentService                      service.IAgentService
	AuditlogService                   service.IAuditlogService
	ClientCertificateService          service.IClientCertificateService
	CommentService                    service.ICommentService
	PortalHospitalNotificationService service.IPortalHospitalNotificationService
	HealthCheckService                service.IHealthCheckService
	ExaminationService                service.IExaminationService
	// FileService                service.IFileService
	HospitalService              service.IHospitalService
	LabelService                 service.ILabelService
	NotificationService          service.INotificationService
	NotificationToPatientService service.INotificationToPatientService
	MessageService               service.IMessageService
	PatientService               service.IPatientService
	SchemaService                service.ISchemaService
	SessionService               service.ISessionService
	SpecialistService            service.ISpecialistService
	AuthService                  service.IAuthService
	StaffService                 service.IStaffService
	SurveyService                service.ISurveyService
	TagService                   service.ITagService
	TaskService                  service.ITaskService
	TemplateDocService           service.ITemplateDocService
	TreatmentCategoryService     service.IMTreatmentCategoryService
	ExamTimeSlotService          service.IExamTimeSlotService
	CalendarService              service.ICalendarService
	ReservationService           service.IReservationService
	TreatmentDepartmentService   service.ITreatmentDepartmentService
	MailService                  service.IMailService
	AddressService               service.IAddressService
	PortalAddressService         service.IPortalAddressService
	PortalHospitalService        service.IPortalHospitalService
	PortalHospitalStaffService   service.IPortalHospitalStaffService
	AgreeService                 service.IAgreeService
	SignupService                service.ISignupService
	MeetingService               service.IMeetingService
	PaymentService               service.IPaymentService
	SMSService                   service.ISMSService
	ZendeskService               service.IZendeskService

	// used by patients
	PatientMessageService        service.IPatientMessageService
	PatientPortalHospitalService service.IPatientPortalHospitalService
	// used by operator
	OperatorService service.IOperatorService
	// sample
	OcrService service.IOcrService
	AIService  service.IAIService

	FaxService service.IFaxService

	FreeeService service.IFreeeService
	// LINE
	LineService service.ILineService

	// pharmacy
	PharmacyReserveService       service.IPharmacyReserveService
	PharmacyHolidayService       service.IPharmacyHolidayService
	PharmacyDeliveryService      service.IPharmacyDeliveryService
	PharmacyReserveDetailService service.IPharmacyReserveDetailService
	PharmacyPatientFileService   service.IPharmacyPatientFileService

	// Import external data
	ImportDataService service.IImportDataService
	// prescription
	PrescriptionReceptionService service.IPrescriptionReceptionService

	HospitalStatusCheckService service.IHospitalStatusCheckService

	MClinicMasterDataService     service.IMClinicMasterDataService
	SurveyAnswerNoPatientService service.ISurveyAnswerNoPatientService
}

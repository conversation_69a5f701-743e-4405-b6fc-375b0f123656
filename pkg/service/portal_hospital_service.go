package service

import (
	"bytes"
	"context"
	"denkaru-server/pkg/adapter/converter"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/session"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/cockroachdb/errors"

	"slices"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"gorm.io/gorm"

	"github.com/PuerkitoBio/goquery"
	"github.com/jaytaylor/html2text"
)

// IPortalHospitalService interface of HospitalService
type IPortalHospitalService interface {
	FindPortalHospitalById(ctx context.Context) (*gqlModel.GetPortalHospitalRes, error)
	ChangePortalHospitalInfo(ctx context.Context, input gqlModel.UpdatePortalHospitalInput) error
	GetPortalHospitalUploadFileURLs(ctx context.Context, input []*gqlModel.GetPortalHospitalUploadFileURLInput) ([]*gqlModel.GetPortalHospitalUploadFileURLRes, error)
	GetPortalMasterStationByKeyword(ctx context.Context, input string) ([]*custom.PortalMasterStation, error)
	GetPortalHospitalFileURL(ctx context.Context, input gqlModel.GetPortalHospitalFileURLInput) (url *string, err error)
	UpdateHospitalStatusToOpenSearch(ctx context.Context, input gqlModel.UpdateHospitalStatusToOpenSearchInput) error
	GetHospitalInfoByHomepageURL(ctx context.Context, URL string, prompt string) (*gqlModel.GetHospitalInfoByHomepageURLRes, error)
}

type portalHospitalService struct {
	repo            repository.IPortalHospitalRepository
	hospSearchRepo  repository.IPortalHospitalSearchRepository
	hospPictureRepo repository.IPortalHospPictureRepository
	pictureRepo     repository.IPortalPictureRepository
	s3PresignClient *s3.PresignClient
	s3Client        *s3.Client
	queuingRepo     repository.IQueuingRepository
	importHospRepo  repository.IImportHospitalRepository
	aiService       IAIService
}

// NewPortalHospitalService return new portalHospitalService
func NewPortalHospitalService(repo repository.IPortalHospitalRepository, hospSearchRepo repository.IPortalHospitalSearchRepository,
	s3PresignClient *s3.PresignClient, hospPictureRepo repository.IPortalHospPictureRepository, pictureRepo repository.IPortalPictureRepository,
	s3Client *s3.Client, queuingRepo repository.IQueuingRepository, importHospRepo repository.IImportHospitalRepository, aiService IAIService) IPortalHospitalService {
	return &portalHospitalService{
		repo:            repo,
		hospSearchRepo:  hospSearchRepo,
		hospPictureRepo: hospPictureRepo,
		pictureRepo:     pictureRepo,
		s3PresignClient: s3PresignClient,
		s3Client:        s3Client,
		queuingRepo:     queuingRepo,
		importHospRepo:  importHospRepo,
		aiService:       aiService,
	}
}

func (s *portalHospitalService) FindPortalHospitalById(ctx context.Context) (*gqlModel.GetPortalHospitalRes, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.New("hospitalId is nil")
	}
	portalHospital := gqlModel.GetPortalHospitalRes{}

	hospitalInfo, err := s.repo.FindPortalHospitalByID(ctx, *hospitalID)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &portalHospital, nil
	}

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	portalHospital.PortalHospital = converter.ConvertPortalHospitalToGqlModel(hospitalInfo)

	// Get tags
	tags := make([]*gqlModel.PortalHospitalTag, 0, len(hospitalInfo.Tags))
	for _, ptrTag := range hospitalInfo.Tags {
		tags = append(tags, &gqlModel.PortalHospitalTag{
			TagID: ptrTag.TagID,
		})
	}
	portalHospital.PortalHospital.Tags = tags

	// Get examinations
	exams := make([]*gqlModel.PortalHospitalExamination, 0, len(hospitalInfo.Examinations))
	for _, ptrExam := range hospitalInfo.Examinations {
		exams = append(exams, &gqlModel.PortalHospitalExamination{
			ExaminationID: ptrExam.ExaminationID,
			Type:          ptrExam.PortalMasterExamination.Type,
		})
	}
	portalHospital.PortalHospital.Examinations = exams

	// Get specialists
	specialists := make([]*gqlModel.PortalHospitalSpecialist, 0, len(hospitalInfo.Specialists))
	for _, ptrExam := range hospitalInfo.Specialists {
		specialists = append(specialists, &gqlModel.PortalHospitalSpecialist{
			SpecialistID: ptrExam.SpecialistID,
		})
	}
	portalHospital.PortalHospital.Specialists = specialists

	// Get Business times
	timelines := make([]*gqlModel.PortalBusinessTime, 0, len(hospitalInfo.BusinessTimes))
	for _, time := range hospitalInfo.BusinessTimes {
		timelines = append(timelines, &gqlModel.PortalBusinessTime{
			BusinessTimeID: time.BusinessTimeID,
			StartTime:      time.StartTime,
			EndTime:        time.EndTime,
			MonFlag:        time.MonFlag,
			TueFlag:        time.TueFlag,
			WedFlag:        time.WedFlag,
			ThuFlag:        time.ThuFlag,
			FriFlag:        time.FriFlag,
			SatFlag:        time.SatFlag,
			SunFlag:        time.SunFlag,
		})
	}
	portalHospital.PortalHospital.BusinessTimes = timelines

	// Get hospital picture
	pictures := make([]*gqlModel.PortalHospitalFile, 0, len(hospitalInfo.Pictures))
	for _, pict := range hospitalInfo.Pictures {

		u, err := util.GetS3FileURL(ctx, s.s3Client, s.s3PresignClient, os.Getenv("S3_HOSPITAL_BUCKET"), pict.PictureDetail.Filepath)
		if err != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
		pictures = append(pictures, &gqlModel.PortalHospitalFile{
			FileID:           &pict.PictID,
			OriginalFileName: pict.PictureDetail.FileName,
			S3Key:            *u,
			CreatedAt:        &pict.CreatedAt,
		})
	}

	portalHospital.PortalHospital.Files = pictures

	// Get hosp station
	stations := make([]*gqlModel.PortalHospitalStation, 0, len(hospitalInfo.Stations))
	for _, station := range hospitalInfo.Stations {
		stations = append(stations, &gqlModel.PortalHospitalStation{
			StationID:     station.StationID,
			WalkingMinute: *station.WalkingMinute,
			StationName:   fmt.Sprintf("%s　%s", station.StationDetail.PortalMasterRailline.Name, station.StationDetail.Name),
		})
	}

	portalHospital.PortalHospital.HospitalStations = stations

	return &portalHospital, nil
}

func (s *portalHospitalService) GetPortalMasterStationByKeyword(_ context.Context, input string) ([]*custom.PortalMasterStation, error) {
	portalStations, err := s.repo.GetPortalMasterStationByKeyword(input)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return nil, err
	}

	return portalStations, nil
}

func (s *portalHospitalService) CreatePortalHospital(ctx context.Context, input gqlModel.UpdatePortalHospitalInput, hospitalID *int) error {

	session, err := session.GetSession(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	portalHospital, err := converter.ConvertGqlPortalHospitalToEntityModel(session, input, hospitalID, nil)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Update lat and long
	detail := ""

	if input.PortalHospitalInput.Address2 != nil {
		detail = util.ToFullwidth(*input.PortalHospitalInput.Address2)
	}

	addressDetail := fmt.Sprintf(constant.JapanAddress, util.FormatZipCode(input.PortalHospitalInput.PostCode), input.PortalHospitalInput.Address1, detail)

	latLogRes, err := s.GetLatitudeLongitudeByAddress(addressDetail)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	portalHospital.Latitude = latLogRes.Latitude
	portalHospital.Longitude = latLogRes.Longitude

	newHospital, err := s.repo.CreatePortalHospital(ctx, portalHospital)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Add hospital picture
	if len(input.AddedFiles) > 0 {
		hospitalPictures := make([]*custom.PortalHospitalPicture, 0, len(input.AddedFiles))
		for _, pict := range input.AddedFiles {
			s3Key, _ := util.GetS3KeyFromUploadURL(pict.UploadFileURL)
			ptrPicture := &entity.PortalPict{
				FileName: pict.OriginalFileName,
				Filepath: s3Key,
			}

			newPict, err := s.pictureRepo.UpdatePortalPicture(ctx, ptrPicture)
			if err != nil {
				return err
			}

			hospitalPicture := custom.PortalHospitalPicture{}
			hospitalPicture.PictID = newPict.PictID
			hospitalPicture.HospitalID = newHospital.HospitalID

			hospitalPictures = append(hospitalPictures, &hospitalPicture)
		}

		err := s.hospPictureRepo.UpdatePortalHospPictures(ctx, hospitalPictures)
		if err != nil {
			return err
		}

		portalHospital.Pictures = hospitalPictures
	}

	queueInput, err := s.hospSearchRepo.GenQueueInputFromHospital(newHospital, constant.ActionTypeCreatePortalHospital)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = s.queuingRepo.Publish(ctx, queueInput)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *portalHospitalService) ChangePortalHospitalInfo(ctx context.Context, input gqlModel.UpdatePortalHospitalInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalID == nil {
		return errors.New("hospitalId is nil")
	}

	portalHospital, err := s.repo.FindPortalHospitalByIDForUpdate(ctx, *hospitalID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		err := s.CreatePortalHospital(ctx, input, hospitalID)

		if err != nil {
			return err
		}
	} else {
		err := s.EditPortalHospital(ctx, hospitalID, input, portalHospital)

		if err != nil {
			return err
		}
	}
	return nil
}

func (s *portalHospitalService) EditPortalHospital(ctx context.Context, hospitalID *int, input gqlModel.UpdatePortalHospitalInput, portalHospital *custom.PortalHospital) error {

	session, err := session.GetSession(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	updatedPortalHospital, err := converter.ConvertGqlPortalHospitalToEntityModel(session, input, hospitalID, portalHospital)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Update scuel_id if hospital is active and scuel_id is nil
	updateScuelID := false
	if *updatedPortalHospital.IsActive && portalHospital.ScuelID == nil {
		updateScuelID = true
	}

	// Update lat and long
	detail := ""

	if input.PortalHospitalInput.Address2 != nil {
		detail = util.ToFullwidth(*input.PortalHospitalInput.Address2)
	}

	addressDetail := fmt.Sprintf(constant.JapanAddress, util.FormatZipCode(input.PortalHospitalInput.PostCode), input.PortalHospitalInput.Address1, detail)

	latLogRes, err := s.GetLatitudeLongitudeByAddress(addressDetail)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	updatedPortalHospital.Latitude = latLogRes.Latitude
	updatedPortalHospital.Longitude = latLogRes.Longitude

	updatedPortalHospital, err = s.repo.EditPortalHospital(ctx, updatedPortalHospital)

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, err, "病院情報"))
		}
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// remove relation import_hospital if updateScuelID is true
	if updateScuelID && updatedPortalHospital.ScuelID != nil {
		importHospital, err := s.importHospRepo.FindPortalImportHospital(ctx, updatedPortalHospital.ScuelID)
		if err != nil {
			return err
		}

		importHospitalQueueInput, err := s.hospSearchRepo.GenQueueInputFromImportHospital(importHospital, constant.ActionTypeDeleteImportPortalHospital)
		if err != nil {
			return err
		}

		err = s.queuingRepo.Publish(ctx, importHospitalQueueInput)
		if err != nil {
			return err
		}
	}

	// Delete hospital picture
	if len(input.DeletedFileIds) > 0 {
		for _, id := range input.DeletedFileIds {
			hospPicture := custom.PortalHospitalPicture{}
			hospPicture.HospitalID = portalHospital.HospitalID
			hospPicture.PictID = id

			err := s.hospPictureRepo.DeletePortalHospPicture(ctx, hospPicture)
			if err != nil {
				return err
			}

			deletePicture, err := s.pictureRepo.GetPortalPicture(id)
			if err != nil {
				return err
			}

			// Delete pict in s3
			err = util.DeleteS3ObjectURL(ctx, s.s3Client, os.Getenv("S3_HOSPITAL_BUCKET"), deletePicture.Filepath)
			if err != nil {
				return errors.New("can delete picture in s3")
			}

			err = s.pictureRepo.DeletePortalPicture(ctx, *deletePicture)
			if err != nil {
				return err
			}

		}
	}

	// Add new hospital picture
	if len(input.AddedFiles) > 0 {
		hospitalPictures := make([]*custom.PortalHospitalPicture, 0, len(input.AddedFiles))
		for _, pict := range input.AddedFiles {
			s3Key, _ := util.GetS3KeyFromUploadURL(pict.UploadFileURL)
			ptrPicture := &entity.PortalPict{
				FileName: pict.OriginalFileName,
				Filepath: s3Key,
			}

			newPict, err := s.pictureRepo.UpdatePortalPicture(ctx, ptrPicture)
			if err != nil {
				return err
			}

			hospitalPicture := custom.PortalHospitalPicture{}
			hospitalPicture.PictID = newPict.PictID
			hospitalPicture.HospitalID = portalHospital.HospitalID
			hospitalPicture.PictureDetail = *newPict

			hospitalPictures = append(hospitalPictures, &hospitalPicture)
		}

		err := s.hospPictureRepo.UpdatePortalHospPictures(ctx, hospitalPictures)
		if err != nil {
			return err
		} else {
			updatedPortalHospital.Pictures = hospitalPictures
		}
	}

	queueInput, err := s.hospSearchRepo.GenQueueInputFromHospital(updatedPortalHospital, constant.ActionTypeEditPortalHospital)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = s.queuingRepo.Publish(ctx, queueInput)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *portalHospitalService) GetPortalHospitalUploadFileURLs(ctx context.Context, input []*gqlModel.GetPortalHospitalUploadFileURLInput) ([]*gqlModel.GetPortalHospitalUploadFileURLRes, error) {
	result := make([]*gqlModel.GetPortalHospitalUploadFileURLRes, 0, len(input))

	if len(input) == 0 {
		return result, nil
	}

	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return result, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	asyncRes := util.WaitAllAsyncDone(input, s.getS3PresignPutURL(*hospitalID))

	for _, eachAsyncResult := range asyncRes {
		if eachAsyncResult.Error != nil {
			return nil, eachAsyncResult.Error
		}

		result = append(result, &gqlModel.GetPortalHospitalUploadFileURLRes{
			FileNameWithExtension: eachAsyncResult.FileNameWithExtension,
			URL:                   eachAsyncResult.URL,
		})
	}

	return result, nil
}

func (s *portalHospitalService) getS3PresignPutURL(hospitalID int) func(input *gqlModel.GetPortalHospitalUploadFileURLInput) model.GetS3UploadUrlResult {
	return func(input *gqlModel.GetPortalHospitalUploadFileURLInput) model.GetS3UploadUrlResult {
		u, err := util.GetS3PresignPutURL(os.Getenv("S3_HOSPITAL_BUCKET"), s.s3PresignClient,
			util.GenS3ObjectKey(hospitalID, util.FeatureNamePortalHospital, input.FileNameWithExtension),
			nil, constant.PresignPutUrlExpireDuration)

		return model.GetS3UploadUrlResult{
			Error:                 err,
			URL:                   u,
			FileNameWithExtension: input.FileNameWithExtension,
		}
	}
}

func (s *portalHospitalService) GetPortalHospitalFileURL(ctx context.Context, input gqlModel.GetPortalHospitalFileURLInput) (*string, error) {
	hospitalID, staffID, err := session.GetHospitalIDAndStaffID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil || staffID == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("hospitalID or staffID is null")))
	}

	hospitalInfo, err := s.repo.FindPortalHospitalByID(ctx, *hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, err, "病院情報"))
		}
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalInfo == nil {
		return nil, errors.New("not found portal hospital")
	}

	pict, err := s.pictureRepo.GetPortalPicture(input.FileID)

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if pict == nil {
		return nil, errors.New("not found picture")
	}

	u, err := util.GetS3FileURL(ctx, s.s3Client, s.s3PresignClient, os.Getenv("S3_HOSPITAL_BUCKET"), pict.Filepath)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return u, nil
}

func (s *portalHospitalService) GetLatitudeLongitudeByAddress(address string) (*model.PortalAddressWithLatLong, error) {
	u := fmt.Sprintf(constant.GetLatitudeLongitudeAPIUrl, url.QueryEscape(address), os.Getenv("GOOGLE_MAP_KEY"))

	res, err := util.HTTPClient.MakeHTTPRequest(http.MethodGet, u, nil, nil, nil)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	var response model.GeocodeResponse

	err = json.Unmarshal(res, &response)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if len(response.Results) == 0 {
		// 本来はdenkaru_errorを使う想定
		return nil, errors.New("no results found")
	}

	location := response.Results[0].Geometry.Location

	return &model.PortalAddressWithLatLong{
		Latitude:  location.Lat,
		Longitude: location.Lng,
	}, nil
}

func (s *portalHospitalService) UpdateHospitalStatusToOpenSearch(ctx context.Context, input gqlModel.UpdateHospitalStatusToOpenSearchInput) error {
	portalHospital, err := s.repo.FindPortalHospitalByID(ctx, input.HpID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	//portal_hospitalレコードない場合、正常系の結果を返すように
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}

	var portalHospitalAction int

	switch input.ActionStatus {
	case gqlModel.ActionStatusTypeCancel:
		portalHospitalAction = constant.ActionTypeDeletePortalHospital

		// Change import_hospital if have scuel_id in OS
		if portalHospital.ScuelID != nil {
			importHospital, err := s.importHospRepo.FindPortalImportHospital(ctx, portalHospital.ScuelID)
			if err != nil {
				return err
			}

			importHospitalQueueInput, err := s.hospSearchRepo.GenQueueInputFromImportHospital(importHospital, constant.ActionTypeCreateImportPortalHospital)
			if err != nil {
				return err
			}

			err = s.queuingRepo.Publish(ctx, importHospitalQueueInput)
			if err != nil {
				return err
			}
		}
	case gqlModel.ActionStatusTypeSuspend:
		portalHospitalAction = constant.ActionTypeEditPortalHospital
		portalHospital.IsActive = util.NewPtr(false)
	case gqlModel.ActionStatusTypeUnsuspend:
		portalHospitalAction = constant.ActionTypeEditPortalHospital
	}

	// Change portal_hospital in OS
	queueInput, err := s.hospSearchRepo.GenQueueInputFromHospital(portalHospital, portalHospitalAction)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = s.queuingRepo.Publish(ctx, queueInput)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *portalHospitalService) GetHospitalInfoByHomepageURL(ctx context.Context, URL string, prompt string) (*gqlModel.GetHospitalInfoByHomepageURLRes, error) {
	// validate URL
	parsedURL, err := url.Parse(URL)
	if err != nil || (parsedURL.Scheme != "http" && parsedURL.Scheme != "https") || parsedURL.Host == "" {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidHomePageURL, fmt.Errorf("invalid homepage url, %v", err)))
	}

	if s.isRestrictedHost(parsedURL.Hostname()) {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidHomePageURL, fmt.Errorf("internal or unsafe host: %s", parsedURL.Hostname())))
	}

	headers := map[string]string{
		"User-Agent": "AIChart/1.0",
		"Accept":     "text/html",
	}

	// request
	reqCtx, reqCancel := context.WithTimeout(ctx, 30*time.Second)
	defer reqCancel()

	respBody, err := util.HTTPClient.MakeHTTPRequestWithContext(
		reqCtx,
		http.MethodGet,
		URL,
		headers,
		nil,
		nil,
	)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeHospitalHomePageError, fmt.Errorf("failed to access homepage, err: %v, request headers: %v, response body: %v", err, headers, respBody)))
	}

	// clean html and extract title
	cleanText, err := s.extractCleanTextWithTitle(respBody)
	if err != nil {
		return nil, err
	}

	// generate description from cleaned HTML (prompt to Bedrock)
	description, err := s.generateHospitalDescription(ctx, cleanText, prompt)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeHospitalHomePageError, fmt.Errorf("generate description fail, err: %v", err)))
	}

	return &gqlModel.GetHospitalInfoByHomepageURLRes{
		Description: description,
	}, nil
}

func (s *portalHospitalService) extractCleanTextWithTitle(htmlContent []byte) (string, error) {
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(htmlContent))
	if err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("parse html fail, err: %v", err)))
	}
	pageTitle := doc.Find("title").First().Text()

	plainText, err := html2text.FromString(string(htmlContent), html2text.Options{PrettyTables: false})
	if err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("html to text fail, err: %v", err)))
	}

	var b strings.Builder
	if pageTitle != "" {
		b.WriteString("<title>")
		b.WriteString(pageTitle)
		b.WriteString("</title>\n\n")
	}
	b.WriteString(strings.TrimSpace(plainText))

	return b.String(), nil
}

func (s *portalHospitalService) generateHospitalDescription(ctx context.Context, homepageHTML string, prompt string) (string, error) {
	// タイムアウト付きコンテキスト
	aiCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	ch := make(chan *gqlModel.AIReport)
	resultCh := make(chan model.AIResult, 1)

	llmModelID := 4 // AWS Bedrock Claude3.5 Sonnet

	go func() {
		report, err := s.aiService.GenerateDataFromAi(ch, homepageHTML, prompt, llmModelID, false)

		for range ch {
		}

		resultCh <- model.AIResult{Report: report, Err: err}
	}()

	// Wait for either timeout or completion
	select {
	case <-aiCtx.Done():
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("bedrock data generation timed out after 30 seconds")))
	case result := <-resultCh:
		if result.Err != nil {
			return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("bedrock data generation failed, err: %v", result.Err)))
		}

		if result.Report.RawData != nil && *result.Report.RawData != "" {
			*result.Report.RawData = strings.TrimLeft(*result.Report.RawData, " ")
		}

		return *result.Report.RawData, nil
	}
}

func (s *portalHospitalService) isRestrictedHost(host string) bool {
	dangerousHosts := []string{
		"localhost", "0.0.0.0",
	}
	for _, dangerous := range dangerousHosts {
		if strings.EqualFold(host, dangerous) {
			return true
		}
	}

	if ip := net.ParseIP(host); ip != nil {
		return s.isUnsafeIP(ip)
	}

	ips, err := net.LookupIP(host)

	if err != nil {
		return true
	}
	return slices.ContainsFunc(ips, s.isUnsafeIP)
}

func (s *portalHospitalService) isUnsafeIP(ip net.IP) bool {
	return ip.IsLoopback() ||
		ip.IsPrivate() ||
		ip.IsLinkLocalUnicast() ||
		ip.IsUnspecified()
}

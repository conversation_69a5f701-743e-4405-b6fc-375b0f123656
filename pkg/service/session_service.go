package service

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/infra/client"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util/session"
	"fmt"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
)

const (
	// アイソル基準のセッションタイムアウト 60min
	sessionTTL = 3600
	// アイソル基準のリフレッシュトークン期限 8hour
	refreshTokenTTL = 28800
)

// ISessionService interface of SessionService
type ISessionService interface {
	CreateSession(ctx context.Context, sess *model.Session) (token, refreshToken string, tokenExpire, refreshTokenExpire time.Time, err error)
	RefreshSession(ctx context.Context, refreshTokenID string, isWebSocket bool) (token, refreshToken string, tokenExpire, refreshTokenExpire time.Time, err error)
	DeleteSession(ctx context.Context, key string) (isSessionRemaining bool, err error)
	GetSessionWithContext(ctx context.Context) (*model.Session, error)
	CheckToken(ctx context.Context, tokenID string) error
}

type sessionService struct {
	gqlClient client.IGraphqlClient
}

// NewSessionService return new ISessionService
func NewSessionService(url string) ISessionService {
	return &sessionService{
		gqlClient: client.NewGraphqlClient(url),
	}
}

// CreateSession トークン(セッション)作成
// 下記3つの情報がRedisに登録される
// 「アクセストークンのJWTのID」のレコード(データ：空、有効期限:sessionTTL)
// 「リフレッシュトークンのJWTのID」のレコード(データ：空、有効期限:refreshTokenTTL)
// 「logout_アクセストークンのJWTのID」のレコード(データ：「リフレッシュトークンのJWTのID」、有効期限:refreshTokenTTL)、ログアウトする時にリフレッシュトークンを無効化するために使用
func (service *sessionService) CreateSession(ctx context.Context, sess *model.Session) (token, refreshToken string, tokenExpire, refreshTokenExpire time.Time, err error) {

	// これを元にmutationが生成される
	var issuedToken struct {
		gqlModel.IssueTokenRes `graphql:"issueToken(input: $input)"`
	}
	req := gqlModel.IssueTokenReq{
		StaffID:         sess.StaffID,
		HospitalID:      sess.HospitalID,
		StaffUserID:     sess.StaffUserID,
		PharmacyFlg:     sess.PharmacyFlg,
		TokenTTL:        sessionTTL,
		RefreshTokenTTL: refreshTokenTTL,
		KarteStatus:     sess.KarteStatus,
	}

	err = service.gqlClient.Mutate(ctx, &issuedToken, map[string]interface{}{"input": req})
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return
	}

	token = issuedToken.Token
	refreshToken = issuedToken.RefreshToken

	tokenExpire, err = time.Parse(time.RFC3339Nano, issuedToken.TokenExpiryTime)
	if err != nil {
		err = fmt.Errorf("failed to parse issuedToken.TokenExpiryTime: %v, error: %w", issuedToken.TokenExpiryTime, err)
		return
	}

	refreshTokenExpire, err = time.Parse(time.RFC3339Nano, issuedToken.RefreshTokenExpiryTime)
	if err != nil {
		err = fmt.Errorf("failed to parse issuedToken.RefreshTokenExpiryTime: %v, error: %w", issuedToken.RefreshTokenExpiryTime, err)
		return
	}

	return
}

// RefreshSession セッション再作成
func (service *sessionService) RefreshSession(ctx context.Context, refreshToken string, isWebSocket bool) (newToken, newRefreshToken string, newTokenExpire, newRefreshTokenExpire time.Time, err error) {
	// これを元にmutationが生成される
	var refreshedToken struct {
		gqlModel.RefreshTokenRes `graphql:"refreshToken(input: $input)"`
	}
	req := gqlModel.RefreshTokenReq{
		RefreshToken:    refreshToken,
		TokenTTL:        sessionTTL,
		RefreshTokenTTL: refreshTokenTTL,
		IsWebSocket:     &isWebSocket,
	}

	err = service.gqlClient.Mutate(ctx, &refreshedToken, map[string]interface{}{"input": req})
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return
	}

	newToken = refreshedToken.Token
	newRefreshToken = refreshedToken.RefreshToken

	newTokenExpire, err = time.Parse(time.RFC3339Nano, refreshedToken.TokenExpiryTime)
	if err != nil {
		err = fmt.Errorf("failed to parse refreshedToken.TokenExpiryTime: %v, error: %w", refreshedToken.TokenExpiryTime, err)
		return
	}

	newRefreshTokenExpire, err = time.Parse(time.RFC3339Nano, refreshedToken.RefreshTokenExpiryTime)
	if err != nil {
		err = fmt.Errorf("failed to parse refreshedToken.RefreshTokenExpiryTime: %v, error: %w", refreshedToken.RefreshTokenExpiryTime, err)
		return
	}

	return
}

// DeleteSession IDからセッションを削除する
// リフレッシュトークンも合わせて削除して無効化する
func (service *sessionService) DeleteSession(ctx context.Context, token string) (isSessionRemaining bool, err error) {
	// これを元にmutationが生成される
	var deleteToken struct {
		gqlModel.DeleteTokenRes `graphql:"deleteToken(token: $token)"`
	}

	err = service.gqlClient.Mutate(ctx, &deleteToken, map[string]interface{}{"token": token})
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return
	}

	if !deleteToken.IsDeleted {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("delete session failed")))
		return
	}

	isSessionRemaining = deleteToken.IsSessionRemaining

	return
}

// GetSessionWithContext Get session with context
func (service *sessionService) GetSessionWithContext(ctx context.Context) (*model.Session, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return nil, err
	}

	return sess, nil
}

// CheckToken checking token is valid
func (service *sessionService) CheckToken(ctx context.Context, token string) (err error) {
	// これを元にmutationが生成される
	var verifiedToken struct {
		gqlModel.VerifyTokenRes `graphql:"verifyToken(input: $input)"`
	}
	req := gqlModel.VerifyTokenReq{
		Token: token,
	}
	err = service.gqlClient.Mutate(ctx, &verifiedToken, map[string]interface{}{"input": req})
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		return
	}

	if !verifiedToken.IsValid {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("token is invalid")))
		return
	}

	return
}

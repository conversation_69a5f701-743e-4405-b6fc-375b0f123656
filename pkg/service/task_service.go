package service

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/array"
	"denkaru-server/pkg/util/session"
	"fmt"
	"os"

	"github.com/cockroachdb/errors"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

// ITaskService interface of TaskService
type ITaskService interface {
	// task
	GetTask(ctx context.Context, taskID int) (*custom.Task, error)
	GetTaskHistoryPatients(ctx context.Context, taskHistories []custom.TaskHistory) ([]*custom.PtInf, error)
	GetTasks(ctx context.Context, input gqlModel.GetTasksInput) (tasks []custom.Task, err error)
	DeleteTask(ctx context.Context, taskID int) error
	CreateTask(ctx context.Context, task gqlModel.CreateTaskInput) (*custom.Task, error)
	EditTask(ctx context.Context, input gqlModel.EditTaskInput) error
	// task file
	GetTaskUploadFileURLs(ctx context.Context, input []*gqlModel.GetTaskUploadFileURLInput) ([]*gqlModel.GetTaskUploadFileURLRes, error)
	GetFileURL(ctx context.Context, input gqlModel.GetTaskFileURLInput) (url *string, err error)
	// task category
	CreateTaskCategory(ctx context.Context, input gqlModel.CreateTaskCategoryInput) (catg *custom.TaskCategory, err error)
	SortTaskCategories(ctx context.Context, input gqlModel.SortTaskCategoriesInput) error
	GetTaskCategoryWithCount(ctx context.Context, input gqlModel.GetTaskCategoryWithCountInput) (taskCategories []custom.TaskCategoryWithTaskCount, totalTaskCount int, err error)
	GetTaskCategories(ctx context.Context) ([]custom.TaskCategory, error)
	EditTaskCategory(ctx context.Context, input gqlModel.EditTaskCategoryInput) error
	DeleteTaskCategory(ctx context.Context, input gqlModel.DeleteTaskCategoryInput) error
	// task status
	GetTaskStatuses(ctx context.Context) ([]custom.TaskStatus, error)
	CreateTaskStatus(ctx context.Context, input gqlModel.CreateTaskStatusInput) (catg *custom.TaskStatus, err error)
	SortTaskStatuses(ctx context.Context, input gqlModel.SortTaskStatusesInput) error
	EditTaskStatus(ctx context.Context, input gqlModel.EditTaskStatusInput) error
	DeleteTaskStatus(ctx context.Context, input gqlModel.DeleteTaskStatusInput) error
	GetTaskStatusesWithTaskCount(hospitalID int, responsibleStaffID *int, taskCategoryID *int) (taskStatuses []custom.TaskStatusWithTaskCount, totalTaskCount int, err error)
}

type taskService struct {
	repo                repository.ITaskRepository
	taskFileRepo        repository.ITaskFileRepository
	staffRepo           repository.IStaffRepository
	hospitalRepo        repository.IHospitalRepository
	taskCommentRepo     repository.ITaskCommentRepository
	taskCategoryRepo    repository.ITaskCategoryRepository
	taskHistoryRepo     repository.ITaskHistoryRepository
	taskStatusRepo      repository.ITaskStatusRepository
	patientRepo         repository.IPatientRepository
	taskCommentFileRepo repository.ITaskCommentFileRepository
	s3Client            *s3.Client
	s3PresignClient     *s3.PresignClient
}

// NewTaskService return new TaskService
func NewTaskService(repo repository.ITaskRepository, taskFileRepo repository.ITaskFileRepository, staffRepo repository.IStaffRepository, hospitalRepo repository.IHospitalRepository,
	taskCommentRepo repository.ITaskCommentRepository, taskCategoryRepo repository.ITaskCategoryRepository, taskStatusRepo repository.ITaskStatusRepository, patientRepo repository.IPatientRepository,
	taskHistoryRepo repository.ITaskHistoryRepository,
	taskCommentFileRepo repository.ITaskCommentFileRepository,
	s3Client *s3.Client, s3PresignClient *s3.PresignClient) ITaskService {
	return &taskService{
		repo:                repo,
		taskFileRepo:        taskFileRepo,
		staffRepo:           staffRepo,
		hospitalRepo:        hospitalRepo,
		taskCommentRepo:     taskCommentRepo,
		taskCategoryRepo:    taskCategoryRepo,
		taskStatusRepo:      taskStatusRepo,
		patientRepo:         patientRepo,
		taskHistoryRepo:     taskHistoryRepo,
		taskCommentFileRepo: taskCommentFileRepo,
		s3Client:            s3Client,
		s3PresignClient:     s3PresignClient,
	}
}

func (service *taskService) GetTasks(ctx context.Context, input gqlModel.GetTasksInput) (tasks []custom.Task, err error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("not found hospitalId")))
	}

	condition := custom.GetTasksCondition{
		HospitalID:         hospitalID,
		ResponsibleStaffID: input.ResponsibleStaffID,
		TaskCategoryID:     input.CategoryID,
		TaskStatusID:       input.StatusID,
		PerPage:            input.Limit,
		CursorID:           input.CursorID,
		CursorDate:         input.CursorDate,
		SortOrder:          input.Order,
		SortType:           input.Sort,
		PatientID:          input.PatientID,
	}

	tasks, err = service.repo.GetTasksAndAssociations(condition)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return tasks, nil
}

func (service *taskService) DeleteTask(ctx context.Context, taskID int) error {
	sess, err := session.GetSession(ctx)
	if err != nil {
		var denkaruError *myerrors.DenkaruError
		if !errors.As(err, &denkaruError) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
		err = errors.Join(err)
		return err
	}
	if sess.IsEmptyStaffSession() {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("invalid session")))
	}
	hospitalID := sess.HospitalID
	staffID := sess.StaffID

	staff, err := service.staffRepo.FindStaffByStaffID(*staffID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if staff == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のスタッフは見つかりません"), "スタッフ"))
	}

	_, err = service.hospitalRepo.FindByID(ctx, *hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の病院は見つかりません"), "病院"))
			return err
		}
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	task, err := service.repo.GetTaskByID(taskID, *hospitalID)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if task == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のタスクは見つかりません"), "タスク"))
	}

	err = task.ValidateTaskDeletePermission(*staff)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeDeleteTaskPermissionError, err))
	}

	_, err = service.taskHistoryRepo.CreateDeletedTaskHistory(ctx, *task, *staff)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	taskCommentFiles, err := service.taskCommentFileRepo.FindTaskCommentFilesByTaskID(ctx, taskID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = service.taskCommentFileRepo.DeleteTaskCommentFiles(ctx, taskCommentFiles)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = service.repo.DeleteTask(ctx, *task)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) CreateTask(ctx context.Context, input gqlModel.CreateTaskInput) (*custom.Task, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		var denkaruError *myerrors.DenkaruError
		if !errors.As(err, &denkaruError) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
		err = errors.Join(err)
		return nil, err
	}
	if sess.IsEmptyStaffSession() {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("invalid session")))
	}
	hospitalID := sess.HospitalID
	staffID := sess.StaffID

	staff, err := service.staffRepo.FindStaffByStaffID(*staffID)

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if staff == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のスタッフは見つかりません"), "スタッフ"))
	}

	hospital, err := service.hospitalRepo.FindByID(ctx, *hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の病院は見つかりません"), "病院"))
			return nil, err
		}
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	task := custom.Task{}

	err = task.MutateFromTaskInput(*input.TaskInput, *staff, *hospital, true)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	task.TaskFiles = array.Map(input.TaskFileInput, func(taskFileInput *gqlModel.TaskFileInput) custom.TaskFile {
		s3Key, _ := util.GetS3KeyFromUploadURL(taskFileInput.UploadFileURL)
		return custom.TaskFile{
			TaskFile: entity.TaskFile{
				OriginalFileName: taskFileInput.OriginalFileName,
				S3Key:            s3Key,
				Size:             taskFileInput.FileSize,
			},
		}
	})

	err = service.repo.CreateTask(ctx, &task)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return &task, err
}

func (service *taskService) GetTask(ctx context.Context, taskID int) (*custom.Task, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.New("hospitalID is nil")
	}

	condition := custom.GetTasksCondition{
		HospitalID: hospitalID,
		TaskIDs:    &[]int{taskID},
	}

	tasks, err := service.repo.GetTasksAndAssociations(condition)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if len(tasks) == 0 {
		// 対象のタスクは存在しません
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のタスクは見つかりません"), "タスク"))
	}

	return &tasks[0], nil
}

func (service *taskService) GetTaskHistoryPatients(ctx context.Context, taskHistories []custom.TaskHistory) ([]*custom.PtInf, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("hospitalID is nil")))
	}

	patientIDs := array.FlatMap(taskHistories, func(his custom.TaskHistory) []int {
		historyJSON := gjson.Parse(string(his.History))
		return []int{
			int(historyJSON.Get("Old.PatientID").Int()),
			int(historyJSON.Get("New.PatientID").Int()),
		}
	})

	params := model.PatientSearchParams{
		HospitalId: *hospitalID,
		PatientIds: &patientIDs,
		Keyword:    "",
	}

	patients, err := service.patientRepo.GetPatientsByConditions(params)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return patients, nil
}

func (service *taskService) EditTask(ctx context.Context, input gqlModel.EditTaskInput) error {
	sess, err := session.GetSession(ctx)
	if err != nil {
		var denkaruError *myerrors.DenkaruError
		if !errors.As(err, &denkaruError) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
		err = errors.Join(err)
		return err
	}
	if sess.IsEmptyStaffSession() {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("invalid session")))
	}
	hospitalID := sess.HospitalID
	staffID := sess.StaffID

	staff, err := service.staffRepo.FindStaffByStaffID(*staffID)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if staff == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("not found staff")))
	}

	hospital, err := service.hospitalRepo.FindByID(ctx, *hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の病院は見つかりません"), "病院"))
			return err
		}
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	task, err := service.repo.GetTaskByID(input.TaskID, *hospitalID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if task == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のタスクは見つかりません"), "タスク"))
	}
	// DBの結果を、オリジナルとして保持するためにコピーする
	// shallow copyであるので、注意
	originalTask := *task

	err = task.MutateFromTaskInput(*input.TaskInput, *staff, *hospital, false)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	var deletedTaskFiles []custom.TaskFile

	if input.DeletedTaskFileIds != nil && len(input.DeletedTaskFileIds) != 0 {
		deletedTaskFilesCondition := custom.GetTaskFileCondition{
			IDs: &input.DeletedTaskFileIds,
		}

		deletedTaskFiles, err = service.taskFileRepo.GetTaskFiles(deletedTaskFilesCondition)
		if err != nil {
			return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
	}

	err = service.taskFileRepo.DeleteTaskFiles(ctx, input.DeletedTaskFileIds)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	task.TaskFiles = array.Map(input.AddedTaskFiles, func(taskFileInput *gqlModel.TaskFileInput) custom.TaskFile {
		s3Key, _ := util.GetS3KeyFromUploadURL(taskFileInput.UploadFileURL)
		return custom.TaskFile{
			TaskFile: entity.TaskFile{
				OriginalFileName: taskFileInput.OriginalFileName,
				S3Key:            s3Key,
				Size:             taskFileInput.FileSize,
			},
		}
	})

	err = service.repo.UpdateTask(ctx, task)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	_, err = service.taskHistoryRepo.CreateTaskHistory(ctx, *task, originalTask, deletedTaskFiles, *staff)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) GetTaskCategoryWithCount(ctx context.Context, input gqlModel.GetTaskCategoryWithCountInput) (taskCategories []custom.TaskCategoryWithTaskCount, totalTaskCount int, err error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("hospitalID or staffID is nil")))
	}
	taskCategories, err = service.taskCategoryRepo.GetTaskCategoryWithCount(*hospitalID, input.ResponsibleStaffID)
	if err != nil {
		return nil, 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	totalTaskCount, err = service.repo.GetCountByCondition(nil, input.ResponsibleStaffID, *hospitalID)
	if err != nil {
		return nil, 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	return taskCategories, totalTaskCount, nil
}

func (service *taskService) GetTaskUploadFileURLs(ctx context.Context, input []*gqlModel.GetTaskUploadFileURLInput) ([]*gqlModel.GetTaskUploadFileURLRes, error) {
	result := make([]*gqlModel.GetTaskUploadFileURLRes, 0, len(input))

	if len(input) == 0 {
		return result, nil
	}

	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return result, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	asyncRes := util.WaitAllAsyncDone(input, service.getS3PresignPutURL(*hospitalID))

	for _, eachAsyncResult := range asyncRes {
		if eachAsyncResult.Error != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, eachAsyncResult.Error))
		}

		result = append(result, &gqlModel.GetTaskUploadFileURLRes{
			FileNameWithExtension: eachAsyncResult.FileNameWithExtension,
			URL:                   eachAsyncResult.URL,
		})
	}

	return result, nil
}

func (service *taskService) getS3PresignPutURL(hospitalID int) func(input *gqlModel.GetTaskUploadFileURLInput) model.GetS3UploadUrlResult {
	return func(input *gqlModel.GetTaskUploadFileURLInput) model.GetS3UploadUrlResult {
		url, err := util.GetS3PresignPutURL(os.Getenv("S3_TASK_BUCKET"), service.s3PresignClient,
			util.GenS3ObjectKey(hospitalID, util.FeatureNameTask, input.FileNameWithExtension),
			nil, constant.PresignPutUrlExpireDuration)

		return model.GetS3UploadUrlResult{
			Error:                 err,
			URL:                   url,
			FileNameWithExtension: input.FileNameWithExtension,
		}
	}
}

func (service *taskService) CreateTaskCategory(ctx context.Context, input gqlModel.CreateTaskCategoryInput) (*custom.TaskCategory, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.New("not found hospitalID")
	}

	err = service.validateCreateTaskCategoryInput(ctx, *hospitalID, input.Name)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	order, err := service.getNextTaskCategoryOrder(ctx, *hospitalID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	catg := custom.TaskCategory{
		TaskCategory: entity.TaskCategory{
			HospitalID: *hospitalID,
			Name:       input.Name,
			Order_:     int16(order),
		},
	}

	err = service.taskCategoryRepo.CreateTaskCategory(ctx, &catg)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return &catg, nil
}

func (service *taskService) SortTaskCategories(ctx context.Context, input gqlModel.SortTaskCategoriesInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	condition := &custom.FindTaskCategoryCondition{
		TaskCategoryIDs: &input.TaskCategoryIds,
		HospitalID:      hospitalID,
	}

	catgs, err := service.taskCategoryRepo.GetTaskCategories(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	for index, catg := range catgs {
		order := array.FindIndex(input.TaskCategoryIds, func(catgId int) bool {
			return catgId == catg.TaskCategoryID
		})

		if order == constant.FindIndexNotFound {
			return errors.New("not found order of taskCategory")
		}

		catgs[index].Order_ = int16(order + 1)
	}

	err = service.taskCategoryRepo.UpdateTaskCategories(ctx, catgs)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) CreateTaskStatus(ctx context.Context, input gqlModel.CreateTaskStatusInput) (catg *custom.TaskStatus, err error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.New("not found hospitalID")
	}

	err = service.validateCreateTaskStatus(ctx, *hospitalID, input.Name)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	color, err := service.getNextStatusColor(ctx, *hospitalID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	order, err := service.getNextTaskStatusOrder(ctx, *hospitalID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	status := custom.TaskStatus{
		TaskStatus: entity.TaskStatus{
			HospitalID: *hospitalID,
			Name:       input.Name,
			Color:      *color,
			Order_:     int16(order),
		},
	}

	err = service.taskStatusRepo.CreateTaskStatus(ctx, &status)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return &status, nil
}

func (service *taskService) GetTaskCategories(ctx context.Context) ([]custom.TaskCategory, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.New("not found hospitalID")
	}

	condition := &custom.FindTaskCategoryCondition{
		HospitalID: hospitalID,
	}
	catgs, err := service.taskCategoryRepo.GetTaskCategories(condition)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return catgs, nil
}

func (service *taskService) EditTaskCategory(ctx context.Context, input gqlModel.EditTaskCategoryInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	condition := &custom.FindTaskCategoryCondition{
		TaskCategoryID: &input.TaskCategoryID,
		HospitalID:     hospitalID,
	}

	catg, err := service.taskCategoryRepo.GetFirstTaskCategory(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if catg == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound,
			fmt.Errorf("対象のカテゴリーは見つかりません"),
			"カテゴリー"))
	}

	if catg.IsDefaultCategory() {
		return errors.New("can not edit default task category")
	}

	condition = &custom.FindTaskCategoryCondition{
		HospitalID: hospitalID,
		Name:       &input.Name,
	}
	existedCatg, err := service.taskCategoryRepo.GetFirstTaskCategory(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if existedCatg != nil && existedCatg.TaskCategoryID != catg.TaskCategoryID {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeTaskStatusTitleAlreadyExist, fmt.Errorf("タスクカテゴリー名重複")))
	}

	catg.Name = input.Name
	err = service.taskCategoryRepo.UpdateTaskCategory(ctx, catg)
	return err
}

func (service *taskService) DeleteTaskCategory(ctx context.Context, input gqlModel.DeleteTaskCategoryInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	catgCondition := &custom.FindTaskCategoryCondition{
		HospitalID:      hospitalID,
		TaskCategoryIDs: &[]int{input.TaskCategoryID},
	}

	catg, err := service.taskCategoryRepo.GetFirstTaskCategory(catgCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if catg == nil {
		return nil
	}

	if catg.IsDefaultCategory() {
		return errors.New("can not delete default task category")
	}

	tasksCondition := custom.GetTasksCondition{
		TaskCategoryID: &input.TaskCategoryID,
	}

	tasks, err := service.repo.GetTasks(tasksCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	catgCondition = &custom.FindTaskCategoryCondition{
		HospitalID: hospitalID,
		Name:       &constant.DefaultTaskCategoryNames[0],
	}

	defaultCatg, err := service.taskCategoryRepo.GetFirstTaskCategory(catgCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if defaultCatg == nil {
		return errors.New("not found default task category")
	}

	for index := range tasks {
		tasks[index].TaskCategoryID = defaultCatg.TaskCategoryID
	}

	err = service.repo.UpdateTasks(ctx, &tasks)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = service.taskCategoryRepo.DeleteTaskCategory(ctx, catg)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) GetTaskStatuses(ctx context.Context) ([]custom.TaskStatus, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return nil, errors.New("not found hospitalID")
	}

	condition := custom.FindTaskStatusCondition{
		HospitalID: hospitalID,
	}
	catgs, err := service.taskStatusRepo.GetTaskStatuses(condition)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return catgs, nil
}

func (service *taskService) SortTaskStatuses(ctx context.Context, input gqlModel.SortTaskStatusesInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	condition := custom.FindTaskStatusCondition{
		TaskStatusIDs: &input.TaskStatusIds,
		HospitalID:    hospitalID,
	}

	statuses, err := service.taskStatusRepo.GetTaskStatuses(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	for index, status := range statuses {
		order := array.FindIndex(input.TaskStatusIds, func(taskStatusId int) bool {
			return taskStatusId == status.TaskStatusID
		})

		if order == constant.FindIndexNotFound {
			return errors.New("not found order of taskStatus")
		}

		statuses[index].Order_ = int16(order + 1)
	}

	err = service.taskStatusRepo.UpdateTaskStatuses(ctx, &statuses)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) EditTaskStatus(ctx context.Context, input gqlModel.EditTaskStatusInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	condition := custom.FindTaskStatusCondition{
		TaskStatusID: &input.TaskStatusID,
		HospitalID:   hospitalID,
	}

	status, err := service.taskStatusRepo.GetFirstTaskStatus(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if status == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のステータス見つかりません"), "タスクステータス"))
	}

	if status.IsDefaultStatus() {
		return errors.New("can not edit default task status")
	}

	condition = custom.FindTaskStatusCondition{
		HospitalID: hospitalID,
		Name:       &input.Name,
	}
	existedStatus, err := service.taskStatusRepo.GetFirstTaskStatus(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if existedStatus != nil && existedStatus.TaskStatusID != status.TaskStatusID {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeTaskStatusTitleAlreadyExist, fmt.Errorf("タスクステータス名重複")))
	}

	status.Name = input.Name
	err = service.taskStatusRepo.UpdateTaskStatus(ctx, status)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) DeleteTaskStatus(ctx context.Context, input gqlModel.DeleteTaskStatusInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil {
		return errors.New("not found hospitalID")
	}

	statusCondition := custom.FindTaskStatusCondition{
		HospitalID:   hospitalID,
		TaskStatusID: &input.TaskStatusID,
	}

	status, err := service.taskStatusRepo.GetFirstTaskStatus(statusCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if status == nil {
		return nil
	}

	if status.IsDefaultStatus() {
		return errors.New("can not delete default task status")
	}

	tasksCondition := custom.GetTasksCondition{
		TaskStatusID: &input.TaskStatusID,
	}

	tasks, err := service.repo.GetTasks(tasksCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	todoStatusName := constant.TodoTaskStatusName
	statusCondition = custom.FindTaskStatusCondition{
		HospitalID: hospitalID,
		Name:       &todoStatusName,
	}

	todoStatus, err := service.taskStatusRepo.GetFirstTaskStatus(statusCondition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if todoStatus == nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("not found default todo task status")))
	}

	for index := range tasks {
		tasks[index].TaskStatusID = todoStatus.TaskStatusID
	}

	err = service.repo.UpdateTasks(ctx, &tasks)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	err = service.taskStatusRepo.DeleteTaskStatus(ctx, *status)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (service *taskService) GetFileURL(ctx context.Context, input gqlModel.GetTaskFileURLInput) (*string, error) {
	hospitalID, staffID, err := session.GetHospitalIDAndStaffID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil || staffID == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("hospitalID or staffid is nil")))
	}

	hospital, err := service.hospitalRepo.FindByID(ctx, *hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の病院は見つかりません"), "病院"))
			return nil, err
		}
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospital == nil {
		return nil, errors.New("not found hospital")
	}

	staff, err := service.staffRepo.FindStaffByStaffID(*staffID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if staff == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("not found staff")))
	}

	taskFile, err := service.taskFileRepo.GetFirstTaskFile(custom.GetTaskFileCondition{ID: &input.TaskFileID})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のタスクは見つかりません"), "タスク"))
		}
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if taskFile == nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("not found taskFile")))
	}

	if taskFile.Task.Hospital.HpID != staff.HpID {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeFileAccessPermissionError, fmt.Errorf("異なるhpIDのタスクです")))
	}

	url, err := util.GetS3FileURL(ctx, service.s3Client, service.s3PresignClient, os.Getenv("S3_TASK_BUCKET"), taskFile.S3Key)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return url, nil
}

func (service *taskService) validateCreateTaskCategoryInput(_ context.Context, hospitalID int, catgName string) error {
	condition := &custom.FindTaskCategoryCondition{
		HospitalID: &hospitalID,
		Name:       &catgName,
	}

	// check name and hospitalID is uniq
	catg, err := service.taskCategoryRepo.GetFirstTaskCategory(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if catg != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("same taskCategory name is already existed")))

	}

	return nil
}

func (service *taskService) validateCreateTaskStatus(_ context.Context, hospitalID int, statusName string) error {
	condition := custom.FindTaskStatusCondition{
		HospitalID: &hospitalID,
		Name:       &statusName,
	}

	// check name and hospitalID is uniq
	status, err := service.taskStatusRepo.GetFirstTaskStatus(condition)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if status != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("same taskStatus name is already existed")))
	}

	// check max count of status
	statuses, err := service.taskStatusRepo.GetTaskStatuses(custom.FindTaskStatusCondition{HospitalID: &hospitalID})
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if len(statuses) >= constant.TaskStatusMax {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("taskStatus reached max number")))
	}

	return nil
}

func (service *taskService) getNextStatusColor(_ context.Context, hospitalID int) (*string, error) {
	condition := custom.FindTaskStatusCondition{
		HospitalID: &hospitalID,
	}

	statuses, err := service.taskStatusRepo.GetTaskStatuses(condition)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	dbColors := array.Map(statuses, func(status custom.TaskStatus) string {
		return status.Color
	})

	diffColors := array.Diff(constant.TaskStatusColors, dbColors)

	if len(diffColors) == 0 {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, errors.New("can not get next color of taskStatus")))
	}

	color := diffColors[0]
	return &color, nil
}

func (service *taskService) getNextTaskStatusOrder(_ context.Context, hospitalID int) (int, error) {
	condition := custom.FindTaskStatusCondition{
		HospitalID: &hospitalID,
	}

	maxOrder, err := service.taskStatusRepo.GetMaxOrder(condition)
	if err != nil {
		return 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	nextOrder := maxOrder + 1

	return nextOrder, nil
}

func (service *taskService) getNextTaskCategoryOrder(_ context.Context, hospitalID int) (int, error) {
	condition := &custom.FindTaskCategoryCondition{
		HospitalID: &hospitalID,
	}

	maxOrder, err := service.taskCategoryRepo.GetMaxOrder(condition)
	if err != nil {
		return 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	nextOrder := maxOrder + 1

	return nextOrder, nil
}

func (service *taskService) GetTaskStatusesWithTaskCount(hospitalID int, responsibleStaffID *int, taskCategoryID *int) (taskStatuses []custom.TaskStatusWithTaskCount, totalTaskCount int, err error) {

	taskStatuses, err = service.taskStatusRepo.GetTaskStatusesWithTaskCount(hospitalID, responsibleStaffID, taskCategoryID)
	if err != nil {
		return nil, 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	for _, status := range taskStatuses {
		totalTaskCount += status.TaskCount
	}

	return taskStatuses, totalTaskCount, nil
}

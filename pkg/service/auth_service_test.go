package service

import (
	"context"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mockRepository "denkaru-server/pkg/test_mock/repository"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/session"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_authService_DeleteToken(t *testing.T) {
	t.<PERSON>llel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx     context.Context
		tokenID string
		sess    *model.Session
	}

	type want struct {
		isSessionRemaining bool
		err                error
	}

	hospitalID := 1
	staffID := 1

	tests := []struct {
		name                  string
		args                  args
		getRefreshTokenID     string
		redisGetError         error
		redisDeleteError      error
		redisDeleteDelayCount int
		redisScanKeyNum       int
		redisScanError        error
		want                  want
	}{
		{
			name: "正常系_AIチャート_別セッションあり",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			getRefreshTokenID: "fugafuga",
			redisScanKeyNum:   1, // 1以上
			redisScanError:    nil,
			want: want{
				isSessionRemaining: true, // 別セッションが残っている
				err:                nil,
			},
		}, {
			name: "正常系_AIチャート_別セッションなし",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					StaffID: &staffID,
				},
			},
			getRefreshTokenID: "fugafuga",
			redisScanKeyNum:   0,
			redisScanError:    nil,
			want: want{
				isSessionRemaining: false, // 別セッションが残っていない
				err:                nil,
			},
		},
		{
			name: "正常系_AIチャート以外",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess:    &model.Session{
					// AIチャート以外はhospitalIDが空
				},
			},
			getRefreshTokenID: "fugafuga",
			redisScanKeyNum:   0,
			redisScanError:    nil,
			want: want{
				isSessionRemaining: false, // AIチャート以外は別セッションが残っていない
				err:                nil,
			},
		},
		{
			name: "redis Getエラー",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			redisScanKeyNum: 0,
			redisScanError:  nil,
			redisGetError:   fmt.Errorf("redis Get Error"),
			want: want{
				isSessionRemaining: false,
				err:                myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Get Error")),
			},
		},
		{
			name: "redis Deleteエラー(アクセストークン)",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			redisDeleteError:      fmt.Errorf("redis Delete Error1"),
			redisDeleteDelayCount: 0,
			want: want{
				isSessionRemaining: false,
				err:                myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Delete Error1")),
			},
		},
		{
			name: "redis Deleteエラー(リフレッシュトークン)",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			redisDeleteError:      fmt.Errorf("redis Delete Error2"),
			redisDeleteDelayCount: 1,
			want: want{
				isSessionRemaining: false,
				err:                myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Delete Error2")),
			},
		},
		{
			name: "redis Deleteエラー(アクセストークンとリフレッシュトークンの紐付け)",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			redisDeleteError:      fmt.Errorf("redis Delete Error3"),
			redisDeleteDelayCount: 2,
			want: want{
				isSessionRemaining: false,
				err:                myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Delete Error3")),
			},
		},
		{
			name: "redis Scanエラー",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
				sess: &model.Session{
					HospitalID: &hospitalID,
					StaffID:    &staffID,
				},
			},
			redisDeleteError:      nil,
			redisDeleteDelayCount: 0,
			redisScanError:        fmt.Errorf("redis Scan Error"),
			want: want{
				isSessionRemaining: false,
				err:                myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Scan Error")),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			service := &authService{
				repo: sessionRepo,
			}

			sessionRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (err error) {
				if tt.redisDeleteDelayCount == 0 {
					return tt.redisDeleteError
				}
				tt.redisDeleteDelayCount--
				return nil
			}).AnyTimes()
			sessionRepo.EXPECT().Get(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (value string, err error) {
				return tt.getRefreshTokenID, tt.redisGetError
			}).AnyTimes()

			sessionRepo.EXPECT().Scan(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (*int, error) {
				return &tt.redisScanKeyNum, tt.redisScanError
			}).AnyTimes()

			got, err := service.DeleteToken(tt.args.ctx, tt.args.tokenID, tt.args.sess)
			if tt.want.err != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.want.err, denkaruError)
				assert.Equal(t, tt.want.isSessionRemaining, got)
			} else {
				assert.Nil(t, err)
				assert.Equal(t, tt.want.isSessionRemaining, got)
			}
		})
	}
}

func Test_authService_IssueToken(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx         context.Context
		isWebSocket bool
	}
	hospitalID := 1
	staffID := 2
	userID := 2
	email := "<EMAIL>"
	portalCustomerID := 3

	tests := []struct {
		args                 args
		name                 string
		sess                 *model.Session
		wantSessionID        string
		redisError           error
		redisErrorDelayCount int
		wantErr              error
	}{
		{
			name: "正常系、denkaruサーバー",
			args: args{
				ctx: context.Background(),
			},
			sess: &model.Session{
				StaffID:     &staffID,
				HospitalID:  &hospitalID,
				StaffUserID: &userID,
			},
			wantErr: nil,
		},
		{
			name: "正常系、denkaruサーバー, isWebSocket=true",
			args: args{
				ctx:         context.Background(),
				isWebSocket: true,
			},
			sess: &model.Session{
				StaffID:     &staffID,
				HospitalID:  &hospitalID,
				StaffUserID: &userID,
			},
			wantErr: nil,
		},
		{
			name: "正常系、portalサーバー",
			args: args{
				ctx: context.Background(),
			},
			sess: &model.Session{
				Email:            &email,
				PortalCustomerID: &portalCustomerID,
			},
			wantErr: nil,
		},
		{
			name: "正常系、KarteStatusが設定されている場合",
			args: args{
				ctx: context.Background(),
			},
			sess: func() *model.Session {
				karteStatus := 1
				return &model.Session{
					StaffID:     &staffID,
					HospitalID:  &hospitalID,
					StaffUserID: &userID,
					KarteStatus: &karteStatus,
				}
			}(),
			wantErr: nil,
		},
		{
			name: "redisへのSetエラー",
			args: args{
				ctx: context.Background(),
			},
			sess: &model.Session{
				StaffID:     &staffID,
				HospitalID:  &hospitalID,
				StaffUserID: &userID,
			},
			redisError: fmt.Errorf("redis Set Error"),
			wantErr:    myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Set Error")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			service := &authService{
				repo: sessionRepo,
			}

			sessionRepo.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ string, _ time.Duration) (err error) {
				if tt.redisErrorDelayCount == 0 {
					return tt.redisError
				}
				tt.redisErrorDelayCount--
				return nil
			}).AnyTimes()

			// Do
			token, tokenExpire, refreshToken, refreshTokenExpire, err := service.IssueToken(tt.args.ctx, tt.sess, sessionTTL, refreshTokenTTL, tt.args.isWebSocket)

			// Assert
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
				// tokenのassert
				gotSess, gotClaims, extractErr := session.ExtractJWTToken(token)
				assert.NoError(t, extractErr)
				if tt.sess.StaffID != nil {
					assert.Equal(t, *tt.sess.StaffID, *gotSess.StaffID)
					assert.Equal(t, float64(*tt.sess.StaffID), gotClaims["staffID"])
				} else {
					assert.Nil(t, gotSess.StaffID)
					assert.NotContains(t, gotClaims, "staffId")
				}
				if tt.sess.HospitalID != nil {
					assert.Equal(t, *tt.sess.HospitalID, *gotSess.HospitalID)
					assert.Equal(t, float64(*tt.sess.HospitalID), gotClaims["hospitalID"])
				} else {
					assert.Nil(t, gotSess.HospitalID)
					assert.NotContains(t, gotClaims, "hospitalID")
				}
				if tt.sess.StaffUserID != nil {
					assert.Equal(t, *tt.sess.StaffUserID, *gotSess.StaffUserID)
					assert.Equal(t, float64(*tt.sess.StaffUserID), gotClaims["staffUserID"])
				} else {
					assert.Nil(t, gotSess.HospitalID)
					assert.NotContains(t, gotClaims, "hospitalID")
				}
				if tt.sess.Email != nil {
					assert.Equal(t, *tt.sess.Email, *gotSess.Email)
					assert.Equal(t, *tt.sess.Email, gotClaims["email"])
				} else {
					assert.Nil(t, gotSess.Email)
					assert.NotContains(t, gotClaims, "email")
				}
				if tt.sess.PortalCustomerID != nil {
					assert.Equal(t, *tt.sess.PortalCustomerID, *gotSess.PortalCustomerID)
					assert.Equal(t, float64(*tt.sess.PortalCustomerID), gotClaims["portalCustomerID"])
				} else {
					assert.Nil(t, gotSess.PortalCustomerID)
					assert.NotContains(t, gotClaims, "portalCustomerID")
				}

				// Add KarteStatus assertion
				if tt.sess.KarteStatus != nil {
					assert.Equal(t, *tt.sess.KarteStatus, *gotSess.KarteStatus)
					assert.Equal(t, float64(*tt.sess.KarteStatus), gotClaims["karteStatus"])
				} else {
					assert.Nil(t, gotSess.KarteStatus)
					assert.NotContains(t, gotClaims, "karteStatus")
				}

				// refreshTokenのassert
				refreshSess, _, extractErr := session.ExtractJWTToken(refreshToken)
				assert.NoError(t, extractErr)
				assert.Nil(t, refreshSess.HospitalID)       // リフレッシュトークンにセッションは設定しないのでnilになる
				assert.Nil(t, refreshSess.StaffID)          // リフレッシュトークンにセッションは設定しないのでnilになる
				assert.Nil(t, refreshSess.Email)            // リフレッシュトークンにセッションは設定しないのでnilになる
				assert.Nil(t, refreshSess.PortalCustomerID) // リフレッシュトークンにセッションは設定しないのでnilになる

				// tokenExpire, refreshTokenExpireのassert
				assert.NotEmpty(t, tokenExpire)
				assert.NotEmpty(t, refreshTokenExpire)
			}
		})
	}
}

func Test_authService_RefreshToken(t *testing.T) {

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	hospitalID := 1
	staffID := 2
	karteStatus := 1

	type args struct {
		ctx            context.Context
		refreshTokenID string
		isWebSocket    bool
	}
	tests := []struct {
		name             string
		args             args
		getSession       *model.Session
		getSessionError  error
		redisDeleteError error
		redisSetError    error
		wantSession      *model.Session
		wantErr          error
	}{
		{
			name: "正常系",
			args: args{
				ctx:            context.Background(),
				refreshTokenID: "bbbb",
			},
			getSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			wantSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			wantErr: nil,
		},
		{
			name: "正常系, isWebSocket=true",
			args: args{
				ctx:            context.Background(),
				refreshTokenID: "bbbb",
				isWebSocket:    true,
			},
			getSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			wantSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			wantErr: nil,
		},
		{
			name: "正常系、KarteStatus付きのセッション",
			args: args{
				ctx:            context.Background(),
				refreshTokenID: "bbbb",
			},
			getSession: &model.Session{
				HospitalID:  &hospitalID,
				StaffID:     &staffID,
				KarteStatus: &karteStatus,
			},
			wantSession: &model.Session{
				HospitalID:  &hospitalID,
				StaffID:     &staffID,
				KarteStatus: &karteStatus,
			},
			wantErr: nil,
		},
		{
			name: "getSessionエラー(redisのGetエラー)",
			args: args{
				ctx:            context.Background(),
				refreshTokenID: "bbbb",
			},
			getSessionError: fmt.Errorf("redis Get Error"),
			wantErr:         myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Get Error")),
		},
		{
			name: "IssueTokenエラー(redisのSetエラー)",
			args: args{
				ctx:            context.Background(),
				refreshTokenID: "bbbb",
			},
			getSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			redisSetError: fmt.Errorf("redis Set Error"),
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis Set Error")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			service := &authService{
				repo: sessionRepo,
			}

			sessionRepo.EXPECT().Get(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (value string, err error) {
				if tt.getSessionError != nil {
					return "", tt.getSessionError
				}
				sessionJSON, err := json.Marshal(tt.getSession)
				return string(sessionJSON), err
			}).AnyTimes()
			sessionRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) error {
				return tt.redisDeleteError
			}).AnyTimes()
			sessionRepo.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ string, _ time.Duration) error {
				return tt.redisSetError
			}).AnyTimes()
			sessionRepo.EXPECT().SetNX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ string, _ time.Duration) (bool, error) {
				return true, tt.redisSetError
			}).AnyTimes()

			gotToken, gotTokenExpire, gotRefreshToken, gotRefreshTokenExpire, err := service.RefreshToken(tt.args.ctx, tt.args.refreshTokenID, sessionTTL, refreshTokenTTL, tt.args.isWebSocket)
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)

				// アクセストークン
				sess, claims, extractErr := session.ExtractJWTToken(gotToken)
				assert.NoError(t, extractErr)
				assert.Equal(t, tt.wantSession.HospitalID, sess.HospitalID)
				assert.Equal(t, tt.wantSession.StaffID, sess.StaffID)

				// KarteStatus assertion for access token
				if tt.wantSession.KarteStatus != nil {
					assert.Equal(t, tt.wantSession.KarteStatus, sess.KarteStatus)
					assert.Equal(t, float64(*tt.wantSession.KarteStatus), claims["karteStatus"])
				} else {
					assert.Nil(t, sess.KarteStatus)
					assert.NotContains(t, claims, "karteStatus")
				}

				t.Log(claims)
				// アクセストークンの有効期限
				parsedExpire, parseErr := time.Parse(time.RFC3339Nano, gotTokenExpire)
				assert.NoError(t, parseErr)                           // パースに成功すること
				assert.Less(t, time.Since(parsedExpire), time.Minute) // 誤差1分以内
				// リフレッシュトークン
				sess, claims, extractErr = session.ExtractJWTToken(gotRefreshToken)
				assert.NoError(t, extractErr)
				assert.Nil(t, sess.HospitalID)  // リフレッシュトークンにセッションは設定しないのでnilになる
				assert.Nil(t, sess.StaffID)     // リフレッシュトークンにセッションは設定しないのでnilになる
				assert.Nil(t, sess.KarteStatus) // リフレッシュトークンにセッションは設定しないのでnilになる
				t.Log(claims)
				// リフレッシュトークンの有効期限
				parsedExpire, parseErr = time.Parse(time.RFC3339Nano, gotRefreshTokenExpire)
				assert.NoError(t, parseErr)                           // パースに成功すること
				assert.Less(t, time.Since(parsedExpire), time.Minute) // 誤差1分以内
			}
		})

	}
}

func Test_authService_VerifyToken(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx     context.Context
		tokenID string
	}
	tests := []struct {
		name          string
		args          args
		redisValue    string // model.Sessionのjson文字列
		redisGetError error
		wantOk        bool
		wantErr       error
	}{
		{
			name: "正常系、認証OK",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaaa",
			},
			redisValue:    `{"hospitalID":1, "staffId":1}`,
			redisGetError: nil,
			wantOk:        true,
			wantErr:       nil,
		},
		{
			name: "正常系、認証NG(有効期限切れ)",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaaa",
			},
			redisValue:    ``, // 値が空になる
			redisGetError: nil,
			wantOk:        false,
			wantErr:       nil,
		},
		{
			name: "異常系、redis Get error",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaaa",
			},
			redisValue:    ``,
			redisGetError: fmt.Errorf("redis get failed"),
			wantOk:        false,
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("redis get failed")),
		},
		{
			name: "異常系、Sessionの値異常",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaaa",
			},
			redisValue:    `invalid value`,
			redisGetError: nil,
			wantOk:        false,
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("invalid character 'i' looking for beginning of value")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			sessionRepo.EXPECT().Get(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (value string, err error) {
				return tt.redisValue, tt.redisGetError
			}).AnyTimes()

			s := &authService{
				repo: sessionRepo,
			}
			gotOk, err := s.VerifyToken(tt.args.ctx, tt.args.tokenID)
			if tt.wantErr == nil {
				assert.NoError(t, err)
			} else {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			}
			assert.Equalf(t, tt.wantOk, gotOk, "VerifyToken(%v)", tt.args.tokenID)
		})
	}
}

func Test_authService_createToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	util.Time = test_mock.NewTimeMock(time.Date(2023, 1, 23, 4, 56, 0, 0, util.JSTLocation))

	type args struct {
		ctx         context.Context
		isWebSocket bool
	}
	tests := []struct {
		args          args
		name          string
		redisSetError error
		wantErr       error
	}{
		{
			args: args{
				ctx: context.Background(),
			},
			name:          "正常系",
			redisSetError: nil,
			wantErr:       nil,
		},
		{
			args: args{
				ctx:         context.Background(),
				isWebSocket: true,
			},
			name:          "正常系, isWebSocket=true",
			redisSetError: nil,
			wantErr:       nil,
		},
		{
			args: args{
				ctx: context.Background(),
			},
			name:          "異常系、redis Set error",
			redisSetError: fmt.Errorf("redis Set error"),
			wantErr:       fmt.Errorf("redis Set error"),
		},
	}

	hospitalID := 1
	staffID := 2
	userID := 2
	email := "<EMAIL>"
	portalCustomerID := 3
	pharmacyFlg := false
	karteStatus := 1

	// Test with KarteStatus
	sess := model.NewSession(&hospitalID, &staffID, &userID, &pharmacyFlg, &email, &portalCustomerID, &karteStatus)
	sessionMap := make(map[string]interface{})

	assert.NoError(t, util.ConvertStructureToMap(sess, &sessionMap))

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			s := &authService{
				repo: sessionRepo,
			}
			sessionRepo.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _, _ string, _ time.Duration) (err error) {
				if tt.redisSetError != nil {
					return tt.redisSetError
				}
				return nil
			}).AnyTimes()

			// Do
			gotTokenUUID, gotSignedToken, gotExpire, err := s.createToken(tt.args.ctx, sessionTTL, sess, sessionMap, tt.args.isWebSocket)

			if tt.wantErr == nil {
				assert.NoError(t, err)
				assert.NotEmpty(t, gotTokenUUID)
				gotSess, gotClaims, extractErr := session.ExtractJWTToken(gotSignedToken)
				assert.NoError(t, extractErr)
				assert.Equal(t, sess.HospitalID, gotSess.HospitalID)
				assert.Equal(t, sess.StaffID, gotSess.StaffID)
				assert.Equal(t, sess.Email, gotSess.Email)
				assert.Equal(t, sess.PharmacyFlg, gotSess.PharmacyFlg)
				assert.Equal(t, sess.KarteStatus, gotSess.KarteStatus)
				assert.Equal(t, sessionMap["hospitalID"], gotClaims["hospitalID"])
				assert.Equal(t, sessionMap["staffId"], gotClaims["staffId"])
				assert.Equal(t, sessionMap["email"], gotClaims["email"])
				assert.Equal(t, sessionMap["pharmacyFlg"], gotClaims["pharmacyFlg"])
				assert.Equal(t, sessionMap["karteStatus"], gotClaims["karteStatus"])
				assert.Equal(t, util.Time.JST().Add(sessionTTL).Format(time.RFC3339Nano), gotExpire)
			} else {
				assert.Equal(t, tt.wantErr, err)
			}
		})
	}
}

func Test_authService_genLogoutTokenID(t *testing.T) {
	t.Parallel()

	s := authService{}
	assert.Equal(t, "logout_dummyToken", s.genLogoutTokenID("dummyToken"))
}

func Test_authService_getSession(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	hospitalID := 1
	staffID := 2

	type args struct {
		ctx     context.Context
		tokenID string
	}
	tests := []struct {
		name           string
		args           args
		getSessionJSON string
		getSessionErr  error
		wantSession    *model.Session
		wantErr        error
	}{
		{
			name: "正常系",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
			},
			getSessionJSON: "{\"hospitalID\":1,\"staffId\":2}",
			wantSession: &model.Session{
				HospitalID: &hospitalID,
				StaffID:    &staffID,
			},
			wantErr: nil,
		},
		{
			name: "GetSessionエラー",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
			},
			getSessionErr: fmt.Errorf("redis GetSession Error"),
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("redis GetSession Error")),
		},
		{
			name: "GetSessionエラー(該当する値なし、正常)",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
			},
			getSessionErr: redis.Nil,
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, redis.Nil),
		},
		{
			name: "値なし",
			args: args{
				ctx:     context.Background(),
				tokenID: "",
			},
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("token not found")),
		},
		{
			name: "GetSessionエラー(json.Unmarshalエラー)",
			args: args{
				ctx:     context.Background(),
				tokenID: "hogehoge",
			},
			getSessionJSON: "fugafuga",
			wantErr:        myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("invalid character 'u' in literal false (expecting 'a')")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

			service := &authService{
				repo: sessionRepo,
			}

			sessionRepo.EXPECT().Get(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, _ string) (value string, err error) {
				return tt.getSessionJSON, tt.getSessionErr
			}).AnyTimes()

			sess, err := service.getSession(tt.args.ctx, tt.args.tokenID)
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
				if tt.wantSession.StaffID == nil {
					assert.Nil(t, sess.StaffID)
				} else {
					assert.Equal(t, fmt.Sprint(*sess.StaffID), fmt.Sprint(*tt.wantSession.StaffID))
				}

				if tt.wantSession.HospitalID == nil {
					assert.Nil(t, sess.HospitalID)
				} else {
					assert.Equal(t, fmt.Sprint(*sess.HospitalID), fmt.Sprint(*tt.wantSession.HospitalID))
				}

				if tt.wantSession.Email == nil {
					assert.Nil(t, sess.Email)
				} else {
					assert.Equal(t, fmt.Sprint(*sess.Email), fmt.Sprint(*tt.wantSession.Email))
				}
			}
		})
	}
}

func Test_getAIChartSessionPrefix(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	sessionRepo := mockRepository.NewMockISessionRepository(ctrl)

	service := &authService{
		repo: sessionRepo,
	}

	staffID := 1
	got := service.getAIChartSessionPrefix(staffID)
	assert.Equal(t, "aichart:1:", got)
}

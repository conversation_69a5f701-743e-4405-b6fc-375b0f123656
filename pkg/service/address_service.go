package service

import (
	"context"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/entity"
	"fmt"

	"github.com/cockroachdb/errors"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"gorm.io/gorm"
)

type IAddressService interface {
	GetAddressByPostcode(postcode string) ([]*entity.PostCodeMst, error)
	GetPrefectures(ctx context.Context) ([]*entity.PortalMPrefecture, error)
}

type addressService struct {
	prefectureRepo        repository.IPrefectureRepository
	commonPostCodeMstRepo repository.ICommonPostCodeMstRepository
}

func NewAddressService(preprefectureRepo repository.IPrefectureRepository, commonPostCodeMstRepo repository.ICommonPostCodeMstRepository) IAddressService {
	return &addressService{
		prefectureRepo:        preprefectureRepo,
		commonPostCodeMstRepo: commonPostCodeMstRepo,
	}
}

func (s *addressService) GetAddressByPostcode(postCode string) ([]*entity.PostCodeMst, error) {
	commonPostCodeMst, err := s.commonPostCodeMstRepo.FindByPostCode(postCode)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の住所は見つかりません"), "住所"))
	}
	return commonPostCodeMst, err
}

func (s *addressService) GetPrefectures(_ context.Context) ([]*entity.PortalMPrefecture, error) {
	return s.prefectureRepo.GetPrefectures()
}

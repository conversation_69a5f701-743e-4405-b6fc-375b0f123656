package model

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"denkaru-server/pkg/myerrors"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
)

// Session セッション情報、電カルとポータル共有のため各メンバ変数はポインタにしている
type Session struct {
	// 電カル用セッション（ポータル側認証時にはnil）
	HospitalID  *int  `json:"hospitalID,omitempty"`
	StaffID     *int  `json:"staffID,omitempty"`
	StaffUserID *int  `json:"staffUserID,omitempty"`
	PharmacyFlg *bool `json:"pharmacyFlg,omitempty"`
	// ポータル用セッション
	Email            *string `json:"email,omitempty"`
	PortalCustomerID *int    `json:"portalCustomerID,omitempty"`
	IsOperator       int     `json:"-"`
	OperatorName     string  `json:"-"`
	KarteStatus      *int    `json:"karteStatus,omitempty"`
}

type RequestHeader struct {
	ContentType         string
	ClientCaCnHeader    string
	ClientCustomReferer string
	Cookie              *http.Cookie
}

// IsEmptyStaffSession クリニックスタッフ用セッション情報が空かどうか確認する
func (s *Session) IsEmptyStaffSession() bool {
	if s.StaffID == nil || s.HospitalID == nil || s.StaffUserID == nil {
		return true
	}

	return false
}

// IsEmptyPatientSession 患者ポータル用セッション情報が空かどうか確認する
func (s *Session) IsEmptyPatientSession() bool {
	if s.PortalCustomerID == nil || s.Email == nil {
		return true
	}

	return false
}

// NewSession sessionを生成する
func NewSession(hospitalID, staffID, userID *int, pharmacyFlg *bool, email *string, portalCustomerID *int, karteStatus *int) *Session {
	return &Session{
		HospitalID:       hospitalID,
		StaffID:          staffID,
		StaffUserID:      userID,
		PharmacyFlg:      pharmacyFlg,
		Email:            email,
		PortalCustomerID: portalCustomerID,
		KarteStatus:      karteStatus,
	}
}

// todo あとでsessionKeyかえる
const sessionKey = "1234567891234567"

func (se *Session) Encrypt() (string, error) {
	key := []byte(sessionKey)
	text, _ := json.Marshal(se)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	ciphertext := make([]byte, aes.BlockSize+len(text))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	cfb := cipher.NewCFBEncrypter(block, iv)
	cfb.XORKeyStream(ciphertext[aes.BlockSize:], []byte(text))

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

func Decrypt(str string) (session *Session, err error) {
	key := []byte(sessionKey)
	b, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if len(b) < aes.BlockSize {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("not fill block size")))
	}
	iv := b[:aes.BlockSize]
	b = b[aes.BlockSize:]
	cfb := cipher.NewCFBDecrypter(block, iv)
	cfb.XORKeyStream(b, b)
	err = json.Unmarshal(b, &session)

	return
}

package model_test

import (
	"denkaru-server/pkg/service/model"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

var (
	staffID          = 1
	userID           = 1
	hospitalID       = 3
	portalCustomerID = 9
	email            = "<EMAIL>"
)

func Test_IsEmptyStaffSession(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name           string
		sess           *model.Session
		expectedResult bool
	}{
		{
			name: "スタッフ用のセッション情報がすべて揃っている",
			sess: &model.Session{
				StaffID:     &staffID,
				StaffUserID: &userID,
				HospitalID:  &hospitalID,
			},
			expectedResult: false,
		},
		{
			name: "StaffIDが空",
			sess: &model.Session{
				StaffUserID: &userID,
				HospitalID:  &hospitalID,
			},
			expectedResult: true,
		},
		{
			name: "UserIDが空",
			sess: &model.Session{
				StaffID:    &staffID,
				HospitalID: &hospitalID,
			},
			expectedResult: true,
		},
		{
			name: "HospitalIDが空",
			sess: &model.Session{
				StaffID:     &staffID,
				StaffUserID: &userID,
			},
			expectedResult: true,
		},
		{
			name: "患者のセッション情報だったとき",
			sess: &model.Session{
				PortalCustomerID: &portalCustomerID,
				Email:            &email,
			},
			expectedResult: true,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			res := testCase.sess.IsEmptyStaffSession()
			assert.Equal(t, testCase.expectedResult, res)
		})
	}

}

func Test_IsEmptyPatientSession(t *testing.T) {
	t.Parallel()

	testCases := []struct {
		name           string
		sess           *model.Session
		expectedResult bool
	}{
		{
			name: "患者用のセッション情報がすべて揃っている",
			sess: &model.Session{
				PortalCustomerID: &portalCustomerID,
				Email:            &email,
			},
			expectedResult: false,
		},
		{
			name: "PortalCustomerIDが空",
			sess: &model.Session{
				Email: &email,
			},
			expectedResult: true,
		},
		{
			name: "Emailが空",
			sess: &model.Session{
				PortalCustomerID: &portalCustomerID,
			},
			expectedResult: true,
		},
		{
			name: "スタッフのセッション情報だったとき",
			sess: &model.Session{
				StaffID:     &staffID,
				StaffUserID: &userID,
				HospitalID:  &hospitalID,
			},
			expectedResult: true,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			res := testCase.sess.IsEmptyPatientSession()
			assert.Equal(t, testCase.expectedResult, res)
		})
	}
}

func TestSessionWithKarteStatus(t *testing.T) {
	t.Run("create session with KarteStatus", func(t *testing.T) {
		hospitalID := 1
		staffID := 2
		userID := 3
		pharmacyFlg := true
		karteStatus := 1

		session := model.NewSession(&hospitalID, &staffID, &userID, &pharmacyFlg, nil, nil, &karteStatus)

		assert.Equal(t, &hospitalID, session.HospitalID)
		assert.Equal(t, &staffID, session.StaffID)
		assert.Equal(t, &userID, session.StaffUserID)
		assert.Equal(t, &pharmacyFlg, session.PharmacyFlg)
		assert.Equal(t, &karteStatus, session.KarteStatus)
	})

	t.Run("encrypt and decrypt session with KarteStatus", func(t *testing.T) {
		// Create a session with KarteStatus
		hospitalID := 1
		staffID := 2
		userID := 3
		pharmacyFlg := true
		karteStatus := 1

		originalSession := model.NewSession(&hospitalID, &staffID, &userID, &pharmacyFlg, nil, nil, &karteStatus)

		// Encrypt the session
		encryptedData, err := originalSession.Encrypt()
		assert.NoError(t, err)
		assert.NotEmpty(t, encryptedData)

		// Decrypt the session
		decryptedSession, err := model.Decrypt(encryptedData)
		assert.NoError(t, err)
		assert.NotNil(t, decryptedSession)

		// Verify KarteStatus is preserved
		assert.Equal(t, &karteStatus, decryptedSession.KarteStatus)
	})

	t.Run("JSON marshaling preserves KarteStatus", func(t *testing.T) {
		hospitalID := 1
		staffID := 2
		karteStatus := 1

		session := model.NewSession(&hospitalID, &staffID, &staffID, nil, nil, nil, &karteStatus)

		// Marshal to JSON
		data, err := json.Marshal(session)
		assert.NoError(t, err)

		// Unmarshal from JSON
		var unmarshaledSession model.Session
		err = json.Unmarshal(data, &unmarshaledSession)
		assert.NoError(t, err)

		// Verify KarteStatus is preserved
		assert.Equal(t, &karteStatus, unmarshaledSession.KarteStatus)
	})
}

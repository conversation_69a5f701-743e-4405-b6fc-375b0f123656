package service

import (
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/session"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/labstack/echo/v4"
	"github.com/vektah/gqlparser/v2/ast"
	"gorm.io/gorm"
)

// IOperatorService interface of OperatorService
type IOperatorService interface {
	Login(ctx context.Context, input *custom.OperatorLoginInput) (output *custom.OperatorLoginOutput, err error)
	VerifyMFACode(ctx context.Context, hospitalID int, input *custom.OperatorVerifyMFACodeInput) (output *custom.OperatorVerifyMFACodeOutput, err error)
	ChangePassword(ctx context.Context, hospitalID int, input *custom.OperatorChangePasswordInput) (output *custom.OperatorChangePasswordOutput, err error)
	HasPermission(operation ast.Operation, operationName string, functionName string, groups []string) (hasPermission bool, err error)
	IsAllowedIPForGroup(echoCtx echo.Context, groupNames []string) (ok bool, err error)
}

type operatorService struct {
	operatorAuthRepo       repository.IOperatorAuthRepository
	operatorPermissionRepo repository.IOperatorPermissionRepository
	staffRepo              repository.IStaffRepository
	hospitalRepo           repository.IHospitalRepository
}

// NewOperatorService initialization of IOperatorService
func NewOperatorService(operatorAuthRepo repository.IOperatorAuthRepository, operatorPermissionRepo repository.IOperatorPermissionRepository, staffRepo repository.IStaffRepository, hospitalRepo repository.IHospitalRepository) IOperatorService {
	return &operatorService{
		operatorAuthRepo:       operatorAuthRepo,
		operatorPermissionRepo: operatorPermissionRepo,
		staffRepo:              staffRepo,
		hospitalRepo:           hospitalRepo,
	}
}

// Login CSオペレーター用のログイン
func (s *operatorService) Login(ctx context.Context, input *custom.OperatorLoginInput) (output *custom.OperatorLoginOutput, err error) {

	// オーナー管理者権限のスタッフを取得する
	staffs, err := s.staffRepo.FindStaffsByManagerKbn(input.HospitalID, constant.ManagerKbnOwnerAdmin)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, err))
		return
	}

	// オーナー管理者権限のスタッフがいないことは無いはずだが、念の為確認する
	if len(staffs) == 0 {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, fmt.Errorf("non existent owner staff data")))
		return
	}

	// オペレーター認証
	output, err = s.operatorAuthRepo.Login(ctx, input)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, err))
		return
	}
	// オーナー管理者のIDをレスポンスに含める
	output.OwnerStaffID = staffs[0].ID

	// IdTokenが空の場合は、後続処理は行わない
	if output.IdToken == nil {
		return
	}

	// 指定されたhospital_idが、薬局かどうか確認
	hospital, err := s.hospitalRepo.FindByID(ctx, input.HospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", input.HospitalID), "病院"))
			return
		}
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, err))
		return
	}

	// PharmacyFlgを設定
	output.PharmacyFlg = hospital.PharmacyFlg

	// KarteStatusを設定
	output.KarteStatus = hospital.KarteStatus

	// Tokenの有効期限を取得して返す
	idTokenClaims, err := session.ExtractCognitoTokenClaims(ctx, *output.IdToken, true)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, err))
		return
	}
	output.IdTokenExpireTime = time.Unix(idTokenClaims.Exp, 0).In(util.JSTLocation)

	return
}

// VerifyMFACode CSオペレーター用の二段階認証コード検証
func (s *operatorService) VerifyMFACode(ctx context.Context, hospitalID int, input *custom.OperatorVerifyMFACodeInput) (output *custom.OperatorVerifyMFACodeOutput, err error) {
	output, err = s.operatorAuthRepo.VerifyMFACode(ctx, input)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, err))
		return
	}

	// 指定されたhospital_idが、薬局かどうか確認
	hospital, err := s.hospitalRepo.FindByID(ctx, hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", hospitalID), "病院"))
			return
		}
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, err))
		return
	}

	// PharmacyFlgを設定
	output.PharmacyFlg = hospital.PharmacyFlg

	// Tokenの有効期限を取得して返す
	idTokenClaims, err := session.ExtractCognitoTokenClaims(ctx, output.IdToken, true)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, err))
		return
	}
	output.IdTokenExpireTime = time.Unix(idTokenClaims.Exp, 0).In(util.JSTLocation)

	return
}

// ChangePassword パスワードを変更する（初期パスワード変更）
func (s *operatorService) ChangePassword(ctx context.Context, hospitalID int, input *custom.OperatorChangePasswordInput) (output *custom.OperatorChangePasswordOutput, err error) {
	output, err = s.operatorAuthRepo.ChangePassword(ctx, input)
	if err != nil {
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidNewOperatorPassword, err))
		return
	}

	// 指定されたhospital_idが、薬局かどうか確認
	hospital, err := s.hospitalRepo.FindByID(ctx, hospitalID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", hospitalID), "病院"))
			return
		}
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidNewOperatorPassword, err))
		return
	}

	// PharmacyFlgを設定
	output.PharmacyFlg = hospital.PharmacyFlg
	return
}

// HasPermission オペレーターが権限を持っているかを確認する
func (s *operatorService) HasPermission(operation ast.Operation, operationName string, functionName string, groupNames []string) (hasPermission bool, err error) {

	var permission *entity.OperatorGroupPermission

	// 所属しているグループごとに権限チェック
	for _, groupName := range groupNames {

		// オペレーターが所属しているグループの情報を取得
		group, e := s.operatorPermissionRepo.FindOperatorGroup(groupName)
		if e != nil {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, e))
			return
		}

		// オペレーターが実行した機能の情報を取得
		feature, e := s.operatorPermissionRepo.FindApplicationFeature(functionName)
		if e != nil {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, e))
			return
		}

		// 権限情報の取得
		permission, e = s.operatorPermissionRepo.FindOperatorGroupPermission(group.OperatorGroupID, feature.FeatureID)
		if e != nil {
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, e))
			return
		}

		// 権限がない場合
		if permission == nil {
			continue
		}

		// 権限チェックに合格したらtrueを返す
		if util.IsOperationValid(operation, operationName, permission.Permission) {
			hasPermission = true
			return
		}
	}

	hasPermission = false
	return
}

// IsAllowedIPForGroup 許可されたIPでアクセスされているかどうかを確認する
func (s *operatorService) IsAllowedIPForGroup(echoCtx echo.Context, groupNames []string) (ok bool, err error) {

	// 明示的なfalse
	ok = false

	// X-Forward-For or X-Real-IPからIPを取得する
	clientIP := net.ParseIP(echoCtx.RealIP())
	if clientIP == nil {
		ok = false
		err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("no client IP in header")))
		return
	}

	for _, groupName := range groupNames {

		// m_operator_groupのallowed_ip_listを取得する
		group, e := s.operatorPermissionRepo.FindOperatorGroup(groupName)
		if e != nil {
			ok = false
			err = errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, e))
			return
		}
		allowedIPList := strings.Split(group.AllowedIPList, ",")

		// 許可されたIPが未設定の場合、全許可
		if len(allowedIPList) == 1 && len(allowedIPList[0]) == 0 {
			ok = true
			break
		}

		// クライアントのIPが許可されたIPかどうか確認する
		for _, allowedIP := range allowedIPList {
			if clientIP.Equal(net.ParseIP(allowedIP)) {
				ok = true
				break
			}
		}
	}

	return ok, err
}

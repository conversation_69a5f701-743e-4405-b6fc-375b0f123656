package service_test

import (
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service"
	"denkaru-server/pkg/test_mock"
	mock_repository "denkaru-server/pkg/test_mock/repository"
	"denkaru-server/pkg/util"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/vektah/gqlparser/v2/ast"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func Test_OperatorService_Login(t *testing.T) {
	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	ctx := context.Background()

	testCases := []struct {
		name                      string
		input                     *custom.OperatorLoginInput
		expectedKey               string
		expectedJWKError          error
		expectedResFunc1          []*entity.UserMst
		expectedErrorFunc1        error
		expectedIdTokenExpireTime time.Time
		expectedResFunc2          *custom.OperatorLoginOutput
		expectedErrorFunc2        error
		expectedResFunc3          *custom.HpInf
		expectedErrorFunc3        error
		expectedError             error
		expectedRes               custom.OperatorLoginOutput
	}{
		{
			name: "正常系_IdTokenなし",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedResFunc2: &custom.OperatorLoginOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedResFunc3: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: false,
				KarteStatus: constant.KarteStatusNoUse,
			},
			expectedRes: custom.OperatorLoginOutput{
				OwnerStaffID:  staffID1,
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				KarteStatus:   constant.KarteStatusNoUse,
			},
		},
		{
			name: "正常系_IdTokenなし 薬局",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedResFunc2: &custom.OperatorLoginOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedResFunc3: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: true,
				KarteStatus: constant.KarteStatusRealInUse,
			},
			expectedRes: custom.OperatorLoginOutput{
				OwnerStaffID:  staffID1,
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				KarteStatus:   constant.KarteStatusRealInUse,
			},
		},
		{
			name: "正常系_IdTokenあり",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "t41ZLwRKaG2y23jPqdR00QODxjUDX597ssKuP73qHWg",
					"alg": "RS256",
					"n": "iTDgNnAapQsPRVZRPHIOcY6Jfsi-teEvJjXsN0TLjiGzeHz4MGtxw6TjEoPLRRLn5amYNp8y00GseIgl3V0j_ozm0opES5PkVzPxE8wAHbSYrLCLjhqortsDkf7KlIXxrEbPPBugAOQdCWYLdVUzaRyvQs85zhKIg8slkYogedGtOkTVVb-5Nz-EFlhpABc_CZaKr4bLHx8KWRi5TGpb3uFbttVzIn4nQxd2yOgc8hi7iW6z-qx1zBLXQPxWrgq38FLqb4bn65Dnsx7cToexawvYAZHDOAoAr9pWDYt7by7I143oJD2j10bxOk7dkTcBfKskS4b6KqkIKmiowpQZuw"
				}
			  ]
			}
			`,
			expectedResFunc2: &custom.OperatorLoginOutput{
				IdToken: util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
			expectedResFunc3: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: false,
			},
			expectedIdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
			expectedRes: custom.OperatorLoginOutput{
				OwnerStaffID: staffID1,
				IdToken:      util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
		},
		{
			name: "正常系_IdTokenあり 薬局",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "t41ZLwRKaG2y23jPqdR00QODxjUDX597ssKuP73qHWg",
					"alg": "RS256",
					"n": "iTDgNnAapQsPRVZRPHIOcY6Jfsi-teEvJjXsN0TLjiGzeHz4MGtxw6TjEoPLRRLn5amYNp8y00GseIgl3V0j_ozm0opES5PkVzPxE8wAHbSYrLCLjhqortsDkf7KlIXxrEbPPBugAOQdCWYLdVUzaRyvQs85zhKIg8slkYogedGtOkTVVb-5Nz-EFlhpABc_CZaKr4bLHx8KWRi5TGpb3uFbttVzIn4nQxd2yOgc8hi7iW6z-qx1zBLXQPxWrgq38FLqb4bn65Dnsx7cToexawvYAZHDOAoAr9pWDYt7by7I143oJD2j10bxOk7dkTcBfKskS4b6KqkIKmiowpQZuw"
				}
			  ]
			}
			`,
			expectedResFunc2: &custom.OperatorLoginOutput{
				IdToken: util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
			expectedResFunc3: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: true,
			},
			expectedIdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
			expectedRes: custom.OperatorLoginOutput{
				OwnerStaffID: staffID1,
				IdToken:      util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
				PharmacyFlg:  true,
			},
		},
		{
			name: "異常系_オーナー管理者がいない",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: make([]*entity.UserMst, 0),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, fmt.Errorf("non existent owner staff data")),
		},
		{
			name: "異常系_オーナー管理者権限の取得エラー",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedErrorFunc1: fmt.Errorf("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, fmt.Errorf("test error")),
		},
		{
			name: "異常系_Cognito認証エラー",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedErrorFunc2: fmt.Errorf("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, fmt.Errorf("test error")),
		},
		{
			name: "異常系_病院情報取得エラー",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedResFunc2: &custom.OperatorLoginOutput{
				IdToken: util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
			expectedErrorFunc3: errors.New("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, errors.New("test error")),
		},
		{
			name: "異常系_病院情報取得 NotFoundエラー",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedResFunc2: &custom.OperatorLoginOutput{
				IdToken: util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
			expectedErrorFunc3: gorm.ErrRecordNotFound,
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", hospitalID), "病院"),
		},
		{
			name: "異常系_JWKエラー",
			input: &custom.OperatorLoginInput{
				OperatorName:     "test",
				OperatorPassword: "P@ssw0rd",
				HospitalID:       hospitalID1,
			},
			expectedResFunc1: []*entity.UserMst{
				{ID: staffID1, ManagerKbn: constant.ManagerKbnOwnerAdmin},
			},
			expectedResFunc2: &custom.OperatorLoginOutput{
				IdToken: util.NewPtr("eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw"),
			},
			expectedResFunc3: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: false,
			},
			expectedJWKError: fmt.Errorf("test error"),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeOperatorLoginFailed, fmt.Errorf("test error")),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorAuthRepo := mock_repository.NewMockIOperatorAuthRepository(ctrl)
			operatorPermissionRepo := mock_repository.NewMockIOperatorPermissionRepository(ctrl)
			staffRepo := mock_repository.NewMockIStaffRepository(ctrl)
			hospitalRepo := mock_repository.NewMockIHospitalRepository(ctrl)
			testee := service.NewOperatorService(operatorAuthRepo, operatorPermissionRepo, staffRepo, hospitalRepo)

			// JWKのクライアントをMockに差し替える
			util.JWKClient = test_mock.NewJWKTestMock(testCase.expectedKey, testCase.expectedJWKError)

			staffRepo.EXPECT().
				FindStaffsByManagerKbn(testCase.input.HospitalID, constant.ManagerKbnOwnerAdmin).
				DoAndReturn(func(_, _ int) (staffs []*entity.UserMst, err error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			operatorAuthRepo.EXPECT().
				Login(ctx, testCase.input).
				DoAndReturn(func(_ context.Context, _ *custom.OperatorLoginInput) (*custom.OperatorLoginOutput, error) {
					return testCase.expectedResFunc2, testCase.expectedErrorFunc2
				}).AnyTimes()

			hospitalRepo.EXPECT().
				FindByID(ctx, testCase.input.HospitalID).
				DoAndReturn(func(_ context.Context, _ int) (hospital *custom.HpInf, err error) {
					return testCase.expectedResFunc3, testCase.expectedErrorFunc3
				}).AnyTimes()

			output, err := testee.Login(ctx, testCase.input)
			if err != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, testCase.expectedError, denkaruError)
				return
			}
			assert.Equal(t, testCase.expectedRes.ChallengeName, output.ChallengeName)
			assert.Equal(t, testCase.expectedRes.SessionValue, output.SessionValue)
			assert.Equal(t, testCase.expectedRes.OwnerStaffID, output.OwnerStaffID)
			assert.Equal(t, testCase.expectedRes.IdToken, output.IdToken)
			assert.Equal(t, testCase.expectedRes.PharmacyFlg, output.PharmacyFlg)
			if !output.IdTokenExpireTime.IsZero() {
				assert.Equal(t, testCase.expectedIdTokenExpireTime, output.IdTokenExpireTime)
			}
		})
	}

}

func Test_OperatorService_VerifyMFACode(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	testCases := []struct {
		name                      string
		input                     *custom.OperatorVerifyMFACodeInput
		hospitalID                int
		expectedKey               string
		expectedJWKError          error
		expectedResFunc1          *custom.OperatorVerifyMFACodeOutput
		expectedErrorFunc1        error
		expectedResFunc2          *custom.HpInf
		expectedErrorFunc2        error
		expectedRes               *custom.OperatorVerifyMFACodeOutput
		expectedIdTokenExpireTime time.Time
		expectedError             error
	}{
		{
			name: "正常系",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID: hospitalID,
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
			},
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "t41ZLwRKaG2y23jPqdR00QODxjUDX597ssKuP73qHWg",
					"alg": "RS256",
					"n": "iTDgNnAapQsPRVZRPHIOcY6Jfsi-teEvJjXsN0TLjiGzeHz4MGtxw6TjEoPLRRLn5amYNp8y00GseIgl3V0j_ozm0opES5PkVzPxE8wAHbSYrLCLjhqortsDkf7KlIXxrEbPPBugAOQdCWYLdVUzaRyvQs85zhKIg8slkYogedGtOkTVVb-5Nz-EFlhpABc_CZaKr4bLHx8KWRi5TGpb3uFbttVzIn4nQxd2yOgc8hi7iW6z-qx1zBLXQPxWrgq38FLqb4bn65Dnsx7cToexawvYAZHDOAoAr9pWDYt7by7I143oJD2j10bxOk7dkTcBfKskS4b6KqkIKmiowpQZuw"
				}
			  ]
			}
			`,
			expectedResFunc2: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: false,
			},
			expectedRes: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
				PharmacyFlg:   false,
			},
			expectedIdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
		},
		{
			name: "正常系 薬局",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID: hospitalID,
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
			},
			expectedKey: `
			{
			  "keys": [
				{
					"kty": "RSA",
					"e": "AQAB",
					"use": "sig",
					"kid": "t41ZLwRKaG2y23jPqdR00QODxjUDX597ssKuP73qHWg",
					"alg": "RS256",
					"n": "iTDgNnAapQsPRVZRPHIOcY6Jfsi-teEvJjXsN0TLjiGzeHz4MGtxw6TjEoPLRRLn5amYNp8y00GseIgl3V0j_ozm0opES5PkVzPxE8wAHbSYrLCLjhqortsDkf7KlIXxrEbPPBugAOQdCWYLdVUzaRyvQs85zhKIg8slkYogedGtOkTVVb-5Nz-EFlhpABc_CZaKr4bLHx8KWRi5TGpb3uFbttVzIn4nQxd2yOgc8hi7iW6z-qx1zBLXQPxWrgq38FLqb4bn65Dnsx7cToexawvYAZHDOAoAr9pWDYt7by7I143oJD2j10bxOk7dkTcBfKskS4b6KqkIKmiowpQZuw"
				}
			  ]
			}
			`,
			expectedResFunc2: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: true,
			},
			expectedRes: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
				PharmacyFlg:   true,
			},
			expectedIdTokenExpireTime: time.Date(2500, 3, 1, 19, 0, 0, 0, util.JSTLocation),
		},
		{
			name: "異常系_MFA認証エラー",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID:         hospitalID,
			expectedErrorFunc1: fmt.Errorf("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, fmt.Errorf("test error")),
		},
		{
			name: "異常系_病院情報取得エラー",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID: hospitalID,
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
			},
			expectedErrorFunc2: fmt.Errorf("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, fmt.Errorf("test error")),
		},
		{
			name: "異常系_病院情報取得 NotFoundエラー",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID: hospitalID,
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
			},
			expectedErrorFunc2: gorm.ErrRecordNotFound,
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", hospitalID), "病院"),
		},
		{
			name: "異常系_JWK認証エラー",
			input: &custom.OperatorVerifyMFACodeInput{
				OperatorName: "test",
				PinCode:      "123456",
				SessionValue: "test_session_value_1",
			},
			hospitalID: hospitalID,
			expectedResFunc2: &custom.HpInf{
				HpID:        hospitalID1,
				PharmacyFlg: false,
			},
			expectedResFunc1: &custom.OperatorVerifyMFACodeOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				IdToken:       "eyJraWQiOiJ0NDFaTHdSS2FHMnkyM2pQcWRSMDBRT0R4alVEWDU5N3NzS3VQNzNxSFdnIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W5UbE7xBKbi7ZeIP9Qm3J2-eFxGbYOTSsYvJEOWu6jzsrAybHLRlvg0C-IuoYJwEiSfAufwvRxHrWWYT1KNGEkkASASZzU3U9MPKQb_wXX89m2h-W9PPfAmErb9jqG2QX8QDK67YJzHU1QGEz6vJMgzzhqWmk3ujqU1upY5sz64utCfbMEFFEqU5mdCozgu3ybhFl5vd-Y-rTEhpjQl0iEmYendfEXRwcXnOBO-5wh2GmHzvoLNUkw-cl0U8X1FuulW0rLcksrTXGYQHwbQpnxQcIzRhA1iWCcqTL_wSInkVq7qUvp3Nn4itg3gwwvzMc13_XfqCFJiUdP3bJnT2aw",
			},
			expectedJWKError: fmt.Errorf("test error"),
			expectedError:    myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidOperatorMFAPinCode, fmt.Errorf("test error")),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorAuthRepo := mock_repository.NewMockIOperatorAuthRepository(ctrl)
			operatorPermissionRepo := mock_repository.NewMockIOperatorPermissionRepository(ctrl)
			staffRepo := mock_repository.NewMockIStaffRepository(ctrl)
			hospitalRepo := mock_repository.NewMockIHospitalRepository(ctrl)
			testee := service.NewOperatorService(operatorAuthRepo, operatorPermissionRepo, staffRepo, hospitalRepo)

			ctx := context.Background()

			// JWKのクライアントをMockに差し替える
			util.JWKClient = test_mock.NewJWKTestMock(testCase.expectedKey, testCase.expectedJWKError)

			operatorAuthRepo.EXPECT().
				VerifyMFACode(ctx, testCase.input).
				DoAndReturn(func(_ context.Context, _ *custom.OperatorVerifyMFACodeInput) (*custom.OperatorVerifyMFACodeOutput, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			hospitalRepo.EXPECT().
				FindByID(ctx, testCase.hospitalID).
				DoAndReturn(func(_ context.Context, _ int) (*custom.HpInf, error) {
					return testCase.expectedResFunc2, testCase.expectedErrorFunc2
				}).AnyTimes()

			output, err := testee.VerifyMFACode(ctx, testCase.hospitalID, testCase.input)
			if err != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, testCase.expectedError, denkaruError)
				return
			}

			assert.Equal(t, testCase.expectedRes.IdToken, output.IdToken)
			assert.Equal(t, testCase.expectedIdTokenExpireTime, output.IdTokenExpireTime)
			assert.Equal(t, testCase.expectedRes.PharmacyFlg, output.PharmacyFlg)
		})
	}

}

func Test_OperatorService_ChangePassword(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	ctx := context.Background()

	testCases := []struct {
		name               string
		hospitalID         int
		input              *custom.OperatorChangePasswordInput
		expectedResFunc1   *custom.OperatorChangePasswordOutput
		expectedErrorFunc1 error
		expectedResFunc2   *custom.HpInf
		expectedErrorFunc2 error
		expectedRes        *custom.OperatorChangePasswordOutput
		expectedError      error
	}{
		{
			name:       "正常系",
			hospitalID: hospitalID,
			input: &custom.OperatorChangePasswordInput{
				OperatorName: "test",
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedResFunc2: &custom.HpInf{
				HpID:        hospitalID,
				PharmacyFlg: false,
			},
			expectedRes: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				PharmacyFlg:   false,
			},
		},
		{
			name:       "正常系 薬局",
			hospitalID: hospitalID,
			input: &custom.OperatorChangePasswordInput{
				OperatorName: "test",
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedResFunc2: &custom.HpInf{
				HpID:        hospitalID,
				PharmacyFlg: true,
			},
			expectedRes: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
				PharmacyFlg:   true,
			},
		},
		{
			name:       "異常系 病院情報の取得エラー",
			hospitalID: hospitalID,
			input: &custom.OperatorChangePasswordInput{
				OperatorName: "test",
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedErrorFunc2: errors.New("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidNewOperatorPassword, errors.New("test error")),
		},
		{
			name:       "異常系 病院情報の取得 NotFoundエラー",
			hospitalID: hospitalID,
			input: &custom.OperatorChangePasswordInput{
				OperatorName: "test",
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedResFunc1: &custom.OperatorChangePasswordOutput{
				ChallengeName: "TEST",
				SessionValue:  util.NewPtr("test_session_value_1"),
			},
			expectedErrorFunc2: gorm.ErrRecordNotFound,
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("hospital:[id=%v] is not found", hospitalID), "病院"),
		},
		{
			name:       "異常系",
			hospitalID: hospitalID,
			input: &custom.OperatorChangePasswordInput{
				OperatorName: "test",
				NewPassword:  "NewP@ssw0rd",
				SessionValue: "test_session_value_1",
			},
			expectedErrorFunc1: fmt.Errorf("test error"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidNewOperatorPassword, fmt.Errorf("test error")),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorAuthRepo := mock_repository.NewMockIOperatorAuthRepository(ctrl)
			operatorPermissionRepo := mock_repository.NewMockIOperatorPermissionRepository(ctrl)
			staffRepo := mock_repository.NewMockIStaffRepository(ctrl)
			hospitalRepo := mock_repository.NewMockIHospitalRepository(ctrl)
			testee := service.NewOperatorService(operatorAuthRepo, operatorPermissionRepo, staffRepo, hospitalRepo)

			operatorAuthRepo.EXPECT().
				ChangePassword(ctx, testCase.input).
				DoAndReturn(func(_ context.Context, _ *custom.OperatorChangePasswordInput) (*custom.OperatorChangePasswordOutput, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			hospitalRepo.EXPECT().
				FindByID(ctx, testCase.hospitalID).
				DoAndReturn(func(_ context.Context, _ int) (*custom.HpInf, error) {
					return testCase.expectedResFunc2, testCase.expectedErrorFunc2
				}).AnyTimes()

			output, err := testee.ChangePassword(ctx, testCase.hospitalID, testCase.input)
			if err != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, testCase.expectedError, denkaruError)
				return
			}
			assert.Equal(t, testCase.expectedRes, output)
		})
	}

}

func Test_HasPermission(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)

	defer ctrl.Finish()

	type args struct {
		operation     ast.Operation
		operationName string
		featureName   string
		groupNames    []string
	}
	testCases := []struct {
		name               string
		args               *args
		expectedResFunc1   *entity.MOperatorGroup
		expectedErrorFunc1 error
		expectedResFunc2   *entity.MApplicationFeature
		expectedErrorFunc2 error
		expectedResFunc3   *entity.OperatorGroupPermission
		expectedErrorFunc3 error
		hasPermission      bool
		expectedError      error
	}{
		{
			name: "正常系_権限RW_Mutation",
			args: &args{
				operation:     ast.Mutation,
				operationName: "createCalender",
				featureName:   "calender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadWrite,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限RW_Query",
			args: &args{
				operation:     ast.Query,
				operationName: "getCalender",
				featureName:   "calender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadWrite,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限RW_Subscription",
			args: &args{
				operation:     ast.Subscription,
				operationName: "notify",
				featureName:   "chat",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "chat",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadWrite,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限RO_Mutation",
			args: &args{
				operation:     ast.Mutation,
				featureName:   "calender",
				operationName: "createCalender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadOnly,
			},
			hasPermission: false,
		},
		{
			name: "正常系_権限RO_Query",
			args: &args{
				operation:     ast.Query,
				featureName:   "calender",
				operationName: "getCalender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadOnly,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限RO_Subscription",
			args: &args{
				operation:     ast.Subscription,
				operationName: "notify",
				featureName:   "chat",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "chat",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionReadOnly,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限None_Mutation",
			args: &args{
				operation:     ast.Mutation,
				featureName:   "calender",
				operationName: "createCalender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionNone,
			},
			hasPermission: false,
		},
		{
			name: "正常系_権限None_Query",
			args: &args{
				operation:     ast.Query,
				featureName:   "calender",
				operationName: "getCalender",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionNone,
			},
			hasPermission: false,
		},
		{
			name: "正常系_権限None_Subscription",
			args: &args{
				operation:     ast.Subscription,
				operationName: "notify",
				featureName:   "chat",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "chat",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionNone,
			},
			hasPermission: false,
		},
		{
			name: "正常系_権限None_Mutation_logout",
			args: &args{
				operation:     ast.Mutation,
				featureName:   "calender",
				operationName: "logout",
				groupNames:    []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      constant.FeaturePermissionNone,
			},
			hasPermission: true,
		},
		{
			name: "正常系_権限が空",
			args: &args{
				operation:   ast.Subscription,
				featureName: "calender",
				groupNames:  []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: nil,
			hasPermission:    false,
		},
		{
			name: "異常系_権限が不明な定義",
			args: &args{
				operation:   ast.Subscription,
				featureName: "calender",
				groupNames:  []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedResFunc3: &entity.OperatorGroupPermission{
				OperatorGroupID: 1,
				FeatureID:       1,
				Permission:      99,
			},
			hasPermission: false,
		},
		{
			name: "異常系_FindOperatorGroup_DBエラー",
			args: &args{
				operation:   ast.Subscription,
				featureName: "calender",
				groupNames:  []string{"developer"},
			},
			expectedErrorFunc1: fmt.Errorf("error func1"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("error func1")),
			hasPermission:      false,
		},
		{
			name: "異常系_FindOperatorFunction_DBエラー",
			args: &args{
				operation:   ast.Subscription,
				featureName: "calender",
				groupNames:  []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedErrorFunc2: fmt.Errorf("error func2"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("error func2")),
			hasPermission:      false,
		},
		{
			name: "異常系_FindOperatorGroupPermission_DBエラー",
			args: &args{
				operation:   ast.Subscription,
				featureName: "calender",
				groupNames:  []string{"developer"},
			},
			expectedResFunc1: &entity.MOperatorGroup{
				OperatorGroupID:   1,
				OperatorGroupName: "developer",
			},
			expectedResFunc2: &entity.MApplicationFeature{
				FeatureID:   1,
				FeatureName: "calender",
			},
			expectedErrorFunc3: fmt.Errorf("error func3"),
			expectedError:      myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("error func3")),
			hasPermission:      false,
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorAuthRepo := mock_repository.NewMockIOperatorAuthRepository(ctrl)
			operatorPermissionRepo := mock_repository.NewMockIOperatorPermissionRepository(ctrl)
			staffRepo := mock_repository.NewMockIStaffRepository(ctrl)
			hospitalRepo := mock_repository.NewMockIHospitalRepository(ctrl)
			testee := service.NewOperatorService(operatorAuthRepo, operatorPermissionRepo, staffRepo, hospitalRepo)

			operatorPermissionRepo.EXPECT().
				FindOperatorGroup(gomock.Any()).
				DoAndReturn(func(_ string) (*entity.MOperatorGroup, error) {
					return testCase.expectedResFunc1, testCase.expectedErrorFunc1
				}).AnyTimes()

			operatorPermissionRepo.EXPECT().
				FindApplicationFeature(gomock.Any()).
				DoAndReturn(func(_ string) (*entity.MApplicationFeature, error) {
					return testCase.expectedResFunc2, testCase.expectedErrorFunc2
				}).AnyTimes()

			operatorPermissionRepo.EXPECT().
				FindOperatorGroupPermission(gomock.Any(), gomock.Any()).
				DoAndReturn(func(_, _ int) (*entity.OperatorGroupPermission, error) {
					return testCase.expectedResFunc3, testCase.expectedErrorFunc3
				}).AnyTimes()

			hasPermission, err := testee.HasPermission(testCase.args.operation, testCase.args.operationName, testCase.args.featureName, testCase.args.groupNames)
			if testCase.expectedError != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, testCase.expectedError, denkaruError)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, testCase.hasPermission, hasPermission)
		})
	}

}

func Test_IsAllowedIPForGroup(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testCases := []struct {
		name                   string
		xForwardFor            string
		xRealIP                string
		groupNames             []string
		findOperatorGroupRes   []*entity.MOperatorGroup
		findOperatorGroupError error
		expected               bool
		expectedError          error
	}{
		{
			name:        "正常系 groupNamesが1つ & マッチ",
			xForwardFor: "*************",
			groupNames:  []string{"test"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "test",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: true,
		},
		{
			name:        "正常系 groupNamesが1つ & マッチしない",
			xForwardFor: "*************",
			groupNames:  []string{"test"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "test",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: false,
		},
		{
			name:        "正常系 groupNamesが1つ & alowedIPが未設定",
			xForwardFor: "*************",
			groupNames:  []string{"test"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "test",
					AllowedIPList:     "",
				},
			},
			expected: true,
		},
		{
			name:        "正常系 groupNamesが2つ以上 & マッチ",
			xForwardFor: "*************",
			groupNames:  []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: true,
		},
		{
			name:        "正常系 X-Forward-Forが複数ある & groupNamesが2つ以上 & マッチ",
			xForwardFor: "*************, **********",
			groupNames:  []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: true,
		},
		{
			name:       "正常系 X-Real-IPのみ & groupNamesが2つ以上",
			xRealIP:    "**********",
			groupNames: []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "**********,**********",
				},
			},
			expected: true,
		},
		{
			name:       "正常系 X-Real-IPのみ & groupNamesが2つ以上 & 許可されていないIP",
			xRealIP:    "**********",
			groupNames: []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "**********,**********",
				},
			},
			expected: false,
		},
		{
			name:        "正常系 groupNamesが2つ以上 & マッチしない",
			xForwardFor: "*************",
			groupNames:  []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: false,
		},
		{
			name:        "正常系 X-Forward-Forが複数ある & groupNamesが2つ以上 & マッチしない",
			xForwardFor: "*************, **********",
			groupNames:  []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "*************,*************",
				},
			},
			expected: false,
		},
		{
			name:        "正常系 groupNamesが2つ以上 & そのうち1つがalloweIP未設定",
			xForwardFor: "*************",
			groupNames:  []string{"hoo", "bar"},
			findOperatorGroupRes: []*entity.MOperatorGroup{
				{
					OperatorGroupName: "hoo",
					AllowedIPList:     "*************,*************",
				},
				{
					OperatorGroupName: "bar",
					AllowedIPList:     "",
				},
			},
			expected: true,
		},
		{
			name:                   "異常系 FindOperatorGroup DBエラー",
			xForwardFor:            "*************",
			groupNames:             []string{"hoo", "bar"},
			findOperatorGroupError: fmt.Errorf("test"),
			expected:               false,
			expectedError:          myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("test")),
		},
		{
			name:                   "異常系 client IPが存在しない",
			groupNames:             []string{"hoo", "bar"},
			findOperatorGroupError: fmt.Errorf("test"),
			expected:               false,
			expectedError:          myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("test")),
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			operatorAuthRepo := mock_repository.NewMockIOperatorAuthRepository(ctrl)
			operatorPermissionRepo := mock_repository.NewMockIOperatorPermissionRepository(ctrl)
			staffRepo := mock_repository.NewMockIStaffRepository(ctrl)
			hospitalRepo := mock_repository.NewMockIHospitalRepository(ctrl)
			testee := service.NewOperatorService(operatorAuthRepo, operatorPermissionRepo, staffRepo, hospitalRepo)

			e := echo.New()
			req := httptest.NewRequest(http.MethodGet, "/", nil)
			if len(testCase.xForwardFor) > 0 {
				req.Header.Set(echo.HeaderXForwardedFor, testCase.xForwardFor)
			}

			if len(testCase.xRealIP) > 0 {
				req.Header.Set(echo.HeaderXRealIP, testCase.xRealIP)
			}

			rec := httptest.NewRecorder()
			echoCtx := e.NewContext(req, rec)

			i := 0
			operatorPermissionRepo.EXPECT().
				FindOperatorGroup(gomock.Any()).
				DoAndReturn(func(_ string) (*entity.MOperatorGroup, error) {
					if len(testCase.findOperatorGroupRes) == 0 {
						return nil, testCase.findOperatorGroupError
					}

					res := testCase.findOperatorGroupRes[i]
					i++
					return res, testCase.findOperatorGroupError
				}).AnyTimes()

			ok, err := testee.IsAllowedIPForGroup(echoCtx, testCase.groupNames)
			if err != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, testCase.expectedError, denkaruError)
				return
			}
			assert.Equal(t, testCase.expected, ok)
		})
	}

}

package service

import (
	"context"
	"denkaru-server/pkg/myerrors"
	serviceModel "denkaru-server/pkg/service/model"
	mockClient "denkaru-server/pkg/test_mock/infra/client"
	"denkaru-server/pkg/util"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/hasura/go-graphql-client"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_sessionService_CheckToken(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	client := mockClient.NewMockIGraphqlClient(ctrl)

	type args struct {
		ctx   context.Context
		token string
	}
	tests := []struct {
		name        string
		args        args
		mutateResp  string
		mutateError error
		wantErr     error
	}{
		{
			name: "正常系、トークン有効",
			args: args{
				ctx:   context.Background(),
				token: "aaaaa",
			},
			mutateResp:  `{"isValid":true}`,
			mutateError: nil,
			wantErr:     nil,
		},
		{
			name: "正常系、トークン無効",
			args: args{
				ctx:   context.Background(),
				token: "aaaaa",
			},
			mutateResp:  `{"isValid":false}`,
			mutateError: nil,
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("token is invalid")),
		},
		{
			name: "異常系、Mutateエラー",
			args: args{
				ctx:   context.Background(),
				token: "aaaaa",
			},
			mutateResp:  "{}",
			mutateError: fmt.Errorf("mutate error"),
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("mutate error")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.EXPECT().Mutate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m interface{}, variables map[string]interface{}, options ...graphql.Option) error {
				err := json.Unmarshal([]byte(tt.mutateResp), m)
				if err != nil {
					return err
				}
				return tt.mutateError
			})
			s := &sessionService{
				gqlClient: client,
			}

			err := s.CheckToken(tt.args.ctx, tt.args.token)

			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_sessionService_CreateSession(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	client := mockClient.NewMockIGraphqlClient(ctrl)

	type args struct {
		ctx         context.Context
		staffID     int
		hospitalID  int
		userID      int
		pharmacyFlg bool
	}
	tests := []struct {
		name                   string
		args                   args
		mutateResp             string
		mutateError            error
		wantToken              string
		wantRefreshToken       string
		wantTokenExpire        time.Time
		wantRefreshTokenExpire time.Time
		wantErr                error
	}{
		{
			name: "正常系",
			args: args{
				ctx:        context.Background(),
				staffID:    1,
				hospitalID: 2,
			},
			mutateResp:             `{"token":"aaaa","refreshToken":"bbbb","tokenExpiryTime":"2024-01-30T12:00:00.000+09:00","refreshTokenExpiryTime":"2024-01-30T23:00:00.000+09:00"}`,
			wantToken:              "aaaa",
			wantRefreshToken:       "bbbb",
			wantTokenExpire:        time.Date(2024, 1, 30, 12, 0, 0, 0, util.JSTLocation),
			wantRefreshTokenExpire: time.Date(2024, 1, 30, 23, 0, 0, 0, util.JSTLocation),
		},
		{
			name: "異常系、Mutateエラー",
			args: args{
				ctx:         context.Background(),
				staffID:     1,
				hospitalID:  2,
				pharmacyFlg: false,
			},
			mutateResp:  `{}`,
			mutateError: fmt.Errorf("mutate error"),
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("mutate error")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.EXPECT().Mutate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m interface{}, variables map[string]interface{}, options ...graphql.Option) error {
				err := json.Unmarshal([]byte(tt.mutateResp), m)
				if err != nil {
					return err
				}
				return tt.mutateError
			})
			s := &sessionService{
				gqlClient: client,
			}

			sess := serviceModel.NewSession(&tt.args.hospitalID, &tt.args.staffID, &tt.args.userID, &tt.args.pharmacyFlg, nil, nil, nil)
			gotToken, gotRefreshToken, gotTokenExpire, gotRefreshTokenExpire, err := s.CreateSession(tt.args.ctx, sess)

			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, gotToken, tt.wantToken)
				assert.Equal(t, gotRefreshToken, tt.wantRefreshToken)
				assert.Equal(t, gotTokenExpire.In(util.JSTLocation), tt.wantTokenExpire)
				assert.Equal(t, gotRefreshTokenExpire.In(util.JSTLocation), tt.wantRefreshTokenExpire)
			}
		})
	}
}

func Test_sessionService_DeleteSession(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	client := mockClient.NewMockIGraphqlClient(ctrl)

	type args struct {
		ctx     context.Context
		tokenID string
	}
	tests := []struct {
		name        string
		args        args
		mutateResp  string
		mutateError error
		wantErr     error
	}{
		{
			name: "正常系",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaa",
			},
			mutateResp: `{"isDeleted":true}`,
		},
		{
			name: "異常系、isDeleted = false",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaa",
			},
			mutateResp: `{"isDeleted":false}`,
			wantErr:    myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("delete session failed")),
		},
		{
			name: "異常系、Mutateエラー",
			args: args{
				ctx:     context.Background(),
				tokenID: "aaaa",
			},
			mutateResp:  `{}`,
			mutateError: fmt.Errorf("mutate error"),
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("mutate error")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.EXPECT().Mutate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m interface{}, variables map[string]interface{}, options ...graphql.Option) error {
				err := json.Unmarshal([]byte(tt.mutateResp), m)
				if err != nil {
					return err
				}
				return tt.mutateError
			})
			s := &sessionService{
				gqlClient: client,
			}

			// got, err := s.DeleteSession(tt.args.ctx, tt.args.tokenID)
			_, err := s.DeleteSession(tt.args.ctx, tt.args.tokenID)
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_sessionService_RefreshSession(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	client := mockClient.NewMockIGraphqlClient(ctrl)

	type args struct {
		ctx          context.Context
		token        string
		refreshToken string
	}
	tests := []struct {
		name                   string
		args                   args
		mutateResp             string
		wantToken              string
		wantRefreshToken       string
		wantTokenExpire        time.Time
		wantRefreshTokenExpire time.Time
		mutateError            error
		wantErr                error
	}{
		{
			name: "正常系",
			args: args{
				ctx:          context.Background(),
				token:        "aaaa",
				refreshToken: "bbbb",
			},
			mutateResp:             `{"token":"aaaa","refreshToken":"bbbb","tokenExpiryTime":"2024-01-30T12:00:00.000+09:00","refreshTokenExpiryTime":"2024-01-30T23:00:00.000+09:00"}`,
			wantToken:              "aaaa",
			wantRefreshToken:       "bbbb",
			wantTokenExpire:        time.Date(2024, 1, 30, 12, 0, 0, 0, util.JSTLocation),
			wantRefreshTokenExpire: time.Date(2024, 1, 30, 23, 0, 0, 0, util.JSTLocation),
		},
		{
			name: "異常系、Mutateエラー",
			args: args{
				ctx:          context.Background(),
				token:        "aaaa",
				refreshToken: "bbbb",
			},
			mutateResp:  `{}`,
			mutateError: fmt.Errorf("mutate error"),
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("mutate error")),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client.EXPECT().Mutate(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, m interface{}, variables map[string]interface{}, options ...graphql.Option) error {
				err := json.Unmarshal([]byte(tt.mutateResp), m)
				if err != nil {
					return err
				}
				return tt.mutateError
			})
			s := &sessionService{
				gqlClient: client,
			}

			gotToken, gotRefreshToken, gotTokenExpire, gotRefreshTokenExpire, err := s.RefreshSession(tt.args.ctx, tt.args.refreshToken, false)
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, gotToken, tt.wantToken)
				assert.Equal(t, gotRefreshToken, tt.wantRefreshToken)
				assert.Equal(t, gotTokenExpire.In(util.JSTLocation), tt.wantTokenExpire)
				assert.Equal(t, gotRefreshTokenExpire.In(util.JSTLocation), tt.wantRefreshTokenExpire)
			}
		})
	}
}

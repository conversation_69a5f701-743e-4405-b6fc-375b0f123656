package service

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mockRepository "denkaru-server/pkg/test_mock/repository"
	"denkaru-server/pkg/util"
	"fmt"
	"regexp"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_portalHospitalStaffService_GetPortalHospitalStaffs(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		ctx     context.Context
		getRes  []*entity.PortalHospitalStaff
		getErr  error
		want    []*gqlModel.PortalHospitalStaff
		wantErr error
	}{
		{
			name: "正常系、0件",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getRes: []*entity.PortalHospitalStaff{},
			want:   []*gqlModel.PortalHospitalStaff{},
		},
		{
			name: "正常系、1件",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getRes: []*entity.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					HospitalID:      11,
					Name:            "テスト１",
				},
			},
			want: []*gqlModel.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					Name:            "テスト１",
				},
			},
		},
		{
			name: "正常系、複数件",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getRes: []*entity.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					HospitalID:      11,
					Name:            "テスト１",
				},
				{
					HospitalStaffID: 2,
					HospitalID:      22,
					Name:            "テスト２",
				},
			},
			want: []*gqlModel.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					Name:            "テスト１",
				},
				{
					HospitalStaffID: 2,
					Name:            "テスト２",
				},
			},
		},
		{
			name:    "異常系、contextにhospitalIdなし",
			ctx:     context.Background(),
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、GetPortalHospitalStaffsエラー",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getErr:  fmt.Errorf("getPortalHospitalStaffs error"),
			wantErr: fmt.Errorf("getPortalHospitalStaffs error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := mockRepository.NewMockIPortalHospitalStaffRepository(ctrl)
			staffPictureRepo := mockRepository.NewMockIPortalStaffPictureRepository(ctrl)

			testee := NewPortalHospitalStaffService(repo, nil, staffPictureRepo, nil, nil)

			repo.EXPECT().GetPortalHospitalStaffs(gomock.Any()).Return(tt.getRes, tt.getErr).AnyTimes()
			staffPictureRepo.EXPECT().GetPortalStaffPictures(gomock.Any()).Return([]entity.PortalStaffPicture{}, nil).AnyTimes()

			got, err := testee.GetPortalHospitalStaffs(tt.ctx)

			assert.Equal(t, len(tt.want), len(got))
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, tt.wantErr, denkaruError)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			GotLoop:
				for _, g := range got {
					for _, w := range tt.want {
						if g.HospitalStaffID == w.HospitalStaffID {
							assert.Equal(t, w.Name, g.Name)
							continue GotLoop
						}
					}
					// ここまできたら一致するものがなかったのでFailにする
					assert.Fail(t, fmt.Sprintf("got %v not found in want", g.HospitalStaffID))
				}
			}
		})
	}
}

func Test_portalHospitalStaffService_GetPortalHospitalStaff(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func Test_portalHospitalStaffService_CreatePortalHospitalStaff(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func Test_portalHospitalStaffService_EditPortalHospitalStaff(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func Test_portalHospitalStaffService_DeletePortalHospitalStaff(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func Test_portalHospitalStaffService_SortPortalHospitalStaffs(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name      string
		ctx       context.Context
		getRes    []*entity.PortalHospitalStaff
		getErr    error
		updated   []*entity.PortalHospitalStaff
		updateErr error
		wantErr   error
	}{
		{
			name: "正常系",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getRes: []*entity.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					Order_:          11,
				},
				{
					HospitalStaffID: 2,
					Order_:          22,
				},
				{
					HospitalStaffID: 3,
					Order_:          33,
				},
			},
			updated: []*entity.PortalHospitalStaff{
				{
					HospitalStaffID: 1,
					Order_:          1,
				},
				{
					HospitalStaffID: 2,
					Order_:          2,
				},
				{
					HospitalStaffID: 3,
					Order_:          3,
				},
			},
		},
		{
			name:    "異常系、contextにhospitalIdなし",
			ctx:     context.Background(),
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
		{
			name: "異常系、GetPortalHospitalStaffsエラー",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getErr:  fmt.Errorf("getPortalHospitalStaffs error"),
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("getPortalHospitalStaffs error")),
		},
		{
			name: "異常系、UpdatePortalHospitalStaffsエラー",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			updateErr: fmt.Errorf("updatePortalHospitalStaffs error"),
			wantErr:   myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("updatePortalHospitalStaffs error")),
		},
		{
			name: "異常系、引数のstaffIDに対応するhospitalStaffが見つからない",
			ctx: test_mock.GetTestContextWithSession(&model.Session{
				HospitalID: util.NewPtr(1),
			}),
			getRes: []*entity.PortalHospitalStaff{
				{
					HospitalStaffID: 99999,
					Order_:          99999,
				},
			},
			wantErr: errors.New("staff order not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := mockRepository.NewMockIPortalHospitalStaffRepository(ctrl)
			staffPictureRepo := mockRepository.NewMockIPortalStaffPictureRepository(ctrl)

			testee := NewPortalHospitalStaffService(repo, nil, staffPictureRepo, nil, nil)

			repo.EXPECT().GetPortalHospitalStaffs(gomock.Any()).Return(tt.getRes, tt.getErr).AnyTimes()
			staffPictureRepo.EXPECT().GetPortalStaffPictures(gomock.Any()).Return([]entity.PortalStaffPicture{}, nil).AnyTimes()
			repo.EXPECT().UpdatePortalHospitalStaffs(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, staffs []*entity.PortalHospitalStaff) error {
				if tt.updateErr != nil {
					return tt.updateErr
				}

				assert.Equal(t, len(tt.updated), len(staffs))
			GotLoop:
				for _, s := range staffs {
					for _, u := range tt.updated {
						if s.HospitalStaffID == u.HospitalStaffID {
							assert.Equal(t, s.Order_, u.Order_)
							continue GotLoop
						}
					}
					// ここまできたら一致するものがなかったのでFailにする
					assert.Fail(t, fmt.Sprintf("got %v not found in want", s.HospitalStaffID))
				}

				return nil
			}).AnyTimes()

			err := testee.SortPortalHospitalStaffs(tt.ctx, &gqlModel.SortPortalHospitalStaffsInput{
				StaffIds: []int{1, 2, 3},
			})

			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, tt.wantErr, denkaruError)
				} else {
					assert.Equal(t, tt.wantErr.Error(), err.Error())
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_portalHospitalStaffService_getNextOrder(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		getMaxOrderRes int
		getMaxOrderErr error
		want           int
		wantErr        error
	}{
		{
			name:           "正常系",
			getMaxOrderRes: 100,
			want:           101,
		},
		{
			name:           "異常系、GetMaxOrderエラー",
			getMaxOrderErr: fmt.Errorf("getMaxOrder error"),
			wantErr:        myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("getMaxOrder error")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := mockRepository.NewMockIPortalHospitalStaffRepository(ctrl)
			staffPictureRepo := mockRepository.NewMockIPortalStaffPictureRepository(ctrl)

			testee := NewPortalHospitalStaffService(repo, nil, staffPictureRepo, nil, nil)

			repo.EXPECT().GetMaxOrder(gomock.Any()).Return(tt.getMaxOrderRes, tt.getMaxOrderErr).AnyTimes()

			got, err := testee.(*portalHospitalStaffService).getNextOrder(context.Background(), 1)

			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_portalHospitalStaffService_GetPortalHospitalStaffUploadFileUrls(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func Test_portalHospitalStaffService_getS3PresignPutURL(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

func TestGenS3ObjectKey(t *testing.T) {
	t.Parallel()

	got := GenS3ObjectKey(1, 2, "test.xlsx")

	pattern := `hospitalId_1/staffs/staffId_2/images/test_.*xlsx`
	re := regexp.MustCompile(pattern)
	assert.True(t, re.MatchString(got), "pattern does not match: %s", got)
}

func Test_portalHospitalStaffService_GetPortalStaffFileURL(t *testing.T) {
	// FIXME S3クラインアントが必要なのでテスト不可、S3クライアントと疎結合にしてからテストコードを作成すること
}

package service

import (
	"context"
	"denkaru-server/pkg/logger"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/util"
	"fmt"
	"io"
	"regexp"
	"strings"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"go.uber.org/zap"
)

type ICommonPostCodeMstService interface {
	ImportCommonPostCode(ctx context.Context) (err error)
}

type commonPostCodeMstService struct {
	importDataRepo        repository.IImportDataS3Repository
	commonPostCodeMstRepo repository.ICommonPostCodeMstRepository
}

const (
	postcodeS3KeyPrefix = "post-code-mst"
	postCodeBatchSize   = 1000
)

var (
	blockCommentRegex      = regexp.MustCompile(`/\*.*?\*/`)
	singleLineCommentRegex = regexp.MustCompile(`(?m)^\s*--.*$`)
)

func NewCommonPostCodeMstService(
	importDataRepo repository.IImportDataS3Repository,
	commonPostCodeMstRepo repository.ICommonPostCodeMstRepository,
) ICommonPostCodeMstService {
	service := &commonPostCodeMstService{
		importDataRepo:        importDataRepo,
		commonPostCodeMstRepo: commonPostCodeMstRepo,
	}
	return service
}

// ImportCommonPostCode ゆうびんばんごうをsqlファイルからインポートする関数
func (s *commonPostCodeMstService) ImportCommonPostCode(ctx context.Context) error {
	files, err := s.importDataRepo.GetListFile(ctx, postcodeS3KeyPrefix)

	if err != nil {
		err = errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), "Error listing files")
		return err
	}

	// Loop through files
	for _, file := range files {
		if !isValidSQLFile(file) {
			continue
		}

		// Process file
		if err := s.processSQLFile(ctx, file); err != nil {
			err = errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), fmt.Sprintf("Failed to process file %s", file))
			return err
		}

		// Stop after successful file processing
		logger.Log.Info(fmt.Sprintf("%sファイルのインポートに成功しました。", file))
		break
	}
	return nil
}

func (s *commonPostCodeMstService) processSQLFile(ctx context.Context, file string) (err error) {
	output, err := s.importDataRepo.ReadFile(ctx, file)
	if err != nil {
		err = errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), "Failed to read file")
		return
	}
	defer func() {
		if closeErr := output.Close(); closeErr != nil {
			logger.Log.Warn("Error closing file:", zap.Error(closeErr))
		}
	}()

	content, err := io.ReadAll(output)
	if err != nil {
		err = errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), "Error reading file content")
		return
	}

	cleanedSQL := removeSQLComments(string(content))
	sqlStatements := strings.Split(cleanedSQL, ";")

	// Prepare data for COPY
	values, columns, err := s.prepareCopyData(sqlStatements)
	if err != nil {
		return errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), "Error preparing COPY data")
	}

	// Execute COPY data
	if err = s.commonPostCodeMstRepo.CreateByCopy(ctx, values, columns, postCodeBatchSize); err != nil {
		return errors.Wrap(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err), "Error executing SQL in file")
	}

	return
}

func isValidSQLFile(file string) bool {
	return strings.HasPrefix(file, postcodeS3KeyPrefix) && strings.HasSuffix(file, ".sql")
}

func removeSQLComments(sqlContent string) string {
	// Remove block comments (/* */)
	cleanedSQL := blockCommentRegex.ReplaceAllString(sqlContent, "")
	// Remove single-line comments (-- ...)
	cleanedSQL = singleLineCommentRegex.ReplaceAllString(cleanedSQL, "")
	return cleanedSQL
}

func (s *commonPostCodeMstService) prepareCopyData(sqlStatements []string) ([]string, []string, error) {
	validColumns, _ := util.GetColumnsAndDataFromStruct(entity.PostCodeMst{})
	columns, values, err := util.ParseSQLInsert(sqlStatements)
	if err != nil {
		return nil, nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	var validValues []string
	columnIndex := make(map[string]int)
	for i, col := range validColumns {
		columnIndex[col] = i
	}

	for _, row := range values {
		if len(row) <= 0 {
			continue
		}

		line := make([]string, len(validColumns))
		for i := range line {
			line[i] = "\\N"
		}

		for i, col := range columns {
			if idx, exists := columnIndex[col]; exists {
				line[idx] = row[i]
			}
		}
		validValues = append(validValues, strings.Join(line, "\t"))
	}

	return validValues, validColumns, nil
}

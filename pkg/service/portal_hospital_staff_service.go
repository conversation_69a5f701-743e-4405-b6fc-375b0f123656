package service

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/custom"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service/model"
	"denkaru-server/pkg/util"
	"denkaru-server/pkg/util/array"
	"denkaru-server/pkg/util/session"
	"fmt"
	"os"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type IPortalHospitalStaffService interface {
	GetPortalHospitalStaffs(ctx context.Context) ([]*entity.PortalHospitalStaff, error)
	SortPortalHospitalStaffs(context.Context, *gqlModel.SortPortalHospitalStaffsInput) error
	GetPortalHospitalStaff(ctx context.Context, staffID int) (staff *gqlModel.GetPortalHospitalStaffRes, err error)
	CreatePortalHospitalStaff(ctx context.Context, input gqlModel.CreatePortalHospitalStaffInput) (*gqlModel.CreatePortalStaffRes, error)
	EditPortalHospitalStaff(ctx context.Context, input gqlModel.EditPortalHospitalStaffInput) error
	DeletePortalHospitalStaff(ctx context.Context, input gqlModel.DeletePortalHospitalStaffInput) error
	GetPortalHospitalStaffUploadFileUrls(ctx context.Context, hospitalStaffID int, input []*gqlModel.GetPortalHospitalStaffUploadFileURLInput) ([]*gqlModel.GetPortalHospitalStaffUploadFileURLRes, error)
	GetPortalStaffFileURL(ctx context.Context, input gqlModel.GetPortalStaffFileURLInput) (url *string, err error)
}

type portalHospitalStaffService struct {
	repo             repository.IPortalHospitalStaffRepository
	staffPictureRepo repository.IPortalStaffPictureRepository
	pictureRepo      repository.IPortalPictureRepository
	s3PresignClient  *s3.PresignClient
	s3Client         *s3.Client
}

func NewPortalHospitalStaffService(repo repository.IPortalHospitalStaffRepository, s3PresignClient *s3.PresignClient, staffPictureRepo repository.IPortalStaffPictureRepository, pictureRepo repository.IPortalPictureRepository, s3Client *s3.Client) IPortalHospitalStaffService {
	return &portalHospitalStaffService{
		repo:             repo,
		s3PresignClient:  s3PresignClient,
		staffPictureRepo: staffPictureRepo,
		pictureRepo:      pictureRepo,
		s3Client:         s3Client,
	}
}

func (s *portalHospitalStaffService) GetPortalHospitalStaffs(ctx context.Context) ([]*entity.PortalHospitalStaff, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	input := &custom.GetPortalHospitalStaffsInput{
		HospitalID: *hospitalID,
		StaffIDs:   nil,
	}

	return s.repo.GetPortalHospitalStaffs(input)
}

func (s *portalHospitalStaffService) GetPortalHospitalStaff(ctx context.Context, staffID int) (staff *gqlModel.GetPortalHospitalStaffRes, err error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalID == nil {
		return nil, errors.New("hospitalId is nil")
	}

	hospitalStaff, err := s.repo.GetPortalHospitalStaffByID(staffID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ"))
		}
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	staffRes := gqlModel.PortalHospitalStaff{
		HospitalStaffID:  hospitalStaff.HospitalStaffID,
		Name:             hospitalStaff.Name,
		Description:      &hospitalStaff.Description,
		Order:            hospitalStaff.Order_,
		SpecialistDetail: &hospitalStaff.SpecialistDetail,
		IsDirector:       hospitalStaff.IsDirector,
		ExperienceDetail: &hospitalStaff.ExperienceDetail,
	}

	// Get staff picture
	staffPictures, err := s.staffPictureRepo.GetPortalStaffPictures(hospitalStaff.HospitalStaffID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	pictIDs := make([]int, 0, len(staffPictures))
	for _, picture := range staffPictures {
		pictIDs = append(pictIDs, picture.PictID)
	}

	pictures := make([]*gqlModel.PortalHospitalStaffFile, 0, len(pictIDs))
	for _, id := range pictIDs {
		pict, err := s.pictureRepo.GetPortalPicture(id)

		if err != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}

		url, err := util.GetS3FileURL(ctx, s.s3Client, s.s3PresignClient, os.Getenv("S3_HOSPITAL_BUCKET"), pict.Filepath)
		if err != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
		pictures = append(pictures, &gqlModel.PortalHospitalStaffFile{
			FileID:           &pict.PictID,
			OriginalFileName: pict.FileName,
			S3Key:            *url,
			CreatedAt:        &pict.CreatedAt,
		})
	}

	staffRes.Files = pictures

	return &gqlModel.GetPortalHospitalStaffRes{
		PortalHospitalStaff: &staffRes,
	}, nil
}

func (s *portalHospitalStaffService) CreatePortalHospitalStaff(ctx context.Context, input gqlModel.CreatePortalHospitalStaffInput) (*gqlModel.CreatePortalStaffRes, error) {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalID == nil {
		return nil, errors.New("hospitalId is nil")
	}

	order, err := s.getNextOrder(ctx, *hospitalID)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	hospitalStaff := entity.PortalHospitalStaff{
		HospitalID: *hospitalID,
		Name:       input.HospitalStaffInput.Name,
		Order_:     order,
		IsDirector: input.HospitalStaffInput.IsDirector,
	}

	if input.HospitalStaffInput.Description != nil {
		hospitalStaff.Description = *input.HospitalStaffInput.Description
	}

	if input.HospitalStaffInput.SpecialistDetail != nil {
		hospitalStaff.SpecialistDetail = *input.HospitalStaffInput.SpecialistDetail
	}

	if input.HospitalStaffInput.ExperienceDetail != nil {
		hospitalStaff.ExperienceDetail = *input.HospitalStaffInput.ExperienceDetail
	}

	newStaff, err := s.repo.CreatePortalHospitalStaff(ctx, &hospitalStaff)

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Add staff picture
	if input.FilesInput != nil && len(input.FilesInput) != 0 {
		staffPictures := make([]entity.PortalStaffPicture, 0, len(input.FilesInput))
		for _, pict := range input.FilesInput {
			s3Key, _ := util.GetS3KeyFromUploadURL(pict.UploadFileURL)
			ptrPicture := &entity.PortalPict{
				FileName: pict.OriginalFileName,
				Filepath: s3Key,
			}

			newPict, err := s.pictureRepo.UpdatePortalPicture(ctx, ptrPicture)
			if err != nil {
				return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
			}

			staffPictures = append(staffPictures, entity.PortalStaffPicture{
				PictID:          newPict.PictID,
				HospitalStaffID: newStaff.HospitalStaffID,
			})
		}

		err := s.staffPictureRepo.UpdatePortalStaffPictures(ctx, staffPictures)
		if err != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
		}
	}

	return &gqlModel.CreatePortalStaffRes{
		PortalStaffID: newStaff.HospitalStaffID,
	}, nil
}

func (s *portalHospitalStaffService) EditPortalHospitalStaff(ctx context.Context, input gqlModel.EditPortalHospitalStaffInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalID == nil {
		return errors.New("hospitalId is nil")
	}

	originalStaff, err := s.repo.GetPortalHospitalStaffByID(input.HospitalStaffID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ"))
		}
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if originalStaff == nil {
		return errors.New("not found staff")
	}

	hospitalStaff := entity.PortalHospitalStaff{
		HospitalStaffID: input.HospitalStaffID,
		HospitalID:      *hospitalID,
		Name:            input.HospitalStaffInput.Name,
		Order_:          originalStaff.Order_,
		IsDirector:      input.HospitalStaffInput.IsDirector,
	}

	if input.HospitalStaffInput.Description != nil {
		hospitalStaff.Description = *input.HospitalStaffInput.Description
	}

	if input.HospitalStaffInput.SpecialistDetail != nil {
		hospitalStaff.SpecialistDetail = *input.HospitalStaffInput.SpecialistDetail
	}

	if input.HospitalStaffInput.ExperienceDetail != nil {
		hospitalStaff.ExperienceDetail = *input.HospitalStaffInput.ExperienceDetail
	}

	err = s.repo.UpdatePortalHospitalStaff(ctx, &hospitalStaff)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Delete staff picture
	if input.DeletedFileIds != nil && len(input.DeletedFileIds) != 0 {
		for _, id := range input.DeletedFileIds {
			hospPicture := entity.PortalStaffPicture{
				HospitalStaffID: input.HospitalStaffID,
				PictID:          id,
			}
			err := s.staffPictureRepo.DeletePortalStaffPicture(ctx, hospPicture)
			if err != nil {
				return err
			}

			deletePicture, err := s.pictureRepo.GetPortalPicture(id)
			if err != nil {
				return err
			}

			// Delete pict in s3
			err = util.DeleteS3ObjectURL(ctx, s.s3Client, os.Getenv("S3_HOSPITAL_BUCKET"), deletePicture.Filepath)
			if err != nil {
				return errors.New("can delete picture in s3")
			}

			err = s.pictureRepo.DeletePortalPicture(ctx, *deletePicture)
			if err != nil {
				return err
			}

		}
	}
	// Add new hospital picture
	if input.AddedFiles != nil && len(input.AddedFiles) != 0 {
		staffPictures := make([]entity.PortalStaffPicture, 0, len(input.AddedFiles))
		for _, pict := range input.AddedFiles {
			s3Key, _ := util.GetS3KeyFromUploadURL(pict.UploadFileURL)
			ptrPicture := &entity.PortalPict{
				FileName: pict.OriginalFileName,
				Filepath: s3Key,
			}

			newPict, err := s.pictureRepo.UpdatePortalPicture(ctx, ptrPicture)
			if err != nil {
				return err
			}

			staffPictures = append(staffPictures, entity.PortalStaffPicture{
				PictID:          newPict.PictID,
				HospitalStaffID: input.HospitalStaffID,
			})
		}

		err := s.staffPictureRepo.UpdatePortalStaffPictures(ctx, staffPictures)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *portalHospitalStaffService) DeletePortalHospitalStaff(ctx context.Context, input gqlModel.DeletePortalHospitalStaffInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if hospitalID == nil {
		return errors.New("hospitalId is nil")
	}

	originalStaff, err := s.repo.GetPortalHospitalStaffByID(input.HospitalStaffID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, errors.New("対象のクリニックスタッフが見つかりません"), "クリニックスタッフ"))
		}
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if originalStaff == nil {
		return errors.New("not found staff")
	}

	hospitalStaff := entity.PortalHospitalStaff{
		HospitalStaffID: input.HospitalStaffID,
		HospitalID:      *hospitalID,
	}

	// Delete staff picture
	staffPictures, err := s.staffPictureRepo.GetPortalStaffPictures(input.HospitalStaffID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	for _, picture := range staffPictures {

		err := s.staffPictureRepo.DeletePortalStaffPicture(ctx, picture)
		if err != nil {
			return err
		}

		deletePicture, err := s.pictureRepo.GetPortalPicture(picture.PictID)
		if err != nil {
			return err
		}

		// Delete pict in s3
		err = util.DeleteS3ObjectURL(ctx, s.s3Client, os.Getenv("S3_HOSPITAL_BUCKET"), deletePicture.Filepath)
		if err != nil {
			return errors.New("can delete picture in s3")
		}

		err = s.pictureRepo.DeletePortalPicture(ctx, *deletePicture)
		if err != nil {
			return err
		}

	}

	err = s.repo.DeletePortalHospitalStaff(ctx, hospitalStaff)

	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *portalHospitalStaffService) SortPortalHospitalStaffs(ctx context.Context, input *gqlModel.SortPortalHospitalStaffsInput) error {
	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	getStaffsInput := &custom.GetPortalHospitalStaffsInput{
		HospitalID: *hospitalID,
		StaffIDs:   &input.StaffIds,
	}

	staffs, err := s.repo.GetPortalHospitalStaffs(getStaffsInput)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// 入力配列の対応する ID のインデックスに基づいてorderの値を更新
	for _, staff := range staffs {
		order := array.FindIndex(input.StaffIds, func(staffID int) bool {
			return staffID == staff.HospitalStaffID
		})

		if order == constant.FindIndexNotFound {
			return errors.New("staff order not found")
		}

		staff.Order_ = order + 1
	}

	err = s.repo.UpdatePortalHospitalStaffs(ctx, staffs)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *portalHospitalStaffService) getNextOrder(_ context.Context, hospitalID int) (int, error) {

	maxOrder, err := s.repo.GetMaxOrder(hospitalID)
	if err != nil {
		return 0, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	nextOrder := maxOrder + 1

	return nextOrder, nil

}

// GetPortalHospitalStaffUploadFileUrls implements IPortalHospitalStaffService.
func (s *portalHospitalStaffService) GetPortalHospitalStaffUploadFileUrls(ctx context.Context, hospitalStaffID int, input []*gqlModel.GetPortalHospitalStaffUploadFileURLInput) ([]*gqlModel.GetPortalHospitalStaffUploadFileURLRes, error) {
	result := make([]*gqlModel.GetPortalHospitalStaffUploadFileURLRes, 0, len(input))

	if len(input) == 0 {
		return result, nil
	}

	hospitalID, err := session.GetHospitalID(ctx)
	if err != nil {
		return result, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	asyncRes := util.WaitAllAsyncDone(input, s.getS3PresignPutURL(hospitalStaffID, *hospitalID))

	for _, eachAsyncResult := range asyncRes {
		if eachAsyncResult.Error != nil {
			return nil, eachAsyncResult.Error
		}

		result = append(result, &gqlModel.GetPortalHospitalStaffUploadFileURLRes{
			FileNameWithExtension: eachAsyncResult.FileNameWithExtension,
			URL:                   eachAsyncResult.URL,
		})
	}

	return result, nil
}

func (s *portalHospitalStaffService) getS3PresignPutURL(hospitalStaffID int, hospitalID int) func(input *gqlModel.GetPortalHospitalStaffUploadFileURLInput) model.GetS3UploadUrlResult {
	return func(input *gqlModel.GetPortalHospitalStaffUploadFileURLInput) model.GetS3UploadUrlResult {
		url, err := util.GetS3PresignPutURL(os.Getenv("S3_HOSPITAL_BUCKET"), s.s3PresignClient,
			GenS3ObjectKey(hospitalID, hospitalStaffID, input.FileNameWithExtension),
			nil, constant.PresignPutUrlExpireDuration)

		return model.GetS3UploadUrlResult{
			Error:                 err,
			URL:                   url,
			FileNameWithExtension: input.FileNameWithExtension,
		}
	}
}

func GenS3ObjectKey(hospitalID int, hospitalStaffID int, fileNameWithExtension string) string {
	uuID := uuid.New().String()
	extension := filepath.Ext(fileNameWithExtension)
	fileName := fileNameWithExtension[0 : len(fileNameWithExtension)-len(extension)]
	return fmt.Sprintf("hospitalId_%v/staffs/staffId_%v/images/%v_%v%v", hospitalID, hospitalStaffID, fileName, uuID, extension)
}

func (s *portalHospitalStaffService) GetPortalStaffFileURL(ctx context.Context, input gqlModel.GetPortalStaffFileURLInput) (*string, error) {
	hospitalID, staffID, err := session.GetHospitalIDAndStaffID(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if hospitalID == nil || staffID == nil {
		return nil, errors.New("hospitalID or staffID is nil")
	}

	pict, err := s.pictureRepo.GetPortalPicture(input.FileID)

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}
	if pict == nil {
		return nil, errors.New("not found picture")
	}

	url, err := util.GetS3FileURL(ctx, s.s3Client, s.s3PresignClient, os.Getenv("S3_HOSPITAL_BUCKET"), pict.Filepath)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return url, nil
}

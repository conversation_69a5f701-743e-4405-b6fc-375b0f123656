package service

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"
	"strconv"
	"strings"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"gorm.io/gorm"
)

type ISystemNotificationService interface {
	GetSystemNotifications(ctx context.Context) ([]*model.SystemNotification, error)
	GetReadSystemNotifications(ctx context.Context, staffID int) ([]*int, error)
	EditReadSystemNotifications(ctx context.Context, staffID int, readList []*int) error
}

type systemNotificationService struct {
	repo                 repository.ISystemNotificationRepository
	readSystemNoticeRepo repository.IReadSystemNotificationRepository
}

func NewSystemNotificationService(repo repository.ISystemNotificationRepository, readSystemNoticeRepo repository.IReadSystemNotificationRepository) ISystemNotificationService {
	return &systemNotificationService{
		repo:                 repo,
		readSystemNoticeRepo: readSystemNoticeRepo,
	}
}

func (s *systemNotificationService) GetSystemNotifications(ctx context.Context) ([]*model.SystemNotification, error) {
	notifications, err := s.repo.GetSystemNotifications(ctx)
	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	result := make([]*model.SystemNotification, 0)
	for _, notification := range notifications {
		tagList := make([]int, 0)
		for _, tag := range strings.Split(notification.Tags, ",") {
			tagInt, _ := strconv.Atoi(tag)
			tagList = append(tagList, tagInt)
		}

		result = append(result, &model.SystemNotification{
			SystemNotificationID: notification.SystemNotificationID,
			Title:                notification.Title,
			Description:          notification.Description,
			Tags:                 tagList,
			CreatedAt:            notification.CreatedAt,
		})
	}
	return result, nil
}

func (s *systemNotificationService) GetReadSystemNotifications(ctx context.Context, staffID int) ([]*int, error) {
	readList, err := s.readSystemNoticeRepo.GetReadSystemNotification(ctx, staffID)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		createErr := s.readSystemNoticeRepo.CreateReadSystemNotification(ctx, staffID, "")

		if createErr != nil {
			return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, createErr))
		}

		return []*int{}, nil
	}

	if err != nil {
		return nil, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if readList == "" {
		return []*int{}, nil
	}

	strReadList := strings.Split(readList, ",")
	intPtrList := make([]*int, len(strReadList))
	for i, s := range strReadList {
		val, _ := strconv.Atoi(s)
		intPtrList[i] = &val
	}

	return intPtrList, nil
}

func (s *systemNotificationService) EditReadSystemNotifications(ctx context.Context, staffID int, readList []*int) error {
	existingReadList, err := s.GetReadSystemNotifications(ctx, staffID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	readIDMap := make(map[string]bool)
	mergedPtrList := append([]*int{}, existingReadList...)
	mergedPtrList = append(mergedPtrList, readList...)
	var mergedReadList []string

	for _, idPtr := range mergedPtrList {
		idStr := strconv.Itoa(*idPtr)
		if !readIDMap[idStr] {
			readIDMap[idStr] = true
			mergedReadList = append(mergedReadList, idStr)
		}
	}

	err = s.readSystemNoticeRepo.EditReadSystemNotification(ctx, staffID, strings.Join(mergedReadList, ","))
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

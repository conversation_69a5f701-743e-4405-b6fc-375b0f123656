package service_test

import (
	"context"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/service"
	mock_repository "denkaru-server/pkg/test_mock/repository"

	"errors"
	"fmt"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func Test_addressService_GetAddressByPostcode(t *testing.T) {
	t.<PERSON>llel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx      context.Context
		postCode string
	}
	tests := []struct {
		name        string
		args        args
		address     []*entity.PostCodeMst
		createError error
		wantErr     error
	}{
		{
			name: "正常系、住所一覧",
			args: args{
				ctx:      context.Background(),
				postCode: "123",
			},
			address: []*entity.PostCodeMst{
				{
					CityName: "CityName",
				},
			},
		},
		{
			name: "異常系、DBエラー",
			args: args{
				ctx:      context.Background(),
				postCode: "123",
			},
			createError: gorm.ErrRecordNotFound,
			wantErr:     myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象の住所は見つかりません"), "住所"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prefectureRepo := mock_repository.NewMockIPrefectureRepository(ctrl)
			commonPostCodeMstRepo := mock_repository.NewMockICommonPostCodeMstRepository(ctrl)

			commonPostCodeMstRepo.EXPECT().FindByPostCode(tt.args.postCode).Return(tt.address, tt.createError)

			s := service.NewAddressService(prefectureRepo, commonPostCodeMstRepo)

			commonPostCodeMst, err := s.GetAddressByPostcode(tt.args.postCode)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(err, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), err.Error())

				}
			} else {
				assert.Equal(t, commonPostCodeMst, tt.address)
				assert.NoError(t, err)
			}
		})
	}
}

func Test_addressService_GetPrefectures(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx      context.Context
		postCode string
	}
	tests := []struct {
		name        string
		args        args
		prefectures []*entity.PortalMPrefecture
		createError error
		wantErr     error
	}{
		{
			name: "正常系、住所一覧",
			args: args{
				ctx:      context.Background(),
				postCode: "123",
			},
			prefectures: []*entity.PortalMPrefecture{
				{
					Name: "Name",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			prefectureRepo := mock_repository.NewMockIPrefectureRepository(ctrl)
			commonPostCodeMstRepo := mock_repository.NewMockICommonPostCodeMstRepository(ctrl)

			prefectureRepo.EXPECT().GetPrefectures().Return(tt.prefectures, tt.createError)

			s := service.NewAddressService(prefectureRepo, commonPostCodeMstRepo)

			commonPostCodeMst, err := s.GetPrefectures(tt.args.ctx)
			if tt.wantErr != nil {
				myErr := new(myerrors.DenkaruError)
				if errors.As(err, &myErr) {
					assert.Equal(t, tt.wantErr.Error(), err.Error())

				}
			} else {
				assert.Equal(t, commonPostCodeMst, tt.prefectures)
				assert.NoError(t, err)
			}
		})
	}
}

package service

import (
	"context"
	gqlModel "denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/entity"
	mockRepository "denkaru-server/pkg/test_mock/repository"
	"fmt"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func Test_portalAddressService_GetPortalAddressByPostcode(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name              string
		findByPostCodeRes []*entity.PostCodeMst
		findByPostCodeErr error
		getCityErr        error
		want              []*gqlModel.PortalAddress
		wantErr           error
	}{
		{
			name: "正常系、1件",
			findByPostCodeRes: []*entity.PostCodeMst{
				{
					PrefName: "東京都",
					CityName: "千代田区",
					PostCd:   "1000001",
					Banti:    "1-1",
				},
			},
			want: []*gqlModel.PortalAddress{
				{
					Postcode:       "1000001",
					PrefectureID:   1111,
					PrefectureName: "東京都",
					CityName:       "千代田区",
					CityID:         1,
					Banti:          "1-1",
				},
			},
		},
		{
			name: "正常系、複数件",
			findByPostCodeRes: []*entity.PostCodeMst{
				{
					PrefName: "東京都",
					CityName: "千代田区",
					PostCd:   "1000001",
					Banti:    "1-1",
				},
				{
					PrefName: "東京都",
					CityName: "千代田区",
					PostCd:   "1000001",
					Banti:    "1-2",
				},
				{
					PrefName: "東京都",
					CityName: "新宿区",
					PostCd:   "1018656",
					Banti:    "123-456",
				},
			},
			want: []*gqlModel.PortalAddress{
				{
					Postcode:       "1000001",
					PrefectureID:   1111,
					PrefectureName: "東京都",
					CityName:       "千代田区",
					CityID:         1,
					Banti:          "1-1",
				},
				{
					Postcode:       "1000001",
					PrefectureID:   1111,
					PrefectureName: "東京都",
					CityName:       "千代田区",
					CityID:         1,
					Banti:          "1-2",
				},
				{
					Postcode:       "1018656",
					PrefectureID:   2222,
					PrefectureName: "東京都",
					CityName:       "新宿区",
					CityID:         2,
					Banti:          "123-456",
				},
			},
		},
		{
			name:              "異常系、FindByPostCodeエラー",
			findByPostCodeErr: fmt.Errorf("findByPostCodeErr error"),
			wantErr:           myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("findByPostCodeErr error")),
		},
		{
			name:              "異常系、FindByPostCode NotFound",
			findByPostCodeErr: gorm.ErrRecordNotFound,
			wantErr:           myerrors.NewDenkaruError(definitions.DenkaruCodeRecordNotFound, fmt.Errorf("対象のポータルアドレスは見つかりません"), "ポータルアドレス"),
		},
		{
			name:              "異常系、FindByPostCode結果0件",
			findByPostCodeRes: []*entity.PostCodeMst{},
			wantErr:           errors.New("address not found"),
		},
		{
			name: "異常系、GetCityByCityNameAndPrefNameエラー",
			findByPostCodeRes: []*entity.PostCodeMst{
				{
					PrefName: "東京都",
					CityName: "千代田区",
					PostCd:   "1000001",
					Banti:    "1-1",
				},
			},
			getCityErr: fmt.Errorf("getCityByCityNameAndPrefName error"),
			wantErr:    myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("getCityByCityNameAndPrefName error")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			commonPostCodeMstRepo := mockRepository.NewMockICommonPostCodeMstRepository(ctrl)
			cityRepo := mockRepository.NewMockIPortalCityRepository(ctrl)

			testee := NewPortalAddressService(cityRepo, nil, commonPostCodeMstRepo)

			commonPostCodeMstRepo.EXPECT().FindByPostCode(gomock.Any()).Return(tt.findByPostCodeRes, tt.findByPostCodeErr).AnyTimes()
			cityRepo.EXPECT().GetCityByCityNameAndPrefName(gomock.Eq("千代田区"), gomock.Any()).Return(&entity.PortalMCity{
				CityID:       1,
				Name:         "千代田区",
				GroupCode:    "111",
				PrefectureID: 1111,
				CityCode:     "11111",
			}, tt.getCityErr).AnyTimes()
			cityRepo.EXPECT().GetCityByCityNameAndPrefName(gomock.Eq("新宿区"), gomock.Any()).Return(&entity.PortalMCity{
				CityID:       2,
				Name:         "新宿区",
				GroupCode:    "222",
				PrefectureID: 2222,
				CityCode:     "22222",
			}, tt.getCityErr).AnyTimes()

			got, err := testee.GetPortalAddressByPostcode(context.Background(), "1000001")

			assert.Equal(t, len(tt.want), len(got))
			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				if errors.As(err, &denkaruError) {
					assert.Equal(t, tt.wantErr, denkaruError)
				} else {
					assert.Equal(t, tt.wantErr.Error(), err.Error())
				}
			} else {
				assert.NoError(t, err)

			GotLoop:
				for _, g := range got {
					for _, w := range tt.want {
						if g.PrefectureName == w.PrefectureName &&
							g.CityName == w.CityName &&
							g.Banti == w.Banti {
							assert.Equal(t, w.PrefectureID, g.PrefectureID)
							assert.Equal(t, w.Postcode, g.Postcode)
							assert.Equal(t, w.CityID, g.CityID)
							continue GotLoop
						}
					}
					// ここまできたら一致するものがなかったのでFailにする
					assert.Fail(t, fmt.Sprintf("got %v%v%v not found in want", g.PrefectureName, g.CityName, g.Banti))
				}
			}
		})
	}
}

func Test_portalAddressService_GetPrefectures(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name              string
		getPrefecturesRes []*entity.PortalMPrefecture
		getPrefecturesErr error
		want              []*entity.PortalMPrefecture
		wantErr           error
	}{
		{
			name:              "正常系、0件",
			getPrefecturesRes: []*entity.PortalMPrefecture{},
			want:              []*entity.PortalMPrefecture{},
		},
		{
			name: "正常系、1件",
			getPrefecturesRes: []*entity.PortalMPrefecture{
				{
					PrefectureID: 1,
					Name:         "住所１",
				},
			},
			want: []*entity.PortalMPrefecture{
				{
					PrefectureID: 1,
					Name:         "住所１",
				},
			},
		},
		{
			name: "正常系、複数件",
			getPrefecturesRes: []*entity.PortalMPrefecture{
				{
					PrefectureID: 1,
					Name:         "住所１",
				},
				{
					PrefectureID: 2,
					Name:         "住所２",
				},
			},
			want: []*entity.PortalMPrefecture{
				{
					PrefectureID: 1,
					Name:         "住所１",
				},
				{
					PrefectureID: 2,
					Name:         "住所２",
				},
			},
		},
		{
			name:              "異常系",
			getPrefecturesErr: fmt.Errorf("getPrefectures error"),
			wantErr:           myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("getPrefectures error")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repo := mockRepository.NewMockIPortalPrefectureRepository(ctrl)

			testee := NewPortalAddressService(nil, repo, nil)

			repo.EXPECT().GetPrefectures().Return(tt.getPrefecturesRes, tt.getPrefecturesErr).AnyTimes()

			got, err := testee.GetPrefectures(context.Background())

			if tt.wantErr != nil {
				var denkaruError *myerrors.DenkaruError
				assert.True(t, errors.As(err, &denkaruError))
				assert.Equal(t, tt.wantErr, denkaruError)
			} else {
				assert.NoError(t, err)

			GotLoop:
				for _, g := range got {
					for _, w := range tt.want {
						if g.PrefectureID == w.PrefectureID {
							assert.Equal(t, w.Name, g.Name)
							continue GotLoop
						}
					}
					// ここまできたら一致するものがなかったのでFailにする
					assert.Fail(t, fmt.Sprintf("got %v not found in want", g.PrefectureID))
				}
			}
		})
	}
}

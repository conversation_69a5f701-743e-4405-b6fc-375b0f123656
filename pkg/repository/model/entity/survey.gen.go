// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameSurvey = "survey"

// Survey mapped from table <survey>
type Survey struct {
	SurveyID   int       `gorm:"column:survey_id;primaryKey;autoIncrement:true" json:"survey_id"`
	FQuesJSON  string    `gorm:"column:f_ques_json;not null" json:"f_ques_json"`
	Name       string    `gorm:"column:name;not null" json:"name"`
	HospitalID int       `gorm:"column:hospital_id;not null" json:"hospital_id"`
	CreatedBy  string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy  string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	IsDeleted  int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	Secret     string    `gorm:"column:secret;comment:sha256.Sum256" json:"secret"` // sha256.Sum256
}

// TableName Survey's table name
func (*Survey) TableName() string {
	return TableNameSurvey
}

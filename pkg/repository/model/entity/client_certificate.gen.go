// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameClientCertificate = "client_certificate"

// ClientCertificate mapped from table <client_certificate>
type ClientCertificate struct {
	ClientCertificateID int       `gorm:"column:client_certificate_id;primaryKey;autoIncrement:true" json:"client_certificate_id"`
	CommonName          string    `gorm:"column:common_name;not null" json:"common_name"`
	ClientCertificate   []uint8   `gorm:"column:client_certificate;not null" json:"client_certificate"`
	DownloadURL         string    `gorm:"column:download_url;not null" json:"download_url"`
	ExpirationDate      time.Time `gorm:"column:expiration_date;not null" json:"expiration_date"`
	HospitalID          int       `gorm:"column:hospital_id;not null" json:"hospital_id"`
	InstallPassword     string    `gorm:"column:install_password;not null" json:"install_password"`
	IssueDate           time.Time `gorm:"column:issue_date;not null" json:"issue_date"`
	Label               string    `gorm:"column:label;not null" json:"label"`
	Token               string    `gorm:"column:token;not null" json:"token"`
	TokenExpireTime     time.Time `gorm:"column:token_expire_time;not null" json:"token_expire_time"`
	CreatedBy           string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy           string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt           time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt           time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted           int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName ClientCertificate's table name
func (*ClientCertificate) TableName() string {
	return TableNameClientCertificate
}

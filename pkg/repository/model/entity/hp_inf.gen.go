// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpInf = "hp_inf"

// HpInf mapped from table <hp_inf>
type HpInf struct {
	HpID                          int       `gorm:"column:hp_id;primaryKey;autoIncrement:true" json:"hp_id"`
	StartDate                     int       `gorm:"column:start_date;not null" json:"start_date"`
	HpCd                          string    `gorm:"column:hp_cd" json:"hp_cd"`
	RousaiHpCd                    string    `gorm:"column:rousai_hp_cd" json:"rousai_hp_cd"`
	HpName                        string    `gorm:"column:hp_name" json:"hp_name"`
	ReceHpName                    string    `gorm:"column:rece_hp_name" json:"rece_hp_name"`
	KaisetuName                   string    `gorm:"column:kaisetu_name" json:"kaisetu_name"`
	PostCd                        string    `gorm:"column:post_cd" json:"post_cd"`
	PrefNo                        int       `gorm:"column:pref_no;not null" json:"pref_no"`
	InsuranceCategory             string    `gorm:"column:insurance_category" json:"insurance_category"`
	Address1                      string    `gorm:"column:address1" json:"address1"`
	Address2                      string    `gorm:"column:address2" json:"address2"`
	Tel                           string    `gorm:"column:tel" json:"tel"`
	CreateDate                    time.Time `gorm:"column:create_date;not null;default:statement_timestamp()" json:"create_date"`
	CreateID                      int       `gorm:"column:create_id;not null" json:"create_id"`
	CreateMachine                 string    `gorm:"column:create_machine" json:"create_machine"`
	UpdateDate                    time.Time `gorm:"column:update_date;not null;default:statement_timestamp()" json:"update_date"`
	UpdateID                      int       `gorm:"column:update_id;not null" json:"update_id"`
	UpdateMachine                 string    `gorm:"column:update_machine" json:"update_machine"`
	FaxNo                         string    `gorm:"column:fax_no" json:"fax_no"`
	OtherContact                  string    `gorm:"column:other_contacts" json:"other_contacts"`
	Status                        int16     `gorm:"column:status;not null" json:"status"`
	IsOpen                        bool      `gorm:"column:is_open" json:"is_open"`
	IsInsuranceMedicalInstitution bool      `gorm:"column:is_insurance_medical_institution" json:"is_insurance_medical_institution"`
	IsReceiveNotification         bool      `gorm:"column:is_receive_notifications;comment:メール配信許諾の可否" json:"is_receive_notifications"` // メール配信許諾の可否
	IsTermsPrivacyAgreed          bool      `gorm:"column:is_terms_privacy_agreed" json:"is_terms_privacy_agreed"`
	HomepageURL                   string    `gorm:"column:homepage_url" json:"homepage_url"`
	SignupDate                    time.Time `gorm:"column:signup_date" json:"signup_date"`
	ReviewCompletedDate           time.Time `gorm:"column:review_completed_date" json:"review_completed_date"`
	CancellationDate              time.Time `gorm:"column:cancellation_date" json:"cancellation_date"`
	PharmacyFlg                   bool      `gorm:"column:pharmacy_flg" json:"pharmacy_flg"`
	IsDeleted                     int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	Tel2                          string    `gorm:"column:tel2;comment:主に薬局24が利用する2つ目の電話番号を保持するカラム" json:"tel2"` // 主に薬局24が利用する2つ目の電話番号を保持するカラム
	IsSmsAuthenticated            bool      `gorm:"column:is_sms_authenticated" json:"is_sms_authenticated"`
	KarteStatus                   *int64    `gorm:"column:karte_status" json:"karte_status"`
}

// TableName HpInf's table name
func (*HpInf) TableName() string {
	return TableNameHpInf
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePortalMImportHospital = "portal_m_import_hospital"

// PortalMImportHospital mapped from table <portal_m_import_hospital>
type PortalMImportHospital struct {
	ImportHospitalID     int       `gorm:"column:import_hospital_id;primaryKey;autoIncrement:true" json:"import_hospital_id"`
	ScuelOfficeCode      string    `gorm:"column:scuel_office_code;comment:SCUEL事業所コード" json:"scuel_office_code"`          // SCUEL事業所コード
	ScuelCorporationCode string    `gorm:"column:scuel_corporation_code;comment:SCUEL法人コード" json:"scuel_corporation_code"` // SCUEL法人コード
	ScuelID              string    `gorm:"column:scuel_id;not null;comment:SCUEL_ID" json:"scuel_id"`                      // SCUEL_ID
	DataUpdateDate       time.Time `gorm:"column:data_update_date;not null;comment:データ更新年月日" json:"data_update_date"`      // データ更新年月日
	Name                 string    `gorm:"column:name;not null;comment:事業所_名称" json:"name"`                                // 事業所_名称
	NameKana             string    `gorm:"column:name_kana;not null;comment:事業所_名称カナ" json:"name_kana"`                    // 事業所_名称カナ
	Postcode             string    `gorm:"column:postcode;not null;comment:事業所_郵便番号" json:"postcode"`                      // 事業所_郵便番号
	PrefectureCode       string    `gorm:"column:prefecture_code;not null;comment:事業所_都道府県コード" json:"prefecture_code"`     // 事業所_都道府県コード
	PrefectureName       string    `gorm:"column:prefecture_name;not null;comment:事業所_都道府県" json:"prefecture_name"`        // 事業所_都道府県
	CityCode             string    `gorm:"column:city_code;not null;comment:事業所_市区町村コード" json:"city_code"`                 // 事業所_市区町村コード
	CityName             string    `gorm:"column:city_name;not null;comment:事業所_市区町村" json:"city_name"`                    // 事業所_市区町村
	AddressCode          string    `gorm:"column:address_code;comment:事業所_住所コード" json:"address_code"`                      // 事業所_住所コード
	AddressDetail        string    `gorm:"column:address_detail;not null;comment:事業所_住所" json:"address_detail"`            // 事業所_住所
	Latitude             float64   `gorm:"column:latitude;comment:事業所_緯度" json:"latitude"`                                 // 事業所_緯度
	Longitude            float64   `gorm:"column:longitude;comment:事業所_経度" json:"longitude"`                               // 事業所_経度
	/*
		事業所_緯度経度精度レベル：
		1：都道府県、2：市区町村、3：大字、4：字丁目、5：街区、6：地番、7：枝番、空：手修正/新住所未確認
	*/
	AddressAccuracyLevel              *int      `gorm:"column:address_accuracy_level;comment:事業所_緯度経度精度レベル：\n1：都道府県、2：市区町村、3：大字、4：字丁目、5：街区、6：地番、7：枝番、空：手修正/新住所未確認" json:"address_accuracy_level"`
	StationID                         string    `gorm:"column:station_id;comment:事業所_駅グループコード" json:"station_id"`            // 事業所_駅グループコード
	StationName                       string    `gorm:"column:station_name;comment:事業所_最寄駅名" json:"station_name"`            // 事業所_最寄駅名
	StationDetail                     string    `gorm:"column:station_detail;comment:事業所_交通手段" json:"station_detail"`        // 事業所_交通手段
	StationDistance                   *int      `gorm:"column:station_distance;comment:事業所_最寄駅との距離" json:"station_distance"` // 事業所_最寄駅との距離
	IsOnlineTreatmentAvailable        bool      `gorm:"column:is_online_treatment_available;not null" json:"is_online_treatment_available"`
	IsElectronicPrescriptionAvailable bool      `gorm:"column:is_electronic_prescription_available;not null" json:"is_electronic_prescription_available"`
	CreatedBy                         string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy                         string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt                         time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	UpdatedAt                         time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	IsDeleted                         int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	PortalHospitalID                  *int      `gorm:"column:portal_hospital_id" json:"portal_hospital_id"`
}

// TableName PortalMImportHospital's table name
func (*PortalMImportHospital) TableName() string {
	return TableNamePortalMImportHospital
}

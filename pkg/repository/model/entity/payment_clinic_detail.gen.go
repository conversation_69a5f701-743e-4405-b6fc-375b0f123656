// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePaymentClinicDetail = "payment_clinic_detail"

// PaymentClinicDetail mapped from table <payment_clinic_detail>
type PaymentClinicDetail struct {
	PaymentClinicDetailID int64     `gorm:"column:payment_clinic_detail_id;primaryKey;autoIncrement:true" json:"payment_clinic_detail_id"`
	HospitalID            int       `gorm:"column:hospital_id;not null" json:"hospital_id"`
	PatientID             int       `gorm:"column:patient_id;not null" json:"patient_id"`
	CustomerID            int       `gorm:"column:customer_id;not null" json:"customer_id"`
	ActionType            int       `gorm:"column:action_type;not null;default:1;comment:constant.go - 会計アクション(クリニック,薬局共通) 参照" json:"action_type"`                         // constant.go - 会計アクション(クリニック,薬局共通) 参照
	PaymentType           int       `gorm:"column:payment_type;not null;comment:constant.go - PaymentTypeの支払い方法 参照constant.go - PaymentTypeの支払い方法 参照" json:"payment_type"` // constant.go - PaymentTypeの支払い方法 参照constant.go - PaymentTypeの支払い方法 参照
	PaymentDate           time.Time `gorm:"column:payment_date;not null;default:statement_timestamp()" json:"payment_date"`
	PaymentStatus         int       `gorm:"column:payment_status;not null;comment:constant.go - 会計ステータス(クリニック,薬局共通) 参照" json:"payment_status"`   // constant.go - 会計ステータス(クリニック,薬局共通) 参照
	FirstSuccessDate      time.Time `gorm:"column:first_success_date;comment:翌月3営業日を超える場合は決済の変更/キャンセルはできないため、その日付計算用" json:"first_success_date"` // 翌月3営業日を超える場合は決済の変更/キャンセルはできないため、その日付計算用
	TotalAmount           int       `gorm:"column:total_amount;not null" json:"total_amount"`
	DepositAmount         int       `gorm:"column:deposit_amount;not null" json:"deposit_amount"`
	IsRetryable           int       `gorm:"column:is_retryable;not null" json:"is_retryable"`
	FincodeErrorCode      string    `gorm:"column:fincode_error_code" json:"fincode_error_code"`
	ErrorMessage          string    `gorm:"column:error_message" json:"error_message"`
	PaymentFincodeOrderID *int64    `gorm:"column:payment_fincode_order_id" json:"payment_fincode_order_id"`
	ReserveDetailID       int       `gorm:"column:reserve_detail_id;not null" json:"reserve_detail_id"`
	ExamDate              time.Time `gorm:"column:exam_date;not null" json:"exam_date"`
	TreatmentCategoryID   int       `gorm:"column:treatment_category_id;not null" json:"treatment_category_id"`
	TreatmentDepartmentID int       `gorm:"column:treatment_department_id;not null" json:"treatment_department_id"`
	CardNo                string    `gorm:"column:card_no;not null" json:"card_no"`
	Expire                string    `gorm:"column:expire;not null" json:"expire"`
	HolderName            string    `gorm:"column:holder_name;not null" json:"holder_name"`
	Brand                 string    `gorm:"column:brand;not null" json:"brand"`
	CreatedAt             time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy             string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt             time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy             string    `gorm:"column:updated_by;not null" json:"updated_by"`
	IsDeleted             int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName PaymentClinicDetail's table name
func (*PaymentClinicDetail) TableName() string {
	return TableNamePaymentClinicDetail
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameCalendarBasicSetting = "calendar_basic_setting"

// CalendarBasicSetting mapped from table <calendar_basic_setting>
type CalendarBasicSetting struct {
	CalendarBasicSettingID int       `gorm:"column:calendar_basic_setting_id;primaryKey;autoIncrement:true" json:"calendar_basic_setting_id"`
	CreatedAt              time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy              string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt              time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy              string    `gorm:"column:updated_by;not null" json:"updated_by"`
	CalendarID             int       `gorm:"column:calendar_id;not null" json:"calendar_id"`
	StartDate              time.Time `gorm:"column:start_date;not null" json:"start_date"`
	EndDate                time.Time `gorm:"column:end_date" json:"end_date"`
	DaysOfWeek             string    `gorm:"column:days_of_week" json:"days_of_week"`
	WeeksOfMonth           string    `gorm:"column:weeks_of_month" json:"weeks_of_month"`
	StartTime              string    `gorm:"column:start_time;not null" json:"start_time"`
	EndTime                string    `gorm:"column:end_time;not null" json:"end_time"`
	CloseOnHolidayFlag     bool      `gorm:"column:close_on_holiday_flag;not null;default:true" json:"close_on_holiday_flag"`
	ReservableSlot         *int      `gorm:"column:reservable_slot" json:"reservable_slot"`
	StartWaitingNumber     *int      `gorm:"column:start_waiting_number" json:"start_waiting_number"`
	IsDeleted              int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName CalendarBasicSetting's table name
func (*CalendarBasicSetting) TableName() string {
	return TableNameCalendarBasicSetting
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameLineAccount = "line_account"

// LineAccount mapped from table <line_account>
type LineAccount struct {
	ID              int       `gorm:"column:id;not null" json:"id"`
	CustomerID      *int      `gorm:"column:customer_id" json:"customer_id"`
	LineUserID      string    `gorm:"column:line_user_id;not null" json:"line_user_id"`
	DisplayName     string    `gorm:"column:display_name" json:"display_name"`
	PictureURL      string    `gorm:"column:picture_url" json:"picture_url"`
	IsLinked        float64   `gorm:"column:is_linked;not null" json:"is_linked"`
	LineLinkToken   string    `gorm:"column:line_link_token" json:"line_link_token"`
	LinkTokenExpiry time.Time `gorm:"column:link_token_expiry" json:"link_token_expiry"`
	CreatedAt       time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy       string    `gorm:"column:created_by" json:"created_by"`
	UpdatedAt       time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy       string    `gorm:"column:updated_by" json:"updated_by"`
	IsDeleted       int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName LineAccount's table name
func (*LineAccount) TableName() string {
	return TableNameLineAccount
}

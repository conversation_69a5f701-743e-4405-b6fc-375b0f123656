// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePostCodeMst = "post_code_mst"

// PostCodeMst mapped from table <post_code_mst>
type PostCodeMst struct {
	PostCd         string    `gorm:"column:post_cd" json:"post_cd"`
	PrefKana       string    `gorm:"column:pref_kana" json:"pref_kana"`
	CityKana       string    `gorm:"column:city_kana" json:"city_kana"`
	PostalTermKana string    `gorm:"column:postal_term_kana" json:"postal_term_kana"`
	PrefName       string    `gorm:"column:pref_name" json:"pref_name"`
	CityName       string    `gorm:"column:city_name" json:"city_name"`
	Banti          string    `gorm:"column:banti" json:"banti"`
	IsDeleted      int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	CreateDate     time.Time `gorm:"column:create_date;not null;default:CURRENT_TIMESTAMP" json:"create_date"`
	CreateID       int       `gorm:"column:create_id;not null" json:"create_id"`
	CreateMachine  string    `gorm:"column:create_machine" json:"create_machine"`
	UpdateDate     time.Time `gorm:"column:update_date;not null" json:"update_date"`
	UpdateID       int       `gorm:"column:update_id;not null" json:"update_id"`
	UpdateMachine  string    `gorm:"column:update_machine" json:"update_machine"`
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
}

// TableName PostCodeMst's table name
func (*PostCodeMst) TableName() string {
	return TableNamePostCodeMst
}

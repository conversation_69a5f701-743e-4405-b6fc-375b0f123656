// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePtInf = "pt_inf"

// PtInf mapped from table <pt_inf>
type PtInf struct {
	HpID             int       `gorm:"column:hp_id;primaryKey" json:"hp_id"`
	PtID             int64     `gorm:"column:pt_id;primaryKey;autoIncrement:true" json:"pt_id"`
	SeqNo            int64     `gorm:"column:seq_no;primaryKey;autoIncrement:true" json:"seq_no"`
	PtNum            int64     `gorm:"column:pt_num;not null" json:"pt_num"`
	KanaName         string    `gorm:"column:kana_name" json:"kana_name"`
	Name             string    `gorm:"column:name" json:"name"`
	Sex              int       `gorm:"column:sex;not null" json:"sex"`
	Birthday         int       `gorm:"column:birthday;not null" json:"birthday"`
	IsDead           int       `gorm:"column:is_dead;not null" json:"is_dead"`
	DeathDate        int       `gorm:"column:death_date;not null" json:"death_date"`
	HomePost         string    `gorm:"column:home_post" json:"home_post"`
	HomeAddress1     string    `gorm:"column:home_address1" json:"home_address1"`
	HomeAddress2     string    `gorm:"column:home_address2" json:"home_address2"`
	Tel1             string    `gorm:"column:tel1" json:"tel1"`
	Tel2             string    `gorm:"column:tel2" json:"tel2"`
	Mail             string    `gorm:"column:mail" json:"mail"`
	Setainusi        string    `gorm:"column:setainusi" json:"setainusi"`
	Zokugara         string    `gorm:"column:zokugara" json:"zokugara"`
	Job              string    `gorm:"column:job" json:"job"`
	RenrakuName      string    `gorm:"column:renraku_name" json:"renraku_name"`
	RenrakuPost      string    `gorm:"column:renraku_post" json:"renraku_post"`
	RenrakuAddress1  string    `gorm:"column:renraku_address1" json:"renraku_address1"`
	RenrakuAddress2  string    `gorm:"column:renraku_address2" json:"renraku_address2"`
	RenrakuTel       string    `gorm:"column:renraku_tel" json:"renraku_tel"`
	RenrakuMemo      string    `gorm:"column:renraku_memo" json:"renraku_memo"`
	OfficeName       string    `gorm:"column:office_name" json:"office_name"`
	OfficePost       string    `gorm:"column:office_post" json:"office_post"`
	OfficeAddress1   string    `gorm:"column:office_address1" json:"office_address1"`
	OfficeAddress2   string    `gorm:"column:office_address2" json:"office_address2"`
	OfficeTel        string    `gorm:"column:office_tel" json:"office_tel"`
	OfficeMemo       string    `gorm:"column:office_memo" json:"office_memo"`
	IsRyosyoDetail   int       `gorm:"column:is_ryosyo_detail;not null;default:1" json:"is_ryosyo_detail"`
	PrimaryDoctor    int       `gorm:"column:primary_doctor;not null" json:"primary_doctor"`
	IsTester         int       `gorm:"column:is_tester;not null" json:"is_tester"`
	IsDelete         int       `gorm:"column:is_delete;not null" json:"is_delete"`
	CreateDate       time.Time `gorm:"column:create_date;not null;default:CURRENT_TIMESTAMP" json:"create_date"`
	CreateID         int       `gorm:"column:create_id;not null" json:"create_id"`
	CreateMachine    string    `gorm:"column:create_machine" json:"create_machine"`
	UpdateDate       time.Time `gorm:"column:update_date;not null;default:CURRENT_TIMESTAMP" json:"update_date"`
	UpdateID         int       `gorm:"column:update_id;not null" json:"update_id"`
	UpdateMachine    string    `gorm:"column:update_machine" json:"update_machine"`
	MainHokenPid     int       `gorm:"column:main_hoken_pid;not null" json:"main_hoken_pid"`
	ReferenceNo      int64     `gorm:"column:reference_no;not null" json:"reference_no"`
	LimitConsFlg     int       `gorm:"column:limit_cons_flg;not null" json:"limit_cons_flg"`
	PortalCustomerID *int      `gorm:"column:portal_customer_id;comment:GMOクリニック・マップの会員ID" json:"portal_customer_id"` // GMOクリニック・マップの会員ID
	RenrakuName2     string    `gorm:"column:renraku_name2" json:"renraku_name2"`
	RenrakuTel2      string    `gorm:"column:renraku_tel2" json:"renraku_tel2"`
	HoumonAgreed     int       `gorm:"column:houmon_agreed;not null;comment:主治医名" json:"houmon_agreed"` // 主治医名
}

// TableName PtInf's table name
func (*PtInf) TableName() string {
	return TableNamePtInf
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePortalHospital = "portal_hospital"

// PortalHospital mapped from table <portal_hospital>
type PortalHospital struct {
	HospitalID          int       `gorm:"column:hospital_id;primaryKey;autoIncrement:true" json:"hospital_id"`
	CreatedAt           time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy           string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt           time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy           string    `gorm:"column:updated_by;not null" json:"updated_by"`
	HpInfID             int       `gorm:"column:hp_inf_id;not null" json:"hp_inf_id"`
	Name                string    `gorm:"column:name;not null" json:"name"`
	Description         string    `gorm:"column:description" json:"description"`
	Telephone           string    `gorm:"column:telephone;not null" json:"telephone"`
	CarparkDetail       string    `gorm:"column:carpark_detail" json:"carpark_detail"`
	PostCode            string    `gorm:"column:post_code;not null" json:"post_code"`
	Address1            string    `gorm:"column:address1;not null;comment:address by post code" json:"address1"` // address by post code
	Address2            string    `gorm:"column:address2;comment:more info address" json:"address2"`             // more info address
	Latitude            float64   `gorm:"column:latitude" json:"latitude"`
	Longitude           float64   `gorm:"column:longitude" json:"longitude"`
	Email               string    `gorm:"column:email;not null;comment:メールアドレス" json:"email"` // メールアドレス
	Fax                 string    `gorm:"column:fax" json:"fax"`
	IsCarpark           bool      `gorm:"column:is_carpark;not null" json:"is_carpark"`
	DescriptionTitle    string    `gorm:"column:description_title;not null" json:"description_title"`
	PaymentDetail       string    `gorm:"column:payment_details;not null" json:"payment_details"`
	IsActive            bool      `gorm:"column:is_active;not null" json:"is_active"`
	BusinessTimeDetail  string    `gorm:"column:business_time_detail" json:"business_time_detail"`
	TimelineDescription string    `gorm:"column:timeline_description" json:"timeline_description"`
	HolidayDetail       string    `gorm:"column:holiday_detail" json:"holiday_detail"`
	AccessDetail        string    `gorm:"column:access_detail" json:"access_detail"`
	HomePage            string    `gorm:"column:home_page" json:"home_page"`
	ScuelID             string    `gorm:"column:scuel_id" json:"scuel_id"`
	IsDeleted           int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	DirectorName        string    `gorm:"column:director_name" json:"director_name"`
	MailAddress         string    `gorm:"column:mail_address" json:"mail_address"`
}

// TableName PortalHospital's table name
func (*PortalHospital) TableName() string {
	return TableNamePortalHospital
}

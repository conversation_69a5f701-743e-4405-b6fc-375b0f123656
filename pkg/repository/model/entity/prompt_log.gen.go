// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePromptLog = "prompt_log"

// PromptLog mapped from table <prompt_log>
type PromptLog struct {
	PromptID   int       `gorm:"column:prompt_id;primaryKey" json:"prompt_id"`
	UserID     int       `gorm:"column:user_id;primaryKey" json:"user_id"`
	OrderValue int       `gorm:"column:order_value;not null;comment:プロンプト表示順番" json:"order_value"`                     // プロンプト表示順番
	IsTemplate bool      `gorm:"column:is_template;not null;comment:true: システムから設定、false: ユーザから設定" json:"is_template"` // true: システムから設定、false: ユーザから設定
	IsLatest   bool      `gorm:"column:is_latest;not null;comment:最後の使うプロンプト" json:"is_latest"`                        // 最後の使うプロンプト
	IsActive   bool      `gorm:"column:is_active;not null;comment:プロンプトの状態、true: 有効、false: 無効" json:"is_active"`       // プロンプトの状態、true: 有効、false: 無効
	CreatedBy  string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy  string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	IsDeleted  int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName PromptLog's table name
func (*PromptLog) TableName() string {
	return TableNamePromptLog
}

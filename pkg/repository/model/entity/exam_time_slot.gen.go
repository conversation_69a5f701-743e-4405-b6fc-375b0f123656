// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameExamTimeSlot = "exam_time_slot"

// ExamTimeSlot mapped from table <exam_time_slot>
type ExamTimeSlot struct {
	ExamTimeSlotID      int       `gorm:"column:exam_time_slot_id;primaryKey;autoIncrement:true" json:"exam_time_slot_id"`
	CreatedAt           time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy           string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt           time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy           string    `gorm:"column:updated_by;not null" json:"updated_by"`
	CalendarID          int       `gorm:"column:calendar_id;not null" json:"calendar_id"`
	ExamStartDate       time.Time `gorm:"column:exam_start_date;not null" json:"exam_start_date"`
	ExamEndDate         time.Time `gorm:"column:exam_end_date;not null" json:"exam_end_date"`
	SlotLimitReserveNum int       `gorm:"column:slot_limit_reserve_num;not null" json:"slot_limit_reserve_num"`
	TreatmentType       int       `gorm:"column:treatment_type;not null" json:"treatment_type"`
	IsDeleted           int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName ExamTimeSlot's table name
func (*ExamTimeSlot) TableName() string {
	return TableNameExamTimeSlot
}

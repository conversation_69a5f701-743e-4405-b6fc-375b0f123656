// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameUserMst = "user_mst"

// UserMst mapped from table <user_mst>
type UserMst struct {
	HpID             int       `gorm:"column:hp_id;not null" json:"hp_id"`
	ID               int       `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID           int       `gorm:"column:user_id;not null" json:"user_id"`
	JobCd            int       `gorm:"column:job_cd;not null" json:"job_cd"`
	ManagerKbn       int       `gorm:"column:manager_kbn;not null;comment:constants.go - スタッフ管理者区分 参照" json:"manager_kbn"` // constants.go - スタッフ管理者区分 参照
	KaID             int       `gorm:"column:ka_id;not null" json:"ka_id"`
	KanaName         string    `gorm:"column:kana_name" json:"kana_name"`
	Name             string    `gorm:"column:name;not null" json:"name"`
	Sname            string    `gorm:"column:sname;not null" json:"sname"`
	LoginID          string    `gorm:"column:login_id;not null" json:"login_id"`
	MayakuLicenseNo  string    `gorm:"column:mayaku_license_no" json:"mayaku_license_no"`
	StartDate        int       `gorm:"column:start_date;not null" json:"start_date"`
	EndDate          int       `gorm:"column:end_date;not null;default:99999999" json:"end_date"`
	SortNo           int       `gorm:"column:sort_no;not null" json:"sort_no"`
	IsDeleted        int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	CreateDate       time.Time `gorm:"column:create_date;not null;default:CURRENT_TIMESTAMP" json:"create_date"`
	CreateID         int       `gorm:"column:create_id;not null" json:"create_id"`
	CreateMachine    string    `gorm:"column:create_machine" json:"create_machine"`
	UpdateDate       time.Time `gorm:"column:update_date;not null" json:"update_date"`
	UpdateID         int       `gorm:"column:update_id;not null" json:"update_id"`
	UpdateMachine    string    `gorm:"column:update_machine" json:"update_machine"`
	RenkeiCd1        string    `gorm:"column:renkei_cd1" json:"renkei_cd1"`
	DrName           string    `gorm:"column:dr_name" json:"dr_name"`
	LoginType        *int      `gorm:"column:login_type" json:"login_type"`
	HpkiSn           string    `gorm:"column:hpki_sn" json:"hpki_sn"`
	HpkiIssuerDn     string    `gorm:"column:hpki_issuer_dn" json:"hpki_issuer_dn"`
	HashPassword     string    `gorm:"column:hash_password;not null" json:"hash_password"`
	Salt             string    `gorm:"column:salt;not null" json:"salt"`
	Email            string    `gorm:"column:email" json:"email"`
	IsInitLoginID    int       `gorm:"column:is_init_login_id;not null;comment:0:変更済みログインID, 1:初回ログインID" json:"is_init_login_id"` // 0:変更済みログインID, 1:初回ログインID
	IsInitPassword   int       `gorm:"column:is_init_password;not null;comment:0:変更済みパスワード, 1:初回パスワード" json:"is_init_password"`   // 0:変更済みパスワード, 1:初回パスワード
	MissLoginCount   int       `gorm:"column:miss_login_count;not null" json:"miss_login_count"`
	Status           int       `gorm:"column:status;not null" json:"status"`
	EmailUpdateDate  time.Time `gorm:"column:email_update_date" json:"email_update_date"`
	MedicalLicenseNo string    `gorm:"column:medical_license_no;comment:医籍登録番号" json:"medical_license_no"` // 医籍登録番号
}

// TableName UserMst's table name
func (*UserMst) TableName() string {
	return TableNameUserMst
}

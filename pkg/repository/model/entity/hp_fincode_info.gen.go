// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpFincodeInfo = "hp_fincode_info"

// HpFincodeInfo mapped from table <hp_fincode_info>
type HpFincodeInfo struct {
	HpID           int       `gorm:"column:hp_id;primaryKey" json:"hp_id"`
	HpTenantShopID string    `gorm:"column:hp_tenant_shop_id;not null" json:"hp_tenant_shop_id"`
	PlatformID     int       `gorm:"column:platform_id;not null;comment:constant.go - 会計プラットフォーム 参照" json:"platform_id"` // constant.go - 会計プラットフォーム 参照
	CreatedAt      time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy      string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt      time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy      string    `gorm:"column:updated_by;not null" json:"updated_by"`
	IsDeleted      int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName HpFincodeInfo's table name
func (*HpFincodeInfo) TableName() string {
	return TableNameHpFincodeInfo
}

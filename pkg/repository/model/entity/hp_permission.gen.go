// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpPermission = "hp_permission"

// HpPermission mapped from table <hp_permission>
type HpPermission struct {
	HpID       int       `gorm:"column:hp_id;primaryKey" json:"hp_id"`
	FeatureID  int       `gorm:"column:feature_id;primaryKey" json:"feature_id"`
	Permission int       `gorm:"column:permission;not null" json:"permission"`
	CreatedBy  string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy  string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt  time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted  int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName HpPermission's table name
func (*HpPermission) TableName() string {
	return TableNameHpPermission
}

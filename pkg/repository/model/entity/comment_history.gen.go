// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameCommentHistory = "comment_history"

// CommentHistory mapped from table <comment_history>
type CommentHistory struct {
	CommentHistoryID int       `gorm:"column:comment_history_id;primaryKey;autoIncrement:true" json:"comment_history_id"`
	CommentID        int       `gorm:"column:comment_id;not null" json:"comment_id"`
	History          string    `gorm:"column:history" json:"history"`
	CreatedBy        string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy        string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt        time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted        int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName CommentHistory's table name
func (*CommentHistory) TableName() string {
	return TableNameCommentHistory
}

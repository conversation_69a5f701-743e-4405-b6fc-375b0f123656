// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpWhiteBoardLog = "hp_white_board_log"

// HpWhiteBoardLog mapped from table <hp_white_board_log>
type HpWhiteBoardLog struct {
	HpID            int       `gorm:"column:hp_id;not null" json:"hp_id"`
	WhiteBoardLogID int       `gorm:"column:white_board_log_id;primaryKey;autoIncrement:true" json:"white_board_log_id"`
	ContentBefore   string    `gorm:"column:content_before;not null" json:"content_before"`
	ContentAfter    string    `gorm:"column:content_after;not null" json:"content_after"`
	CreatedBy       string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy       string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt       time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt       time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted       int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName HpWhiteBoardLog's table name
func (*HpWhiteBoardLog) TableName() string {
	return TableNameHpWhiteBoardLog
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameSystemNotification = "system_notification"

// SystemNotification mapped from table <system_notification>
type SystemNotification struct {
	SystemNotificationID int       `gorm:"column:system_notification_id;primaryKey;autoIncrement:true" json:"system_notification_id"`
	Title                string    `gorm:"column:title" json:"title"`
	Description          string    `gorm:"column:description" json:"description"`
	Tags                 string    `gorm:"column:tags" json:"tags"`
	CreatedBy            string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy            string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt            time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt            time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted            int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName SystemNotification's table name
func (*SystemNotification) TableName() string {
	return TableNameSystemNotification
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameCalendar = "calendar"

// Calendar mapped from table <calendar>
type Calendar struct {
	CalendarID                         int       `gorm:"column:calendar_id;primaryKey;autoIncrement:true" json:"calendar_id"`
	CreatedAt                          time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy                          string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt                          time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy                          string    `gorm:"column:updated_by;not null" json:"updated_by"`
	Label                              string    `gorm:"column:label" json:"label"`
	DoctorID                           *int      `gorm:"column:doctor_id" json:"doctor_id"`
	CalendarName                       string    `gorm:"column:calendar_name" json:"calendar_name"`
	NumberOfDoctor                     *int      `gorm:"column:number_of_doctors" json:"number_of_doctors"`
	CalendarNameSettingType            *int      `gorm:"column:calendar_name_setting_type" json:"calendar_name_setting_type"`
	ReservationMethodType              *int      `gorm:"column:reservation_method_type" json:"reservation_method_type"`
	ReservableSlotSettingType          *int      `gorm:"column:reservable_slot_setting_type" json:"reservable_slot_setting_type"`
	ReservableStartDay                 *int      `gorm:"column:reservable_start_days" json:"reservable_start_days"`
	ReservableStartTime                string    `gorm:"column:reservable_start_time" json:"reservable_start_time"`
	ReservableMinutesBeforeExamEndTime *int      `gorm:"column:reservable_minutes_before_exam_end_time" json:"reservable_minutes_before_exam_end_time"`
	HospitalID                         int       `gorm:"column:hospital_id;not null" json:"hospital_id"`
	IsActive                           bool      `gorm:"column:is_active;not null;default:true" json:"is_active"`
	IsDeleted                          int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	CalendarTimeSlot                   int       `gorm:"column:calendar_time_slot;not null;default:30" json:"calendar_time_slot"`
}

// TableName Calendar's table name
func (*Calendar) TableName() string {
	return TableNameCalendar
}

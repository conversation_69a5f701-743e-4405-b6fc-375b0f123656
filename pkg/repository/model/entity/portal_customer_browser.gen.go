// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePortalCustomerBrowser = "portal_customer_browser"

// PortalCustomerBrowser mapped from table <portal_customer_browser>
type PortalCustomerBrowser struct {
	BrowserID   int       `gorm:"column:browser_id;primaryKey;autoIncrement:true" json:"browser_id"`
	CustomerID  int       `gorm:"column:customer_id;not null" json:"customer_id"`
	BrowserName string    `gorm:"column:browser_name;not null" json:"browser_name"`
	CreatedAt   time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	CreatedBy   string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy   string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	IsDeleted   int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName PortalCustomerBrowser's table name
func (*PortalCustomerBrowser) TableName() string {
	return TableNamePortalCustomerBrowser
}

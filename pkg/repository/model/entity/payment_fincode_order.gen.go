// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNamePaymentFincodeOrder = "payment_fincode_order"

// PaymentFincodeOrder mapped from table <payment_fincode_order>
type PaymentFincodeOrder struct {
	PaymentFincodeOrderID int64     `gorm:"column:payment_fincode_order_id;primaryKey;autoIncrement:true" json:"payment_fincode_order_id"`
	PaymentFincodeID      string    `gorm:"column:payment_fincode_id;not null" json:"payment_fincode_id"`
	PlatformID            int       `gorm:"column:platform_id;not null" json:"platform_id"`
	ShopID                string    `gorm:"column:shop_id;not null" json:"shop_id"`
	PayType               string    `gorm:"column:pay_type;comment:constant.go - Fincodeの決済種別 参照" json:"pay_type"` // constant.go - Fincodeの決済種別 参照
	AccessID              string    `gorm:"column:access_id;not null" json:"access_id"`
	CustomerID            string    `gorm:"column:customer_id;not null" json:"customer_id"`
	CreatedAt             time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy             string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt             time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy             string    `gorm:"column:updated_by;not null" json:"updated_by"`
	IsDeleted             int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName PaymentFincodeOrder's table name
func (*PaymentFincodeOrder) TableName() string {
	return TableNamePaymentFincodeOrder
}

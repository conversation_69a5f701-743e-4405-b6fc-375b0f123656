// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameReserve = "reserve"

// Reserve mapped from table <reserve>
type Reserve struct {
	ReserveID                 int       `gorm:"column:reserve_id;primaryKey;autoIncrement:true" json:"reserve_id"`
	PatientID                 *int      `gorm:"column:patient_id" json:"patient_id"`
	PrescriptionReceiveMethod int       `gorm:"column:prescription_receive_method;not null;comment:constant.go - 薬の受け取り方 参照" json:"prescription_receive_method"` // constant.go - 薬の受け取り方 参照
	CreatedAt                 time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy                 string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt                 time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy                 string    `gorm:"column:updated_by;not null" json:"updated_by"`
	IsDeleted                 int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName Reserve's table name
func (*Reserve) TableName() string {
	return TableNameReserve
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameMTreatmentCategory = "m_treatment_category"

// MTreatmentCategory mapped from table <m_treatment_category>
type MTreatmentCategory struct {
	TreatmentCategoryID      int       `gorm:"column:treatment_category_id;primaryKey;autoIncrement:true" json:"treatment_category_id"`
	CreatedAt                time.Time `gorm:"column:created_at;not null;default:statement_timestamp()" json:"created_at"`
	CreatedBy                string    `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt                time.Time `gorm:"column:updated_at;not null;default:statement_timestamp()" json:"updated_at"`
	UpdatedBy                string    `gorm:"column:updated_by;not null" json:"updated_by"`
	Name                     string    `gorm:"column:name;not null" json:"name"`
	GroupType                int       `gorm:"column:group_type;not null" json:"group_type"`
	IsDeleted                int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
	SearchCategoryName       string    `gorm:"column:search_category_name" json:"search_category_name"`
	TreatmentCategoryGroupID *int64    `gorm:"column:treatment_category_group_id" json:"treatment_category_group_id"`
	SearchSortOrder          *int      `gorm:"column:search_sort_order" json:"search_sort_order"`
}

// TableName MTreatmentCategory's table name
func (*MTreatmentCategory) TableName() string {
	return TableNameMTreatmentCategory
}

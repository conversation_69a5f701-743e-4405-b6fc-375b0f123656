// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameEveryPatientMessageSetting = "every_patient_message_setting"

// EveryPatientMessageSetting mapped from table <every_patient_message_setting>
type EveryPatientMessageSetting struct {
	HospitalID int       `gorm:"column:hospital_id;primaryKey" json:"hospital_id"`
	Sendable   int       `gorm:"column:sendable;not null" json:"sendable"`
	CreatedBy  string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy  string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt  time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted  int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName EveryPatientMessageSetting's table name
func (*EveryPatientMessageSetting) TableName() string {
	return TableNameEveryPatientMessageSetting
}

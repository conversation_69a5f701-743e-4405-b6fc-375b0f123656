// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameSignupUser = "signup_user"

// SignupUser mapped from table <signup_user>
type SignupUser struct {
	SignupUserID              int       `gorm:"column:signup_user_id;primaryKey;autoIncrement:true" json:"signup_user_id"`
	HpID                      int       `gorm:"column:hp_id;not null" json:"hp_id"`
	UserID                    int       `gorm:"column:user_id;not null" json:"user_id"`
	Name                      string    `gorm:"column:name;not null;comment:改名などによる影響を受けず申し込み時の名前を保持する" json:"name"`                            // 改名などによる影響を受けず申し込み時の名前を保持する
	MedicalProfessionalType   int16     `gorm:"column:medical_professional_type;not null;comment:医師=1,歯科医師=2" json:"medical_professional_type"` // 医師=1,歯科医師=2
	Gender                    int16     `gorm:"column:gender;not null;comment:男=1,女=2" json:"gender"`                                           // 男=1,女=2
	Birthday                  time.Time `gorm:"column:birthday" json:"birthday"`
	DoctorLicenceNumber       string    `gorm:"column:doctor_licence_number;not null" json:"doctor_licence_number"`
	DoctorLicenceRegisterDate time.Time `gorm:"column:doctor_licence_register_date" json:"doctor_licence_register_date"`
	PhoneNumber               string    `gorm:"column:phone_number" json:"phone_number"`
	Aid                       string    `gorm:"column:aid" json:"aid"`
	Bid                       string    `gorm:"column:bid" json:"bid"`
	Cid                       string    `gorm:"column:cid" json:"cid"`
	IsDeleted                 int       `gorm:"column:is_deleted;not null;comment:0=有効、1=削除" json:"is_deleted"` // 0=有効、1=削除
	KanaName                  string    `gorm:"column:kana_name" json:"kana_name"`
	ApplierCategory           int16     `gorm:"column:applier_category" json:"applier_category"`
	IsDownloadPamphlet        bool      `gorm:"column:is_download_pamphlet;not null" json:"is_download_pamphlet"`
}

// TableName SignupUser's table name
func (*SignupUser) TableName() string {
	return TableNameSignupUser
}

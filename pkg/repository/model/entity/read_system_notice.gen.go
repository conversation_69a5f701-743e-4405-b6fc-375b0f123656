// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameReadSystemNotice = "read_system_notice"

// ReadSystemNotice mapped from table <read_system_notice>
type ReadSystemNotice struct {
	ReadSystemNoticeID int       `gorm:"column:read_system_notice_id;primaryKey" json:"read_system_notice_id"`
	HospitalStaffID    int       `gorm:"column:hospital_staff_id;not null" json:"hospital_staff_id"`
	ReadList           string    `gorm:"column:read_list" json:"read_list"`
	CreatedBy          string    `gorm:"column:created_by;default:system" json:"created_by"`
	UpdatedBy          string    `gorm:"column:updated_by;default:system" json:"updated_by"`
	CreatedAt          time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt          time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted          int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName ReadSystemNotice's table name
func (*ReadSystemNotice) TableName() string {
	return TableNameReadSystemNotice
}

// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpSchedule = "hp_schedule"

// HpSchedule mapped from table <hp_schedule>
type HpSchedule struct {
	HpID       int       `gorm:"column:hp_id;not null" json:"hp_id"`
	ScheduleID int       `gorm:"column:schedule_id;primaryKey" json:"schedule_id"`
	Title      string    `gorm:"column:title" json:"title"`
	Comment    string    `gorm:"column:comment" json:"comment"`
	StartDate  time.Time `gorm:"column:start_date;not null" json:"start_date"`
	EndDate    time.Time `gorm:"column:end_date;not null" json:"end_date"`
	IsAllDay   bool      `gorm:"column:is_all_day" json:"is_all_day"`
	CreatedBy  string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy  string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt  time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted  int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName HpSchedule's table name
func (*HpSchedule) TableName() string {
	return TableNameHpSchedule
}

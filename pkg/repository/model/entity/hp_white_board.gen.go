// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameHpWhiteBoard = "hp_white_board"

// HpWhiteBoard mapped from table <hp_white_board>
type HpWhiteBoard struct {
	HpID         int       `gorm:"column:hp_id;not null" json:"hp_id"`
	WhiteBoardID int       `gorm:"column:white_board_id;primaryKey" json:"white_board_id"`
	Content      string    `gorm:"column:content" json:"content"`
	CreatedBy    string    `gorm:"column:created_by;not null;default:system" json:"created_by"`
	UpdatedBy    string    `gorm:"column:updated_by;not null;default:system" json:"updated_by"`
	CreatedAt    time.Time `gorm:"column:created_at;default:statement_timestamp()" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;default:statement_timestamp()" json:"updated_at"`
	IsDeleted    int       `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName HpWhiteBoard's table name
func (*HpWhiteBoard) TableName() string {
	return TableNameHpWhiteBoard
}

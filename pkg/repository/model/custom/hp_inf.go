package custom

import (
	"denkaru-server/pkg/adapter/graphql/model"
	"denkaru-server/pkg/repository/model/entity"
	"denkaru-server/pkg/repository/model/enum"
	"time"
)

const TableNameHospital = "hp_inf"

type HpInf struct {
	HpID                          int        `gorm:"column:hp_id;primaryKey;autoIncrement:true" json:"hp_id"`
	StartDate                     int        `gorm:"column:start_date;not null" json:"start_date"`
	HpCd                          string     `gorm:"column:hp_cd" json:"hp_cd"`
	RousaiHpCd                    string     `gorm:"column:rousai_hp_cd" json:"rousai_hp_cd"`
	HpName                        string     `gorm:"column:hp_name" json:"hp_name"`
	ReceHpName                    string     `gorm:"column:rece_hp_name" json:"rece_hp_name"`
	KaisetuName                   string     `gorm:"column:kaisetu_name" json:"kaisetu_name"`
	PostCd                        string     `gorm:"column:post_cd" json:"post_cd"`
	PrefNo                        int        `gorm:"column:pref_no;not null" json:"pref_no"`
	InsuranceCategory             *string    `gorm:"column:insurance_category" json:"insurance_category"`
	Address1                      string     `gorm:"column:address1" json:"address1"`
	Address2                      string     `gorm:"column:address2" json:"address2"`
	Tel                           string     `gorm:"column:tel" json:"tel"`
	Tel2                          string     `gorm:"column:tel2" json:"tel2"`
	CreateDate                    time.Time  `gorm:"column:create_date;not null;default:statement_timestamp()" json:"create_date"`
	CreateID                      int        `gorm:"column:create_id;not null" json:"create_id"`
	CreateMachine                 string     `gorm:"column:create_machine" json:"create_machine"`
	UpdateDate                    time.Time  `gorm:"column:update_date;not null;default:statement_timestamp()" json:"update_date"`
	UpdateID                      int        `gorm:"column:update_id;not null" json:"update_id"`
	UpdateMachine                 string     `gorm:"column:update_machine" json:"update_machine"`
	FaxNo                         string     `gorm:"column:fax_no" json:"fax_no"`
	OtherContact                  string     `gorm:"column:other_contacts" json:"other_contacts"`
	Status                        int16      `gorm:"column:status;not null" json:"status"`
	IsOpen                        bool       `gorm:"column:is_open" json:"is_open"`
	IsInsuranceMedicalInstitution bool       `gorm:"column:is_insurance_medical_institution" json:"is_insurance_medical_institution"`
	IsReceiveNotification         bool       `gorm:"column:is_receive_notifications" json:"is_receive_notifications"`
	IsTermsPrivacyAgreed          bool       `gorm:"column:is_terms_privacy_agreed" json:"is_terms_privacy_agreed"`
	HomepageURL                   string     `gorm:"column:homepage_url" json:"homepage_url"`
	SignupDate                    *time.Time `gorm:"column:signup_date" json:"signup_date"`                     // ここをnullableにしたかったがgormをいじると全体影響があるのでcustomとして再定義
	ReviewCompletedDate           *time.Time `gorm:"column:review_completed_date" json:"review_completed_date"` // ここをnullableにしたかったがgormをいじると全体影響があるのでcustomとして再定義
	CancellationDate              *time.Time `gorm:"column:cancellation_date" json:"cancellation_date"`         // ここをnullableにしたかったがgormをいじると全体影響があるのでcustomとして再定義
	PharmacyFlg                   bool       `gorm:"column:pharmacy_flg" json:"pharmacy_flg"`
	IsDeleted                     int        `gorm:"column:is_deleted;not null" json:"is_deleted"`
	IsSmsAuthenticated            bool       `gorm:"column:is_sms_authenticated" json:"is_sms_authenticated"`
	KarteStatus                   int64      `gorm:"column:karte_status" json:"karte_status"`
}

func (*HpInf) TableName() string {
	return TableNameHospital
}

func (hp *HpInf) MutateFromChangeHospitalInput(hospitalInput model.ChangeHospitalInfoInput, hpId int) error {
	hp.HpID = hpId
	hp.HpName = hospitalInput.Clinic.Name
	hp.IsOpen = hospitalInput.ClinicStatus.IsOpenClinic
	hp.IsInsuranceMedicalInstitution = *hospitalInput.ClinicStatus.IsInsuranceMedicalInstitution

	if hospitalInput.Clinic.MedicalInstitutionCode != nil {
		hp.HpCd = *hospitalInput.Clinic.MedicalInstitutionCode
	}

	if hospitalInput.Clinic.PostCode != nil {
		hp.PostCd = *hospitalInput.Clinic.PostCode
	}

	if hospitalInput.Clinic.Address1 != nil {
		hp.Address1 = *hospitalInput.Clinic.Address1
	}

	if hospitalInput.Clinic.Address2 != nil {
		hp.Address2 = *hospitalInput.Clinic.Address2
	}

	if hospitalInput.Clinic.PhoneNumber != nil && !*hospitalInput.ClinicStatus.IsInsuranceMedicalInstitution {
		hp.Tel = *hospitalInput.Clinic.PhoneNumber
	}

	if hospitalInput.Clinic.FaxNumber != nil {
		hp.FaxNo = *hospitalInput.Clinic.FaxNumber
	}

	if *hospitalInput.ClinicStatus.IsInsuranceMedicalInstitution {
		// 保険医療機関の場合、医師情報確認完了（2）ステータスにする
		hp.Status = enum.HpStatusDoctorLicenceCheckOK.Int16()
	} else {
		// 保険医療機関ではない場合、手動審査待ち（4）ステータスにする
		hp.Status = enum.HpStatusAwaitingManualReview.Int16()
	}

	if hospitalInput.Clinic.PrefNo != nil {
		hp.PrefNo = *hospitalInput.Clinic.PrefNo
	}

	if hospitalInput.Clinic.InsuranceCategory != nil {
		hp.InsuranceCategory = hospitalInput.Clinic.InsuranceCategory
	}

	if hospitalInput.Clinic.HomepageURL != nil {
		hp.HomepageURL = *hospitalInput.Clinic.HomepageURL
	}

	return nil
}

type HospitalSignupUser struct {
	HpInf
	SignupUser []*entity.SignupUser `gorm:"foreignKey:HpID;references:HpID" json:"signup_user"`
}

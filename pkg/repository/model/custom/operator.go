package custom

import "time"

// OperatorLoginInput ...
type OperatorLoginInput struct {
	OperatorName     string `json:"operatorName"`
	OperatorPassword string `json:"operatorPassword"`
	HospitalID       int    `json:"hospitalId"`
}

// OperatorLoginOutput ...
type OperatorLoginOutput struct {
	ChallengeName     string    `json:"challengeName"`
	SessionValue      *string   `json:"sessionValue"`
	OwnerStaffID      int       `json:"ownerStaffID"`
	PharmacyFlg       bool      `json:"pharmacyFlg"`
	IdToken           *string   `json:"idToken"`
	IdTokenExpireTime time.Time `json:"accessTokenExpireTime"`
	KarteStatus       int64     `json:"karteStatus"`
}

// OperatorVerifyMFACodeInput ...
type OperatorVerifyMFACodeInput struct {
	OperatorName string `json:"operatorName"`
	SessionValue string `json:"sessionValue"`
	PinCode      string `json:"pinCode"`
}

// OperatorVerifyMFACodeOutput ...
type OperatorVerifyMFACodeOutput struct {
	ChallengeName     string    `json:"challengeName"`
	SessionValue      *string   `json:"sessionValue"`
	IdToken           string    `json:"idToken"`
	PharmacyFlg       bool      `json:"pharmacyFlg"`
	IdTokenExpireTime time.Time `json:"accessTokenExpireTime"`
}

// OperatorChangePasswordInput ...
type OperatorChangePasswordInput struct {
	OperatorName string `json:"operatorName"`
	NewPassword  string `json:"newPassword"`
	SessionValue string `json:"sessionValue"`
}

// OperatorChangePasswordOutput ...
type OperatorChangePasswordOutput struct {
	ChallengeName string  `json:"challengeName"`
	SessionValue  *string `json:"sessionValue"`
	PharmacyFlg   bool    `json:"pharmacyFlg"`
}

// Package repository ...
//
//go:generate mockgen -source=$GOFILE -destination=../test_mock/$GOPACKAGE/$GOFILE
package repository

import (
	"context"
	"denkaru-server/pkg/repository/model/entity"
)

// ICommonPostCodeMstRepository interface of CommonPostCodeMstRepository
type ICommonPostCodeMstRepository interface {
	TableName() string
	IDColumn() string
	FindByPostCode(postCode string) (address []*entity.PostCodeMst, err error)
	CreateByCopy(ctx context.Context, values []string, columns []string, batchSize int) error
}

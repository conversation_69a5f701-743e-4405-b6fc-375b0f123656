//go:build wireinject
// +build wireinject

// Package wire Auto DI
package wire

import (
	"context"
	"denkaru-server/pkg/infra/client"
	"denkaru-server/pkg/infra/cognito"
	"denkaru-server/pkg/infra/faxapi"
	"denkaru-server/pkg/infra/fincode"
	"denkaru-server/pkg/infra/freee"
	lineRepo "denkaru-server/pkg/infra/line"
	"denkaru-server/pkg/infra/myredis"
	opensearch2 "denkaru-server/pkg/infra/opensearch"
	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/infra/rdb"
	infraS3 "denkaru-server/pkg/infra/s3"
	sesRepo "denkaru-server/pkg/infra/ses"
	"denkaru-server/pkg/infra/sms"
	sqsRepo "denkaru-server/pkg/infra/sqs"
	"denkaru-server/pkg/service"
	"denkaru-server/pkg/service/pdf"
	"denkaru-server/pkg/service/validator/message"
	"denkaru-server/pkg/service/validator/message/component"
	"time"

	chime "denkaru-server/pkg/infra/chime"

	"github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings"
	cognitoIDp "github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	pinpoint "github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/google/wire"
	"github.com/line/line-bot-sdk-go/v8/linebot"
	"github.com/opensearch-project/opensearch-go/v2"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"github.com/jackc/pgx/v5/pgxpool"
)

// InitializeAgentService Initialize Of AgentService
func InitializeAgentService(_ *gorm.DB) service.IAgentService {
	wire.Build(
		service.NewAgentService,
		postgresql.NewAgentTokenRepository,
		postgresql.NewAgentConfigRepository)
	return nil
}

// InitializeAuditlogService Initialize Of AuditlogService
func InitializeAuditlogService(_ *gorm.DB) service.IAuditlogService {
	wire.Build(
		service.NewAuditlogService,
		postgresql.NewAuditlogRepository)
	return nil
}

// InitializeClientCertificateService Initialize Of ClientCertificateService
func InitializeClientCertificateService(_ *gorm.DB) service.IClientCertificateService {
	wire.Build(
		service.NewClientCertificateService,
		postgresql.NewClientCertificateRepository,
	)
	return nil
}

// InitializeCommentService Initialize Of CommentService
func InitializeCommentService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient) service.ICommentService {
	wire.Build(
		service.NewTaskCommentService,
		postgresql.NewTaskCommentRepository,
		postgresql.NewTaskRepository,
		postgresql.NewTaskCommentFileRepository,
		postgresql.NewStaffRepository,
		postgresql.NewHospitalRepository,
	)
	return nil
}

// InitializePortalHospitalNotification Initialize Of PortalHospitalNotificationService
func InitializePortalHospitalNotificationService(_ *gorm.DB) service.IPortalHospitalNotificationService {
	wire.Build(
		service.NewPortalHospitalNotificationService,
		postgresql.NewPortalHospitalNotificationRepository,
		postgresql.NewPortalHospitalRepository,
	)
	return nil
}

// InitializeHealthCheckService Initialize of DBHealthCheckService
func InitializeHealthCheckService(_ *gorm.DB, _ redis.UniversalClient) service.IHealthCheckService {
	wire.Build(
		service.NewHealthCheckService,
		postgresql.NewDBHealthCheckRepository,
		myredis.NewCommonRedisRepository,
	)
	return nil
}

// InitializeExaminationService Initialize Of ExaminationService
func InitializeExaminationService(_ *gorm.DB) service.IExaminationService {
	wire.Build(
		service.NewExaminationService,
		postgresql.NewExaminationRepository)
	return nil
}

// InitializeFileService Initialize of FileService
// func InitializeFileService() service.IFileService {
// 	wire.Build(
// 		service.NewFileService,
// 	)
// 	return nil
// }

// InitializeGenerateService Initialize Of GenerateService
func InitializeGenerateService(_ *gorm.DB, _ redis.UniversalClient) pdf.IGenerateService {
	wire.Build(
		pdf.NewGenerateService,
		postgresql.NewSurveyRepository,
	)
	return nil
}

// InitializeHospitalUseCase Initialize Of HospitalUseCase
func InitializeHospitalUseCase(_ *gorm.DB, _ redis.UniversalClient, _ *sqs.Client, _ *sesv2.Client, _ *opensearch.Client) service.IHospitalService {
	wire.Build(
		service.NewHospitalService,
		postgresql.NewHospitalRepository,
		postgresql.NewHpPermissionRepository,
		service.NewSignupService,
		postgresql.NewSignupRepository,
		postgresql.NewOpesysMemoRepository,
		postgresql.NewPortalPrefectureRepository,
		service.NewMailService,
		postgresql.NewTemplateMailRepository,
		postgresql.NewMailDeliverySettingsRepository,
		postgresql.NewMailVerificationRequestsRepository,
		postgresql.NewStaffRepository,
		postgresql.NewExamTimeSlotRepository,
		postgresql.NewTreatmentDepartmentRepository,
		postgresql.NewCalendarRepository,
		postgresql.NewReservationRepository,
		postgresql.NewMessageChannelRepository,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewTaskStatusRepository,
		postgresql.NewTaskCategoryRepository,
		sqsRepo.NewQueuingRepository,
		sesRepo.NewMailRepository,
		postgresql.NewSetMstRepository,
		postgresql.NewSetGenerationMstRepository,
		postgresql.NewImportServiceRepository,
		opensearch2.NewPortalHospitalSearchRepository,
		postgresql.NewImportCashlessRepository,
	)
	return nil
}

// InitializeLabelService Initialize of LalbelUsecase
func InitializeLabelService(_ *gorm.DB) service.ILabelService {
	wire.Build(
		service.NewLabelService,
		postgresql.NewRaiinKbnMstRepository)
	return nil
}

// InitializeMeetingUsecase Initialize of MeetingUsecase
func InitializeMeetingService(_ *gorm.DB, _ *chimesdkmeetings.Client, _ redis.UniversalClient, _ *opensearch.Client, _ *sqs.Client, _ *s3.Client, _ *s3.PresignClient) service.IMeetingService {
	wire.Build(
		service.NewMeetingService,
		chime.NewChimeRepository,
		postgresql.NewMeetingRepository,
		service.NewPatientMessageService,
		postgresql.NewMessageChannelRepository,
		postgresql.NewMessageRepository,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewAuditlogRepository,
		postgresql.NewPortalCustomerRepository,
		myredis.NewMessageNotificationRepository,
		opensearch2.NewMessageSearchRepository,
		postgresql.NewStaffRepository,
		postgresql.NewPatientRepository,
		sqsRepo.NewQueuingRepository,
		message.NewServiceValidator,
		component.NewMessageValidator,
		infraS3.NewMessageS3Repository,
		myredis.NewMeetingNotificationRepository,
		service.NewMessageService,
		postgresql.NewHospitalRepository,
	)
	return nil
}

// InitializeNotificationService Initialize of NotificationService
func InitializeNotificationService(_ *gorm.DB, _ redis.UniversalClient) service.INotificationService {
	wire.Build(
		service.NewNotificationService,
		postgresql.NewStaffRepository,
		postgresql.NewPatientRepository,
		postgresql.NewMessageRepository,
		myredis.NewMessageNotificationRepository)
	return nil
}

// InitializeNotificationToPatientService Initialize of NotificationToPatientService
func InitializeNotificationToPatientService(_ *gorm.DB, _ redis.UniversalClient) service.INotificationToPatientService {
	wire.Build(
		service.NewNotificationToPatientService,
		postgresql.NewMessageRepository,
		myredis.NewMessageNotificationRepository)
	return nil
}

// InitializeMessageService Initialize of MessageService
func InitializeMessageService(_ *gorm.DB, _ redis.UniversalClient, _ *opensearch.Client, _ *sqs.Client, _ *s3.Client, _ *s3.PresignClient) service.IMessageService {
	wire.Build(
		service.NewMessageService,
		postgresql.NewMessageChannelRepository,
		postgresql.NewMessageRepository,
		myredis.NewMessageNotificationRepository,
		opensearch2.NewMessageSearchRepository,
		postgresql.NewStaffRepository,
		postgresql.NewPatientRepository,
		postgresql.NewPortalCustomerRepository,
		sqsRepo.NewQueuingRepository,
		message.NewServiceValidator,
		component.NewMessageValidator,
		infraS3.NewMessageS3Repository,
	)
	return nil
}

// InitializePatientService Initialize Of PatientSerivce
func InitializePatientService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient) service.IPatientService {
	wire.Build(
		service.NewPatientService,
		postgresql.NewPatientRepository,
		postgresql.NewPatientMemoRepository,
		postgresql.NewPortalCustomerFileRepository,
		postgresql.NewPortalCustomerRepository,
		postgresql.NewReserveDetailRepository,
		infraS3.NewCustomerS3Repository,
	)
	return nil
}

// InitializePublishService Initialize Of PublishService
func InitializePublishService(_ context.Context, _ *gorm.DB, _ *sqs.Client, _ redis.UniversalClient) pdf.IPublishService {
	wire.Build(
		pdf.NewPublishService,
		postgresql.NewSurveyRepository,
		sqsRepo.NewQueuingRepository,
	)
	return nil
}

// InitializeSchemaService Initialize Of SchemaService
func InitializeSchemaService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient) service.ISchemaService {
	wire.Build(
		service.NewSchemaService,
		postgresql.NewSchemaImageRepository,
		infraS3.NewSchemaImageS3Repository,
	)
	return nil
}

// InitializeSessionService Initialize Of SessionService
func InitializeSessionService(_ string) service.ISessionService {
	wire.Build(
		service.NewSessionService)
	return nil
}

// InitializeAuthService Initialize Of AuthService
func InitializeAuthService(_ redis.UniversalClient) service.IAuthService {
	wire.Build(
		service.NewAuthService,
		myredis.NewSessionRepository)
	return nil
}

// InitializeSpecialistService Initialize Of SpecialistService
func InitializeSpecialistService(_ *gorm.DB) service.ISpecialistService {
	wire.Build(
		service.NewSpecialistService,
		postgresql.NewSpecialistRepository,
	)
	return nil
}

// InitializeStaffService Initialize Of StaffService
func InitializeStaffService(_ *gorm.DB, _ redis.UniversalClient) service.IStaffService {
	wire.Build(
		service.NewStaffService,
		postgresql.NewStaffRepository,
		postgresql.NewHospitalRepository,
		postgresql.NewPermissionRepository,
		postgresql.NewTaskRepository,
		service.NewHospitalStatusCheckService,
		postgresql.NewSetMstRepository,
		postgresql.NewSetGenerationMstRepository,
	)
	return nil
}

// InitializeSurveyUsecase Initialize of SurveyService
func InitializeSurveyUsecase(_ *gorm.DB, _ *sqs.Client, _ *s3.Client, _ *s3.PresignClient, _ redis.UniversalClient) service.ISurveyService {
	wire.Build(
		service.NewSurveyService,
		postgresql.NewSurveyRepository,
		postgresql.NewSurveyAnswerRepository,
		sqsRepo.NewQueuingRepository,
		postgresql.NewSurveyTemplateRepository,
		infraS3.NewSurveyFileS3Repository,
	)
	return nil
}

// InitializeTagService Initialize Of TagService
func InitializeTagService(_ *gorm.DB) service.ITagService {
	wire.Build(
		service.NewTagService,
		postgresql.NewTagRepository)
	return nil
}

// InitializeTaskService Initialize of TaskService
func InitializeTaskService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient, _ redis.UniversalClient) service.ITaskService {
	wire.Build(
		service.NewTaskService,
		postgresql.NewTaskFileRepository,
		postgresql.NewStaffRepository,
		postgresql.NewHospitalRepository,
		postgresql.NewTaskRepository,
		postgresql.NewTaskCommentRepository,
		postgresql.NewTaskCategoryRepository,
		postgresql.NewTaskStatusRepository,
		postgresql.NewPatientRepository,
		postgresql.NewTaskHistoryRepository,
		postgresql.NewTaskCommentFileRepository,
	)
	return nil
}

// InitializeTemplateDocService Initialize of TemplateDocService
func InitializeTemplateDocService(_ *gorm.DB, _ *s3.PresignClient, _ redis.UniversalClient, _ *s3.Client) service.ITemplateDocService {
	wire.Build(
		service.NewTemplateDocService,
		postgresql.NewTemplateDocRepository,
		postgresql.NewPatientRepository,
		postgresql.NewHospitalRepository,
	)
	return nil
}

// InitializeTreatmentCategoryService Initialize Of TreatmentCategoryService
func InitializeTreatmentCategoryService(_ *gorm.DB) service.IMTreatmentCategoryService {
	wire.Build(
		service.NewMTreatmentCategoryService,
		postgresql.NewMTreatmentCategoryRepository)
	return nil
}

// InitializeExamTimeSlotService Initialize Of ExamTimeSlotService
func InitializeExamTimeSlotService(_ *gorm.DB) service.IExamTimeSlotService {
	wire.Build(
		service.NewExamTimeSlotService,
		postgresql.NewExamTimeSlotRepository)
	return nil
}

// InitializeCalendarService Initialize Of CalendarService
func InitializeCalendarService(_ *gorm.DB, _ *opensearch.Client, _ *sqs.Client) service.ICalendarService {
	wire.Build(
		service.NewCalendarService,
		opensearch2.NewExamTimeSlotSearchRepository,
		postgresql.NewCalendarRepository,
		sqsRepo.NewQueuingRepository,
	)
	return nil
}

// InitializeReservationService Initialize Of ReservationService
func InitializeReservationService(_ *gorm.DB, _ *opensearch.Client, _ *sqs.Client) service.IReservationService {
	wire.Build(
		service.NewReservationService,
		postgresql.NewExamTimeSlotRepository,
		postgresql.NewPatientRepository,
		postgresql.NewMeetingRepository,
		postgresql.NewCalendarRepository,
		postgresql.NewReservationRepository,
		opensearch2.NewExamTimeSlotSearchRepository,
		sqsRepo.NewQueuingRepository,
	)
	return nil
}

// InitializeETLService Initialize Of ETLService
func InitializeETLService(_ *opensearch.Client) service.IETLService {
	wire.Build(
		service.NewETLService,
		opensearch2.NewETLRepository,
	)
	return nil
}

// InitializeTreatmentDepartmentService Initialize of TreatmentDepartmentUsecase
func InitializeTreatmentDepartmentService(_ *gorm.DB, _ *opensearch.Client, _ *sqs.Client) service.ITreatmentDepartmentService {
	wire.Build(
		service.NewTreatmentDepartmentService,
		postgresql.NewTreatmentDepartmentRepository,
		postgresql.NewStaffRepository,
		postgresql.NewCalendarTreatmentRepository,
		postgresql.NewReserveDetailRepository,
		postgresql.NewReserveDetailHistoryRepository,
		postgresql.NewCalendarRepository,
		postgresql.NewPortalHospitalRepository,
		opensearch2.NewPortalHospitalSearchRepository,
		sqsRepo.NewQueuingRepository,
		postgresql.NewTreatmentFeeListRepository,
	)
	return nil
}

// InitializeMailService Initialize Of MailService
func InitializeMailService(_ *gorm.DB, _ *sqs.Client, _ *sesv2.Client) service.IMailService {
	wire.Build(
		service.NewMailService,
		postgresql.NewTemplateMailRepository,
		postgresql.NewMailDeliverySettingsRepository,
		postgresql.NewMailVerificationRequestsRepository,
		postgresql.NewStaffRepository,
		postgresql.NewExamTimeSlotRepository,
		postgresql.NewTreatmentDepartmentRepository,
		postgresql.NewCalendarRepository,
		postgresql.NewReservationRepository,
		postgresql.NewMessageChannelRepository,
		postgresql.NewPharmacyReserveRepository,
		sqsRepo.NewQueuingRepository,
		sesRepo.NewMailRepository)
	return nil
}

// InitializeAddressService Initialize of AddressService
func InitializeAddressService(_ *gorm.DB) service.IAddressService {
	wire.Build(
		service.NewAddressService,
		postgresql.NewPrefectureRepository,
		postgresql.NewCommonPostCodeMstRepository,
	)
	return nil
}

// InitializePortalAddressService Initialize of PortalAddressService
func InitializePortalAddressService(_ *gorm.DB) service.IPortalAddressService {
	wire.Build(
		service.NewPortalAddressService,
		postgresql.NewPortalCityRepository,
		postgresql.NewPortalPrefectureRepository,
		postgresql.NewCommonPostCodeMstRepository,
	)
	return nil
}

func InitializePortalHospitalStaffService(_ *gorm.DB, _ *s3.PresignClient, _ *s3.Client) service.IPortalHospitalStaffService {
	wire.Build(
		service.NewPortalHospitalStaffService,
		postgresql.NewPortalHospitalStaffRepository,
		postgresql.NewPortalStaffPictureRepository,
		postgresql.NewPortalPictureRepository,
	)
	return nil
}

// InitializePatientMessageService Initialize of PatientMessageService
func InitializePatientMessageService(_ *gorm.DB, _ redis.UniversalClient, _ *opensearch.Client, _ *sqs.Client, _ *s3.Client, _ *s3.PresignClient) service.IPatientMessageService {
	wire.Build(
		service.NewPatientMessageService,
		postgresql.NewMessageChannelRepository,
		postgresql.NewMessageRepository,
		myredis.NewMessageNotificationRepository,
		opensearch2.NewMessageSearchRepository,
		postgresql.NewStaffRepository,
		postgresql.NewPatientRepository,
		postgresql.NewPortalCustomerRepository,
		sqsRepo.NewQueuingRepository,
		message.NewServiceValidator,
		component.NewMessageValidator,
		infraS3.NewMessageS3Repository,
		service.NewMessageService,
	)
	return nil
}

// InitializePatientPortalHospitalService Initialize Of PatientPortalHospital
func InitializePatientPortalHospitalService(_ *gorm.DB) service.IPatientPortalHospitalService {
	wire.Build(
		service.NewPatientPortalHospitalService,
		postgresql.NewPortalHospitalRepository,
	)
	return nil
}

// InitializeAgreeService Initialize of AgreeService
func InitializeAgreeService(_ *gorm.DB) service.IAgreeService {
	wire.Build(
		service.NewAgreeService,
		postgresql.NewAgreeRepository,
		postgresql.NewStaffRepository,
	)
	return nil
}

// InitializeOcrService Initialize of OcrService
func InitializeOcrService(_ *s3.Client, _ *s3.PresignClient, _ redis.UniversalClient) service.IOcrService {
	wire.Build(
		service.NewOcrService,
		infraS3.NewOcrS3Repository,
		myredis.NewCommonRedisRepository,
	)
	return nil
}

// InitializeAIService Initialize of AIService
func InitializeAIService(_ context.Context, _ *gorm.DB) service.IAIService {
	wire.Build(
		service.NewAIService,
		postgresql.NewAIRepository,
		postgresql.NewMTreatmentCategoryRepository)
	return nil
}

// InitializePortalHospitalService Initialize Of PortalHospital
func InitializePortalHospitalService(_ context.Context, _ *gorm.DB, _ *s3.PresignClient, _ *s3.Client, _ *opensearch.Client, _ *sqs.Client) service.IPortalHospitalService {
	wire.Build(
		service.NewPortalHospitalService,
		postgresql.NewPortalHospitalRepository,
		opensearch2.NewPortalHospitalSearchRepository,
		postgresql.NewPortalHospPictureRepository,
		postgresql.NewPortalPictureRepository,
		sqsRepo.NewQueuingRepository,
		postgresql.NewImportHospitalRepository,
		service.NewAIService,
		postgresql.NewAIRepository,
		postgresql.NewMTreatmentCategoryRepository,
	)
	return nil
}

// InitializeOperatorService Initialize of OperatorService
func InitializeOperatorService(_ *cognitoIDp.Client, _ *gorm.DB) service.IOperatorService {
	wire.Build(
		service.NewOperatorService,
		cognito.NewOperatorAuthRepository,
		postgresql.NewOperatorPermissionRepository,
		postgresql.NewStaffRepository,
		postgresql.NewHospitalRepository,
	)
	return nil
}

// InitializeSignupService Initialize of SignupService
func InitializeSignupService(_ *gorm.DB, _ redis.UniversalClient) service.ISignupService {
	wire.Build(
		service.NewSignupService,
		postgresql.NewSignupRepository,
		postgresql.NewHospitalRepository,
		postgresql.NewOpesysMemoRepository,
		postgresql.NewTaskStatusRepository,
		postgresql.NewTaskCategoryRepository,
		postgresql.NewPortalPrefectureRepository,
		postgresql.NewSetMstRepository,
		postgresql.NewSetGenerationMstRepository,
	)
	return nil
}

// InitializePaymentService Initialize Of PaymentService
func InitializePaymentService(_ *gorm.DB, _ time.Duration) service.IPaymentService {
	wire.Build(
		service.NewPaymentService,
		postgresql.NewReservationRepository,
		postgresql.NewReserveDetailRepository,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewPharmacyReserveDetailRepository,
		postgresql.NewPaymentRepository,
		postgresql.NewHpFincodeInfoRepository,
		postgresql.NewPortalCustomerPaymentRepository,
		postgresql.NewPortalCustomerRepository,
		postgresql.NewPatientRepository,
		postgresql.NewMeetingRepository,
		postgresql.NewHospitalRepository,
		fincode.NewFincodeAPIRepository,
		client.NewHTTPClientWrapper,
		rdb.NewDBTransactionRepository,
		postgresql.NewAuditlogRepository,
		postgresql.NewCalcStatusRepository,
	)
	return nil
}

// InitializeSMSService Initialize Of SMSService
func InitializeSMSService(_ *gorm.DB, _ *sqs.Client, _ redis.UniversalClient) service.ISMSService {
	wire.Build(
		service.NewSMSService,
		postgresql.NewTemplateSMSRepository,
		postgresql.NewReservationRepository,
		postgresql.NewPatientRepository,
		postgresql.NewMeetingRepository,
		sqsRepo.NewQueuingRepository,
		sms.NewSMSRepository,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewLineAccountRepository,
		postgresql.NewPrescriptionReceptionRepository,
		postgresql.NewPharmacyReserveDetailRepository,
		postgresql.NewSignupRepository,
	)
	return nil
}

// InitializeSecondarySMSService Initialize Of SecondarySMSService
func InitializeSecondarySMSService(_ *gorm.DB, _ *sqs.Client, _ *pinpoint.Client, _ redis.UniversalClient) service.ISMSService {
	wire.Build(
		service.NewSMSService,
		postgresql.NewTemplateSMSRepository,
		postgresql.NewReservationRepository,
		postgresql.NewPatientRepository,
		postgresql.NewMeetingRepository,
		sqsRepo.NewQueuingRepository,
		sms.NewSecondarySMSRepository,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewLineAccountRepository,
		postgresql.NewPrescriptionReceptionRepository,
		postgresql.NewPharmacyReserveDetailRepository,
		postgresql.NewSignupRepository,
	)
	return nil
}

// InitializeFaxService Initialize Of FaxService
func InitializeFaxService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient, _ *sqs.Client) service.IFaxService {
	wire.Build(
		service.NewFaxService,
		postgresql.NewFaxRepository,
		postgresql.NewFaxNumberRepository,
		faxapi.NewFaxAPIClient,
		infraS3.NewFaxS3Repository,
		sqsRepo.NewQueuingRepository)
	return nil
}

// InitializeZendeskService Initialize Of ZendeskService
func InitializeZendeskService(_ *gorm.DB, _ redis.UniversalClient) service.IZendeskService {
	wire.Build(
		service.NewZendeskService,
		postgresql.NewStaffRepository,
		postgresql.NewSignupRepository,
		postgresql.NewHospitalRepository)
	return nil
}

// InitializePortalHospitalSearchService Initialize Of PortalHospitalSearchService
func InitializePortalHospitalSearchService(_ *gorm.DB, _ *opensearch.Client) service.IPortalHospitalSearchService {
	wire.Build(
		service.NewPortalHospitalSearchService,
		opensearch2.NewPortalHospitalSearchRepository,
		opensearch2.NewExamTimeSlotSearchRepository,
	)
	return nil
}

// InitializeFaxService Initialize Of FaxService
func InitializeFreeeService(_ *gorm.DB) service.IFreeeService {
	wire.Build(
		service.NewFreeeService,
		postgresql.NewFreeeDummyRepository,
		freee.NewFreeeAPIClient)
	return nil
}

// InitializeLineService Initialize of LineService
func InitializeLineService(_ *gorm.DB, _ *linebot.Client, _ *sqs.Client) service.ILineService {
	wire.Build(
		service.NewLineService,
		postgresql.NewLineAccountRepository,
		postgresql.NewTemplateMailRepository,
		postgresql.NewReservationRepository,
		lineRepo.NewLineRepository,
		sqsRepo.NewQueuingRepository,
		postgresql.NewPharmacyReserveRepository,
	)
	return nil
}

// InitializePharmacyHolidayService Initialize Of PharmacyHolidayService
func InitializePharmacyHolidayService(_ *gorm.DB) service.IPharmacyHolidayService {
	wire.Build(
		service.NewPharmacyHolidayService,
		postgresql.NewPharmacyHolidayRepository)
	return nil
}

// InitializePharmacyDeliveryService Initialize of PharmacyDeliveryService
func InitializePharmacyDeliveryService(_ *gorm.DB) service.IPharmacyDeliveryService {
	wire.Build(
		service.NewPharmacyDeliveryService,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewPharmacyDeliveryHistoryRepository)
	return nil
}

// InitializePharmacyReserveService Initialize Of PharmacyReserveService
func InitializePharmacyReserveService(_ *gorm.DB, _ *sqs.Client) service.IPharmacyReserveService {
	wire.Build(
		service.NewPharmacyReserveService,
		postgresql.NewPharmacyReserveRepository,
		postgresql.NewPharmacyReserveDetailRepository,
		postgresql.NewPharmacyReserveStatusHistoryRepository,
		postgresql.NewPharmacyDesiredDateRepository,
		sqsRepo.NewQueuingRepository,
		postgresql.NewMeetingRepository,
		postgresql.NewTemplateSMSRepository,
		postgresql.NewPatientRepository,
		postgresql.NewLineAccountRepository,
	)
	return nil
}

// InitializePharmacyReserveDetailService Initialize of PharmacyReserveDetailService
func InitializePharmacyReserveDetailService(_ *gorm.DB) service.IPharmacyReserveDetailService {
	wire.Build(
		service.NewPharmacyReserveDetailService,
		postgresql.NewPharmacyReserveDetailRepository)
	return nil
}

// InitializePharmacyPatientFileService Initialize of PharmacyPatientFileService
func InitializePharmacyPatientFileService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient) service.IPharmacyPatientFileService {
	wire.Build(
		service.NewPharmacyPatientFileService,
		postgresql.NewPharmacyPatientFileRepository,
		infraS3.NewCustomerS3Repository,
	)
	return nil
}

// InitializeImportDataService Initialize of ImportDataService
func InitializeImportDataService(_ *gorm.DB, _ *s3.Client) service.IImportDataService {
	wire.Build(
		service.NewImportDataService,
		infraS3.NewImportDataS3Repository,
		postgresql.NewImportHospitalRepository,
		postgresql.NewImportServiceRepository,
		postgresql.NewImportOperatingHourRepository,
		postgresql.NewImportBarrierFreeRepository,
		postgresql.NewImportCashlessRepository,
		postgresql.NewImportIctReservationRepository,
		postgresql.NewImportMedicalCheckupRepository,
		postgresql.NewImportMedicalSpecialistRepository,
		postgresql.NewImportMultilingualRepository,
		postgresql.NewImportParkingRepository,
		postgresql.NewImportElectronicPrescriptionRepository,
	)
	return nil
}

// InitializePrescriptionReceptionService Initialize Of PrescriptionReceptionService
func InitializePrescriptionReceptionService(_ *gorm.DB, _ *s3.Client, _ *s3.PresignClient) service.IPrescriptionReceptionService {
	wire.Build(
		service.NewPrescriptionReceptionService,
		postgresql.NewPrescriptionReceptionRepository,
		postgresql.NewPrescriptionImageRepository,
		infraS3.NewPrescriptionS3Repository,
	)
	return nil
}

// InitializeHospitalCheckService Initialize Of HospitalCheckService
func InitializeHospitalStatusCheckService(_ *gorm.DB) service.IHospitalStatusCheckService {
	wire.Build(
		service.NewHospitalStatusCheckService,
		postgresql.NewHospitalRepository,
	)
	return nil
}

// InitializeFixImportDataService Initialize of FixImportDataService
func InitializeFixImportDataService(_ *gorm.DB, _ *s3.Client) service.IFixImportDataService {
	wire.Build(
		service.NewFixImportDataService,
		infraS3.NewImportDataS3Repository,
		postgresql.NewImportHospitalRepository,
		postgresql.NewImportServiceRepository,
		postgresql.NewImportOperatingHourRepository,
		postgresql.NewImportBarrierFreeRepository,
		postgresql.NewImportCashlessRepository,
		postgresql.NewImportIctReservationRepository,
		postgresql.NewImportMedicalCheckupRepository,
		postgresql.NewImportMedicalSpecialistRepository,
		postgresql.NewImportMultilingualRepository,
		postgresql.NewImportParkingRepository,
	)
	return nil
}

// InitializeCommonPostCodeService Initialize of CommonPostCodeService
func InitializeCommonPostCodeService(_ *gorm.DB, _ *s3.Client) service.ICommonPostCodeMstService {
	wire.Build(
		service.NewCommonPostCodeMstService,
		infraS3.NewImportDataS3Repository,
		postgresql.NewCommonPostCodeMstRepository,
	)
	return nil
}

func InitializeMClinicMasterDataService(_ *gorm.DB, _ *pgxpool.Pool, _ *s3.Client) service.IMClinicMasterDataService {
	wire.Build(
		service.NewMClinicMasterDataService,
		postgresql.NewMClinicMasterDataRepository,
		postgresql.NewHospitalRepository,
		rdb.NewPGXTransactionRepository,
	)
	return nil
}

// InitializeSurveyAnswerNoPatientService Initialize of SurveyAnswerNoPatientService
func InitializeSurveyAnswerNoPatientService(_ *gorm.DB, _ *s3.PresignClient) service.ISurveyAnswerNoPatientService {
	wire.Build(
		service.NewSurveyAnswerNoPatientService,
		postgresql.NewSurveyAnswerNoPatientRepository,
		postgresql.NewSurveyRepository,
	)
	return nil
}

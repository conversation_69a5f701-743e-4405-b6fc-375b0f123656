// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"denkaru-server/pkg/infra/chime"
	"denkaru-server/pkg/infra/client"
	"denkaru-server/pkg/infra/cognito"
	"denkaru-server/pkg/infra/faxapi"
	"denkaru-server/pkg/infra/fincode"
	"denkaru-server/pkg/infra/freee"
	"denkaru-server/pkg/infra/line"
	"denkaru-server/pkg/infra/myredis"
	opensearch2 "denkaru-server/pkg/infra/opensearch"
	"denkaru-server/pkg/infra/postgresql"
	"denkaru-server/pkg/infra/rdb"
	s3_2 "denkaru-server/pkg/infra/s3"
	"denkaru-server/pkg/infra/ses"
	"denkaru-server/pkg/infra/sms"
	sqs2 "denkaru-server/pkg/infra/sqs"
	"denkaru-server/pkg/service"
	"denkaru-server/pkg/service/pdf"
	"denkaru-server/pkg/service/validator/message"
	"denkaru-server/pkg/service/validator/message/component"
	"github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/line/line-bot-sdk-go/v8/linebot"
	"github.com/opensearch-project/opensearch-go/v2"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"time"
)

// Injectors from service.go:

// InitializeAgentService Initialize Of AgentService
func InitializeAgentService(db *gorm.DB) service.IAgentService {
	iAgentTokenRepository := postgresql.NewAgentTokenRepository(db)
	iAgentConfigRepository := postgresql.NewAgentConfigRepository(db)
	iAgentService := service.NewAgentService(iAgentTokenRepository, iAgentConfigRepository)
	return iAgentService
}

// InitializeAuditlogService Initialize Of AuditlogService
func InitializeAuditlogService(db *gorm.DB) service.IAuditlogService {
	iAuditlogRepository := postgresql.NewAuditlogRepository(db)
	iAuditlogService := service.NewAuditlogService(iAuditlogRepository)
	return iAuditlogService
}

// InitializeClientCertificateService Initialize Of ClientCertificateService
func InitializeClientCertificateService(db *gorm.DB) service.IClientCertificateService {
	iClientCertificateRepository := postgresql.NewClientCertificateRepository(db)
	iClientCertificateService := service.NewClientCertificateService(iClientCertificateRepository)
	return iClientCertificateService
}

// InitializeCommentService Initialize Of CommentService
func InitializeCommentService(db *gorm.DB, client *s3.Client, presignClient *s3.PresignClient) service.ICommentService {
	iTaskCommentRepository := postgresql.NewTaskCommentRepository(db)
	iTaskRepository := postgresql.NewTaskRepository(db)
	iTaskCommentFileRepository := postgresql.NewTaskCommentFileRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iCommentService := service.NewTaskCommentService(iTaskCommentRepository, iTaskRepository, iTaskCommentFileRepository, iStaffRepository, iHospitalRepository, client, presignClient)
	return iCommentService
}

// InitializePortalHospitalNotification Initialize Of PortalHospitalNotificationService
func InitializePortalHospitalNotificationService(db *gorm.DB) service.IPortalHospitalNotificationService {
	iPortalHospitalNotificationRepository := postgresql.NewPortalHospitalNotificationRepository(db)
	iPortalHospitalRepository := postgresql.NewPortalHospitalRepository(db)
	iPortalHospitalNotificationService := service.NewPortalHospitalNotificationService(iPortalHospitalNotificationRepository, iPortalHospitalRepository)
	return iPortalHospitalNotificationService
}

// InitializeHealthCheckService Initialize of DBHealthCheckService
func InitializeHealthCheckService(db *gorm.DB, universalClient redis.UniversalClient) service.IHealthCheckService {
	idbHealthCheckRepository := postgresql.NewDBHealthCheckRepository(db)
	iCommonRedisRepository := myredis.NewCommonRedisRepository(universalClient)
	iHealthCheckService := service.NewHealthCheckService(idbHealthCheckRepository, iCommonRedisRepository)
	return iHealthCheckService
}

// InitializeExaminationService Initialize Of ExaminationService
func InitializeExaminationService(db *gorm.DB) service.IExaminationService {
	iExaminationRepository := postgresql.NewExaminationRepository(db)
	iExaminationService := service.NewExaminationService(iExaminationRepository)
	return iExaminationService
}

// InitializeGenerateService Initialize Of GenerateService
func InitializeGenerateService(db *gorm.DB, universalClient redis.UniversalClient) pdf.IGenerateService {
	iSurveyRepository := postgresql.NewSurveyRepository(db)
	iGenerateService := pdf.NewGenerateService(iSurveyRepository)
	return iGenerateService
}

// InitializeHospitalUseCase Initialize Of HospitalUseCase
func InitializeHospitalUseCase(db *gorm.DB, universalClient redis.UniversalClient, client *sqs.Client, sesv2Client *sesv2.Client, opensearchClient *opensearch.Client) service.IHospitalService {
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iHpPermissionRepository := postgresql.NewHpPermissionRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iOpesysMemoRepository := postgresql.NewOpesysMemoRepository(db)
	iSignupRepository := postgresql.NewSignupRepository(db, universalClient)
	iTaskStatusRepository := postgresql.NewTaskStatusRepository(db)
	iTaskCategoryRepository := postgresql.NewTaskCategoryRepository(db)
	iPortalPrefectureRepository := postgresql.NewPortalPrefectureRepository(db)
	iSetMstRepository := postgresql.NewSetMstRepository(db)
	iSetGenerationMstRepository := postgresql.NewSetGenerationMstRepository(db)
	iSignupService := service.NewSignupService(iSignupRepository, iHospitalRepository, iOpesysMemoRepository, iTaskStatusRepository, iTaskCategoryRepository, iPortalPrefectureRepository, iSetMstRepository, iSetGenerationMstRepository)
	iTemplateMailRepository := postgresql.NewTemplateMailRepository(db)
	iMailDeliverySettingsRepository := postgresql.NewMailDeliverySettingsRepository(db)
	iMailVerificationRequestsRepository := postgresql.NewMailVerificationRequestsRepository(db)
	iExamTimeSlotRepository := postgresql.NewExamTimeSlotRepository(db)
	iTreatmentDepartmentRepository := postgresql.NewTreatmentDepartmentRepository(db)
	iCalendarRepository := postgresql.NewCalendarRepository(db)
	iReservationRepository := postgresql.NewReservationRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(client)
	iMailRepository := ses.NewMailRepository(sesv2Client)
	iMessageChannelRepository := postgresql.NewMessageChannelRepository(db)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iMailService := service.NewMailService(iTemplateMailRepository, iMailDeliverySettingsRepository, iMailVerificationRequestsRepository, iStaffRepository, iExamTimeSlotRepository, iTreatmentDepartmentRepository, iCalendarRepository, iReservationRepository, iQueuingRepository, iMailRepository, iMessageChannelRepository, iPharmacyReserveRepository)
	iImportServiceRepository := postgresql.NewImportServiceRepository(db)
	iPortalHospitalSearchRepository := opensearch2.NewPortalHospitalSearchRepository(opensearchClient)
	iImportCashlessRepository := postgresql.NewImportCashlessRepository(db)
	iHospitalService := service.NewHospitalService(iHospitalRepository, iHpPermissionRepository, iStaffRepository, iOpesysMemoRepository, iSignupService, iMailService, iSetMstRepository, iSetGenerationMstRepository, iImportServiceRepository, iPortalHospitalSearchRepository, iImportCashlessRepository)
	return iHospitalService
}

// InitializeLabelService Initialize of LalbelUsecase
func InitializeLabelService(db *gorm.DB) service.ILabelService {
	iRaiinKbnMstRepository := postgresql.NewRaiinKbnMstRepository(db)
	iLabelService := service.NewLabelService(iRaiinKbnMstRepository)
	return iLabelService
}

// InitializeMeetingUsecase Initialize of MeetingUsecase
func InitializeMeetingService(db *gorm.DB, client *chimesdkmeetings.Client, universalClient redis.UniversalClient, opensearchClient *opensearch.Client, sqsClient *sqs.Client, s3Client *s3.Client, presignClient *s3.PresignClient) service.IMeetingService {
	iiMeetingChimeRepository := chime.NewChimeRepository(client)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	iMessageChannelRepository := postgresql.NewMessageChannelRepository(db)
	iMessageRepository := postgresql.NewMessageRepository(db)
	iMessageNotificationRepository := myredis.NewMessageNotificationRepository(universalClient)
	iMessageSearchRepository := opensearch2.NewMessageSearchRepository(opensearchClient)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iPortalCustomerRepository := postgresql.NewPortalCustomerRepository(db)
	iMessageValidator := component.NewMessageValidator(iMessageChannelRepository, iMessageRepository, iStaffRepository, iPatientRepository, iPortalCustomerRepository)
	iServiceValidator := message.NewServiceValidator(iMessageValidator)
	iMessageS3Repository := s3_2.NewMessageS3Repository(s3Client, presignClient)
	iMessageService := service.NewMessageService(iMessageChannelRepository, iMessageRepository, iMessageNotificationRepository, iMessageSearchRepository, iStaffRepository, iPatientRepository, iQueuingRepository, iServiceValidator, iMessageS3Repository)
	iPatientMessageService := service.NewPatientMessageService(iMessageChannelRepository, iMessageRepository, iMessageNotificationRepository, iMessageSearchRepository, iStaffRepository, iPatientRepository, iQueuingRepository, iServiceValidator, iMessageS3Repository, iMessageService)
	iMeetingNotificationRepository := myredis.NewMeetingNotificationRepository(universalClient)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iAuditlogRepository := postgresql.NewAuditlogRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iMeetingService := service.NewMeetingService(iiMeetingChimeRepository, iMeetingRepository, iPatientMessageService, iMeetingNotificationRepository, iPharmacyReserveRepository, iAuditlogRepository, iStaffRepository, iHospitalRepository)
	return iMeetingService
}

// InitializeNotificationService Initialize of NotificationService
func InitializeNotificationService(db *gorm.DB, universalClient redis.UniversalClient) service.INotificationService {
	iMessageNotificationRepository := myredis.NewMessageNotificationRepository(universalClient)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iMessageRepository := postgresql.NewMessageRepository(db)
	iNotificationService := service.NewNotificationService(iMessageNotificationRepository, iStaffRepository, iPatientRepository, iMessageRepository)
	return iNotificationService
}

// InitializeNotificationToPatientService Initialize of NotificationToPatientService
func InitializeNotificationToPatientService(db *gorm.DB, universalClient redis.UniversalClient) service.INotificationToPatientService {
	iMessageNotificationRepository := myredis.NewMessageNotificationRepository(universalClient)
	iMessageRepository := postgresql.NewMessageRepository(db)
	iNotificationToPatientService := service.NewNotificationToPatientService(iMessageNotificationRepository, iMessageRepository)
	return iNotificationToPatientService
}

// InitializeMessageService Initialize of MessageService
func InitializeMessageService(db *gorm.DB, universalClient redis.UniversalClient, client *opensearch.Client, sqsClient *sqs.Client, s3Client *s3.Client, presignClient *s3.PresignClient) service.IMessageService {
	iMessageChannelRepository := postgresql.NewMessageChannelRepository(db)
	iMessageRepository := postgresql.NewMessageRepository(db)
	iMessageNotificationRepository := myredis.NewMessageNotificationRepository(universalClient)
	iMessageSearchRepository := opensearch2.NewMessageSearchRepository(client)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iPortalCustomerRepository := postgresql.NewPortalCustomerRepository(db)
	iMessageValidator := component.NewMessageValidator(iMessageChannelRepository, iMessageRepository, iStaffRepository, iPatientRepository, iPortalCustomerRepository)
	iServiceValidator := message.NewServiceValidator(iMessageValidator)
	iMessageS3Repository := s3_2.NewMessageS3Repository(s3Client, presignClient)
	iMessageService := service.NewMessageService(iMessageChannelRepository, iMessageRepository, iMessageNotificationRepository, iMessageSearchRepository, iStaffRepository, iPatientRepository, iQueuingRepository, iServiceValidator, iMessageS3Repository)
	return iMessageService
}

// InitializePatientService Initialize Of PatientSerivce
func InitializePatientService(db *gorm.DB, client *s3.Client, presignClient *s3.PresignClient) service.IPatientService {
	iPatientRepository := postgresql.NewPatientRepository(db)
	iPatientMemoRepository := postgresql.NewPatientMemoRepository(db)
	iPortalCustomerFileRepository := postgresql.NewPortalCustomerFileRepository(db)
	iPortalCustomerRepository := postgresql.NewPortalCustomerRepository(db)
	iReserveDetailRepository := postgresql.NewReserveDetailRepository(db)
	iCustomerFileS3Repository := s3_2.NewCustomerS3Repository(client, presignClient)
	iPatientService := service.NewPatientService(iPatientRepository, iPatientMemoRepository, iPortalCustomerFileRepository, iPortalCustomerRepository, iReserveDetailRepository, iCustomerFileS3Repository)
	return iPatientService
}

// InitializePublishService Initialize Of PublishService
func InitializePublishService(contextContext context.Context, db *gorm.DB, client *sqs.Client, universalClient redis.UniversalClient) pdf.IPublishService {
	iSurveyRepository := postgresql.NewSurveyRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(client)
	iPublishService := pdf.NewPublishService(contextContext, iSurveyRepository, iQueuingRepository)
	return iPublishService
}

// InitializeSchemaService Initialize Of SchemaService
func InitializeSchemaService(db *gorm.DB, client *s3.Client, presignClient *s3.PresignClient) service.ISchemaService {
	iSchemaImageRepository := postgresql.NewSchemaImageRepository(db)
	iSchemaImageS3Repository := s3_2.NewSchemaImageS3Repository(client, presignClient)
	iSchemaService := service.NewSchemaService(iSchemaImageRepository, iSchemaImageS3Repository)
	return iSchemaService
}

// InitializeSessionService Initialize Of SessionService
func InitializeSessionService(string2 string) service.ISessionService {
	iSessionService := service.NewSessionService(string2)
	return iSessionService
}

// InitializeAuthService Initialize Of AuthService
func InitializeAuthService(universalClient redis.UniversalClient) service.IAuthService {
	iSessionRepository := myredis.NewSessionRepository(universalClient)
	iAuthService := service.NewAuthService(iSessionRepository)
	return iAuthService
}

// InitializeSpecialistService Initialize Of SpecialistService
func InitializeSpecialistService(db *gorm.DB) service.ISpecialistService {
	iSpecialistRepository := postgresql.NewSpecialistRepository(db)
	iSpecialistService := service.NewSpecialistService(iSpecialistRepository)
	return iSpecialistService
}

// InitializeStaffService Initialize Of StaffService
func InitializeStaffService(db *gorm.DB, universalClient redis.UniversalClient) service.IStaffService {
	iStaffRepository := postgresql.NewStaffRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iPermissionRepository := postgresql.NewPermissionRepository(db)
	iTaskRepository := postgresql.NewTaskRepository(db)
	iHospitalStatusCheckService := service.NewHospitalStatusCheckService(iHospitalRepository)
	iSetMstRepository := postgresql.NewSetMstRepository(db)
	iSetGenerationMstRepository := postgresql.NewSetGenerationMstRepository(db)
	iStaffService := service.NewStaffService(iStaffRepository, iHospitalRepository, iPermissionRepository, iTaskRepository, iHospitalStatusCheckService, iSetMstRepository, iSetGenerationMstRepository)
	return iStaffService
}

// InitializeSurveyUsecase Initialize of SurveyService
func InitializeSurveyUsecase(db *gorm.DB, client *sqs.Client, s3Client *s3.Client, presignClient *s3.PresignClient, universalClient redis.UniversalClient) service.ISurveyService {
	iSurveyRepository := postgresql.NewSurveyRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(client)
	iSurveyAnswerRepository := postgresql.NewSurveyAnswerRepository(db)
	iSurveyTemplateRepository := postgresql.NewSurveyTemplateRepository(db)
	iSurveyFileS3Repository := s3_2.NewSurveyFileS3Repository(s3Client, presignClient)
	iSurveyService := service.NewSurveyService(iSurveyRepository, iQueuingRepository, iSurveyAnswerRepository, iSurveyTemplateRepository, s3Client, presignClient, iSurveyFileS3Repository)
	return iSurveyService
}

// InitializeTagService Initialize Of TagService
func InitializeTagService(db *gorm.DB) service.ITagService {
	iTagRepository := postgresql.NewTagRepository(db)
	iTagService := service.NewTagService(iTagRepository)
	return iTagService
}

// InitializeTaskService Initialize of TaskService
func InitializeTaskService(db *gorm.DB, client *s3.Client, presignClient *s3.PresignClient, universalClient redis.UniversalClient) service.ITaskService {
	iTaskRepository := postgresql.NewTaskRepository(db)
	iTaskFileRepository := postgresql.NewTaskFileRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iTaskCommentRepository := postgresql.NewTaskCommentRepository(db)
	iTaskCategoryRepository := postgresql.NewTaskCategoryRepository(db)
	iTaskStatusRepository := postgresql.NewTaskStatusRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iTaskHistoryRepository := postgresql.NewTaskHistoryRepository(db)
	iTaskCommentFileRepository := postgresql.NewTaskCommentFileRepository(db)
	iTaskService := service.NewTaskService(iTaskRepository, iTaskFileRepository, iStaffRepository, iHospitalRepository, iTaskCommentRepository, iTaskCategoryRepository, iTaskStatusRepository, iPatientRepository, iTaskHistoryRepository, iTaskCommentFileRepository, client, presignClient)
	return iTaskService
}

// InitializeTemplateDocService Initialize of TemplateDocService
func InitializeTemplateDocService(db *gorm.DB, presignClient *s3.PresignClient, universalClient redis.UniversalClient, client *s3.Client) service.ITemplateDocService {
	iTemplateDocRepository := postgresql.NewTemplateDocRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iTemplateDocService := service.NewTemplateDocService(iTemplateDocRepository, iPatientRepository, iHospitalRepository, presignClient, client)
	return iTemplateDocService
}

// InitializeTreatmentCategoryService Initialize Of TreatmentCategoryService
func InitializeTreatmentCategoryService(db *gorm.DB) service.IMTreatmentCategoryService {
	imTreatmentCategoryRepository := postgresql.NewMTreatmentCategoryRepository(db)
	imTreatmentCategoryService := service.NewMTreatmentCategoryService(imTreatmentCategoryRepository)
	return imTreatmentCategoryService
}

// InitializeExamTimeSlotService Initialize Of ExamTimeSlotService
func InitializeExamTimeSlotService(db *gorm.DB) service.IExamTimeSlotService {
	iExamTimeSlotRepository := postgresql.NewExamTimeSlotRepository(db)
	iExamTimeSlotService := service.NewExamTimeSlotService(iExamTimeSlotRepository)
	return iExamTimeSlotService
}

// InitializeCalendarService Initialize Of CalendarService
func InitializeCalendarService(db *gorm.DB, client *opensearch.Client, sqsClient *sqs.Client) service.ICalendarService {
	iCalendarRepository := postgresql.NewCalendarRepository(db)
	iExamTimeSlotSearchRepository := opensearch2.NewExamTimeSlotSearchRepository(db, client)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iCalendarService := service.NewCalendarService(iCalendarRepository, iExamTimeSlotSearchRepository, iQueuingRepository)
	return iCalendarService
}

// InitializeReservationService Initialize Of ReservationService
func InitializeReservationService(db *gorm.DB, client *opensearch.Client, sqsClient *sqs.Client) service.IReservationService {
	iReservationRepository := postgresql.NewReservationRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iExamTimeSlotRepository := postgresql.NewExamTimeSlotRepository(db)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	iCalendarRepository := postgresql.NewCalendarRepository(db)
	iExamTimeSlotSearchRepository := opensearch2.NewExamTimeSlotSearchRepository(db, client)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iReservationService := service.NewReservationService(iReservationRepository, iPatientRepository, iExamTimeSlotRepository, iMeetingRepository, iCalendarRepository, iExamTimeSlotSearchRepository, iQueuingRepository)
	return iReservationService
}

// InitializeETLService Initialize Of ETLService
func InitializeETLService(client *opensearch.Client) service.IETLService {
	ietlRepository := opensearch2.NewETLRepository(client)
	ietlService := service.NewETLService(ietlRepository)
	return ietlService
}

// InitializeTreatmentDepartmentService Initialize of TreatmentDepartmentUsecase
func InitializeTreatmentDepartmentService(db *gorm.DB, client *opensearch.Client, sqsClient *sqs.Client) service.ITreatmentDepartmentService {
	iTreatmentDepartmentRepository := postgresql.NewTreatmentDepartmentRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iCalendarTreatmentRepository := postgresql.NewCalendarTreatmentRepository(db)
	iReserveDetailRepository := postgresql.NewReserveDetailRepository(db)
	iReserveDetailHistoryRepository := postgresql.NewReserveDetailHistoryRepository(db)
	iCalendarRepository := postgresql.NewCalendarRepository(db)
	iPortalHospitalRepository := postgresql.NewPortalHospitalRepository(db)
	iPortalHospitalSearchRepository := opensearch2.NewPortalHospitalSearchRepository(client)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iTreatmentFeeListRepository := postgresql.NewTreatmentFeeListRepository(db)
	iTreatmentDepartmentService := service.NewTreatmentDepartmentService(iTreatmentDepartmentRepository, iStaffRepository, iCalendarTreatmentRepository, iReserveDetailRepository, iReserveDetailHistoryRepository, iCalendarRepository, iPortalHospitalRepository, iPortalHospitalSearchRepository, iQueuingRepository, iTreatmentFeeListRepository)
	return iTreatmentDepartmentService
}

// InitializeMailService Initialize Of MailService
func InitializeMailService(db *gorm.DB, client *sqs.Client, sesv2Client *sesv2.Client) service.IMailService {
	iTemplateMailRepository := postgresql.NewTemplateMailRepository(db)
	iMailDeliverySettingsRepository := postgresql.NewMailDeliverySettingsRepository(db)
	iMailVerificationRequestsRepository := postgresql.NewMailVerificationRequestsRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iExamTimeSlotRepository := postgresql.NewExamTimeSlotRepository(db)
	iTreatmentDepartmentRepository := postgresql.NewTreatmentDepartmentRepository(db)
	iCalendarRepository := postgresql.NewCalendarRepository(db)
	iReservationRepository := postgresql.NewReservationRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(client)
	iMailRepository := ses.NewMailRepository(sesv2Client)
	iMessageChannelRepository := postgresql.NewMessageChannelRepository(db)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iMailService := service.NewMailService(iTemplateMailRepository, iMailDeliverySettingsRepository, iMailVerificationRequestsRepository, iStaffRepository, iExamTimeSlotRepository, iTreatmentDepartmentRepository, iCalendarRepository, iReservationRepository, iQueuingRepository, iMailRepository, iMessageChannelRepository, iPharmacyReserveRepository)
	return iMailService
}

// InitializeAddressService Initialize of AddressService
func InitializeAddressService(db *gorm.DB) service.IAddressService {
	iPrefectureRepository := postgresql.NewPrefectureRepository(db)
	iCommonPostCodeMstRepository := postgresql.NewCommonPostCodeMstRepository(db)
	iAddressService := service.NewAddressService(iPrefectureRepository, iCommonPostCodeMstRepository)
	return iAddressService
}

// InitializePortalAddressService Initialize of PortalAddressService
func InitializePortalAddressService(db *gorm.DB) service.IPortalAddressService {
	iPortalCityRepository := postgresql.NewPortalCityRepository(db)
	iPortalPrefectureRepository := postgresql.NewPortalPrefectureRepository(db)
	iCommonPostCodeMstRepository := postgresql.NewCommonPostCodeMstRepository(db)
	iPortalAddressService := service.NewPortalAddressService(iPortalCityRepository, iPortalPrefectureRepository, iCommonPostCodeMstRepository)
	return iPortalAddressService
}

func InitializePortalHospitalStaffService(db *gorm.DB, presignClient *s3.PresignClient, client *s3.Client) service.IPortalHospitalStaffService {
	iPortalHospitalStaffRepository := postgresql.NewPortalHospitalStaffRepository(db)
	iPortalStaffPictureRepository := postgresql.NewPortalStaffPictureRepository(db)
	iPortalPictureRepository := postgresql.NewPortalPictureRepository(db)
	iPortalHospitalStaffService := service.NewPortalHospitalStaffService(iPortalHospitalStaffRepository, presignClient, iPortalStaffPictureRepository, iPortalPictureRepository, client)
	return iPortalHospitalStaffService
}

// InitializePatientMessageService Initialize of PatientMessageService
func InitializePatientMessageService(db *gorm.DB, universalClient redis.UniversalClient, client *opensearch.Client, sqsClient *sqs.Client, s3Client *s3.Client, presignClient *s3.PresignClient) service.IPatientMessageService {
	iMessageChannelRepository := postgresql.NewMessageChannelRepository(db)
	iMessageRepository := postgresql.NewMessageRepository(db)
	iMessageNotificationRepository := myredis.NewMessageNotificationRepository(universalClient)
	iMessageSearchRepository := opensearch2.NewMessageSearchRepository(client)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iPortalCustomerRepository := postgresql.NewPortalCustomerRepository(db)
	iMessageValidator := component.NewMessageValidator(iMessageChannelRepository, iMessageRepository, iStaffRepository, iPatientRepository, iPortalCustomerRepository)
	iServiceValidator := message.NewServiceValidator(iMessageValidator)
	iMessageS3Repository := s3_2.NewMessageS3Repository(s3Client, presignClient)
	iMessageService := service.NewMessageService(iMessageChannelRepository, iMessageRepository, iMessageNotificationRepository, iMessageSearchRepository, iStaffRepository, iPatientRepository, iQueuingRepository, iServiceValidator, iMessageS3Repository)
	iPatientMessageService := service.NewPatientMessageService(iMessageChannelRepository, iMessageRepository, iMessageNotificationRepository, iMessageSearchRepository, iStaffRepository, iPatientRepository, iQueuingRepository, iServiceValidator, iMessageS3Repository, iMessageService)
	return iPatientMessageService
}

// InitializePatientPortalHospitalService Initialize Of PatientPortalHospital
func InitializePatientPortalHospitalService(db *gorm.DB) service.IPatientPortalHospitalService {
	iPortalHospitalRepository := postgresql.NewPortalHospitalRepository(db)
	iPatientPortalHospitalService := service.NewPatientPortalHospitalService(iPortalHospitalRepository)
	return iPatientPortalHospitalService
}

// InitializeAgreeService Initialize of AgreeService
func InitializeAgreeService(db *gorm.DB) service.IAgreeService {
	iAgreeRepository := postgresql.NewAgreeRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iAgreeService := service.NewAgreeService(iAgreeRepository, iStaffRepository)
	return iAgreeService
}

// InitializeOcrService Initialize of OcrService
func InitializeOcrService(client *s3.Client, presignClient *s3.PresignClient, universalClient redis.UniversalClient) service.IOcrService {
	iOcrS3Repository := s3_2.NewOcrS3Repository(client, presignClient)
	iCommonRedisRepository := myredis.NewCommonRedisRepository(universalClient)
	iOcrService := service.NewOcrService(iOcrS3Repository, iCommonRedisRepository)
	return iOcrService
}

// InitializeAIService Initialize of AIService
func InitializeAIService(contextContext context.Context, db *gorm.DB) service.IAIService {
	iaiRepository := postgresql.NewAIRepository(db)
	imTreatmentCategoryRepository := postgresql.NewMTreatmentCategoryRepository(db)
	iaiService := service.NewAIService(contextContext, iaiRepository, imTreatmentCategoryRepository)
	return iaiService
}

// InitializePortalHospitalService Initialize Of PortalHospital
func InitializePortalHospitalService(contextContext context.Context, db *gorm.DB, presignClient *s3.PresignClient, client *s3.Client, opensearchClient *opensearch.Client, sqsClient *sqs.Client) service.IPortalHospitalService {
	iPortalHospitalRepository := postgresql.NewPortalHospitalRepository(db)
	iPortalHospitalSearchRepository := opensearch2.NewPortalHospitalSearchRepository(opensearchClient)
	iPortalHospPictureRepository := postgresql.NewPortalHospPictureRepository(db)
	iPortalPictureRepository := postgresql.NewPortalPictureRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iImportHospitalRepository := postgresql.NewImportHospitalRepository(db)
	iaiRepository := postgresql.NewAIRepository(db)
	imTreatmentCategoryRepository := postgresql.NewMTreatmentCategoryRepository(db)
	iaiService := service.NewAIService(contextContext, iaiRepository, imTreatmentCategoryRepository)
	iPortalHospitalService := service.NewPortalHospitalService(iPortalHospitalRepository, iPortalHospitalSearchRepository, presignClient, iPortalHospPictureRepository, iPortalPictureRepository, client, iQueuingRepository, iImportHospitalRepository, iaiService)
	return iPortalHospitalService
}

// InitializeOperatorService Initialize of OperatorService
func InitializeOperatorService(client *cognitoidentityprovider.Client, db *gorm.DB) service.IOperatorService {
	iOperatorAuthRepository := cognito.NewOperatorAuthRepository(client)
	iOperatorPermissionRepository := postgresql.NewOperatorPermissionRepository(db)
	iStaffRepository := postgresql.NewStaffRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iOperatorService := service.NewOperatorService(iOperatorAuthRepository, iOperatorPermissionRepository, iStaffRepository, iHospitalRepository)
	return iOperatorService
}

// InitializeSignupService Initialize of SignupService
func InitializeSignupService(db *gorm.DB, universalClient redis.UniversalClient) service.ISignupService {
	iSignupRepository := postgresql.NewSignupRepository(db, universalClient)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iOpesysMemoRepository := postgresql.NewOpesysMemoRepository(db)
	iTaskStatusRepository := postgresql.NewTaskStatusRepository(db)
	iTaskCategoryRepository := postgresql.NewTaskCategoryRepository(db)
	iPortalPrefectureRepository := postgresql.NewPortalPrefectureRepository(db)
	iSetMstRepository := postgresql.NewSetMstRepository(db)
	iSetGenerationMstRepository := postgresql.NewSetGenerationMstRepository(db)
	iSignupService := service.NewSignupService(iSignupRepository, iHospitalRepository, iOpesysMemoRepository, iTaskStatusRepository, iTaskCategoryRepository, iPortalPrefectureRepository, iSetMstRepository, iSetGenerationMstRepository)
	return iSignupService
}

// InitializePaymentService Initialize Of PaymentService
func InitializePaymentService(db *gorm.DB, duration time.Duration) service.IPaymentService {
	iReservationRepository := postgresql.NewReservationRepository(db)
	iReserveDetailRepository := postgresql.NewReserveDetailRepository(db)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iPharmacyReserveDetailRepository := postgresql.NewPharmacyReserveDetailRepository(db)
	iPaymentRepository := postgresql.NewPaymentRepository(db)
	ihttpClientWrapper := client.NewHTTPClientWrapper(duration)
	iFincodeAPIRepository := fincode.NewFincodeAPIRepository(ihttpClientWrapper)
	iHpFincodeInfoRepository := postgresql.NewHpFincodeInfoRepository(db)
	iPortalCustomerPaymentRepository := postgresql.NewPortalCustomerPaymentRepository(db)
	iPortalCustomerRepository := postgresql.NewPortalCustomerRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	idbTransactionRepository := rdb.NewDBTransactionRepository(db)
	iAuditlogRepository := postgresql.NewAuditlogRepository(db)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iCalcStatusRepository := postgresql.NewCalcStatusRepository(db)
	iPaymentService := service.NewPaymentService(iReservationRepository, iReserveDetailRepository, iPharmacyReserveRepository, iPharmacyReserveDetailRepository, iPaymentRepository, iFincodeAPIRepository, iHpFincodeInfoRepository, iPortalCustomerPaymentRepository, iPortalCustomerRepository, iPatientRepository, iMeetingRepository, idbTransactionRepository, iAuditlogRepository, iHospitalRepository, iCalcStatusRepository)
	return iPaymentService
}

// InitializeSMSService Initialize Of SMSService
func InitializeSMSService(db *gorm.DB, sqsClient *sqs.Client, universalClient redis.UniversalClient) service.ISMSService {
	ismsRepository := sms.NewSMSRepository()
	iReservationRepository := postgresql.NewReservationRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iTemplateSMSRepository := postgresql.NewTemplateSMSRepository(db)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	iLineAccountRepository := postgresql.NewLineAccountRepository(db)
	iPrescriptionReceptionRepository := postgresql.NewPrescriptionReceptionRepository(db)
	iPharmacyReserveDetailRepository := postgresql.NewPharmacyReserveDetailRepository(db)
	iSignupRepository := postgresql.NewSignupRepository(db, universalClient)
	ismsService := service.NewSMSService(ismsRepository, iReservationRepository, iQueuingRepository, iTemplateSMSRepository, iPharmacyReserveRepository, iPatientRepository, iMeetingRepository, iLineAccountRepository, iPrescriptionReceptionRepository, iPharmacyReserveDetailRepository, iSignupRepository)
	return ismsService
}

// InitializeSecondarySMSService Initialize Of SecondarySMSService
func InitializeSecondarySMSService(db *gorm.DB, sqsClient *sqs.Client, pinpointsmsvoicev2Client *pinpointsmsvoicev2.Client, universalClient redis.UniversalClient) service.ISMSService {
	ismsRepository := sms.NewSecondarySMSRepository(pinpointsmsvoicev2Client)
	iReservationRepository := postgresql.NewReservationRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iTemplateSMSRepository := postgresql.NewTemplateSMSRepository(db)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	iLineAccountRepository := postgresql.NewLineAccountRepository(db)
	iPrescriptionReceptionRepository := postgresql.NewPrescriptionReceptionRepository(db)
	iPharmacyReserveDetailRepository := postgresql.NewPharmacyReserveDetailRepository(db)
	iSignupRepository := postgresql.NewSignupRepository(db, universalClient)
	ismsService := service.NewSMSService(ismsRepository, iReservationRepository, iQueuingRepository, iTemplateSMSRepository, iPharmacyReserveRepository, iPatientRepository, iMeetingRepository, iLineAccountRepository, iPrescriptionReceptionRepository, iPharmacyReserveDetailRepository, iSignupRepository)
	return ismsService
}

// InitializeFaxService Initialize Of FaxService
func InitializeFaxService(db *gorm.DB, s3Client *s3.Client, presignClient *s3.PresignClient, sqsClient *sqs.Client) service.IFaxService {
	iFaxRepository := postgresql.NewFaxRepository(db)
	iFaxNumberRepository := postgresql.NewFaxNumberRepository(db)
	iFaxAPIClient := faxapi.NewFaxAPIClient()
	iFaxS3Repository := s3_2.NewFaxS3Repository(s3Client, presignClient)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iFaxService := service.NewFaxService(iFaxRepository, iFaxNumberRepository, iFaxAPIClient, iFaxS3Repository, iQueuingRepository)
	return iFaxService
}

// InitializeZendeskService Initialize Of ZendeskService
func InitializeZendeskService(db *gorm.DB, universalClient redis.UniversalClient) service.IZendeskService {
	iStaffRepository := postgresql.NewStaffRepository(db)
	iSignupRepository := postgresql.NewSignupRepository(db, universalClient)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iZendeskService := service.NewZendeskService(iStaffRepository, iSignupRepository, iHospitalRepository)
	return iZendeskService
}

// InitializePortalHospitalSearchService Initialize Of PortalHospitalSearchService
func InitializePortalHospitalSearchService(db *gorm.DB, opensearchClient *opensearch.Client) service.IPortalHospitalSearchService {
	iPortalHospitalSearchRepository := opensearch2.NewPortalHospitalSearchRepository(opensearchClient)
	iExamTimeSlotSearchRepository := opensearch2.NewExamTimeSlotSearchRepository(db, opensearchClient)
	iPortalHospitalSearchService := service.NewPortalHospitalSearchService(iPortalHospitalSearchRepository, iExamTimeSlotSearchRepository)
	return iPortalHospitalSearchService
}

// InitializeFaxService Initialize Of FaxService
func InitializeFreeeService(db *gorm.DB) service.IFreeeService {
	iFreeeAPIClient := freee.NewFreeeAPIClient()
	iFreeeDummyRepository := postgresql.NewFreeeDummyRepository(db)
	iFreeeService := service.NewFreeeService(iFreeeAPIClient, iFreeeDummyRepository)
	return iFreeeService
}

// InitializeLineService Initialize of LineService
func InitializeLineService(db *gorm.DB, linebotClient *linebot.Client, sqsClient *sqs.Client) service.ILineService {
	iLineAccountRepository := postgresql.NewLineAccountRepository(db)
	iTemplateMailRepository := postgresql.NewTemplateMailRepository(db)
	iReservationRepository := postgresql.NewReservationRepository(db)
	iLineRepository := line.NewLineRepository(linebotClient)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iLineService := service.NewLineService(iLineAccountRepository, iTemplateMailRepository, iReservationRepository, iLineRepository, iQueuingRepository, iPharmacyReserveRepository)
	return iLineService
}

// InitializePharmacyHolidayService Initialize Of PharmacyHolidayService
func InitializePharmacyHolidayService(db *gorm.DB) service.IPharmacyHolidayService {
	iPharmacyHolidayRepository := postgresql.NewPharmacyHolidayRepository(db)
	iPharmacyHolidayService := service.NewPharmacyHolidayService(iPharmacyHolidayRepository)
	return iPharmacyHolidayService
}

// InitializePharmacyDeliveryService Initialize of PharmacyDeliveryService
func InitializePharmacyDeliveryService(db *gorm.DB) service.IPharmacyDeliveryService {
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iPharmacyDeliveryHistoryRepository := postgresql.NewPharmacyDeliveryHistoryRepository(db)
	iPharmacyDeliveryService := service.NewPharmacyDeliveryService(iPharmacyReserveRepository, iPharmacyDeliveryHistoryRepository)
	return iPharmacyDeliveryService
}

// InitializePharmacyReserveService Initialize Of PharmacyReserveService
func InitializePharmacyReserveService(db *gorm.DB, sqsClient *sqs.Client) service.IPharmacyReserveService {
	iPharmacyReserveRepository := postgresql.NewPharmacyReserveRepository(db)
	iPharmacyReserveDetailRepository := postgresql.NewPharmacyReserveDetailRepository(db)
	iPharmacyReserveStatusHistoryRepository := postgresql.NewPharmacyReserveStatusHistoryRepository(db)
	iPharmacyDesiredDateRepository := postgresql.NewPharmacyDesiredDateRepository(db)
	iQueuingRepository := sqs2.NewQueuingRepository(sqsClient)
	iMeetingRepository := postgresql.NewMeetingRepository(db)
	iTemplateSMSRepository := postgresql.NewTemplateSMSRepository(db)
	iPatientRepository := postgresql.NewPatientRepository(db)
	iLineAccountRepository := postgresql.NewLineAccountRepository(db)
	iPharmacyReserveService := service.NewPharmacyReserveService(iPharmacyReserveRepository, iPharmacyReserveDetailRepository, iPharmacyReserveStatusHistoryRepository, iPharmacyDesiredDateRepository, iQueuingRepository, iMeetingRepository, iTemplateSMSRepository, iPatientRepository, iLineAccountRepository)
	return iPharmacyReserveService
}

// InitializePharmacyReserveDetailService Initialize of PharmacyReserveDetailService
func InitializePharmacyReserveDetailService(db *gorm.DB) service.IPharmacyReserveDetailService {
	iPharmacyReserveDetailRepository := postgresql.NewPharmacyReserveDetailRepository(db)
	iPharmacyReserveDetailService := service.NewPharmacyReserveDetailService(iPharmacyReserveDetailRepository)
	return iPharmacyReserveDetailService
}

// InitializePharmacyPatientFileService Initialize of PharmacyPatientFileService
func InitializePharmacyPatientFileService(db *gorm.DB, s3Client *s3.Client, presignClient *s3.PresignClient) service.IPharmacyPatientFileService {
	iPharmacyPatientFileRepository := postgresql.NewPharmacyPatientFileRepository(db)
	iCustomerFileS3Repository := s3_2.NewCustomerS3Repository(s3Client, presignClient)
	iPharmacyPatientFileService := service.NewPharmacyPatientFileService(iPharmacyPatientFileRepository, iCustomerFileS3Repository)
	return iPharmacyPatientFileService
}

// InitializeImportDataService Initialize of ImportDataService
func InitializeImportDataService(db *gorm.DB, s3Client *s3.Client) service.IImportDataService {
	iImportDataS3Repository := s3_2.NewImportDataS3Repository(s3Client)
	iImportHospitalRepository := postgresql.NewImportHospitalRepository(db)
	iImportServiceRepository := postgresql.NewImportServiceRepository(db)
	iImportOperatingHourRepository := postgresql.NewImportOperatingHourRepository(db)
	iImportBarrierFreeRepository := postgresql.NewImportBarrierFreeRepository(db)
	iImportCashlessRepository := postgresql.NewImportCashlessRepository(db)
	iImportIctReservationRepository := postgresql.NewImportIctReservationRepository(db)
	iImportMedicalCheckupRepository := postgresql.NewImportMedicalCheckupRepository(db)
	iImportMedicalSpecialistRepository := postgresql.NewImportMedicalSpecialistRepository(db)
	iImportMultilingualRepository := postgresql.NewImportMultilingualRepository(db)
	iImportParkingRepository := postgresql.NewImportParkingRepository(db)
	iImportElectronicPrescriptionRepository := postgresql.NewImportElectronicPrescriptionRepository(db)
	iImportDataService := service.NewImportDataService(iImportDataS3Repository, iImportHospitalRepository, iImportServiceRepository, iImportOperatingHourRepository, iImportBarrierFreeRepository, iImportCashlessRepository, iImportIctReservationRepository, iImportMedicalCheckupRepository, iImportMedicalSpecialistRepository, iImportMultilingualRepository, iImportParkingRepository, iImportElectronicPrescriptionRepository)
	return iImportDataService
}

// InitializePrescriptionReceptionService Initialize Of PrescriptionReceptionService
func InitializePrescriptionReceptionService(db *gorm.DB, s3Client *s3.Client, presignClient *s3.PresignClient) service.IPrescriptionReceptionService {
	iPrescriptionReceptionRepository := postgresql.NewPrescriptionReceptionRepository(db)
	iPrescriptionImageRepository := postgresql.NewPrescriptionImageRepository(db)
	iPrescriptionS3Repository := s3_2.NewPrescriptionS3Repository(s3Client, presignClient)
	iPrescriptionReceptionService := service.NewPrescriptionReceptionService(iPrescriptionReceptionRepository, iPrescriptionImageRepository, iPrescriptionS3Repository)
	return iPrescriptionReceptionService
}

// InitializeHospitalCheckService Initialize Of HospitalCheckService
func InitializeHospitalStatusCheckService(db *gorm.DB) service.IHospitalStatusCheckService {
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	iHospitalStatusCheckService := service.NewHospitalStatusCheckService(iHospitalRepository)
	return iHospitalStatusCheckService
}

// InitializeFixImportDataService Initialize of FixImportDataService
func InitializeFixImportDataService(db *gorm.DB, s3Client *s3.Client) service.IFixImportDataService {
	iImportDataS3Repository := s3_2.NewImportDataS3Repository(s3Client)
	iImportHospitalRepository := postgresql.NewImportHospitalRepository(db)
	iImportServiceRepository := postgresql.NewImportServiceRepository(db)
	iImportOperatingHourRepository := postgresql.NewImportOperatingHourRepository(db)
	iImportBarrierFreeRepository := postgresql.NewImportBarrierFreeRepository(db)
	iImportCashlessRepository := postgresql.NewImportCashlessRepository(db)
	iImportIctReservationRepository := postgresql.NewImportIctReservationRepository(db)
	iImportMedicalCheckupRepository := postgresql.NewImportMedicalCheckupRepository(db)
	iImportMedicalSpecialistRepository := postgresql.NewImportMedicalSpecialistRepository(db)
	iImportMultilingualRepository := postgresql.NewImportMultilingualRepository(db)
	iImportParkingRepository := postgresql.NewImportParkingRepository(db)
	iFixImportDataService := service.NewFixImportDataService(iImportDataS3Repository, iImportHospitalRepository, iImportServiceRepository, iImportOperatingHourRepository, iImportBarrierFreeRepository, iImportCashlessRepository, iImportIctReservationRepository, iImportMedicalCheckupRepository, iImportMedicalSpecialistRepository, iImportMultilingualRepository, iImportParkingRepository)
	return iFixImportDataService
}

// InitializeCommonPostCodeService Initialize of CommonPostCodeService
func InitializeCommonPostCodeService(db *gorm.DB, s3Client *s3.Client) service.ICommonPostCodeMstService {
	iImportDataS3Repository := s3_2.NewImportDataS3Repository(s3Client)
	iCommonPostCodeMstRepository := postgresql.NewCommonPostCodeMstRepository(db)
	iCommonPostCodeMstService := service.NewCommonPostCodeMstService(iImportDataS3Repository, iCommonPostCodeMstRepository)
	return iCommonPostCodeMstService
}

func InitializeMClinicMasterDataService(db *gorm.DB, pool *pgxpool.Pool, s3Client *s3.Client) service.IMClinicMasterDataService {
	imClinicMasterDataRepository := postgresql.NewMClinicMasterDataRepository(s3Client)
	iHospitalRepository := postgresql.NewHospitalRepository(db)
	ipgxTransactionRepository := rdb.NewPGXTransactionRepository(pool)
	imClinicMasterDataService := service.NewMClinicMasterDataService(imClinicMasterDataRepository, iHospitalRepository, ipgxTransactionRepository)
	return imClinicMasterDataService
}

// InitializeSurveyAnswerNoPatientService Initialize of SurveyAnswerNoPatientService
func InitializeSurveyAnswerNoPatientService(db *gorm.DB, presignClient *s3.PresignClient) service.ISurveyAnswerNoPatientService {
	iSurveyAnswerNoPatientRepository := postgresql.NewSurveyAnswerNoPatientRepository(db, presignClient)
	iSurveyRepository := postgresql.NewSurveyRepository(db)
	iSurveyAnswerNoPatientService := service.NewSurveyAnswerNoPatientService(iSurveyAnswerNoPatientRepository, iSurveyRepository, presignClient)
	return iSurveyAnswerNoPatientService
}

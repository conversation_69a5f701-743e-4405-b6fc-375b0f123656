#!/bin/sh

# .envファイルを読み込む
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
else
  echo ".env file not found"
  exit 1
fi

# pg_dumpコマンドの存在を確認
if ! command -v pg_dump &> /dev/null; then
    echo "pg_dump not found, installing PostgreSQL..."
    brew install postgresql@16
else
    # pg_dumpのバージョンを確認
    PG_DUMP_VERSION=$(pg_dump -V | grep -o '[0-9]\+.[0-9]\+')
    echo "pg_dump version: $PG_DUMP_VERSION"

    # バージョンが16.xでない場合、バージョン16をインストール
    if [[ ! "$PG_DUMP_VERSION" =~ ^16\. ]]; then
        echo "Installing PostgreSQL version 16..."
        brew install postgresql@16
        # PostgreSQL 16のパスを追加
        export PATH="`brew --prefix`/opt/postgresql@16/bin:$PATH"
    fi
fi

# 指定されたスキーマ（DDL）のみをダンプする
# --exclude-table で指定したテーブルを除外してダンプする
#PGPASSWORD=$DB_PASSWORD pg_dump -c --no-owner -x -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema-only --schema $DB_SCHEMA -f ./db/dev2/init.sql --if-exists
PGPASSWORD=$DB_PASSWORD pg_dump -c --no-owner -x -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME\
    --exclude-table='accounting_form_mst*' \
    --exclude-table='approval_inf*' \
    --exclude-table='"AuditLogs"' \
    --exclude-table='"AuditLogs_LogId_seq"' \
    --exclude-table='audit_trail_log*' \
    --exclude-table='audit_trail_log_detail*' \
    --exclude-table='auto_santei_mst*' \
    --exclude-table='backup_req*' \
    --exclude-table='bui_odr_byomei_mst*' \
    --exclude-table='bui_odr_item_byomei_mst*' \
    --exclude-table='bui_odr_item_mst*' \
    --exclude-table='bui_odr_mst*' \
    --exclude-table='byomei_mst_aftercare*' \
    --exclude-table='byomei_mst*' \
    --exclude-table='byomei_set_generation_mst*' \
    --exclude-table='byomei_set_mst*' \
    --exclude-table='calc_log*' \
    --exclude-table='calc_status*' \
    --exclude-table='cmt_check_mst*' \
    --exclude-table='cmt_kbn_mst*' \
    --exclude-table='column_setting*' \
    --exclude-table='common_kensa_center_mst' \
    --exclude-table='common_kensa_mst' \
    --exclude-table='common_jlac10_*' \
    --exclude-table='container_mst*' \
    --exclude-table='conversion_item_inf*' \
    --exclude-table='def_hoken_no*' \
    --exclude-table='densi_haihan_custom*' \
    --exclude-table='densi_haihan_day*' \
    --exclude-table='densi_haihan_karte*' \
    --exclude-table='densi_haihan_month*' \
    --exclude-table='densi_haihan_week*' \
    --exclude-table='densi_hojyo*' \
    --exclude-table='densi_houkatu_grp*' \
    --exclude-table='densi_houkatu*' \
    --exclude-table='densi_santei_kaisu*' \
    --exclude-table='doc_category_mst*' \
    --exclude-table='doc_comment_detail*' \
    --exclude-table='doc_comment*' \
    --exclude-table='doc_inf*' \
    --exclude-table='dosage_mst*' \
    --exclude-table='drug_day_limit*' \
    --exclude-table='drug_inf*' \
    --exclude-table='drug_unit_conv*' \
    --exclude-table='eps_chk_detail*' \
    --exclude-table='eps_chk*' \
    --exclude-table='eps_dispensing_list*' \
    --exclude-table='eps_dispensing_req*' \
    --exclude-table='eps_dispensing*' \
    --exclude-table='eps_prescription*' \
    --exclude-table='eps_reference*' \
    --exclude-table='eps_req*' \
    --exclude-table='event_mst*' \
    --exclude-table='except_hokensya*' \
    --exclude-table='fco_api_keys' \
    --exclude-table='filing_auto_imp*' \
    --exclude-table='filing_category_mst*' \
    --exclude-table='filing_inf*' \
    --exclude-table='function_mst*' \
    --exclude-table='gc_std_mst*' \
    --exclude-table='hoken_mst_temp*' \
    --exclude-table='hoken_mst*' \
    --exclude-table='hokensya_mst*' \
    --exclude-table='holiday_mst*' \
    --exclude-table='ipn_kasan_exclude_item*' \
    --exclude-table='ipn_kasan_exclude*' \
    --exclude-table='ipn_kasan_mst*' \
    --exclude-table='ipn_min_yakka_mst*' \
    --exclude-table='ipn_name_mst*' \
    --exclude-table='item_grp_mst*' \
    --exclude-table='jihi_sbt_mst*' \
    --exclude-table='job_mst*' \
    --exclude-table='json_setting*' \
    --exclude-table='ka_mst*' \
    --exclude-table='kacode_mst*' \
    --exclude-table='kacode_rece_yousiki*' \
    --exclude-table='kacode_yousiki_mst*' \
    --exclude-table='kaikei_detail*' \
    --exclude-table='kaikei_inf*' \
    --exclude-table='kantoku_mst*' \
    --exclude-table='karte_filter_detail*' \
    --exclude-table='karte_filter_mst*' \
    --exclude-table='karte_img_inf*' \
    --exclude-table='karte_inf*' \
    --exclude-table='karte_kbn_mst*' \
    --exclude-table='kensa_center_mst*' \
    --exclude-table='kensa_center_partnerships' \
    --exclude-table='kensa_cmt_mst*' \
    --exclude-table='kensa_inf_detail*' \
    --exclude-table='kensa_inf*' \
    --exclude-table='kensa_irai_log*' \
    --exclude-table='kensa_mst*' \
    --exclude-table='kensa_result_log*' \
    --exclude-table='kensa_set_detail*' \
    --exclude-table='kensa_set*' \
    --exclude-table='kensa_std_mst*' \
    --exclude-table='kinki_mst*' \
    --exclude-table='kogaku_limit*' \
    --exclude-table='kohi_priority*' \
    --exclude-table='koui_houkatu_mst*' \
    --exclude-table='koui_kbn_mst*' \
    --exclude-table='limit_cnt_list_inf*' \
    --exclude-table='limit_list_inf*' \
    --exclude-table='list_set_generation_mst*' \
    --exclude-table='list_set_mst*' \
    --exclude-table='lock_inf*' \
    --exclude-table='lock_mst*' \
    --exclude-table='m01_kijyo_cmt*' \
    --exclude-table='m01_kinki_cmt*' \
    --exclude-table='m01_kinki*' \
    --exclude-table='m10_day_limit*' \
    --exclude-table='m12_food_alrgy_kbn*' \
    --exclude-table='m12_food_alrgy*' \
    --exclude-table='m14_age_check*' \
    --exclude-table='m14_cmt_code*' \
    --exclude-table='m28_drug_mst*' \
    --exclude-table='m34_ar_code*' \
    --exclude-table='m34_ar_discon_code*' \
    --exclude-table='m34_ar_discon*' \
    --exclude-table='m34_drug_info_main*' \
    --exclude-table='m34_form_code*' \
    --exclude-table='m34_indication_code*' \
    --exclude-table='m34_interaction_pat_code*' \
    --exclude-table='m34_interaction_pat*' \
    --exclude-table='m34_precaution_code*' \
    --exclude-table='m34_precautions*' \
    --exclude-table='m34_property_code*' \
    --exclude-table='m34_sar_symptom_code*' \
    --exclude-table='m38_class_code*' \
    --exclude-table='m38_ing_code*' \
    --exclude-table='m38_ingredients*' \
    --exclude-table='m38_major_div_code*' \
    --exclude-table='m38_otc_form_code*' \
    --exclude-table='m38_otc_main*' \
    --exclude-table='m38_otc_maker_code*' \
    --exclude-table='m41_supple_indexcode*' \
    --exclude-table='m41_supple_indexdef*' \
    --exclude-table='m41_supple_ingre*' \
    --exclude-table='m42_contra_cmt*' \
    --exclude-table='m42_contraindi_dis_bc*' \
    --exclude-table='m42_contraindi_dis_class*' \
    --exclude-table='m42_contraindi_dis_con*' \
    --exclude-table='m42_contraindi_drug_main_ex*' \
    --exclude-table='m46_dosage_dosage*' \
    --exclude-table='m46_dosage_drug*' \
    --exclude-table='m56_alrgy_derivatives*' \
    --exclude-table='m56_analogue_cd*' \
    --exclude-table='m56_drug_class*' \
    --exclude-table='m56_drvalrgy_code*' \
    --exclude-table='m56_ex_analogue*' \
    --exclude-table='m56_ex_ed_ingredients*' \
    --exclude-table='m56_ex_ing_code*' \
    --exclude-table='m56_ex_ingrdt_main*' \
    --exclude-table='m56_prodrug_cd*' \
    --exclude-table='m56_usage_code*' \
    --exclude-table='m56_yj_drug_class*' \
    --exclude-table='mall_message_inf*' \
    --exclude-table='mall_renkei_inf*' \
    --exclude-table='material_mst*' \
    --exclude-table='monshin_inf*' \
    --exclude-table='odr_date_detail*' \
    --exclude-table='odr_date_inf*' \
    --exclude-table='odr_inf_cmt*' \
    --exclude-table='odr_inf_detail*' \
    --exclude-table='odr_inf*' \
    --exclude-table='online_confirmation_history*' \
    --exclude-table='online_confirmation*' \
    --exclude-table='online_consent*' \
    --exclude-table='path_conf*' \
    --exclude-table='payment_method_mst*' \
    --exclude-table='physical_average*' \
    --exclude-table='pi_image*' \
    --exclude-table='pi_inf_detail*' \
    --exclude-table='pi_inf*' \
    --exclude-table='pi_product_inf*' \
    --exclude-table='portal_m_import_**' \
    --exclude-table='post_code_mst*' \
    --exclude-table='priority_haihan_mst*' \
    --exclude-table='pt_alrgy_drug*' \
    --exclude-table='pt_alrgy_else*' \
    --exclude-table='pt_alrgy_food*' \
    --exclude-table='pt_byomei*' \
    --exclude-table='pt_cmt_inf*' \
    --exclude-table='pt_family_reki*' \
    --exclude-table='pt_family*' \
    --exclude-table='pt_grp_inf*' \
    --exclude-table='pt_grp_item*' \
    --exclude-table='pt_grp_name_mst*' \
    --exclude-table='pt_hoken_check*' \
    --exclude-table='pt_hoken_inf*' \
    --exclude-table='pt_hoken_pattern*' \
    --exclude-table='pt_hoken_scan*' \
    --exclude-table='pt_infection*' \
    --exclude-table='pt_jibai_doc*' \
    --exclude-table='pt_jibkar*' \
    --exclude-table='pt_kio_reki*' \
    --exclude-table='pt_kohi*' \
    --exclude-table='pt_kyusei*' \
    --exclude-table='pt_last_visit_date*' \
    --exclude-table='pt_memo*' \
    --exclude-table='pt_otc_drug*' \
    --exclude-table='pt_other_drug*' \
    --exclude-table='pt_pregnancy*' \
    --exclude-table='pt_pregnant*' \
    --exclude-table='pt_rousai_tenki*' \
    --exclude-table='pt_santei_conf*' \
    --exclude-table='pt_social_history*' \
    --exclude-table='pt_supple*' \
    --exclude-table='pt_tag*' \
    --exclude-table='raiin_cmt_inf*' \
    --exclude-table='raiin_filter_ka*' \
    --exclude-table='raiin_filter_kbn*' \
    --exclude-table='raiin_filter_mst*' \
    --exclude-table='raiin_filter_sort*' \
    --exclude-table='raiin_filter_state*' \
    --exclude-table='raiin_filter_treatment_department*' \
    --exclude-table='raiin_filter_user*' \
    --exclude-table='raiin_inf*' \
    --exclude-table='raiin_kbn_detail*' \
    --exclude-table='raiin_kbn_inf*' \
    --exclude-table='raiin_kbn_item*' \
    --exclude-table='raiin_kbn_koui*' \
    --exclude-table='raiin_kbn_mst*' \
    --exclude-table='raiin_kbn_yoyaku*' \
    --exclude-table='raiin_list_cmt*' \
    --exclude-table='raiin_list_detail*' \
    --exclude-table='raiin_list_doc*' \
    --exclude-table='raiin_list_file*' \
    --exclude-table='raiin_list_inf*' \
    --exclude-table='raiin_list_item*' \
    --exclude-table='raiin_list_koui*' \
    --exclude-table='raiin_list_mst*' \
    --exclude-table='raiin_list_tag*' \
    --exclude-table='raiin_status_mst*' \
    --exclude-table='rece_check_cmt*' \
    --exclude-table='rece_check_err*' \
    --exclude-table='rece_check_opt*' \
    --exclude-table='rece_cmt*' \
    --exclude-table='rece_futan_kbn*' \
    --exclude-table='rece_inf_edit*' \
    --exclude-table='rece_inf_jd*' \
    --exclude-table='rece_inf_pre_edit*' \
    --exclude-table='rece_inf*' \
    --exclude-table='rece_seikyu*' \
    --exclude-table='rece_status*' \
    --exclude-table='receden_cmt_select*' \
    --exclude-table='receden_hen_jiyuu*' \
    --exclude-table='receden_rireki_inf*' \
    --exclude-table='reception_list_master_status*' \
    --exclude-table='releasenote_read*' \
    --exclude-table='renkei_conf*' \
    --exclude-table='renkei_mst*' \
    --exclude-table='renkei_path_conf*' \
    --exclude-table='renkei_req*' \
    --exclude-table='renkei_template_mst*' \
    --exclude-table='renkei_timing_conf*' \
    --exclude-table='renkei_timing_mst*' \
    --exclude-table='roudou_mst*' \
    --exclude-table='rousai_gosei_mst*' \
    --exclude-table='rsv_day_comment*' \
    --exclude-table='rsv_frame_day_ptn*' \
    --exclude-table='rsv_frame_inf*' \
    --exclude-table='rsv_frame_mst*' \
    --exclude-table='rsv_frame_week_ptn*' \
    --exclude-table='rsv_frame_with*' \
    --exclude-table='rsv_grp_mst*' \
    --exclude-table='rsv_inf*' \
    --exclude-table='rsv_renkei_inf_tk*' \
    --exclude-table='rsv_renkei_inf*' \
    --exclude-table='rsvkrt_byomei*' \
    --exclude-table='rsvkrt_karte_img_inf*' \
    --exclude-table='rsvkrt_karte_inf*' \
    --exclude-table='rsvkrt_mst*' \
    --exclude-table='rsvkrt_odr_inf_cmt*' \
    --exclude-table='rsvkrt_odr_inf_detail*' \
    --exclude-table='rsvkrt_odr_inf*' \
    --exclude-table='santei_auto_order_detail*' \
    --exclude-table='santei_auto_order*' \
    --exclude-table='santei_cnt_check*' \
    --exclude-table='santei_grp_detail*' \
    --exclude-table='santei_grp_mst*' \
    --exclude-table='santei_inf_detail*' \
    --exclude-table='santei_inf*' \
    --exclude-table='schema_cmt_mst*' \
    --exclude-table='seikatureki_inf*' \
    --exclude-table='sentence_list*' \
    --exclude-table='session_inf*' \
    --exclude-table='set_byomei*' \
    --exclude-table='set_generation_mst*' \
    --exclude-table='set_karte_img_inf*' \
    --exclude-table='set_karte_inf*' \
    --exclude-table='set_kbn_mst*' \
    --exclude-table='set_mst*' \
    --exclude-table='set_odr_inf_cmt*' \
    --exclude-table='set_odr_inf_detail*' \
    --exclude-table='set_odr_inf*' \
    --exclude-table='sin_koui_count*' \
    --exclude-table='sin_koui_detail*' \
    --exclude-table='sin_koui*' \
    --exclude-table='sin_rp_inf*' \
    --exclude-table='sin_rp_no_inf*' \
    --exclude-table='single_dose_mst*' \
    --exclude-table='sinreki_filter_mst_detail*' \
    --exclude-table='sinreki_filter_mst_koui*' \
    --exclude-table='sinreki_filter_mst*' \
    --exclude-table='smartkarte_app_signalr_port*' \
    --exclude-table='sokatu_mst*' \
    --exclude-table='sta_conf*' \
    --exclude-table='sta_csv*' \
    --exclude-table='sta_grp*' \
    --exclude-table='sta_menu*' \
    --exclude-table='sta_mst*' \
    --exclude-table='summary_inf*' \
    --exclude-table='syobyo_keika*' \
    --exclude-table='syouki_inf*' \
    --exclude-table='syouki_kbn_mst*' \
    --exclude-table='system_change_log*' \
    --exclude-table='system_conf_item*' \
    --exclude-table='system_conf_menu*' \
    --exclude-table='system_conf*' \
    --exclude-table='system_generation_conf*' \
    --exclude-table='syuno_nyukin*' \
    --exclude-table='syuno_seikyu*' \
    --exclude-table='tag_grp_mst*' \
    --exclude-table='tekiou_byomei_mst_excluded*' \
    --exclude-table='tekiou_byomei_mst*' \
    --exclude-table='template_detail*' \
    --exclude-table='template_dsp_conf*' \
    --exclude-table='template_menu_detail*' \
    --exclude-table='template_menu_mst*' \
    --exclude-table='template_mst*' \
    --exclude-table='ten_mst_mother*' \
    --exclude-table='ten_mst_temp*' \
    --exclude-table='ten_mst*' \
    --exclude-table='"TENANT"' \
    --exclude-table='time_zone_conf*' \
    --exclude-table='time_zone_day_inf*' \
    --exclude-table='todo_grp_mst*' \
    --exclude-table='todo_inf*' \
    --exclude-table='todo_kbn_mst*' \
    --exclude-table='tokki_mst*' \
    --exclude-table='treatment_category*' \
    --exclude-table='treatment_menu*' \
    --exclude-table='uketuke_sbt_day_inf*' \
    --exclude-table='uketuke_sbt_mst*' \
    --exclude-table='unit_mst*' \
    --exclude-table='user_conf*' \
    --exclude-table='user_permission*' \
    --exclude-table='user_token*' \
    --exclude-table='wrk_sin_koui_detail_del*' \
    --exclude-table='wrk_sin_koui_detail*' \
    --exclude-table='wrk_sin_koui*' \
    --exclude-table='wrk_sin_rp_inf*' \
    --exclude-table='yakka_syusai_mst*' \
    --exclude-table='yoho_hosoku*' \
    --exclude-table='yoho_inf_mst*' \
    --exclude-table='yoho_mst*' \
    --exclude-table='yoho_replace_mst*' \
    --exclude-table='yoho_set_mst*' \
    --exclude-table='yoho_word_mst*' \
    --exclude-table='yousiki1_inf_detail*' \
    --exclude-table='yousiki1_inf*' \
    --exclude-table='z_doc_inf*' \
    --exclude-table='z_filing_inf*' \
    --exclude-table='z_kensa_inf_detail*' \
    --exclude-table='z_kensa_inf*' \
    --exclude-table='z_limit_cnt_list_inf*' \
    --exclude-table='z_limit_list_inf*' \
    --exclude-table='z_monshin_inf*' \
    --exclude-table='z_pt_alrgy_drug*' \
    --exclude-table='z_pt_alrgy_else*' \
    --exclude-table='z_pt_alrgy_food*' \
    --exclude-table='z_pt_cmt_inf*' \
    --exclude-table='z_pt_family_reki*' \
    --exclude-table='z_pt_family*' \
    --exclude-table='z_pt_grp_inf*' \
    --exclude-table='z_pt_hoken_check*' \
    --exclude-table='z_pt_hoken_inf*' \
    --exclude-table='z_pt_hoken_pattern*' \
    --exclude-table='z_pt_hoken_scan*' \
    --exclude-table='z_pt_inf*' \
    --exclude-table='z_pt_infection*' \
    --exclude-table='z_pt_jibkar*' \
    --exclude-table='z_pt_kio_reki*' \
    --exclude-table='z_pt_kohi*' \
    --exclude-table='z_pt_kyusei*' \
    --exclude-table='z_pt_memo*' \
    --exclude-table='z_pt_otc_drug*' \
    --exclude-table='z_pt_other_drug*' \
    --exclude-table='z_pt_pregnancy*' \
    --exclude-table='z_pt_rousai_tenki*' \
    --exclude-table='z_pt_santei_conf*' \
    --exclude-table='z_pt_supple*' \
    --exclude-table='z_pt_tag*' \
    --exclude-table='z_raiin_cmt_inf*' \
    --exclude-table='z_raiin_inf*' \
    --exclude-table='z_raiin_kbn_inf*' \
    --exclude-table='z_raiin_list_cmt*' \
    --exclude-table='z_raiin_list_tag*' \
    --exclude-table='z_rece_check_cmt*' \
    --exclude-table='z_rece_cmt*' \
    --exclude-table='z_rece_inf_edit*' \
    --exclude-table='z_rece_seikyu*' \
    --exclude-table='z_rece_status*' \
    --exclude-table='z_rsv_day_comment*' \
    --exclude-table='z_rsv_inf*' \
    --exclude-table='z_santei_inf_detail*' \
    --exclude-table='z_seikatureki_inf*' \
    --exclude-table='z_summary_inf*' \
    --exclude-table='z_syobyo_keika*' \
    --exclude-table='z_syouki_inf*' \
    --exclude-table='z_syuno_nyukin*' \
    --exclude-table='z_todo_inf*' \
    --exclude-table='z_uketuke_sbt_day_inf*' \
    --exclude-table='z_yousiki1_inf_detail*' \
    --exclude-table='z_yousiki1_inf*' \
    --exclude-table='karte_edition*' \
    --exclude-table='del_karte*' \
    --exclude-table='del_odr*' \
    --exclude-table='subscription*' \
    --exclude-table='subsription*' \
    --exclude-table='common_center_std_mst*' \
    --exclude-table='common_center_kensa_mst*' \
    --exclude-table='online_confirmation_detail*' \
    --exclude-table='raiin_filter_detail*' \
    --exclude-table='label*' \
    --exclude-table='online_agreed_consent*' \
    --exclude-table='online_agreed_houmon*' \
    --exclude-table='online_agreed_online*' \
    --exclude-table='online_agreed_prescription*' \
    --exclude-table='online_consignment*' \
    --exclude-table='z_ten_mst*' \
    --exclude-table='agent_setting*' \
    --exclude-table='pt_smoking_related*' \
    --exclude-table='pt_pregnancy_related*' \
    --exclude-table='custom_button_conf*' \
    --exclude-table='custom_button_param_mst*' \
    --exclude-table='renkei_output_data*' \
    --schema-only -f ./db/dev2/init.sql --if-exists
if [ $? -ne 0 ]; then
    echo "Failed to dump schema. Exiting..."
    exit 1
fi

# 指定されたスキーマ（DDL）のみをダンプする
# -t で指定したテーブルのみをダンプする
#PGPASSWORD=$DB_PASSWORD pg_dump -c --no-owner -x -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema-only --schema $DB_SCHEMA -f ./db/dev2/init_smartkarte_server.sql --if-exists
PGPASSWORD=$DB_PASSWORD pg_dump -c --no-owner -x -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME\
    -t 'accounting_form_mst*' \
    -t 'approval_inf*' \
    -t '"AuditLogs"' \
    -t '"AuditLogs_LogId_seq"' \
    -t 'audit_trail_log*' \
    -t 'audit_trail_log_detail*' \
    -t 'auto_santei_mst*' \
    -t 'backup_req*' \
    -t 'bui_odr_byomei_mst*' \
    -t 'bui_odr_item_byomei_mst*' \
    -t 'bui_odr_item_mst*' \
    -t 'bui_odr_mst*' \
    -t 'byomei_mst_aftercare*' \
    -t 'byomei_mst*' \
    -t 'byomei_set_generation_mst*' \
    -t 'byomei_set_mst*' \
    -t 'calc_log*' \
    -t 'calc_status*' \
    -t 'cmt_check_mst*' \
    -t 'cmt_kbn_mst*' \
    -t 'column_setting*' \
    -t 'common_kensa_center_mst' \
    -t 'common_kensa_mst' \
    -t 'common_jlac10_*' \
    -t 'container_mst*' \
    -t 'conversion_item_inf*' \
    -t 'def_hoken_no*' \
    -t 'densi_haihan_custom*' \
    -t 'densi_haihan_day*' \
    -t 'densi_haihan_karte*' \
    -t 'densi_haihan_month*' \
    -t 'densi_haihan_week*' \
    -t 'densi_hojyo*' \
    -t 'densi_houkatu_grp*' \
    -t 'densi_houkatu*' \
    -t 'densi_santei_kaisu*' \
    -t 'doc_category_mst*' \
    -t 'doc_comment_detail*' \
    -t 'doc_comment*' \
    -t 'doc_inf*' \
    -t 'dosage_mst*' \
    -t 'drug_day_limit*' \
    -t 'drug_inf*' \
    -t 'drug_unit_conv*' \
    -t 'eps_chk_detail*' \
    -t 'eps_chk*' \
    -t 'eps_dispensing_list*' \
    -t 'eps_dispensing_req*' \
    -t 'eps_dispensing*' \
    -t 'eps_prescription*' \
    -t 'eps_reference*' \
    -t 'eps_req*' \
    -t 'event_mst*' \
    -t 'except_hokensya*' \
    -t 'fco_api_keys' \
    -t 'filing_auto_imp*' \
    -t 'filing_category_mst*' \
    -t 'filing_inf*' \
    -t 'function_mst*' \
    -t 'gc_std_mst*' \
    -t 'hoken_mst_temp*' \
    -t 'hoken_mst*' \
    -t 'hokensya_mst*' \
    -t 'holiday_mst*' \
    -t 'ipn_kasan_exclude_item*' \
    -t 'ipn_kasan_exclude*' \
    -t 'ipn_kasan_mst*' \
    -t 'ipn_min_yakka_mst*' \
    -t 'ipn_name_mst*' \
    -t 'item_grp_mst*' \
    -t 'jihi_sbt_mst*' \
    -t 'job_mst*' \
    -t 'json_setting*' \
    -t 'ka_mst*' \
    -t 'kacode_mst*' \
    -t 'kacode_rece_yousiki*' \
    -t 'kacode_yousiki_mst*' \
    -t 'kaikei_detail*' \
    -t 'kaikei_inf*' \
    -t 'kantoku_mst*' \
    -t 'karte_filter_detail*' \
    -t 'karte_filter_mst*' \
    -t 'karte_img_inf*' \
    -t 'karte_inf*' \
    -t 'karte_kbn_mst*' \
    -t 'kensa_center_mst*' \
    -t 'kensa_center_partnerships' \
    -t 'kensa_cmt_mst*' \
    -t 'kensa_inf_detail*' \
    -t 'kensa_inf*' \
    -t 'kensa_irai_log*' \
    -t 'kensa_mst*' \
    -t 'kensa_result_log*' \
    -t 'kensa_set_detail*' \
    -t 'kensa_set*' \
    -t 'kensa_std_mst*' \
    -t 'kinki_mst*' \
    -t 'kogaku_limit*' \
    -t 'kohi_priority*' \
    -t 'koui_houkatu_mst*' \
    -t 'koui_kbn_mst*' \
    -t 'limit_cnt_list_inf*' \
    -t 'limit_list_inf*' \
    -t 'list_set_generation_mst*' \
    -t 'list_set_mst*' \
    -t 'lock_inf*' \
    -t 'lock_mst*' \
    -t 'mall_message_inf*' \
    -t 'mall_renkei_inf*' \
    -t 'material_mst*' \
    -t 'monshin_inf*' \
    -t 'odr_date_detail*' \
    -t 'odr_date_inf*' \
    -t 'odr_inf_cmt*' \
    -t 'odr_inf_detail*' \
    -t 'odr_inf*' \
    -t 'online_confirmation_history*' \
    -t 'online_confirmation*' \
    -t 'online_consent*' \
    -t 'path_conf*' \
    -t 'payment_method_mst*' \
    -t 'physical_average*' \
    -t 'pi_image*' \
    -t 'pi_inf_detail*' \
    -t 'pi_inf*' \
    -t 'pi_product_inf*' \
    -t 'post_code_mst*' \
    -t 'priority_haihan_mst*' \
    -t 'pt_alrgy_drug*' \
    -t 'pt_alrgy_else*' \
    -t 'pt_alrgy_food*' \
    -t 'pt_byomei*' \
    -t 'pt_cmt_inf*' \
    -t 'pt_family_reki*' \
    -t 'pt_family*' \
    -t 'pt_grp_inf*' \
    -t 'pt_grp_item*' \
    -t 'pt_grp_name_mst*' \
    -t 'pt_hoken_check*' \
    -t 'pt_hoken_inf*' \
    -t 'pt_hoken_pattern*' \
    -t 'pt_hoken_scan*' \
    -t 'pt_infection*' \
    -t 'pt_jibai_doc*' \
    -t 'pt_jibkar*' \
    -t 'pt_kio_reki*' \
    -t 'pt_kohi*' \
    -t 'pt_kyusei*' \
    -t 'pt_last_visit_date*' \
    -t 'pt_memo*' \
    -t 'pt_otc_drug*' \
    -t 'pt_other_drug*' \
    -t 'pt_pregnancy*' \
    -t 'pt_pregnant*' \
    -t 'pt_rousai_tenki*' \
    -t 'pt_santei_conf*' \
    -t 'pt_social_history*' \
    -t 'pt_supple*' \
    -t 'pt_tag*' \
    -t 'raiin_cmt_inf*' \
    -t 'raiin_filter_ka*' \
    -t 'raiin_filter_kbn*' \
    -t 'raiin_filter_mst*' \
    -t 'raiin_filter_sort*' \
    -t 'raiin_filter_state*' \
    -t 'raiin_filter_treatment_department*' \
    -t 'raiin_filter_user*' \
    -t 'raiin_inf*' \
    -t 'raiin_kbn_detail*' \
    -t 'raiin_kbn_inf*' \
    -t 'raiin_kbn_item*' \
    -t 'raiin_kbn_koui*' \
    -t 'raiin_kbn_mst*' \
    -t 'raiin_kbn_yoyaku*' \
    -t 'raiin_list_cmt*' \
    -t 'raiin_list_detail*' \
    -t 'raiin_list_doc*' \
    -t 'raiin_list_file*' \
    -t 'raiin_list_inf*' \
    -t 'raiin_list_item*' \
    -t 'raiin_list_koui*' \
    -t 'raiin_list_mst*' \
    -t 'raiin_list_tag*' \
    -t 'raiin_status_mst*' \
    -t 'rece_check_cmt*' \
    -t 'rece_check_err*' \
    -t 'rece_check_opt*' \
    -t 'rece_cmt*' \
    -t 'rece_futan_kbn*' \
    -t 'rece_inf_edit*' \
    -t 'rece_inf_jd*' \
    -t 'rece_inf_pre_edit*' \
    -t 'rece_inf*' \
    -t 'rece_seikyu*' \
    -t 'rece_status*' \
    -t 'receden_cmt_select*' \
    -t 'receden_hen_jiyuu*' \
    -t 'receden_rireki_inf*' \
    -t 'reception_list_master_status*' \
    -t 'releasenote_read*' \
    -t 'renkei_conf*' \
    -t 'renkei_mst*' \
    -t 'renkei_path_conf*' \
    -t 'renkei_req*' \
    -t 'renkei_template_mst*' \
    -t 'renkei_timing_conf*' \
    -t 'renkei_timing_mst*' \
    -t 'roudou_mst*' \
    -t 'rousai_gosei_mst*' \
    -t 'rsv_day_comment*' \
    -t 'rsv_frame_day_ptn*' \
    -t 'rsv_frame_inf*' \
    -t 'rsv_frame_mst*' \
    -t 'rsv_frame_week_ptn*' \
    -t 'rsv_frame_with*' \
    -t 'rsv_grp_mst*' \
    -t 'rsv_inf*' \
    -t 'rsv_renkei_inf_tk*' \
    -t 'rsv_renkei_inf*' \
    -t 'rsvkrt_byomei*' \
    -t 'rsvkrt_karte_img_inf*' \
    -t 'rsvkrt_karte_inf*' \
    -t 'rsvkrt_mst*' \
    -t 'rsvkrt_odr_inf_cmt*' \
    -t 'rsvkrt_odr_inf_detail*' \
    -t 'rsvkrt_odr_inf*' \
    -t 'santei_auto_order_detail*' \
    -t 'santei_auto_order*' \
    -t 'santei_cnt_check*' \
    -t 'santei_grp_detail*' \
    -t 'santei_grp_mst*' \
    -t 'santei_inf_detail*' \
    -t 'santei_inf*' \
    -t 'schema_cmt_mst*' \
    -t 'seikatureki_inf*' \
    -t 'sentence_list*' \
    -t 'session_inf*' \
    -t 'set_byomei*' \
    -t 'set_generation_mst*' \
    -t 'set_karte_img_inf*' \
    -t 'set_karte_inf*' \
    -t 'set_kbn_mst*' \
    -t 'set_mst*' \
    -t 'set_odr_inf_cmt*' \
    -t 'set_odr_inf_detail*' \
    -t 'set_odr_inf*' \
    -t 'sin_koui_count*' \
    -t 'sin_koui_detail*' \
    -t 'sin_koui*' \
    -t 'sin_rp_inf*' \
    -t 'sin_rp_no_inf*' \
    -t 'single_dose_mst*' \
    -t 'sinreki_filter_mst_detail*' \
    -t 'sinreki_filter_mst_koui*' \
    -t 'sinreki_filter_mst*' \
    -t 'smartkarte_app_signalr_port*' \
    -t 'sokatu_mst*' \
    -t 'sta_conf*' \
    -t 'sta_csv*' \
    -t 'sta_grp*' \
    -t 'sta_menu*' \
    -t 'sta_mst*' \
    -t 'summary_inf*' \
    -t 'syobyo_keika*' \
    -t 'syouki_inf*' \
    -t 'syouki_kbn_mst*' \
    -t 'system_change_log*' \
    -t 'system_conf_item*' \
    -t 'system_conf_menu*' \
    -t 'system_conf*' \
    -t 'system_generation_conf*' \
    -t 'syuno_nyukin*' \
    -t 'syuno_seikyu*' \
    -t 'tag_grp_mst*' \
    -t 'tekiou_byomei_mst_excluded*' \
    -t 'tekiou_byomei_mst*' \
    -t 'template_detail*' \
    -t 'template_dsp_conf*' \
    -t 'template_menu_detail*' \
    -t 'template_menu_mst*' \
    -t 'template_mst*' \
    -t 'ten_mst_mother*' \
    -t 'ten_mst_temp*' \
    -t 'ten_mst*' \
    -t '"TENANT"' \
    -t 'time_zone_conf*' \
    -t 'time_zone_day_inf*' \
    -t 'todo_grp_mst*' \
    -t 'todo_inf*' \
    -t 'todo_kbn_mst*' \
    -t 'tokki_mst*' \
    -t 'treatment_category*' \
    -t 'treatment_menu*' \
    -t 'uketuke_sbt_day_inf*' \
    -t 'uketuke_sbt_mst*' \
    -t 'unit_mst*' \
    -t 'user_conf*' \
    -t 'user_permission*' \
    -t 'user_token*' \
    -t 'wrk_sin_koui_detail_del*' \
    -t 'wrk_sin_koui_detail*' \
    -t 'wrk_sin_koui*' \
    -t 'wrk_sin_rp_inf*' \
    -t 'yakka_syusai_mst*' \
    -t 'yoho_hosoku*' \
    -t 'yoho_inf_mst*' \
    -t 'yoho_mst*' \
    -t 'yoho_replace_mst*' \
    -t 'yoho_set_mst*' \
    -t 'yoho_word_mst*' \
    -t 'z_doc_inf*' \
    -t 'z_filing_inf*' \
    -t 'z_kensa_inf_detail*' \
    -t 'z_kensa_inf*' \
    -t 'z_limit_cnt_list_inf*' \
    -t 'z_limit_list_inf*' \
    -t 'z_monshin_inf*' \
    -t 'z_pt_alrgy_drug*' \
    -t 'z_pt_alrgy_else*' \
    -t 'z_pt_alrgy_food*' \
    -t 'z_pt_cmt_inf*' \
    -t 'z_pt_family_reki*' \
    -t 'z_pt_family*' \
    -t 'z_pt_grp_inf*' \
    -t 'z_pt_hoken_check*' \
    -t 'z_pt_hoken_inf*' \
    -t 'z_pt_hoken_pattern*' \
    -t 'z_pt_hoken_scan*' \
    -t 'z_pt_inf*' \
    -t 'z_pt_infection*' \
    -t 'z_pt_jibkar*' \
    -t 'z_pt_kio_reki*' \
    -t 'z_pt_kohi*' \
    -t 'z_pt_kyusei*' \
    -t 'z_pt_memo*' \
    -t 'z_pt_otc_drug*' \
    -t 'z_pt_other_drug*' \
    -t 'z_pt_pregnancy*' \
    -t 'z_pt_rousai_tenki*' \
    -t 'z_pt_santei_conf*' \
    -t 'z_pt_supple*' \
    -t 'z_pt_tag*' \
    -t 'z_raiin_cmt_inf*' \
    -t 'z_raiin_inf*' \
    -t 'z_raiin_kbn_inf*' \
    -t 'z_raiin_list_cmt*' \
    -t 'z_raiin_list_tag*' \
    -t 'z_rece_check_cmt*' \
    -t 'z_rece_cmt*' \
    -t 'z_rece_inf_edit*' \
    -t 'z_rece_seikyu*' \
    -t 'z_rece_status*' \
    -t 'z_rsv_day_comment*' \
    -t 'z_rsv_inf*' \
    -t 'z_santei_inf_detail*' \
    -t 'z_seikatureki_inf*' \
    -t 'z_summary_inf*' \
    -t 'z_syobyo_keika*' \
    -t 'z_syouki_inf*' \
    -t 'z_syuno_nyukin*' \
    -t 'z_todo_inf*' \
    -t 'z_uketuke_sbt_day_inf*' \
    -t 'm01_kijyo_cmt*' \
    -t 'm01_kinki_cmt*' \
    -t 'm01_kinki*' \
    -t 'm10_day_limit*' \
    -t 'm12_food_alrgy_kbn*' \
    -t 'm12_food_alrgy*' \
    -t 'm14_age_check*' \
    -t 'm14_cmt_code*' \
    -t 'm28_drug_mst*' \
    -t 'm34_ar_code*' \
    -t 'm34_ar_discon_code*' \
    -t 'm34_ar_discon*' \
    -t 'm34_drug_info_main*' \
    -t 'm34_form_code*' \
    -t 'm34_indication_code*' \
    -t 'm34_interaction_pat_code*' \
    -t 'm34_interaction_pat*' \
    -t 'm34_precaution_code*' \
    -t 'm34_precautions*' \
    -t 'm34_property_code*' \
    -t 'm34_sar_symptom_code*' \
    -t 'm38_class_code*' \
    -t 'm38_ing_code*' \
    -t 'm38_ingredients*' \
    -t 'm38_major_div_code*' \
    -t 'm38_otc_form_code*' \
    -t 'm38_otc_main*' \
    -t 'm38_otc_maker_code*' \
    -t 'm41_supple_indexcode*' \
    -t 'm41_supple_indexdef*' \
    -t 'm41_supple_ingre*' \
    -t 'm42_contra_cmt*' \
    -t 'm42_contraindi_dis_bc*' \
    -t 'm42_contraindi_dis_class*' \
    -t 'm42_contraindi_dis_con*' \
    -t 'm42_contraindi_drug_main_ex*' \
    -t 'm46_dosage_dosage*' \
    -t 'm46_dosage_drug*' \
    -t 'm56_alrgy_derivatives*' \
    -t 'm56_analogue_cd*' \
    -t 'm56_drug_class*' \
    -t 'm56_drvalrgy_code*' \
    -t 'm56_ex_analogue*' \
    -t 'm56_ex_ed_ingredients*' \
    -t 'm56_ex_ing_code*' \
    -t 'm56_ex_ingrdt_main*' \
    -t 'm56_prodrug_cd*' \
    -t 'm56_usage_code*' \
    -t 'm56_yj_drug_class*' \
    -t 'yousiki1_inf_detail*' \
    -t 'yousiki1_inf*' \
    -t 'z_yousiki1_inf_detail*' \
    -t 'z_yousiki1_inf*' \
    -t 'karte_edition*' \
    -t 'del_karte*' \
    -t 'del_odr*' \
    -t 'subscription*' \
    -t 'common_center_std_mst*' \
    -t 'common_center_kensa_mst*' \
    -t 'online_confirmation_detail*' \
    -t 'raiin_filter_detail*' \
    -t 'label*' \
    -t 'online_agreed_consent*' \
    -t 'online_agreed_houmon*' \
    -t 'online_agreed_online*' \
    -t 'online_agreed_prescription*' \
    -t 'online_consignment*' \
    -t 'z_ten_mst*' \
    -t 'agent_setting*' \
    -t 'pt_smoking_related*' \
    -t 'pt_pregnancy_related*' \
    -t 'custom_button_conf*' \
    -t 'custom_button_param_mst*' \
    -t 'renkei_output_data*' \
    --schema-only -f ./db/dev2/init_smartkarte_server.sql --if-exists
if [ $? -ne 0 ]; then
    echo "Failed to dump schema. Exiting..."
    exit 1
fi

# portal_m_import_* table
PGPASSWORD=$DB_PASSWORD pg_dump -c --no-owner -x -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t 'portal_m_import_*' --schema-only -f ./db/dev2/init_import_table.sql --if-exists
if [ $? -ne 0 ]; then
    echo "Failed to dump schema. Exiting..."
    exit 1
fi

# portal_m_city のダンプ
echo "dumping portal_m_city..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t "portal_m_city" --data-only -f ./db/dev2/master_data_portal_m_city.sql
if [ $? -ne 0 ]; then
    echo "Failed to dump portal_m_city data. Exiting..."
    exit 1
fi

# portal_m_station のダンプ
echo "dumping portal_m_station..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t "portal_m_station" --data-only -f ./db/dev2/master_data_portal_m_station.sql
if [ $? -ne 0 ]; then
    echo "Failed to dump portal_m_station data. Exiting..."
    exit 1
fi

# m_template_mailテーブルのダンプ
# m_template_mailテーブルはCOPYだとメール本文の修正が面倒なため、INSERT文でダンプする
echo "dumping remaining tables..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t 'm_template_mail' --data-only --column-inserts --inserts -f ./db/dev2/master_data_m_template_mail.sql

# 既存のレコードの VALUES の間に改行を挿入
sed -i '' 's/),(/),\n(/g' ./db/dev2/master_data_m_template_mail.sql

# カラムリストのカンマを残したまま改行 + インデントする
sed -i '' 's/INSERT INTO \([^()]*\) (\([^)]*\))/INSERT INTO \1 (\n    \2\n)/' ./db/dev2/master_data_m_template_mail.sql
sed -i '' 's/, /,\n    /g' ./db/dev2/master_data_m_template_mail.sql

if [ $? -ne 0 ]; then
    echo "Failed to dump other data. Exiting..."
    exit 1
fi

# event_mstテーブルのダンプ
echo "dumping remaining tables..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
    -t 'event_mst*' \
    --data-only --column-inserts --inserts -f ./db/dev2/master_data_event_mst.sql
if [ $? -ne 0 ]; then
    echo "Failed to dump other data. Exiting..."
    exit 1
fi
# バージョン情報行を削除
sed -i '' '/^-- Dumped by pg_dump version/d' ./db/dev2/master_data_event_mst.sql

# event_mstのダンプデータをevent_id順にソート
echo "Sorting event_mst by event_id..."
# 一時ファイルを作成
cp ./db/dev2/master_data_event_mst.sql ./db/dev2/master_data_event_mst_temp.sql
# ヘッダー部分（最初のINSERT文の前まで）を抽出
sed -n '1,/^INSERT INTO/p' ./db/dev2/master_data_event_mst_temp.sql | head -n -1 > ./db/dev2/master_data_event_mst_header.sql
# INSERT文を抽出してソート（event_idフィールドでソート）
grep "^INSERT INTO" ./db/dev2/master_data_event_mst_temp.sql | sort -t "," -k 2 -n > ./db/dev2/master_data_event_mst_inserts.sql
# ヘッダーとソートしたINSERT文をマージ
cat ./db/dev2/master_data_event_mst_header.sql ./db/dev2/master_data_event_mst_inserts.sql > ./db/dev2/master_data_event_mst.sql
# 一時ファイルを削除
rm ./db/dev2/master_data_event_mst_temp.sql ./db/dev2/master_data_event_mst_header.sql ./db/dev2/master_data_event_mst_inserts.sql

# その他のテーブルのダンプ
echo "dumping remaining tables..."
PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
    -t 'fax_number*' \
    -t 'agree_log*' \
    -t 'hp_inf*' \
    -t 'portal_*' \
    -t 'opesys_*' \
    -t 'm_*' \
    -t 'staff_role*' \
    -t 'pt_inf*' \
    -t 'pt_hoken_inf*' \
    -t 'task_status*' \
    -t 'task_category*' \
    -t 'task*' \
    -t 'task_file*' \
    -t 'task_history*' \
    -t 'task_comment*' \
    -t 'task_comment_file*' \
    -t 'schema_image*' \
    -t 'treatment_department*' \
    -t 'calendar*' \
    -t 'calendar_treatment*' \
    -t 'calendar_basic_setting*' \
    -t 'exam_time_slot*' \
    -t 'reserve*' \
    -t 'reserve_detail*' \
    -t 'reserve_detail_history*' \
    -t 'survey*' \
    -t 'survey_*' \
    -t 'user_mst*' \
    -t 'job_mst*' \
    -t 'job_mst_*' \
    -t 'signup_user*' \
    -t 'user_permission*' \
    -t 'permission_mst*' \
    -t 'function_mst*' \
    -t 'client_certificate*' \
    -t 'portal_hospital_staff*' \
    -t 'portal_customer_payment*' \
    -t 'portal_customer_delivery_address*' \
    -t 'portal_customer_file*' \
    -t 'operator_group_permission*' \
    -t 'hp_permission*' \
    -t 'hp_fincode_info*' \
    -t 'freee_*' \
    -t 'm_application_feature*' \
    -t 'pharmacy_reserve*' \
    -t 'pharmacy_reserve_detail*' \
    -t 'pharmacy_desired_date*' \
    -t 'pharmacy_patient_file*' \
    -t 'payment_pharmacy_detail' \
    -t 'payment_pharmacy_detail_history' \
    -t 'pharmacy_reserve_status_history*' \
    -t 'pharmacy_delivery_address*' \
    -t "pharmacy_delivery_history" \
    -t 'prescription_reception*' \
    -t 'prescription_image*' \
    -t 'meeting*' \
    -t 'rece_inf' \
    -t 'pt_byomei' \
    -t 'pt_hoken_pattern' \
    -t 'pt_hoken_check' \
    -t 'pt_rousai_tenki' \
    -t 'kaikei_inf' \
    -t 'sin_koui_count' \
    -t 'raiin_inf' \
    -t 'system_conf' \
    -t 'system_conf_menu' \
    -t 'odr_inf' \
    -t 'odr_inf_detail' \
    -t 'syuno_seikyu' \
    -t 'rece_check_err' \
    -t 'sin_rp_inf' \
    -t 'sin_koui' \
    -t 'sin_koui_detail' \
    -t 'syobyo_keika' \
    -t 'pt_grp_item' \
    --exclude-table='m_template_mail' \
    --exclude-table='portal_m_city' \
    --exclude-table='portal_m_station' \
    --exclude-table='portal_m_import_*' \
    --data-only -f ./db/dev2/master_data_other.sql
if [ $? -ne 0 ]; then
    echo "Failed to dump other data. Exiting..."
    exit 1
fi

# local_gmohtユーザー（ロール）で実行するとエラーになる行をコメントアウト
sed -i '' 's/DROP SCHEMA IF EXISTS local_gmoht;/--DROP SCHEMA IF EXISTS local_gmoht;/g' ./db/dev2/init.sql
sed -i '' 's/CREATE SCHEMA local_gmoht;/--CREATE SCHEMA local_gmoht;/g' ./db/dev2/init.sql

sed -i '' 's/DROP SCHEMA IF EXISTS local_gmoht;/--DROP SCHEMA IF EXISTS local_gmoht;/g' ./db/dev2/init_smartkarte_server.sql
sed -i '' 's/CREATE SCHEMA local_gmoht;/--CREATE SCHEMA local_gmoht;/g' ./db/dev2/init_smartkarte_server.sql

# DROP TABLE と DROP SEQUENCE 文に CASCADE キーワードを追加
sed -i '' 's/DROP TABLE \(.*\);/DROP TABLE \1 CASCADE;/g' ./db/dev2/init.sql
sed -i '' 's/DROP SEQUENCE \(.*\);/DROP SEQUENCE \1 CASCADE;/g' ./db/dev2/init.sql

echo "Added CASCADE to DROP TABLE and DROP SEQUENCE statements in init.sql."

sed -i '' 's/DROP TABLE \(.*\);/DROP TABLE \1 CASCADE;/g' ./db/dev2/init_smartkarte_server.sql
sed -i '' 's/DROP SEQUENCE \(.*\);/DROP SEQUENCE \1 CASCADE;/g' ./db/dev2/init_smartkarte_server.sql

echo "Added CASCADE to DROP TABLE and DROP SEQUENCE statements in init_smartkarte_server.sql."

# local_gmohtユーザー（ロール）で実行するとエラーになる行をコメントアウト
sed -i '' 's/DROP SCHEMA IF EXISTS local_gmoht;/--DROP SCHEMA IF EXISTS local_gmoht;/g' ./db/dev2/init_import_table.sql
sed -i '' 's/CREATE SCHEMA local_gmoht;/--CREATE SCHEMA local_gmoht;/g' ./db/dev2/init_import_table.sql

# DROP TABLE と DROP SEQUENCE 文に CASCADE キーワードを追加
sed -i '' 's/DROP TABLE \(.*\);/DROP TABLE \1 CASCADE;/g' ./db/dev2/init_import_table.sql
sed -i '' 's/DROP SEQUENCE \(.*\);/DROP SEQUENCE \1 CASCADE;/g' ./db/dev2/init_import_table.sql

echo "Added CASCADE to DROP TABLE and DROP SEQUENCE statements in init_import_table.sql."

# pg_dumpするとテーブル名の前にスキーマ名がつくので、取り除く
sed -i "" "s/local_gmoht\.//g" ./db/dev2/init.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/init_smartkarte_server.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/init_import_table.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/master_data_other.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/master_data_event_mst.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/master_data_portal_m_city.sql
sed -i "" "s/local_gmoht\.//g" ./db/dev2/master_data_portal_m_station.sql

# SELECT pg_catalog.set_config('search_path', 'local_gmoht', false); をコメントアウトする
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/init.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/init_smartkarte_server.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/init_import_table.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/master_data_other.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/master_data_event_mst.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/master_data_portal_m_city.sql
sed -i "" "s/SELECT pg_catalog\.set_config/-- SELECT pg_catalog\.set_config/g" ./db/dev2/master_data_portal_m_station.sql

# バージョン情報行を削除
sed -i '' '/^-- Dumped by pg_dump version/d' ./db/dev2/*.sql

echo "Database dump completed successfully."

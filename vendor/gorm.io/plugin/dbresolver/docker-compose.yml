version: '3'

services:
  mysql1:
    image: 'mysql/mysql-server:latest'
    ports:
      - 9911:3306
    environment:
      - MYSQL_DATABASE=gorm
      - MYSQL_USER=gorm
      - MYSQL_PASSWORD=gorm
      - MYSQL_RANDOM_ROOT_PASSWORD="yes"
  mysql2:
    image: 'mysql/mysql-server:latest'
    ports:
      - 9912:3306
    environment:
      - MYSQL_DATABASE=gorm
      - MYSQL_USER=gorm
      - MYSQL_PASSWORD=gorm
      - MYSQL_RANDOM_ROOT_PASSWORD="yes"
  mysql3:
    image: 'mysql/mysql-server:latest'
    ports:
      - 9913:3306
    environment:
      - MYSQL_DATABASE=gorm
      - MYSQL_USER=gorm
      - MYSQL_PASSWORD=gorm
      - MYSQL_RANDOM_ROOT_PASSWORD="yes"
  mysql4:
    image: 'mysql/mysql-server:latest'
    ports:
      - 9914:3306
    environment:
      - MYSQL_DATABASE=gorm
      - MYSQL_USER=gorm
      - MYSQL_PASSWORD=gorm
      - MYSQL_RANDOM_ROOT_PASSWORD="yes"

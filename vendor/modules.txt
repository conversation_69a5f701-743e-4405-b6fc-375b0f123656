# cloud.google.com/go v0.112.0
## explicit; go 1.19
cloud.google.com/go
# cloud.google.com/go/compute v1.24.0
## explicit; go 1.19
cloud.google.com/go/compute/internal
# cloud.google.com/go/compute/metadata v0.2.3
## explicit; go 1.19
cloud.google.com/go/compute/metadata
# cloud.google.com/go/longrunning v0.5.5
## explicit; go 1.19
cloud.google.com/go/longrunning
cloud.google.com/go/longrunning/autogen
cloud.google.com/go/longrunning/autogen/longrunningpb
# cloud.google.com/go/vision v1.2.0
## explicit; go 1.16
cloud.google.com/go/vision/apiv1
cloud.google.com/go/vision/internal
# cloud.google.com/go/vision/v2 v2.8.0
## explicit; go 1.19
cloud.google.com/go/vision/v2/apiv1/visionpb
# dario.cat/mergo v1.0.0
## explicit; go 1.13
dario.cat/mergo
# github.com/99designs/gqlgen v0.17.43
## explicit; go 1.18
github.com/99designs/gqlgen
github.com/99designs/gqlgen/api
github.com/99designs/gqlgen/codegen
github.com/99designs/gqlgen/codegen/config
github.com/99designs/gqlgen/codegen/templates
github.com/99designs/gqlgen/complexity
github.com/99designs/gqlgen/graphql
github.com/99designs/gqlgen/graphql/errcode
github.com/99designs/gqlgen/graphql/executor
github.com/99designs/gqlgen/graphql/handler
github.com/99designs/gqlgen/graphql/handler/extension
github.com/99designs/gqlgen/graphql/handler/lru
github.com/99designs/gqlgen/graphql/handler/transport
github.com/99designs/gqlgen/graphql/introspection
github.com/99designs/gqlgen/graphql/playground
github.com/99designs/gqlgen/internal/code
github.com/99designs/gqlgen/internal/imports
github.com/99designs/gqlgen/internal/rewrite
github.com/99designs/gqlgen/plugin
github.com/99designs/gqlgen/plugin/federation
github.com/99designs/gqlgen/plugin/federation/fieldset
github.com/99designs/gqlgen/plugin/modelgen
github.com/99designs/gqlgen/plugin/resolvergen
github.com/99designs/gqlgen/plugin/servergen
# github.com/PuerkitoBio/goquery v1.9.0
## explicit; go 1.18
github.com/PuerkitoBio/goquery
# github.com/agnivade/levenshtein v1.1.1
## explicit; go 1.13
github.com/agnivade/levenshtein
# github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a
## explicit
github.com/alicebob/gopher-json
# github.com/alicebob/miniredis/v2 v2.33.0
## explicit; go 1.17
github.com/alicebob/miniredis/v2
github.com/alicebob/miniredis/v2/fpconv
github.com/alicebob/miniredis/v2/geohash
github.com/alicebob/miniredis/v2/hyperloglog
github.com/alicebob/miniredis/v2/metro
github.com/alicebob/miniredis/v2/proto
github.com/alicebob/miniredis/v2/server
github.com/alicebob/miniredis/v2/size
# github.com/andybalholm/cascadia v1.3.2
## explicit; go 1.16
github.com/andybalholm/cascadia
# github.com/aws/aws-lambda-go v1.41.0
## explicit; go 1.18
github.com/aws/aws-lambda-go/events
github.com/aws/aws-lambda-go/lambda
github.com/aws/aws-lambda-go/lambda/handlertrace
github.com/aws/aws-lambda-go/lambda/messages
github.com/aws/aws-lambda-go/lambdacontext
# github.com/aws/aws-sdk-go v1.54.19
## explicit; go 1.19
github.com/aws/aws-sdk-go/aws
github.com/aws/aws-sdk-go/aws/auth/bearer
github.com/aws/aws-sdk-go/aws/awserr
github.com/aws/aws-sdk-go/aws/awsutil
github.com/aws/aws-sdk-go/aws/client
github.com/aws/aws-sdk-go/aws/client/metadata
github.com/aws/aws-sdk-go/aws/corehandlers
github.com/aws/aws-sdk-go/aws/credentials
github.com/aws/aws-sdk-go/aws/credentials/ec2rolecreds
github.com/aws/aws-sdk-go/aws/credentials/endpointcreds
github.com/aws/aws-sdk-go/aws/credentials/processcreds
github.com/aws/aws-sdk-go/aws/credentials/ssocreds
github.com/aws/aws-sdk-go/aws/credentials/stscreds
github.com/aws/aws-sdk-go/aws/csm
github.com/aws/aws-sdk-go/aws/defaults
github.com/aws/aws-sdk-go/aws/ec2metadata
github.com/aws/aws-sdk-go/aws/endpoints
github.com/aws/aws-sdk-go/aws/request
github.com/aws/aws-sdk-go/aws/session
github.com/aws/aws-sdk-go/aws/signer/v4
github.com/aws/aws-sdk-go/internal/context
github.com/aws/aws-sdk-go/internal/encoding/gzip
github.com/aws/aws-sdk-go/internal/ini
github.com/aws/aws-sdk-go/internal/sdkio
github.com/aws/aws-sdk-go/internal/sdkmath
github.com/aws/aws-sdk-go/internal/sdkrand
github.com/aws/aws-sdk-go/internal/sdkuri
github.com/aws/aws-sdk-go/internal/shareddefaults
github.com/aws/aws-sdk-go/internal/strings
github.com/aws/aws-sdk-go/internal/sync/singleflight
github.com/aws/aws-sdk-go/private/protocol
github.com/aws/aws-sdk-go/private/protocol/json/jsonutil
github.com/aws/aws-sdk-go/private/protocol/jsonrpc
github.com/aws/aws-sdk-go/private/protocol/query
github.com/aws/aws-sdk-go/private/protocol/query/queryutil
github.com/aws/aws-sdk-go/private/protocol/rest
github.com/aws/aws-sdk-go/private/protocol/restjson
github.com/aws/aws-sdk-go/private/protocol/xml/xmlutil
github.com/aws/aws-sdk-go/service/cloudwatch
github.com/aws/aws-sdk-go/service/sso
github.com/aws/aws-sdk-go/service/sso/ssoiface
github.com/aws/aws-sdk-go/service/ssooidc
github.com/aws/aws-sdk-go/service/sts
github.com/aws/aws-sdk-go/service/sts/stsiface
# github.com/aws/aws-sdk-go-v2 v1.24.0
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/aws
github.com/aws/aws-sdk-go-v2/aws/arn
github.com/aws/aws-sdk-go-v2/aws/defaults
github.com/aws/aws-sdk-go-v2/aws/middleware
github.com/aws/aws-sdk-go-v2/aws/middleware/private/metrics
github.com/aws/aws-sdk-go-v2/aws/protocol/query
github.com/aws/aws-sdk-go-v2/aws/protocol/restjson
github.com/aws/aws-sdk-go-v2/aws/protocol/xml
github.com/aws/aws-sdk-go-v2/aws/ratelimit
github.com/aws/aws-sdk-go-v2/aws/retry
github.com/aws/aws-sdk-go-v2/aws/signer/internal/v4
github.com/aws/aws-sdk-go-v2/aws/signer/v4
github.com/aws/aws-sdk-go-v2/aws/transport/http
github.com/aws/aws-sdk-go-v2/internal/auth
github.com/aws/aws-sdk-go-v2/internal/auth/smithy
github.com/aws/aws-sdk-go-v2/internal/awsutil
github.com/aws/aws-sdk-go-v2/internal/context
github.com/aws/aws-sdk-go-v2/internal/endpoints
github.com/aws/aws-sdk-go-v2/internal/endpoints/awsrulesfn
github.com/aws/aws-sdk-go-v2/internal/rand
github.com/aws/aws-sdk-go-v2/internal/sdk
github.com/aws/aws-sdk-go-v2/internal/sdkio
github.com/aws/aws-sdk-go-v2/internal/shareddefaults
github.com/aws/aws-sdk-go-v2/internal/strings
github.com/aws/aws-sdk-go-v2/internal/sync/singleflight
github.com/aws/aws-sdk-go-v2/internal/timeconv
# github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream v1.5.3
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream
github.com/aws/aws-sdk-go-v2/aws/protocol/eventstream/eventstreamapi
# github.com/aws/aws-sdk-go-v2/config v1.25.10
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/config
# github.com/aws/aws-sdk-go-v2/credentials v1.16.8
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/credentials
github.com/aws/aws-sdk-go-v2/credentials/ec2rolecreds
github.com/aws/aws-sdk-go-v2/credentials/endpointcreds
github.com/aws/aws-sdk-go-v2/credentials/endpointcreds/internal/client
github.com/aws/aws-sdk-go-v2/credentials/processcreds
github.com/aws/aws-sdk-go-v2/credentials/ssocreds
github.com/aws/aws-sdk-go-v2/credentials/stscreds
# github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.14.8
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/feature/ec2/imds
github.com/aws/aws-sdk-go-v2/feature/ec2/imds/internal/config
# github.com/aws/aws-sdk-go-v2/feature/s3/manager v1.15.3
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/feature/s3/manager
# github.com/aws/aws-sdk-go-v2/internal/configsources v1.2.9
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/internal/configsources
# github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.5.9
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/internal/endpoints/v2
# github.com/aws/aws-sdk-go-v2/internal/ini v1.7.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/internal/ini
# github.com/aws/aws-sdk-go-v2/internal/v4a v1.2.7
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/internal/v4a
github.com/aws/aws-sdk-go-v2/internal/v4a/internal/crypto
github.com/aws/aws-sdk-go-v2/internal/v4a/internal/v4
# github.com/aws/aws-sdk-go-v2/service/bedrockruntime v1.5.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/bedrockruntime
github.com/aws/aws-sdk-go-v2/service/bedrockruntime/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/bedrockruntime/types
# github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings v1.20.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings
github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/chimesdkmeetings/types
# github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider v1.32.0
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider
github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types
# github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.10.3
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding
# github.com/aws/aws-sdk-go-v2/service/internal/checksum v1.2.7
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/internal/checksum
# github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.10.7
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/internal/presigned-url
# github.com/aws/aws-sdk-go-v2/service/internal/s3shared v1.16.7
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/internal/s3shared
github.com/aws/aws-sdk-go-v2/service/internal/s3shared/arn
github.com/aws/aws-sdk-go-v2/service/internal/s3shared/config
# github.com/aws/aws-sdk-go-v2/service/lambda v1.43.0
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/lambda
github.com/aws/aws-sdk-go-v2/service/lambda/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/lambda/types
# github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2 v1.7.5
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2
github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/pinpointsmsvoicev2/types
# github.com/aws/aws-sdk-go-v2/service/s3 v1.47.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/s3
github.com/aws/aws-sdk-go-v2/service/s3/internal/arn
github.com/aws/aws-sdk-go-v2/service/s3/internal/customizations
github.com/aws/aws-sdk-go-v2/service/s3/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/s3/types
# github.com/aws/aws-sdk-go-v2/service/sesv2 v1.24.2
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/sesv2
github.com/aws/aws-sdk-go-v2/service/sesv2/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/sesv2/types
# github.com/aws/aws-sdk-go-v2/service/sqs v1.29.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/sqs
github.com/aws/aws-sdk-go-v2/service/sqs/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/sqs/types
# github.com/aws/aws-sdk-go-v2/service/sso v1.18.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/sso
github.com/aws/aws-sdk-go-v2/service/sso/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/sso/types
# github.com/aws/aws-sdk-go-v2/service/ssooidc v1.21.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/ssooidc
github.com/aws/aws-sdk-go-v2/service/ssooidc/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/ssooidc/types
# github.com/aws/aws-sdk-go-v2/service/sts v1.26.1
## explicit; go 1.19
github.com/aws/aws-sdk-go-v2/service/sts
github.com/aws/aws-sdk-go-v2/service/sts/internal/endpoints
github.com/aws/aws-sdk-go-v2/service/sts/types
# github.com/aws/smithy-go v1.20.2
## explicit; go 1.20
github.com/aws/smithy-go
github.com/aws/smithy-go/auth
github.com/aws/smithy-go/auth/bearer
github.com/aws/smithy-go/container/private/cache
github.com/aws/smithy-go/container/private/cache/lru
github.com/aws/smithy-go/context
github.com/aws/smithy-go/document
github.com/aws/smithy-go/encoding
github.com/aws/smithy-go/encoding/httpbinding
github.com/aws/smithy-go/encoding/json
github.com/aws/smithy-go/encoding/xml
github.com/aws/smithy-go/endpoints
github.com/aws/smithy-go/endpoints/private/rulesfn
github.com/aws/smithy-go/internal/sync/singleflight
github.com/aws/smithy-go/io
github.com/aws/smithy-go/logging
github.com/aws/smithy-go/middleware
github.com/aws/smithy-go/ptr
github.com/aws/smithy-go/rand
github.com/aws/smithy-go/sync
github.com/aws/smithy-go/time
github.com/aws/smithy-go/transport/http
github.com/aws/smithy-go/transport/http/internal/io
github.com/aws/smithy-go/waiter
# github.com/bizleap-healthcare/denkaru-codes v0.0.0-20250528014449-117fffa92021
## explicit; go 1.22
github.com/bizleap-healthcare/denkaru-codes/definitions
# github.com/cespare/xxhash/v2 v2.2.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cockroachdb/errors v1.11.3
## explicit; go 1.19
github.com/cockroachdb/errors
github.com/cockroachdb/errors/assert
github.com/cockroachdb/errors/barriers
github.com/cockroachdb/errors/contexttags
github.com/cockroachdb/errors/domains
github.com/cockroachdb/errors/errbase
github.com/cockroachdb/errors/errorspb
github.com/cockroachdb/errors/errutil
github.com/cockroachdb/errors/hintdetail
github.com/cockroachdb/errors/issuelink
github.com/cockroachdb/errors/join
github.com/cockroachdb/errors/markers
github.com/cockroachdb/errors/report
github.com/cockroachdb/errors/safedetails
github.com/cockroachdb/errors/secondary
github.com/cockroachdb/errors/stdstrings
github.com/cockroachdb/errors/telemetrykeys
github.com/cockroachdb/errors/withstack
# github.com/cockroachdb/logtags v0.0.0-20230118201751-21c54148d20b
## explicit; go 1.16
github.com/cockroachdb/logtags
# github.com/cockroachdb/redact v1.1.5
## explicit; go 1.14
github.com/cockroachdb/redact
github.com/cockroachdb/redact/builder
github.com/cockroachdb/redact/interfaces
github.com/cockroachdb/redact/internal/buffer
github.com/cockroachdb/redact/internal/escape
github.com/cockroachdb/redact/internal/fmtforward
github.com/cockroachdb/redact/internal/markers
github.com/cockroachdb/redact/internal/redact
github.com/cockroachdb/redact/internal/rfmt
github.com/cockroachdb/redact/internal/rfmt/fmtsort
# github.com/cpuguy83/go-md2man/v2 v2.0.3
## explicit; go 1.11
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/decred/dcrd/dcrec/secp256k1/v4 v4.2.0
## explicit; go 1.17
github.com/decred/dcrd/dcrec/secp256k1/v4
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/felixge/httpsnoop v1.0.4
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/fergusstrange/embedded-postgres v1.26.0
## explicit; go 1.18
github.com/fergusstrange/embedded-postgres
# github.com/gabriel-vasile/mimetype v1.4.2
## explicit; go 1.20
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/getsentry/sentry-go v0.27.0
## explicit; go 1.18
github.com/getsentry/sentry-go
github.com/getsentry/sentry-go/echo
github.com/getsentry/sentry-go/internal/debug
github.com/getsentry/sentry-go/internal/otel/baggage
github.com/getsentry/sentry-go/internal/otel/baggage/internal/baggage
github.com/getsentry/sentry-go/internal/ratelimit
github.com/getsentry/sentry-go/internal/traceparser
# github.com/go-co-op/gocron/v2 v2.2.5
## explicit; go 1.20
github.com/go-co-op/gocron/v2
# github.com/go-logr/logr v1.4.1
## explicit; go 1.18
github.com/go-logr/logr
github.com/go-logr/logr/funcr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.16.0
## explicit; go 1.18
github.com/go-playground/validator/v10
# github.com/go-redis/redis v6.15.9+incompatible
## explicit
github.com/go-redis/redis
github.com/go-redis/redis/internal
github.com/go-redis/redis/internal/consistenthash
github.com/go-redis/redis/internal/hashtag
github.com/go-redis/redis/internal/pool
github.com/go-redis/redis/internal/proto
github.com/go-redis/redis/internal/util
# github.com/go-sql-driver/mysql v1.7.1
## explicit; go 1.13
github.com/go-sql-driver/mysql
# github.com/goccy/go-json v0.10.2
## explicit; go 1.12
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/gogoproto
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/protoc-gen-gogo/descriptor
github.com/gogo/protobuf/sortkeys
github.com/gogo/protobuf/types
# github.com/golang-jwt/jwt v3.2.2+incompatible
## explicit
github.com/golang-jwt/jwt
# github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da
## explicit
github.com/golang/groupcache/lru
# github.com/golang/protobuf v1.5.3
## explicit; go 1.9
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
# github.com/google/go-cmp v0.7.0
## explicit; go 1.21
github.com/google/go-cmp/cmp
github.com/google/go-cmp/cmp/cmpopts
github.com/google/go-cmp/cmp/internal/diff
github.com/google/go-cmp/cmp/internal/flags
github.com/google/go-cmp/cmp/internal/function
github.com/google/go-cmp/cmp/internal/value
# github.com/google/s2a-go v0.1.7
## explicit; go 1.19
github.com/google/s2a-go
github.com/google/s2a-go/fallback
github.com/google/s2a-go/internal/authinfo
github.com/google/s2a-go/internal/handshaker
github.com/google/s2a-go/internal/handshaker/service
github.com/google/s2a-go/internal/proto/common_go_proto
github.com/google/s2a-go/internal/proto/s2a_context_go_proto
github.com/google/s2a-go/internal/proto/s2a_go_proto
github.com/google/s2a-go/internal/proto/v2/common_go_proto
github.com/google/s2a-go/internal/proto/v2/s2a_context_go_proto
github.com/google/s2a-go/internal/proto/v2/s2a_go_proto
github.com/google/s2a-go/internal/record
github.com/google/s2a-go/internal/record/internal/aeadcrypter
github.com/google/s2a-go/internal/record/internal/halfconn
github.com/google/s2a-go/internal/tokenmanager
github.com/google/s2a-go/internal/v2
github.com/google/s2a-go/internal/v2/certverifier
github.com/google/s2a-go/internal/v2/remotesigner
github.com/google/s2a-go/internal/v2/tlsconfigstore
github.com/google/s2a-go/retry
github.com/google/s2a-go/stream
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/google/wire v0.5.0
## explicit; go 1.12
github.com/google/wire
# github.com/googleapis/enterprise-certificate-proxy v0.3.2
## explicit; go 1.19
github.com/googleapis/enterprise-certificate-proxy/client
github.com/googleapis/enterprise-certificate-proxy/client/util
# github.com/googleapis/gax-go/v2 v2.12.1
## explicit; go 1.19
github.com/googleapis/gax-go/v2
github.com/googleapis/gax-go/v2/apierror
github.com/googleapis/gax-go/v2/apierror/internal/proto
github.com/googleapis/gax-go/v2/callctx
github.com/googleapis/gax-go/v2/internal
# github.com/gorilla/websocket v1.5.1
## explicit; go 1.20
github.com/gorilla/websocket
# github.com/graph-gophers/dataloader/v7 v7.1.0
## explicit; go 1.18
github.com/graph-gophers/dataloader/v7
# github.com/hashicorp/golang-lru/v2 v2.0.3
## explicit; go 1.18
github.com/hashicorp/golang-lru/v2
github.com/hashicorp/golang-lru/v2/simplelru
# github.com/hasura/go-graphql-client v0.10.1
## explicit; go 1.20
github.com/hasura/go-graphql-client
github.com/hasura/go-graphql-client/ident
github.com/hasura/go-graphql-client/pkg/jsonutil
# github.com/holiday-jp/holiday_jp-go v0.0.0-20220125203534-53124b4cc19c
## explicit; go 1.15
github.com/holiday-jp/holiday_jp-go
# github.com/ikawaha/kagome-dict v1.0.9
## explicit; go 1.19
github.com/ikawaha/kagome-dict/dict
github.com/ikawaha/kagome-dict/dict/trie
# github.com/ikawaha/kagome-dict/ipa v1.0.10
## explicit; go 1.19
github.com/ikawaha/kagome-dict/ipa
# github.com/ikawaha/kagome/v2 v2.9.5
## explicit; go 1.19
github.com/ikawaha/kagome/v2/tokenizer
github.com/ikawaha/kagome/v2/tokenizer/lattice
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgx/v5 v5.5.4
## explicit; go 1.19
github.com/jackc/pgx/v5
github.com/jackc/pgx/v5/internal/anynil
github.com/jackc/pgx/v5/internal/iobufpool
github.com/jackc/pgx/v5/internal/pgio
github.com/jackc/pgx/v5/internal/sanitize
github.com/jackc/pgx/v5/internal/stmtcache
github.com/jackc/pgx/v5/pgconn
github.com/jackc/pgx/v5/pgconn/internal/bgreader
github.com/jackc/pgx/v5/pgconn/internal/ctxwatch
github.com/jackc/pgx/v5/pgproto3
github.com/jackc/pgx/v5/pgtype
github.com/jackc/pgx/v5/pgxpool
github.com/jackc/pgx/v5/stdlib
# github.com/jackc/puddle/v2 v2.2.1
## explicit; go 1.19
github.com/jackc/puddle/v2
github.com/jackc/puddle/v2/internal/genstack
# github.com/jaytaylor/html2text v0.0.0-20230321000545-74c2419ad056
## explicit
github.com/jaytaylor/html2text
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/jmespath/go-jmespath v0.4.0
## explicit; go 1.14
github.com/jmespath/go-jmespath
# github.com/joho/godotenv v1.5.1
## explicit; go 1.12
github.com/joho/godotenv
# github.com/jonboulle/clockwork v0.4.0
## explicit; go 1.15
github.com/jonboulle/clockwork
# github.com/kr/pretty v0.3.1
## explicit; go 1.12
github.com/kr/pretty
# github.com/kr/text v0.2.0
## explicit
github.com/kr/text
# github.com/labstack/echo/v4 v4.10.0
## explicit; go 1.17
github.com/labstack/echo/v4
github.com/labstack/echo/v4/middleware
# github.com/labstack/gommon v0.4.0
## explicit; go 1.12
github.com/labstack/gommon/bytes
github.com/labstack/gommon/color
github.com/labstack/gommon/log
github.com/labstack/gommon/random
# github.com/leodido/go-urn v1.2.4
## explicit; go 1.16
github.com/leodido/go-urn
# github.com/lestrrat-go/blackmagic v1.0.2
## explicit; go 1.16
github.com/lestrrat-go/blackmagic
# github.com/lestrrat-go/httpcc v1.0.1
## explicit; go 1.16
github.com/lestrrat-go/httpcc
# github.com/lestrrat-go/httprc v1.0.5
## explicit; go 1.17
github.com/lestrrat-go/httprc
# github.com/lestrrat-go/iter v1.0.2
## explicit; go 1.13
github.com/lestrrat-go/iter/arrayiter
github.com/lestrrat-go/iter/mapiter
# github.com/lestrrat-go/jwx/v2 v2.0.21
## explicit; go 1.18
github.com/lestrrat-go/jwx/v2/cert
github.com/lestrrat-go/jwx/v2/internal/base64
github.com/lestrrat-go/jwx/v2/internal/ecutil
github.com/lestrrat-go/jwx/v2/internal/iter
github.com/lestrrat-go/jwx/v2/internal/json
github.com/lestrrat-go/jwx/v2/internal/pool
github.com/lestrrat-go/jwx/v2/jwa
github.com/lestrrat-go/jwx/v2/jwk
github.com/lestrrat-go/jwx/v2/x25519
# github.com/lestrrat-go/option v1.0.1
## explicit; go 1.16
github.com/lestrrat-go/option
# github.com/lib/pq v1.10.9
## explicit; go 1.13
github.com/lib/pq
github.com/lib/pq/oid
github.com/lib/pq/scram
# github.com/line/line-bot-sdk-go/v8 v8.7.0
## explicit; go 1.20
github.com/line/line-bot-sdk-go/v8/linebot
# github.com/mattn/go-colorable v0.1.13
## explicit; go 1.15
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.19
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/mattn/go-runewidth v0.0.16
## explicit; go 1.9
github.com/mattn/go-runewidth
# github.com/mattn/go-sqlite3 v1.14.17
## explicit; go 1.16
# github.com/microsoft/go-mssqldb v1.0.0
## explicit; go 1.13
# github.com/mileusna/useragent v1.3.4
## explicit; go 1.14
github.com/mileusna/useragent
# github.com/mitchellh/mapstructure v1.5.0
## explicit; go 1.14
github.com/mitchellh/mapstructure
# github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826
## explicit
github.com/mohae/deepcopy
# github.com/nxadm/tail v1.4.11
## explicit; go 1.13
# github.com/nyaruka/phonenumbers v1.4.0
## explicit; go 1.19
github.com/nyaruka/phonenumbers
github.com/nyaruka/phonenumbers/gen
# github.com/olekukonko/tablewriter v0.0.5
## explicit; go 1.12
github.com/olekukonko/tablewriter
# github.com/onsi/ginkgo v1.16.4
## explicit; go 1.15
# github.com/onsi/gomega v1.15.0
## explicit; go 1.14
# github.com/opensearch-project/opensearch-go/v2 v2.3.0
## explicit; go 1.15
github.com/opensearch-project/opensearch-go/v2
github.com/opensearch-project/opensearch-go/v2/internal/version
github.com/opensearch-project/opensearch-go/v2/opensearchapi
github.com/opensearch-project/opensearch-go/v2/opensearchtransport
github.com/opensearch-project/opensearch-go/v2/signer
# github.com/patrickmn/go-cache v2.1.0+incompatible
## explicit
github.com/patrickmn/go-cache
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/redis/go-redis/v9 v9.7.0
## explicit; go 1.18
github.com/redis/go-redis/v9
github.com/redis/go-redis/v9/internal
github.com/redis/go-redis/v9/internal/hashtag
github.com/redis/go-redis/v9/internal/hscan
github.com/redis/go-redis/v9/internal/pool
github.com/redis/go-redis/v9/internal/proto
github.com/redis/go-redis/v9/internal/rand
github.com/redis/go-redis/v9/internal/util
# github.com/richardlehane/mscfb v1.0.4
## explicit
github.com/richardlehane/mscfb
# github.com/richardlehane/msoleps v1.0.3
## explicit
github.com/richardlehane/msoleps/types
# github.com/rivo/uniseg v0.2.0
## explicit; go 1.12
github.com/rivo/uniseg
# github.com/robfig/cron/v3 v3.0.1
## explicit; go 1.12
github.com/robfig/cron/v3
# github.com/rogpeppe/go-internal v1.11.0
## explicit; go 1.19
github.com/rogpeppe/go-internal/fmtsort
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/segmentio/asm v1.2.0
## explicit; go 1.18
github.com/segmentio/asm/base64
github.com/segmentio/asm/cpu
github.com/segmentio/asm/cpu/arm
github.com/segmentio/asm/cpu/arm64
github.com/segmentio/asm/cpu/cpuid
github.com/segmentio/asm/cpu/x86
github.com/segmentio/asm/internal/unsafebytes
# github.com/sosodev/duration v1.2.0
## explicit; go 1.17
github.com/sosodev/duration
# github.com/ssor/bom v0.0.0-20170718123548-6386211fdfcf
## explicit
github.com/ssor/bom
# github.com/stretchr/testify v1.9.0
## explicit; go 1.17
github.com/stretchr/testify/assert
# github.com/tidwall/gjson v1.14.2
## explicit; go 1.12
github.com/tidwall/gjson
# github.com/tidwall/match v1.1.1
## explicit; go 1.15
github.com/tidwall/match
# github.com/tidwall/pretty v1.2.0
## explicit; go 1.16
github.com/tidwall/pretty
# github.com/tidwall/sjson v1.2.5
## explicit; go 1.14
github.com/tidwall/sjson
# github.com/urfave/cli/v2 v2.27.1
## explicit; go 1.18
github.com/urfave/cli/v2
# github.com/valyala/bytebufferpool v1.0.0
## explicit
github.com/valyala/bytebufferpool
# github.com/valyala/fasttemplate v1.2.2
## explicit; go 1.12
github.com/valyala/fasttemplate
# github.com/vektah/gqlparser/v2 v2.5.15
## explicit; go 1.19
github.com/vektah/gqlparser/v2
github.com/vektah/gqlparser/v2/ast
github.com/vektah/gqlparser/v2/gqlerror
github.com/vektah/gqlparser/v2/lexer
github.com/vektah/gqlparser/v2/parser
github.com/vektah/gqlparser/v2/validator
github.com/vektah/gqlparser/v2/validator/rules
# github.com/xi2/xz v0.0.0-20171230120015-48954b6210f8
## explicit
github.com/xi2/xz
# github.com/xrash/smetrics v0.0.0-20231213231151-1d8dd44e695e
## explicit
github.com/xrash/smetrics
# github.com/xuri/efp v0.0.0-20231025114914-d1ff6096ae53
## explicit; go 1.11
github.com/xuri/efp
# github.com/xuri/excelize/v2 v2.8.1
## explicit; go 1.18
github.com/xuri/excelize/v2
# github.com/xuri/nfp v0.0.0-20230919160717-d98342af3f05
## explicit; go 1.15
github.com/xuri/nfp
# github.com/yuin/gopher-lua v1.1.1
## explicit; go 1.17
github.com/yuin/gopher-lua
github.com/yuin/gopher-lua/ast
github.com/yuin/gopher-lua/parse
github.com/yuin/gopher-lua/pm
# go.opencensus.io v0.24.0
## explicit; go 1.13
go.opencensus.io
go.opencensus.io/internal
go.opencensus.io/internal/tagencoding
go.opencensus.io/metric/metricdata
go.opencensus.io/metric/metricproducer
go.opencensus.io/plugin/ocgrpc
go.opencensus.io/plugin/ochttp
go.opencensus.io/plugin/ochttp/propagation/b3
go.opencensus.io/resource
go.opencensus.io/stats
go.opencensus.io/stats/internal
go.opencensus.io/stats/view
go.opencensus.io/tag
go.opencensus.io/trace
go.opencensus.io/trace/internal
go.opencensus.io/trace/propagation
go.opencensus.io/trace/tracestate
# go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.48.0
## explicit; go 1.20
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc/internal
# go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.48.0
## explicit; go 1.20
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconvutil
# go.opentelemetry.io/otel v1.23.1
## explicit; go 1.20
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/v1.17.0
go.opentelemetry.io/otel/semconv/v1.20.0
# go.opentelemetry.io/otel/metric v1.23.1
## explicit; go 1.20
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/embedded
go.opentelemetry.io/otel/metric/noop
# go.opentelemetry.io/otel/trace v1.23.1
## explicit; go 1.20
go.opentelemetry.io/otel/trace
go.opentelemetry.io/otel/trace/embedded
# go.uber.org/atomic v1.9.0
## explicit; go 1.13
go.uber.org/atomic
# go.uber.org/mock v0.4.0
## explicit; go 1.20
go.uber.org/mock/gomock
# go.uber.org/multierr v1.6.0
## explicit; go 1.12
go.uber.org/multierr
# go.uber.org/zap v1.24.0
## explicit; go 1.19
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/crypto v0.23.0
## explicit; go 1.18
golang.org/x/crypto/acme
golang.org/x/crypto/acme/autocert
golang.org/x/crypto/argon2
golang.org/x/crypto/blake2b
golang.org/x/crypto/chacha20
golang.org/x/crypto/chacha20poly1305
golang.org/x/crypto/cryptobyte
golang.org/x/crypto/cryptobyte/asn1
golang.org/x/crypto/curve25519
golang.org/x/crypto/curve25519/internal/field
golang.org/x/crypto/ed25519
golang.org/x/crypto/hkdf
golang.org/x/crypto/internal/alias
golang.org/x/crypto/internal/poly1305
golang.org/x/crypto/md4
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/ripemd160
golang.org/x/crypto/sha3
# golang.org/x/exp v0.0.0-20240525044651-4c93da0ed11d
## explicit; go 1.20
golang.org/x/exp/constraints
golang.org/x/exp/maps
golang.org/x/exp/slices
golang.org/x/exp/slog
golang.org/x/exp/slog/internal
golang.org/x/exp/slog/internal/buffer
# golang.org/x/mod v0.17.0
## explicit; go 1.18
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.25.0
## explicit; go 1.18
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/html/charset
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/socks
golang.org/x/net/internal/timeseries
golang.org/x/net/proxy
golang.org/x/net/trace
golang.org/x/net/websocket
# golang.org/x/oauth2 v0.17.0
## explicit; go 1.18
golang.org/x/oauth2
golang.org/x/oauth2/authhandler
golang.org/x/oauth2/google
golang.org/x/oauth2/google/internal/externalaccount
golang.org/x/oauth2/google/internal/externalaccountauthorizeduser
golang.org/x/oauth2/google/internal/stsexchange
golang.org/x/oauth2/internal
golang.org/x/oauth2/jws
golang.org/x/oauth2/jwt
# golang.org/x/sync v0.7.0
## explicit; go 1.18
golang.org/x/sync/errgroup
golang.org/x/sync/semaphore
# golang.org/x/sys v0.20.0
## explicit; go 1.18
golang.org/x/sys/cpu
golang.org/x/sys/execabs
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/text v0.15.0
## explicit; go 1.18
golang.org/x/text/cases
golang.org/x/text/encoding
golang.org/x/text/encoding/charmap
golang.org/x/text/encoding/htmlindex
golang.org/x/text/encoding/internal
golang.org/x/text/encoding/internal/identifier
golang.org/x/text/encoding/japanese
golang.org/x/text/encoding/korean
golang.org/x/text/encoding/simplifiedchinese
golang.org/x/text/encoding/traditionalchinese
golang.org/x/text/encoding/unicode
golang.org/x/text/feature/plural
golang.org/x/text/internal
golang.org/x/text/internal/catmsg
golang.org/x/text/internal/format
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/number
golang.org/x/text/internal/stringset
golang.org/x/text/internal/tag
golang.org/x/text/internal/utf8internal
golang.org/x/text/language
golang.org/x/text/language/display
golang.org/x/text/message
golang.org/x/text/message/catalog
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# golang.org/x/time v0.5.0
## explicit; go 1.18
golang.org/x/time/rate
# golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d
## explicit; go 1.19
golang.org/x/tools/go/analysis
golang.org/x/tools/go/analysis/internal/analysisflags
golang.org/x/tools/go/analysis/internal/checker
golang.org/x/tools/go/analysis/singlechecker
golang.org/x/tools/go/analysis/unitchecker
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/internal/packagesdriver
golang.org/x/tools/go/packages
golang.org/x/tools/go/types/objectpath
golang.org/x/tools/imports
golang.org/x/tools/internal/aliases
golang.org/x/tools/internal/analysisinternal
golang.org/x/tools/internal/diff
golang.org/x/tools/internal/diff/lcs
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/facts
golang.org/x/tools/internal/gcimporter
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/packagesinternal
golang.org/x/tools/internal/pkgbits
golang.org/x/tools/internal/robustio
golang.org/x/tools/internal/stdlib
golang.org/x/tools/internal/tokeninternal
golang.org/x/tools/internal/typesinternal
golang.org/x/tools/internal/versions
# google.golang.org/api v0.165.0
## explicit; go 1.19
google.golang.org/api/googleapi
google.golang.org/api/googleapi/transport
google.golang.org/api/internal
google.golang.org/api/internal/cert
google.golang.org/api/internal/impersonate
google.golang.org/api/internal/third_party/uritemplates
google.golang.org/api/iterator
google.golang.org/api/option
google.golang.org/api/option/internaloption
google.golang.org/api/transport/grpc
google.golang.org/api/transport/http
google.golang.org/api/transport/http/internal/propagation
# google.golang.org/appengine v1.6.8
## explicit; go 1.11
google.golang.org/appengine
google.golang.org/appengine/internal
google.golang.org/appengine/internal/app_identity
google.golang.org/appengine/internal/base
google.golang.org/appengine/internal/datastore
google.golang.org/appengine/internal/log
google.golang.org/appengine/internal/modules
google.golang.org/appengine/internal/remote_api
google.golang.org/appengine/internal/urlfetch
google.golang.org/appengine/urlfetch
# google.golang.org/genproto v0.0.0-20240213162025-012b6fc9bca9
## explicit; go 1.19
google.golang.org/genproto/googleapis/cloud/vision/v1
google.golang.org/genproto/googleapis/longrunning
google.golang.org/genproto/googleapis/type/color
google.golang.org/genproto/googleapis/type/latlng
google.golang.org/genproto/internal
# google.golang.org/genproto/googleapis/api v0.0.0-20240213162025-012b6fc9bca9
## explicit; go 1.19
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
# google.golang.org/genproto/googleapis/rpc v0.0.0-20240213162025-012b6fc9bca9
## explicit; go 1.19
google.golang.org/genproto/googleapis/rpc/code
google.golang.org/genproto/googleapis/rpc/errdetails
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.61.1
## explicit; go 1.19
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb
google.golang.org/grpc/balancer/grpclb/grpc_lb_v1
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/alts
google.golang.org/grpc/credentials/alts/internal
google.golang.org/grpc/credentials/alts/internal/authinfo
google.golang.org/grpc/credentials/alts/internal/conn
google.golang.org/grpc/credentials/alts/internal/handshaker
google.golang.org/grpc/credentials/alts/internal/handshaker/service
google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcp
google.golang.org/grpc/credentials/google
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/credentials/oauth
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/grpclog
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/googlecloud
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcrand
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/resolver/manual
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.34.1
## explicit; go 1.17
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# gopkg.in/square/go-jose.v2 v2.6.0
## explicit
gopkg.in/square/go-jose.v2
gopkg.in/square/go-jose.v2/cipher
gopkg.in/square/go-jose.v2/json
gopkg.in/square/go-jose.v2/jwt
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/datatypes v1.1.1-0.20230130040222-c43177d3cf8c
## explicit; go 1.18
gorm.io/datatypes
# gorm.io/driver/mysql v1.5.1-0.20230509030346-3715c134c25b
## explicit; go 1.14
gorm.io/driver/mysql
# gorm.io/driver/postgres v1.5.2
## explicit; go 1.18
gorm.io/driver/postgres
# gorm.io/gen v0.3.23
## explicit; go 1.18
gorm.io/gen
gorm.io/gen/field
gorm.io/gen/helper
gorm.io/gen/internal/generate
gorm.io/gen/internal/model
gorm.io/gen/internal/parser
gorm.io/gen/internal/template
gorm.io/gen/internal/utils/pools
# gorm.io/gorm v1.25.2
## explicit; go 1.16
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
gorm.io/gorm/utils/tests
# gorm.io/hints v1.1.0
## explicit; go 1.14
gorm.io/hints
# gorm.io/plugin/dbresolver v1.3.0
## explicit; go 1.14
gorm.io/plugin/dbresolver
# nhooyr.io/websocket v1.8.10
## explicit; go 1.19
nhooyr.io/websocket
nhooyr.io/websocket/internal/bpool
nhooyr.io/websocket/internal/errd
nhooyr.io/websocket/internal/util
nhooyr.io/websocket/internal/wsjs
nhooyr.io/websocket/internal/xsync
nhooyr.io/websocket/wsjson
# software.sslmate.com/src/go-pkcs12 v0.4.0
## explicit; go 1.19
software.sslmate.com/src/go-pkcs12
software.sslmate.com/src/go-pkcs12/internal/rc2

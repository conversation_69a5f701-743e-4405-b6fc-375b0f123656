// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/type/color.proto

package color

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents a color in the RGBA color space. This representation is designed
// for simplicity of conversion to/from color representations in various
// languages over compactness. For example, the fields of this representation
// can be trivially provided to the constructor of `java.awt.Color` in Java; it
// can also be trivially provided to UIColor's `+colorWithRed:green:blue:alpha`
// method in iOS; and, with just a little work, it can be easily formatted into
// a CSS `rgba()` string in JavaScript.
//
// This reference page doesn't carry information about the absolute color
// space
// that should be used to interpret the RGB value (e.g. sRGB, Adobe RGB,
// DCI-P3, BT.2020, etc.). By default, applications should assume the sRGB color
// space.
//
// When color equality needs to be decided, implementations, unless
// documented otherwise, treat two colors as equal if all their red,
// green, blue, and alpha values each differ by at most 1e-5.
//
// Example (Java):
//
//	import com.google.type.Color;
//
//	// ...
//	public static java.awt.Color fromProto(Color protocolor) {
//	  float alpha = protocolor.hasAlpha()
//	      ? protocolor.getAlpha().getValue()
//	      : 1.0;
//
//	  return new java.awt.Color(
//	      protocolor.getRed(),
//	      protocolor.getGreen(),
//	      protocolor.getBlue(),
//	      alpha);
//	}
//
//	public static Color toProto(java.awt.Color color) {
//	  float red = (float) color.getRed();
//	  float green = (float) color.getGreen();
//	  float blue = (float) color.getBlue();
//	  float denominator = 255.0;
//	  Color.Builder resultBuilder =
//	      Color
//	          .newBuilder()
//	          .setRed(red / denominator)
//	          .setGreen(green / denominator)
//	          .setBlue(blue / denominator);
//	  int alpha = color.getAlpha();
//	  if (alpha != 255) {
//	    result.setAlpha(
//	        FloatValue
//	            .newBuilder()
//	            .setValue(((float) alpha) / denominator)
//	            .build());
//	  }
//	  return resultBuilder.build();
//	}
//	// ...
//
// Example (iOS / Obj-C):
//
//	    // ...
//	    static UIColor* fromProto(Color* protocolor) {
//	       float red = [protocolor red];
//	       float green = [protocolor green];
//	       float blue = [protocolor blue];
//	       FloatValue* alpha_wrapper = [protocolor alpha];
//	       float alpha = 1.0;
//	       if (alpha_wrapper != nil) {
//	         alpha = [alpha_wrapper value];
//	       }
//	       return [UIColor colorWithRed:red green:green blue:blue alpha:alpha];
//	    }
//
//	    static Color* toProto(UIColor* color) {
//	        CGFloat red, green, blue, alpha;
//	        if (![color getRed:&red green:&green blue:&blue alpha:&alpha]) {
//	          return nil;
//	        }
//	        Color* result = [[Color alloc] init];
//	        [result setRed:red];
//	        [result setGreen:green];
//	        [result setBlue:blue];
//	        if (alpha <= 0.9999) {
//	          [result setAlpha:floatWrapperWithValue(alpha)];
//	        }
//	        [result autorelease];
//	        return result;
//	   }
//	   // ...
//
//	Example (JavaScript):
//
//	   // ...
//
//	   var protoToCssColor = function(rgb_color) {
//	      var redFrac = rgb_color.red || 0.0;
//	      var greenFrac = rgb_color.green || 0.0;
//	      var blueFrac = rgb_color.blue || 0.0;
//	      var red = Math.floor(redFrac * 255);
//	      var green = Math.floor(greenFrac * 255);
//	      var blue = Math.floor(blueFrac * 255);
//
//	      if (!('alpha' in rgb_color)) {
//	         return rgbToCssColor(red, green, blue);
//	      }
//
//	      var alphaFrac = rgb_color.alpha.value || 0.0;
//	      var rgbParams = [red, green, blue].join(',');
//	      return ['rgba(', rgbParams, ',', alphaFrac, ')'].join('');
//	   };
//
//	   var rgbToCssColor = function(red, green, blue) {
//	     var rgbNumber = new Number((red << 16) | (green << 8) | blue);
//	     var hexString = rgbNumber.toString(16);
//	     var missingZeros = 6 - hexString.length;
//	     var resultBuilder = ['#'];
//	     for (var i = 0; i < missingZeros; i++) {
//	        resultBuilder.push('0');
//	     }
//	     resultBuilder.push(hexString);
//	     return resultBuilder.join('');
//	   };
//
//	   // ...
type Color struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The amount of red in the color as a value in the interval [0, 1].
	Red float32 `protobuf:"fixed32,1,opt,name=red,proto3" json:"red,omitempty"`
	// The amount of green in the color as a value in the interval [0, 1].
	Green float32 `protobuf:"fixed32,2,opt,name=green,proto3" json:"green,omitempty"`
	// The amount of blue in the color as a value in the interval [0, 1].
	Blue float32 `protobuf:"fixed32,3,opt,name=blue,proto3" json:"blue,omitempty"`
	// The fraction of this color that should be applied to the pixel. That is,
	// the final pixel color is defined by the equation:
	//
	//   `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)`
	//
	// This means that a value of 1.0 corresponds to a solid color, whereas
	// a value of 0.0 corresponds to a completely transparent color. This
	// uses a wrapper message rather than a simple float scalar so that it is
	// possible to distinguish between a default value and the value being unset.
	// If omitted, this color object is rendered as a solid color
	// (as if the alpha value had been explicitly given a value of 1.0).
	Alpha *wrapperspb.FloatValue `protobuf:"bytes,4,opt,name=alpha,proto3" json:"alpha,omitempty"`
}

func (x *Color) Reset() {
	*x = Color{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_type_color_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Color) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Color) ProtoMessage() {}

func (x *Color) ProtoReflect() protoreflect.Message {
	mi := &file_google_type_color_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Color.ProtoReflect.Descriptor instead.
func (*Color) Descriptor() ([]byte, []int) {
	return file_google_type_color_proto_rawDescGZIP(), []int{0}
}

func (x *Color) GetRed() float32 {
	if x != nil {
		return x.Red
	}
	return 0
}

func (x *Color) GetGreen() float32 {
	if x != nil {
		return x.Green
	}
	return 0
}

func (x *Color) GetBlue() float32 {
	if x != nil {
		return x.Blue
	}
	return 0
}

func (x *Color) GetAlpha() *wrapperspb.FloatValue {
	if x != nil {
		return x.Alpha
	}
	return nil
}

var File_google_type_color_proto protoreflect.FileDescriptor

var file_google_type_color_proto_rawDesc = []byte{
	0x0a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x76, 0x0a, 0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x72, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x72, 0x65,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x05, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x62, 0x6c, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x42, 0x60,
	0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a,
	0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f,
	0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x3b, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0xf8, 0x01, 0x01, 0xa2, 0x02, 0x03, 0x47, 0x54, 0x50,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_type_color_proto_rawDescOnce sync.Once
	file_google_type_color_proto_rawDescData = file_google_type_color_proto_rawDesc
)

func file_google_type_color_proto_rawDescGZIP() []byte {
	file_google_type_color_proto_rawDescOnce.Do(func() {
		file_google_type_color_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_type_color_proto_rawDescData)
	})
	return file_google_type_color_proto_rawDescData
}

var file_google_type_color_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_type_color_proto_goTypes = []interface{}{
	(*Color)(nil),                 // 0: google.type.Color
	(*wrapperspb.FloatValue)(nil), // 1: google.protobuf.FloatValue
}
var file_google_type_color_proto_depIdxs = []int32{
	1, // 0: google.type.Color.alpha:type_name -> google.protobuf.FloatValue
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_google_type_color_proto_init() }
func file_google_type_color_proto_init() {
	if File_google_type_color_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_type_color_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Color); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_type_color_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_type_color_proto_goTypes,
		DependencyIndexes: file_google_type_color_proto_depIdxs,
		MessageInfos:      file_google_type_color_proto_msgTypes,
	}.Build()
	File_google_type_color_proto = out.File
	file_google_type_color_proto_rawDesc = nil
	file_google_type_color_proto_goTypes = nil
	file_google_type_color_proto_depIdxs = nil
}

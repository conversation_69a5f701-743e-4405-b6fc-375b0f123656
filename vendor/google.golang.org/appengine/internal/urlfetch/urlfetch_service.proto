syntax = "proto2";
option go_package = "urlfetch";

package appengine;

message URLFetchServiceError {
  enum ErrorCode {
    OK = 0;
    INVALID_URL = 1;
    FETCH_ERROR = 2;
    UNSPECIFIED_ERROR = 3;
    RESPONSE_TOO_LARGE = 4;
    DEADLINE_EXCEEDED = 5;
    SSL_CERTIFICATE_ERROR = 6;
    DNS_ERROR = 7;
    CLOSED = 8;
    INTERNAL_TRANSIENT_ERROR = 9;
    TOO_MANY_REDIRECTS = 10;
    MALFORMED_REPLY = 11;
    CONNECTION_ERROR = 12;
  }
}

message URLFetchRequest {
  enum RequestMethod {
    GET = 1;
    POST = 2;
    HEAD = 3;
    PUT = 4;
    DELETE = 5;
    PATCH = 6;
  }
  required RequestMethod Method = 1;
  required string Url = 2;
  repeated group Header = 3 {
    required string Key = 4;
    required string Value = 5;
  }
  optional bytes Payload = 6 [ctype=CORD];

  optional bool FollowRedirects = 7 [default=true];

  optional double Deadline = 8;

  optional bool MustValidateServerCertificate = 9 [default=true];
}

message URLFetchResponse {
  optional bytes Content = 1;
  required int32 StatusCode = 2;
  repeated group Header = 3 {
    required string Key = 4;
    required string Value = 5;
  }
  optional bool ContentWasTruncated = 6 [default=false];
  optional int64 ExternalBytesSent = 7;
  optional int64 ExternalBytesReceived = 8;

  optional string FinalUrl = 9;

  optional int64 ApiCpuMilliseconds = 10 [default=0];
  optional int64 ApiBytesSent = 11 [default=0];
  optional int64 ApiBytesReceived = 12 [default=0];
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google.golang.org/appengine/internal/log/log_service.proto

package log

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LogServiceError_ErrorCode int32

const (
	LogServiceError_OK              LogServiceError_ErrorCode = 0
	LogServiceError_INVALID_REQUEST LogServiceError_ErrorCode = 1
	LogServiceError_STORAGE_ERROR   LogServiceError_ErrorCode = 2
)

var LogServiceError_ErrorCode_name = map[int32]string{
	0: "OK",
	1: "INVALID_REQUEST",
	2: "STORAGE_ERROR",
}
var LogServiceError_ErrorCode_value = map[string]int32{
	"OK":              0,
	"INVALID_REQUEST": 1,
	"STORAGE_ERROR":   2,
}

func (x LogServiceError_ErrorCode) Enum() *LogServiceError_ErrorCode {
	p := new(LogServiceError_ErrorCode)
	*p = x
	return p
}
func (x LogServiceError_ErrorCode) String() string {
	return proto.EnumName(LogServiceError_ErrorCode_name, int32(x))
}
func (x *LogServiceError_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LogServiceError_ErrorCode_value, data, "LogServiceError_ErrorCode")
	if err != nil {
		return err
	}
	*x = LogServiceError_ErrorCode(value)
	return nil
}
func (LogServiceError_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{0, 0}
}

type LogServiceError struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogServiceError) Reset()         { *m = LogServiceError{} }
func (m *LogServiceError) String() string { return proto.CompactTextString(m) }
func (*LogServiceError) ProtoMessage()    {}
func (*LogServiceError) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{0}
}
func (m *LogServiceError) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogServiceError.Unmarshal(m, b)
}
func (m *LogServiceError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogServiceError.Marshal(b, m, deterministic)
}
func (dst *LogServiceError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogServiceError.Merge(dst, src)
}
func (m *LogServiceError) XXX_Size() int {
	return xxx_messageInfo_LogServiceError.Size(m)
}
func (m *LogServiceError) XXX_DiscardUnknown() {
	xxx_messageInfo_LogServiceError.DiscardUnknown(m)
}

var xxx_messageInfo_LogServiceError proto.InternalMessageInfo

type UserAppLogLine struct {
	TimestampUsec        *int64   `protobuf:"varint,1,req,name=timestamp_usec,json=timestampUsec" json:"timestamp_usec,omitempty"`
	Level                *int64   `protobuf:"varint,2,req,name=level" json:"level,omitempty"`
	Message              *string  `protobuf:"bytes,3,req,name=message" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAppLogLine) Reset()         { *m = UserAppLogLine{} }
func (m *UserAppLogLine) String() string { return proto.CompactTextString(m) }
func (*UserAppLogLine) ProtoMessage()    {}
func (*UserAppLogLine) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{1}
}
func (m *UserAppLogLine) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAppLogLine.Unmarshal(m, b)
}
func (m *UserAppLogLine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAppLogLine.Marshal(b, m, deterministic)
}
func (dst *UserAppLogLine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAppLogLine.Merge(dst, src)
}
func (m *UserAppLogLine) XXX_Size() int {
	return xxx_messageInfo_UserAppLogLine.Size(m)
}
func (m *UserAppLogLine) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAppLogLine.DiscardUnknown(m)
}

var xxx_messageInfo_UserAppLogLine proto.InternalMessageInfo

func (m *UserAppLogLine) GetTimestampUsec() int64 {
	if m != nil && m.TimestampUsec != nil {
		return *m.TimestampUsec
	}
	return 0
}

func (m *UserAppLogLine) GetLevel() int64 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *UserAppLogLine) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type UserAppLogGroup struct {
	LogLine              []*UserAppLogLine `protobuf:"bytes,2,rep,name=log_line,json=logLine" json:"log_line,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserAppLogGroup) Reset()         { *m = UserAppLogGroup{} }
func (m *UserAppLogGroup) String() string { return proto.CompactTextString(m) }
func (*UserAppLogGroup) ProtoMessage()    {}
func (*UserAppLogGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{2}
}
func (m *UserAppLogGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAppLogGroup.Unmarshal(m, b)
}
func (m *UserAppLogGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAppLogGroup.Marshal(b, m, deterministic)
}
func (dst *UserAppLogGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAppLogGroup.Merge(dst, src)
}
func (m *UserAppLogGroup) XXX_Size() int {
	return xxx_messageInfo_UserAppLogGroup.Size(m)
}
func (m *UserAppLogGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAppLogGroup.DiscardUnknown(m)
}

var xxx_messageInfo_UserAppLogGroup proto.InternalMessageInfo

func (m *UserAppLogGroup) GetLogLine() []*UserAppLogLine {
	if m != nil {
		return m.LogLine
	}
	return nil
}

type FlushRequest struct {
	Logs                 []byte   `protobuf:"bytes,1,opt,name=logs" json:"logs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlushRequest) Reset()         { *m = FlushRequest{} }
func (m *FlushRequest) String() string { return proto.CompactTextString(m) }
func (*FlushRequest) ProtoMessage()    {}
func (*FlushRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{3}
}
func (m *FlushRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlushRequest.Unmarshal(m, b)
}
func (m *FlushRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlushRequest.Marshal(b, m, deterministic)
}
func (dst *FlushRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushRequest.Merge(dst, src)
}
func (m *FlushRequest) XXX_Size() int {
	return xxx_messageInfo_FlushRequest.Size(m)
}
func (m *FlushRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FlushRequest proto.InternalMessageInfo

func (m *FlushRequest) GetLogs() []byte {
	if m != nil {
		return m.Logs
	}
	return nil
}

type SetStatusRequest struct {
	Status               *string  `protobuf:"bytes,1,req,name=status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStatusRequest) Reset()         { *m = SetStatusRequest{} }
func (m *SetStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetStatusRequest) ProtoMessage()    {}
func (*SetStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{4}
}
func (m *SetStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStatusRequest.Unmarshal(m, b)
}
func (m *SetStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStatusRequest.Merge(dst, src)
}
func (m *SetStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetStatusRequest.Size(m)
}
func (m *SetStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetStatusRequest proto.InternalMessageInfo

func (m *SetStatusRequest) GetStatus() string {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return ""
}

type LogOffset struct {
	RequestId            []byte   `protobuf:"bytes,1,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogOffset) Reset()         { *m = LogOffset{} }
func (m *LogOffset) String() string { return proto.CompactTextString(m) }
func (*LogOffset) ProtoMessage()    {}
func (*LogOffset) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{5}
}
func (m *LogOffset) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogOffset.Unmarshal(m, b)
}
func (m *LogOffset) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogOffset.Marshal(b, m, deterministic)
}
func (dst *LogOffset) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogOffset.Merge(dst, src)
}
func (m *LogOffset) XXX_Size() int {
	return xxx_messageInfo_LogOffset.Size(m)
}
func (m *LogOffset) XXX_DiscardUnknown() {
	xxx_messageInfo_LogOffset.DiscardUnknown(m)
}

var xxx_messageInfo_LogOffset proto.InternalMessageInfo

func (m *LogOffset) GetRequestId() []byte {
	if m != nil {
		return m.RequestId
	}
	return nil
}

type LogLine struct {
	Time                 *int64   `protobuf:"varint,1,req,name=time" json:"time,omitempty"`
	Level                *int32   `protobuf:"varint,2,req,name=level" json:"level,omitempty"`
	LogMessage           *string  `protobuf:"bytes,3,req,name=log_message,json=logMessage" json:"log_message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogLine) Reset()         { *m = LogLine{} }
func (m *LogLine) String() string { return proto.CompactTextString(m) }
func (*LogLine) ProtoMessage()    {}
func (*LogLine) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{6}
}
func (m *LogLine) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogLine.Unmarshal(m, b)
}
func (m *LogLine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogLine.Marshal(b, m, deterministic)
}
func (dst *LogLine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogLine.Merge(dst, src)
}
func (m *LogLine) XXX_Size() int {
	return xxx_messageInfo_LogLine.Size(m)
}
func (m *LogLine) XXX_DiscardUnknown() {
	xxx_messageInfo_LogLine.DiscardUnknown(m)
}

var xxx_messageInfo_LogLine proto.InternalMessageInfo

func (m *LogLine) GetTime() int64 {
	if m != nil && m.Time != nil {
		return *m.Time
	}
	return 0
}

func (m *LogLine) GetLevel() int32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *LogLine) GetLogMessage() string {
	if m != nil && m.LogMessage != nil {
		return *m.LogMessage
	}
	return ""
}

type RequestLog struct {
	AppId                   *string    `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id,omitempty"`
	ModuleId                *string    `protobuf:"bytes,37,opt,name=module_id,json=moduleId,def=default" json:"module_id,omitempty"`
	VersionId               *string    `protobuf:"bytes,2,req,name=version_id,json=versionId" json:"version_id,omitempty"`
	RequestId               []byte     `protobuf:"bytes,3,req,name=request_id,json=requestId" json:"request_id,omitempty"`
	Offset                  *LogOffset `protobuf:"bytes,35,opt,name=offset" json:"offset,omitempty"`
	Ip                      *string    `protobuf:"bytes,4,req,name=ip" json:"ip,omitempty"`
	Nickname                *string    `protobuf:"bytes,5,opt,name=nickname" json:"nickname,omitempty"`
	StartTime               *int64     `protobuf:"varint,6,req,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime                 *int64     `protobuf:"varint,7,req,name=end_time,json=endTime" json:"end_time,omitempty"`
	Latency                 *int64     `protobuf:"varint,8,req,name=latency" json:"latency,omitempty"`
	Mcycles                 *int64     `protobuf:"varint,9,req,name=mcycles" json:"mcycles,omitempty"`
	Method                  *string    `protobuf:"bytes,10,req,name=method" json:"method,omitempty"`
	Resource                *string    `protobuf:"bytes,11,req,name=resource" json:"resource,omitempty"`
	HttpVersion             *string    `protobuf:"bytes,12,req,name=http_version,json=httpVersion" json:"http_version,omitempty"`
	Status                  *int32     `protobuf:"varint,13,req,name=status" json:"status,omitempty"`
	ResponseSize            *int64     `protobuf:"varint,14,req,name=response_size,json=responseSize" json:"response_size,omitempty"`
	Referrer                *string    `protobuf:"bytes,15,opt,name=referrer" json:"referrer,omitempty"`
	UserAgent               *string    `protobuf:"bytes,16,opt,name=user_agent,json=userAgent" json:"user_agent,omitempty"`
	UrlMapEntry             *string    `protobuf:"bytes,17,req,name=url_map_entry,json=urlMapEntry" json:"url_map_entry,omitempty"`
	Combined                *string    `protobuf:"bytes,18,req,name=combined" json:"combined,omitempty"`
	ApiMcycles              *int64     `protobuf:"varint,19,opt,name=api_mcycles,json=apiMcycles" json:"api_mcycles,omitempty"`
	Host                    *string    `protobuf:"bytes,20,opt,name=host" json:"host,omitempty"`
	Cost                    *float64   `protobuf:"fixed64,21,opt,name=cost" json:"cost,omitempty"`
	TaskQueueName           *string    `protobuf:"bytes,22,opt,name=task_queue_name,json=taskQueueName" json:"task_queue_name,omitempty"`
	TaskName                *string    `protobuf:"bytes,23,opt,name=task_name,json=taskName" json:"task_name,omitempty"`
	WasLoadingRequest       *bool      `protobuf:"varint,24,opt,name=was_loading_request,json=wasLoadingRequest" json:"was_loading_request,omitempty"`
	PendingTime             *int64     `protobuf:"varint,25,opt,name=pending_time,json=pendingTime" json:"pending_time,omitempty"`
	ReplicaIndex            *int32     `protobuf:"varint,26,opt,name=replica_index,json=replicaIndex,def=-1" json:"replica_index,omitempty"`
	Finished                *bool      `protobuf:"varint,27,opt,name=finished,def=1" json:"finished,omitempty"`
	CloneKey                []byte     `protobuf:"bytes,28,opt,name=clone_key,json=cloneKey" json:"clone_key,omitempty"`
	Line                    []*LogLine `protobuf:"bytes,29,rep,name=line" json:"line,omitempty"`
	LinesIncomplete         *bool      `protobuf:"varint,36,opt,name=lines_incomplete,json=linesIncomplete" json:"lines_incomplete,omitempty"`
	AppEngineRelease        []byte     `protobuf:"bytes,38,opt,name=app_engine_release,json=appEngineRelease" json:"app_engine_release,omitempty"`
	ExitReason              *int32     `protobuf:"varint,30,opt,name=exit_reason,json=exitReason" json:"exit_reason,omitempty"`
	WasThrottledForTime     *bool      `protobuf:"varint,31,opt,name=was_throttled_for_time,json=wasThrottledForTime" json:"was_throttled_for_time,omitempty"`
	WasThrottledForRequests *bool      `protobuf:"varint,32,opt,name=was_throttled_for_requests,json=wasThrottledForRequests" json:"was_throttled_for_requests,omitempty"`
	ThrottledTime           *int64     `protobuf:"varint,33,opt,name=throttled_time,json=throttledTime" json:"throttled_time,omitempty"`
	ServerName              []byte     `protobuf:"bytes,34,opt,name=server_name,json=serverName" json:"server_name,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}   `json:"-"`
	XXX_unrecognized        []byte     `json:"-"`
	XXX_sizecache           int32      `json:"-"`
}

func (m *RequestLog) Reset()         { *m = RequestLog{} }
func (m *RequestLog) String() string { return proto.CompactTextString(m) }
func (*RequestLog) ProtoMessage()    {}
func (*RequestLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{7}
}
func (m *RequestLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RequestLog.Unmarshal(m, b)
}
func (m *RequestLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RequestLog.Marshal(b, m, deterministic)
}
func (dst *RequestLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestLog.Merge(dst, src)
}
func (m *RequestLog) XXX_Size() int {
	return xxx_messageInfo_RequestLog.Size(m)
}
func (m *RequestLog) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestLog.DiscardUnknown(m)
}

var xxx_messageInfo_RequestLog proto.InternalMessageInfo

const Default_RequestLog_ModuleId string = "default"
const Default_RequestLog_ReplicaIndex int32 = -1
const Default_RequestLog_Finished bool = true

func (m *RequestLog) GetAppId() string {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return ""
}

func (m *RequestLog) GetModuleId() string {
	if m != nil && m.ModuleId != nil {
		return *m.ModuleId
	}
	return Default_RequestLog_ModuleId
}

func (m *RequestLog) GetVersionId() string {
	if m != nil && m.VersionId != nil {
		return *m.VersionId
	}
	return ""
}

func (m *RequestLog) GetRequestId() []byte {
	if m != nil {
		return m.RequestId
	}
	return nil
}

func (m *RequestLog) GetOffset() *LogOffset {
	if m != nil {
		return m.Offset
	}
	return nil
}

func (m *RequestLog) GetIp() string {
	if m != nil && m.Ip != nil {
		return *m.Ip
	}
	return ""
}

func (m *RequestLog) GetNickname() string {
	if m != nil && m.Nickname != nil {
		return *m.Nickname
	}
	return ""
}

func (m *RequestLog) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *RequestLog) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *RequestLog) GetLatency() int64 {
	if m != nil && m.Latency != nil {
		return *m.Latency
	}
	return 0
}

func (m *RequestLog) GetMcycles() int64 {
	if m != nil && m.Mcycles != nil {
		return *m.Mcycles
	}
	return 0
}

func (m *RequestLog) GetMethod() string {
	if m != nil && m.Method != nil {
		return *m.Method
	}
	return ""
}

func (m *RequestLog) GetResource() string {
	if m != nil && m.Resource != nil {
		return *m.Resource
	}
	return ""
}

func (m *RequestLog) GetHttpVersion() string {
	if m != nil && m.HttpVersion != nil {
		return *m.HttpVersion
	}
	return ""
}

func (m *RequestLog) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *RequestLog) GetResponseSize() int64 {
	if m != nil && m.ResponseSize != nil {
		return *m.ResponseSize
	}
	return 0
}

func (m *RequestLog) GetReferrer() string {
	if m != nil && m.Referrer != nil {
		return *m.Referrer
	}
	return ""
}

func (m *RequestLog) GetUserAgent() string {
	if m != nil && m.UserAgent != nil {
		return *m.UserAgent
	}
	return ""
}

func (m *RequestLog) GetUrlMapEntry() string {
	if m != nil && m.UrlMapEntry != nil {
		return *m.UrlMapEntry
	}
	return ""
}

func (m *RequestLog) GetCombined() string {
	if m != nil && m.Combined != nil {
		return *m.Combined
	}
	return ""
}

func (m *RequestLog) GetApiMcycles() int64 {
	if m != nil && m.ApiMcycles != nil {
		return *m.ApiMcycles
	}
	return 0
}

func (m *RequestLog) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

func (m *RequestLog) GetCost() float64 {
	if m != nil && m.Cost != nil {
		return *m.Cost
	}
	return 0
}

func (m *RequestLog) GetTaskQueueName() string {
	if m != nil && m.TaskQueueName != nil {
		return *m.TaskQueueName
	}
	return ""
}

func (m *RequestLog) GetTaskName() string {
	if m != nil && m.TaskName != nil {
		return *m.TaskName
	}
	return ""
}

func (m *RequestLog) GetWasLoadingRequest() bool {
	if m != nil && m.WasLoadingRequest != nil {
		return *m.WasLoadingRequest
	}
	return false
}

func (m *RequestLog) GetPendingTime() int64 {
	if m != nil && m.PendingTime != nil {
		return *m.PendingTime
	}
	return 0
}

func (m *RequestLog) GetReplicaIndex() int32 {
	if m != nil && m.ReplicaIndex != nil {
		return *m.ReplicaIndex
	}
	return Default_RequestLog_ReplicaIndex
}

func (m *RequestLog) GetFinished() bool {
	if m != nil && m.Finished != nil {
		return *m.Finished
	}
	return Default_RequestLog_Finished
}

func (m *RequestLog) GetCloneKey() []byte {
	if m != nil {
		return m.CloneKey
	}
	return nil
}

func (m *RequestLog) GetLine() []*LogLine {
	if m != nil {
		return m.Line
	}
	return nil
}

func (m *RequestLog) GetLinesIncomplete() bool {
	if m != nil && m.LinesIncomplete != nil {
		return *m.LinesIncomplete
	}
	return false
}

func (m *RequestLog) GetAppEngineRelease() []byte {
	if m != nil {
		return m.AppEngineRelease
	}
	return nil
}

func (m *RequestLog) GetExitReason() int32 {
	if m != nil && m.ExitReason != nil {
		return *m.ExitReason
	}
	return 0
}

func (m *RequestLog) GetWasThrottledForTime() bool {
	if m != nil && m.WasThrottledForTime != nil {
		return *m.WasThrottledForTime
	}
	return false
}

func (m *RequestLog) GetWasThrottledForRequests() bool {
	if m != nil && m.WasThrottledForRequests != nil {
		return *m.WasThrottledForRequests
	}
	return false
}

func (m *RequestLog) GetThrottledTime() int64 {
	if m != nil && m.ThrottledTime != nil {
		return *m.ThrottledTime
	}
	return 0
}

func (m *RequestLog) GetServerName() []byte {
	if m != nil {
		return m.ServerName
	}
	return nil
}

type LogModuleVersion struct {
	ModuleId             *string  `protobuf:"bytes,1,opt,name=module_id,json=moduleId,def=default" json:"module_id,omitempty"`
	VersionId            *string  `protobuf:"bytes,2,opt,name=version_id,json=versionId" json:"version_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogModuleVersion) Reset()         { *m = LogModuleVersion{} }
func (m *LogModuleVersion) String() string { return proto.CompactTextString(m) }
func (*LogModuleVersion) ProtoMessage()    {}
func (*LogModuleVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{8}
}
func (m *LogModuleVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogModuleVersion.Unmarshal(m, b)
}
func (m *LogModuleVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogModuleVersion.Marshal(b, m, deterministic)
}
func (dst *LogModuleVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogModuleVersion.Merge(dst, src)
}
func (m *LogModuleVersion) XXX_Size() int {
	return xxx_messageInfo_LogModuleVersion.Size(m)
}
func (m *LogModuleVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_LogModuleVersion.DiscardUnknown(m)
}

var xxx_messageInfo_LogModuleVersion proto.InternalMessageInfo

const Default_LogModuleVersion_ModuleId string = "default"

func (m *LogModuleVersion) GetModuleId() string {
	if m != nil && m.ModuleId != nil {
		return *m.ModuleId
	}
	return Default_LogModuleVersion_ModuleId
}

func (m *LogModuleVersion) GetVersionId() string {
	if m != nil && m.VersionId != nil {
		return *m.VersionId
	}
	return ""
}

type LogReadRequest struct {
	AppId                *string             `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id,omitempty"`
	VersionId            []string            `protobuf:"bytes,2,rep,name=version_id,json=versionId" json:"version_id,omitempty"`
	ModuleVersion        []*LogModuleVersion `protobuf:"bytes,19,rep,name=module_version,json=moduleVersion" json:"module_version,omitempty"`
	StartTime            *int64              `protobuf:"varint,3,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *int64              `protobuf:"varint,4,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	Offset               *LogOffset          `protobuf:"bytes,5,opt,name=offset" json:"offset,omitempty"`
	RequestId            [][]byte            `protobuf:"bytes,6,rep,name=request_id,json=requestId" json:"request_id,omitempty"`
	MinimumLogLevel      *int32              `protobuf:"varint,7,opt,name=minimum_log_level,json=minimumLogLevel" json:"minimum_log_level,omitempty"`
	IncludeIncomplete    *bool               `protobuf:"varint,8,opt,name=include_incomplete,json=includeIncomplete" json:"include_incomplete,omitempty"`
	Count                *int64              `protobuf:"varint,9,opt,name=count" json:"count,omitempty"`
	CombinedLogRegex     *string             `protobuf:"bytes,14,opt,name=combined_log_regex,json=combinedLogRegex" json:"combined_log_regex,omitempty"`
	HostRegex            *string             `protobuf:"bytes,15,opt,name=host_regex,json=hostRegex" json:"host_regex,omitempty"`
	ReplicaIndex         *int32              `protobuf:"varint,16,opt,name=replica_index,json=replicaIndex" json:"replica_index,omitempty"`
	IncludeAppLogs       *bool               `protobuf:"varint,10,opt,name=include_app_logs,json=includeAppLogs" json:"include_app_logs,omitempty"`
	AppLogsPerRequest    *int32              `protobuf:"varint,17,opt,name=app_logs_per_request,json=appLogsPerRequest" json:"app_logs_per_request,omitempty"`
	IncludeHost          *bool               `protobuf:"varint,11,opt,name=include_host,json=includeHost" json:"include_host,omitempty"`
	IncludeAll           *bool               `protobuf:"varint,12,opt,name=include_all,json=includeAll" json:"include_all,omitempty"`
	CacheIterator        *bool               `protobuf:"varint,13,opt,name=cache_iterator,json=cacheIterator" json:"cache_iterator,omitempty"`
	NumShards            *int32              `protobuf:"varint,18,opt,name=num_shards,json=numShards" json:"num_shards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LogReadRequest) Reset()         { *m = LogReadRequest{} }
func (m *LogReadRequest) String() string { return proto.CompactTextString(m) }
func (*LogReadRequest) ProtoMessage()    {}
func (*LogReadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{9}
}
func (m *LogReadRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogReadRequest.Unmarshal(m, b)
}
func (m *LogReadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogReadRequest.Marshal(b, m, deterministic)
}
func (dst *LogReadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogReadRequest.Merge(dst, src)
}
func (m *LogReadRequest) XXX_Size() int {
	return xxx_messageInfo_LogReadRequest.Size(m)
}
func (m *LogReadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LogReadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LogReadRequest proto.InternalMessageInfo

func (m *LogReadRequest) GetAppId() string {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return ""
}

func (m *LogReadRequest) GetVersionId() []string {
	if m != nil {
		return m.VersionId
	}
	return nil
}

func (m *LogReadRequest) GetModuleVersion() []*LogModuleVersion {
	if m != nil {
		return m.ModuleVersion
	}
	return nil
}

func (m *LogReadRequest) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *LogReadRequest) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *LogReadRequest) GetOffset() *LogOffset {
	if m != nil {
		return m.Offset
	}
	return nil
}

func (m *LogReadRequest) GetRequestId() [][]byte {
	if m != nil {
		return m.RequestId
	}
	return nil
}

func (m *LogReadRequest) GetMinimumLogLevel() int32 {
	if m != nil && m.MinimumLogLevel != nil {
		return *m.MinimumLogLevel
	}
	return 0
}

func (m *LogReadRequest) GetIncludeIncomplete() bool {
	if m != nil && m.IncludeIncomplete != nil {
		return *m.IncludeIncomplete
	}
	return false
}

func (m *LogReadRequest) GetCount() int64 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *LogReadRequest) GetCombinedLogRegex() string {
	if m != nil && m.CombinedLogRegex != nil {
		return *m.CombinedLogRegex
	}
	return ""
}

func (m *LogReadRequest) GetHostRegex() string {
	if m != nil && m.HostRegex != nil {
		return *m.HostRegex
	}
	return ""
}

func (m *LogReadRequest) GetReplicaIndex() int32 {
	if m != nil && m.ReplicaIndex != nil {
		return *m.ReplicaIndex
	}
	return 0
}

func (m *LogReadRequest) GetIncludeAppLogs() bool {
	if m != nil && m.IncludeAppLogs != nil {
		return *m.IncludeAppLogs
	}
	return false
}

func (m *LogReadRequest) GetAppLogsPerRequest() int32 {
	if m != nil && m.AppLogsPerRequest != nil {
		return *m.AppLogsPerRequest
	}
	return 0
}

func (m *LogReadRequest) GetIncludeHost() bool {
	if m != nil && m.IncludeHost != nil {
		return *m.IncludeHost
	}
	return false
}

func (m *LogReadRequest) GetIncludeAll() bool {
	if m != nil && m.IncludeAll != nil {
		return *m.IncludeAll
	}
	return false
}

func (m *LogReadRequest) GetCacheIterator() bool {
	if m != nil && m.CacheIterator != nil {
		return *m.CacheIterator
	}
	return false
}

func (m *LogReadRequest) GetNumShards() int32 {
	if m != nil && m.NumShards != nil {
		return *m.NumShards
	}
	return 0
}

type LogReadResponse struct {
	Log                  []*RequestLog `protobuf:"bytes,1,rep,name=log" json:"log,omitempty"`
	Offset               *LogOffset    `protobuf:"bytes,2,opt,name=offset" json:"offset,omitempty"`
	LastEndTime          *int64        `protobuf:"varint,3,opt,name=last_end_time,json=lastEndTime" json:"last_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *LogReadResponse) Reset()         { *m = LogReadResponse{} }
func (m *LogReadResponse) String() string { return proto.CompactTextString(m) }
func (*LogReadResponse) ProtoMessage()    {}
func (*LogReadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{10}
}
func (m *LogReadResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogReadResponse.Unmarshal(m, b)
}
func (m *LogReadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogReadResponse.Marshal(b, m, deterministic)
}
func (dst *LogReadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogReadResponse.Merge(dst, src)
}
func (m *LogReadResponse) XXX_Size() int {
	return xxx_messageInfo_LogReadResponse.Size(m)
}
func (m *LogReadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LogReadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LogReadResponse proto.InternalMessageInfo

func (m *LogReadResponse) GetLog() []*RequestLog {
	if m != nil {
		return m.Log
	}
	return nil
}

func (m *LogReadResponse) GetOffset() *LogOffset {
	if m != nil {
		return m.Offset
	}
	return nil
}

func (m *LogReadResponse) GetLastEndTime() int64 {
	if m != nil && m.LastEndTime != nil {
		return *m.LastEndTime
	}
	return 0
}

type LogUsageRecord struct {
	VersionId            *string  `protobuf:"bytes,1,opt,name=version_id,json=versionId" json:"version_id,omitempty"`
	StartTime            *int32   `protobuf:"varint,2,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *int32   `protobuf:"varint,3,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	Count                *int64   `protobuf:"varint,4,opt,name=count" json:"count,omitempty"`
	TotalSize            *int64   `protobuf:"varint,5,opt,name=total_size,json=totalSize" json:"total_size,omitempty"`
	Records              *int32   `protobuf:"varint,6,opt,name=records" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogUsageRecord) Reset()         { *m = LogUsageRecord{} }
func (m *LogUsageRecord) String() string { return proto.CompactTextString(m) }
func (*LogUsageRecord) ProtoMessage()    {}
func (*LogUsageRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{11}
}
func (m *LogUsageRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogUsageRecord.Unmarshal(m, b)
}
func (m *LogUsageRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogUsageRecord.Marshal(b, m, deterministic)
}
func (dst *LogUsageRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogUsageRecord.Merge(dst, src)
}
func (m *LogUsageRecord) XXX_Size() int {
	return xxx_messageInfo_LogUsageRecord.Size(m)
}
func (m *LogUsageRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_LogUsageRecord.DiscardUnknown(m)
}

var xxx_messageInfo_LogUsageRecord proto.InternalMessageInfo

func (m *LogUsageRecord) GetVersionId() string {
	if m != nil && m.VersionId != nil {
		return *m.VersionId
	}
	return ""
}

func (m *LogUsageRecord) GetStartTime() int32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *LogUsageRecord) GetEndTime() int32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *LogUsageRecord) GetCount() int64 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *LogUsageRecord) GetTotalSize() int64 {
	if m != nil && m.TotalSize != nil {
		return *m.TotalSize
	}
	return 0
}

func (m *LogUsageRecord) GetRecords() int32 {
	if m != nil && m.Records != nil {
		return *m.Records
	}
	return 0
}

type LogUsageRequest struct {
	AppId                *string  `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id,omitempty"`
	VersionId            []string `protobuf:"bytes,2,rep,name=version_id,json=versionId" json:"version_id,omitempty"`
	StartTime            *int32   `protobuf:"varint,3,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *int32   `protobuf:"varint,4,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	ResolutionHours      *uint32  `protobuf:"varint,5,opt,name=resolution_hours,json=resolutionHours,def=1" json:"resolution_hours,omitempty"`
	CombineVersions      *bool    `protobuf:"varint,6,opt,name=combine_versions,json=combineVersions" json:"combine_versions,omitempty"`
	UsageVersion         *int32   `protobuf:"varint,7,opt,name=usage_version,json=usageVersion" json:"usage_version,omitempty"`
	VersionsOnly         *bool    `protobuf:"varint,8,opt,name=versions_only,json=versionsOnly" json:"versions_only,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LogUsageRequest) Reset()         { *m = LogUsageRequest{} }
func (m *LogUsageRequest) String() string { return proto.CompactTextString(m) }
func (*LogUsageRequest) ProtoMessage()    {}
func (*LogUsageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{12}
}
func (m *LogUsageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogUsageRequest.Unmarshal(m, b)
}
func (m *LogUsageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogUsageRequest.Marshal(b, m, deterministic)
}
func (dst *LogUsageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogUsageRequest.Merge(dst, src)
}
func (m *LogUsageRequest) XXX_Size() int {
	return xxx_messageInfo_LogUsageRequest.Size(m)
}
func (m *LogUsageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LogUsageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LogUsageRequest proto.InternalMessageInfo

const Default_LogUsageRequest_ResolutionHours uint32 = 1

func (m *LogUsageRequest) GetAppId() string {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return ""
}

func (m *LogUsageRequest) GetVersionId() []string {
	if m != nil {
		return m.VersionId
	}
	return nil
}

func (m *LogUsageRequest) GetStartTime() int32 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *LogUsageRequest) GetEndTime() int32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *LogUsageRequest) GetResolutionHours() uint32 {
	if m != nil && m.ResolutionHours != nil {
		return *m.ResolutionHours
	}
	return Default_LogUsageRequest_ResolutionHours
}

func (m *LogUsageRequest) GetCombineVersions() bool {
	if m != nil && m.CombineVersions != nil {
		return *m.CombineVersions
	}
	return false
}

func (m *LogUsageRequest) GetUsageVersion() int32 {
	if m != nil && m.UsageVersion != nil {
		return *m.UsageVersion
	}
	return 0
}

func (m *LogUsageRequest) GetVersionsOnly() bool {
	if m != nil && m.VersionsOnly != nil {
		return *m.VersionsOnly
	}
	return false
}

type LogUsageResponse struct {
	Usage                []*LogUsageRecord `protobuf:"bytes,1,rep,name=usage" json:"usage,omitempty"`
	Summary              *LogUsageRecord   `protobuf:"bytes,2,opt,name=summary" json:"summary,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *LogUsageResponse) Reset()         { *m = LogUsageResponse{} }
func (m *LogUsageResponse) String() string { return proto.CompactTextString(m) }
func (*LogUsageResponse) ProtoMessage()    {}
func (*LogUsageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_log_service_f054fd4b5012319d, []int{13}
}
func (m *LogUsageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LogUsageResponse.Unmarshal(m, b)
}
func (m *LogUsageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LogUsageResponse.Marshal(b, m, deterministic)
}
func (dst *LogUsageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogUsageResponse.Merge(dst, src)
}
func (m *LogUsageResponse) XXX_Size() int {
	return xxx_messageInfo_LogUsageResponse.Size(m)
}
func (m *LogUsageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LogUsageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LogUsageResponse proto.InternalMessageInfo

func (m *LogUsageResponse) GetUsage() []*LogUsageRecord {
	if m != nil {
		return m.Usage
	}
	return nil
}

func (m *LogUsageResponse) GetSummary() *LogUsageRecord {
	if m != nil {
		return m.Summary
	}
	return nil
}

func init() {
	proto.RegisterType((*LogServiceError)(nil), "appengine.LogServiceError")
	proto.RegisterType((*UserAppLogLine)(nil), "appengine.UserAppLogLine")
	proto.RegisterType((*UserAppLogGroup)(nil), "appengine.UserAppLogGroup")
	proto.RegisterType((*FlushRequest)(nil), "appengine.FlushRequest")
	proto.RegisterType((*SetStatusRequest)(nil), "appengine.SetStatusRequest")
	proto.RegisterType((*LogOffset)(nil), "appengine.LogOffset")
	proto.RegisterType((*LogLine)(nil), "appengine.LogLine")
	proto.RegisterType((*RequestLog)(nil), "appengine.RequestLog")
	proto.RegisterType((*LogModuleVersion)(nil), "appengine.LogModuleVersion")
	proto.RegisterType((*LogReadRequest)(nil), "appengine.LogReadRequest")
	proto.RegisterType((*LogReadResponse)(nil), "appengine.LogReadResponse")
	proto.RegisterType((*LogUsageRecord)(nil), "appengine.LogUsageRecord")
	proto.RegisterType((*LogUsageRequest)(nil), "appengine.LogUsageRequest")
	proto.RegisterType((*LogUsageResponse)(nil), "appengine.LogUsageResponse")
}

func init() {
	proto.RegisterFile("google.golang.org/appengine/internal/log/log_service.proto", fileDescriptor_log_service_f054fd4b5012319d)
}

var fileDescriptor_log_service_f054fd4b5012319d = []byte{
	// 1553 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdd, 0x72, 0xdb, 0xc6,
	0x15, 0x2e, 0x48, 0x51, 0x24, 0x0f, 0x49, 0x91, 0x5a, 0xcb, 0xce, 0xda, 0xae, 0x6b, 0x1a, 0x4e,
	0x1c, 0xd6, 0x93, 0x48, 0x93, 0xa4, 0x57, 0xca, 0x95, 0xd3, 0x2a, 0x8e, 0x26, 0xb4, 0xd5, 0x40,
	0x72, 0x3a, 0xd3, 0x1b, 0x0c, 0x0a, 0x1c, 0x81, 0x18, 0x2f, 0xb1, 0xc8, 0xee, 0xc2, 0x91, 0x72,
	0xdb, 0xdb, 0x3e, 0x46, 0x1f, 0xa2, 0xaf, 0xd2, 0xb7, 0xe9, 0xec, 0xd9, 0x05, 0x44, 0x2a, 0x4d,
	0xc6, 0x33, 0xb9, 0xe0, 0x10, 0xfb, 0x9d, 0x83, 0xdd, 0xf3, 0xf3, 0x9d, 0x6f, 0x01, 0xc7, 0xb9,
	0x94, 0xb9, 0xc0, 0xc3, 0x5c, 0x8a, 0xa4, 0xcc, 0x0f, 0xa5, 0xca, 0x8f, 0x92, 0xaa, 0xc2, 0x32,
	0x2f, 0x4a, 0x3c, 0x2a, 0x4a, 0x83, 0xaa, 0x4c, 0xc4, 0x91, 0x90, 0xb9, 0xfd, 0xc5, 0x1a, 0xd5,
	0xbb, 0x22, 0xc5, 0xc3, 0x4a, 0x49, 0x23, 0xd9, 0xb0, 0xf5, 0x0c, 0x5f, 0xc3, 0x74, 0x29, 0xf3,
	0x73, 0x67, 0x3e, 0x51, 0x4a, 0xaa, 0xf0, 0x4b, 0x18, 0xd2, 0xc3, 0x9f, 0x65, 0x86, 0x6c, 0x17,
	0x3a, 0x67, 0xdf, 0xce, 0x7e, 0xc7, 0xee, 0xc0, 0xf4, 0xf4, 0xf5, 0xf7, 0x2f, 0x96, 0xa7, 0x7f,
	0x89, 0xa3, 0x93, 0xef, 0xde, 0x9c, 0x9c, 0x5f, 0xcc, 0x02, 0xb6, 0x0f, 0x93, 0xf3, 0x8b, 0xb3,
	0xe8, 0xc5, 0xcb, 0x93, 0xf8, 0x24, 0x8a, 0xce, 0xa2, 0x59, 0x27, 0xcc, 0x61, 0xef, 0x8d, 0x46,
	0xf5, 0xa2, 0xaa, 0x96, 0x32, 0x5f, 0x16, 0x25, 0xb2, 0x8f, 0x60, 0xcf, 0x14, 0x6b, 0xd4, 0x26,
	0x59, 0x57, 0x71, 0xad, 0x31, 0xe5, 0xc1, 0xbc, 0xb3, 0xe8, 0x46, 0x93, 0x16, 0x7d, 0xa3, 0x31,
	0x65, 0x07, 0xd0, 0x13, 0xf8, 0x0e, 0x05, 0xef, 0x90, 0xd5, 0x2d, 0x18, 0x87, 0xfe, 0x1a, 0xb5,
	0x4e, 0x72, 0xe4, 0xdd, 0x79, 0x67, 0x31, 0x8c, 0x9a, 0x65, 0xf8, 0x12, 0xa6, 0x37, 0x07, 0xbd,
	0x54, 0xb2, 0xae, 0xd8, 0x9f, 0x60, 0x60, 0x73, 0x15, 0x45, 0x89, 0xbc, 0x33, 0xef, 0x2e, 0x46,
	0x9f, 0xdf, 0x3f, 0x6c, 0x33, 0x3d, 0xdc, 0x0e, 0x2b, 0xea, 0x0b, 0xf7, 0x10, 0x86, 0x30, 0xfe,
	0x5a, 0xd4, 0x7a, 0x15, 0xe1, 0x0f, 0x35, 0x6a, 0xc3, 0x18, 0xec, 0x08, 0x99, 0x6b, 0x1e, 0xcc,
	0x83, 0xc5, 0x38, 0xa2, 0xe7, 0xf0, 0x39, 0xcc, 0xce, 0xd1, 0x9c, 0x9b, 0xc4, 0xd4, 0xba, 0xf1,
	0xbb, 0x07, 0xbb, 0x9a, 0x00, 0xca, 0x67, 0x18, 0xf9, 0x55, 0xf8, 0x1c, 0x86, 0x4b, 0x99, 0x9f,
	0x5d, 0x5e, 0x6a, 0x34, 0xec, 0x11, 0x80, 0x72, 0xfe, 0x71, 0x91, 0xf9, 0x2d, 0x87, 0x1e, 0x39,
	0xcd, 0xc2, 0x0b, 0xe8, 0x37, 0x65, 0x62, 0xb0, 0x63, 0x0b, 0xe2, 0x8b, 0x43, 0xcf, 0xdb, 0x35,
	0xe9, 0x35, 0x35, 0x79, 0x0c, 0x23, 0x9b, 0xe6, 0x76, 0x5d, 0x40, 0xc8, 0xfc, 0x95, 0x2f, 0xcd,
	0x3f, 0x01, 0xc0, 0x47, 0xb9, 0x94, 0x39, 0xbb, 0x0b, 0xbb, 0x49, 0x55, 0xb9, 0xf3, 0xad, 0x6b,
	0x2f, 0xa9, 0xaa, 0xd3, 0x8c, 0x7d, 0x08, 0xc3, 0xb5, 0xcc, 0x6a, 0x81, 0xd6, 0xf2, 0xd1, 0x3c,
	0x58, 0x0c, 0x8f, 0xfb, 0x19, 0x5e, 0x26, 0xb5, 0x30, 0xd1, 0xc0, 0x59, 0x4e, 0x33, 0x9b, 0xc0,
	0x3b, 0x54, 0xba, 0x90, 0xa5, 0x75, 0xeb, 0xd0, 0x06, 0x43, 0x8f, 0x38, 0xf3, 0x46, 0x7e, 0x36,
	0x94, 0xcd, 0xfc, 0xd8, 0x27, 0xb0, 0x2b, 0xa9, 0x10, 0xfc, 0xe9, 0x3c, 0x58, 0x8c, 0x3e, 0x3f,
	0xd8, 0xe8, 0x47, 0x5b, 0xa4, 0xc8, 0xfb, 0xb0, 0x3d, 0xe8, 0x14, 0x15, 0xdf, 0xa1, 0x33, 0x3a,
	0x45, 0xc5, 0x1e, 0xc0, 0xa0, 0x2c, 0xd2, 0xb7, 0x65, 0xb2, 0x46, 0xde, 0xb3, 0x01, 0x46, 0xed,
	0xda, 0x1e, 0xac, 0x4d, 0xa2, 0x4c, 0x4c, 0x45, 0xdb, 0xa5, 0xa2, 0x0d, 0x09, 0xb9, 0xb0, 0x95,
	0xbb, 0x0f, 0x03, 0x2c, 0x33, 0x67, 0xec, 0x93, 0xb1, 0x8f, 0x65, 0x46, 0x26, 0x0e, 0x7d, 0x91,
	0x18, 0x2c, 0xd3, 0x6b, 0x3e, 0x70, 0x16, 0xbf, 0x24, 0xb2, 0xa5, 0xd7, 0xa9, 0x40, 0xcd, 0x87,
	0xce, 0xe2, 0x97, 0xb6, 0xd7, 0x6b, 0x34, 0x2b, 0x99, 0x71, 0x70, 0xbd, 0x76, 0x2b, 0x1b, 0xa1,
	0x42, 0x2d, 0x6b, 0x95, 0x22, 0x1f, 0x91, 0xa5, 0x5d, 0xb3, 0x27, 0x30, 0x5e, 0x19, 0x53, 0xc5,
	0xbe, 0x58, 0x7c, 0x4c, 0xf6, 0x91, 0xc5, 0xbe, 0x77, 0xd0, 0x06, 0x85, 0x26, 0xd4, 0x60, 0xbf,
	0x62, 0x4f, 0x61, 0xa2, 0x50, 0x57, 0xb2, 0xd4, 0x18, 0xeb, 0xe2, 0x27, 0xe4, 0x7b, 0x14, 0xce,
	0xb8, 0x01, 0xcf, 0x8b, 0x9f, 0xd0, 0x9d, 0x7d, 0x89, 0x4a, 0xa1, 0xe2, 0x53, 0x57, 0x9d, 0x66,
	0x6d, 0xab, 0x53, 0x6b, 0x54, 0x71, 0x92, 0x63, 0x69, 0xf8, 0x8c, 0xac, 0x43, 0x8b, 0xbc, 0xb0,
	0x00, 0x0b, 0x61, 0x52, 0x2b, 0x11, 0xaf, 0x93, 0x2a, 0xc6, 0xd2, 0xa8, 0x6b, 0xbe, 0xef, 0x62,
	0xab, 0x95, 0x78, 0x95, 0x54, 0x27, 0x16, 0xb2, 0xdb, 0xa7, 0x72, 0xfd, 0x8f, 0xa2, 0xc4, 0x8c,
	0x33, 0x97, 0x5a, 0xb3, 0xb6, 0x0c, 0x4c, 0xaa, 0x22, 0x6e, 0x8a, 0x75, 0x67, 0x1e, 0x2c, 0xba,
	0x11, 0x24, 0x55, 0xf1, 0xca, 0xd7, 0x8b, 0xc1, 0xce, 0x4a, 0x6a, 0xc3, 0x0f, 0xe8, 0x64, 0x7a,
	0xb6, 0x58, 0x6a, 0xb1, 0xbb, 0xf3, 0x60, 0x11, 0x44, 0xf4, 0xcc, 0x9e, 0xc1, 0xd4, 0x24, 0xfa,
	0x6d, 0xfc, 0x43, 0x8d, 0x35, 0xc6, 0xd4, 0xe8, 0x7b, 0xf4, 0xca, 0xc4, 0xc2, 0xdf, 0x59, 0xf4,
	0xb5, 0xed, 0xf6, 0x43, 0x18, 0x92, 0x1f, 0x79, 0x7c, 0xe0, 0x92, 0xb5, 0x00, 0x19, 0x0f, 0xe1,
	0xce, 0x8f, 0x89, 0x8e, 0x85, 0x4c, 0xb2, 0xa2, 0xcc, 0x63, 0xcf, 0x3e, 0xce, 0xe7, 0xc1, 0x62,
	0x10, 0xed, 0xff, 0x98, 0xe8, 0xa5, 0xb3, 0x34, 0x83, 0xfb, 0x04, 0xc6, 0x15, 0x96, 0xe4, 0x4b,
	0xfc, 0xb8, 0x4f, 0xe1, 0x8f, 0x3c, 0x46, 0x1c, 0xf9, 0xd8, 0x36, 0xa0, 0x12, 0x45, 0x9a, 0xc4,
	0x45, 0x99, 0xe1, 0x15, 0x7f, 0x30, 0x0f, 0x16, 0xbd, 0xe3, 0xce, 0xa7, 0x9f, 0xd9, 0x26, 0x90,
	0xe1, 0xd4, 0xe2, 0x6c, 0x0e, 0x83, 0xcb, 0xa2, 0x2c, 0xf4, 0x0a, 0x33, 0xfe, 0xd0, 0x1e, 0x78,
	0xbc, 0x63, 0x54, 0x8d, 0x51, 0x8b, 0xda, 0xd0, 0x53, 0x21, 0x4b, 0x8c, 0xdf, 0xe2, 0x35, 0xff,
	0x3d, 0x09, 0xc0, 0x80, 0x80, 0x6f, 0xf1, 0x9a, 0x3d, 0x83, 0x1d, 0x52, 0xab, 0x47, 0xa4, 0x56,
	0x6c, 0x7b, 0x3a, 0x48, 0xa6, 0xc8, 0xce, 0xfe, 0x08, 0x33, 0xfb, 0xaf, 0xe3, 0xa2, 0x4c, 0xe5,
	0xba, 0x12, 0x68, 0x90, 0x7f, 0x48, 0xf9, 0x4d, 0x09, 0x3f, 0x6d, 0x61, 0xf6, 0x09, 0x30, 0x3b,
	0xed, 0x6e, 0x9b, 0x58, 0xa1, 0xc0, 0x44, 0x23, 0x7f, 0x46, 0x07, 0xcf, 0x92, 0xaa, 0x3a, 0x21,
	0x43, 0xe4, 0x70, 0xdb, 0x49, 0xbc, 0x2a, 0x4c, 0xac, 0x30, 0xd1, 0xb2, 0xe4, 0x7f, 0xb0, 0x69,
	0x46, 0x60, 0xa1, 0x88, 0x10, 0xf6, 0x05, 0xdc, 0xb3, 0xc5, 0x35, 0x2b, 0x25, 0x8d, 0x11, 0x98,
	0xc5, 0x97, 0x52, 0xb9, 0xb2, 0x3d, 0xa6, 0xf3, 0x6d, 0xe9, 0x2f, 0x1a, 0xe3, 0xd7, 0x52, 0x51,
	0xf9, 0xbe, 0x84, 0x07, 0x3f, 0x7f, 0xc9, 0xf7, 0x45, 0xf3, 0x39, 0xbd, 0xf8, 0xc1, 0xad, 0x17,
	0x7d, 0x77, 0x34, 0xdd, 0x17, 0xed, 0x8b, 0x74, 0xd2, 0x13, 0x6a, 0xd0, 0xa4, 0x45, 0xe9, 0x8c,
	0xc7, 0x30, 0xb2, 0x97, 0x1a, 0x2a, 0x47, 0x8a, 0x90, 0x12, 0x04, 0x07, 0x59, 0x5a, 0x84, 0x7f,
	0x83, 0xd9, 0x52, 0xe6, 0xaf, 0x48, 0xc8, 0x9a, 0x81, 0xdb, 0xd2, 0xbc, 0xe0, 0x7d, 0x35, 0x2f,
	0xd8, 0xd2, 0xbc, 0xf0, 0xbf, 0x3d, 0xd8, 0x5b, 0xca, 0x3c, 0xc2, 0x24, 0x6b, 0x28, 0xf5, 0x0b,
	0x12, 0x7b, 0x7b, 0xa3, 0xee, 0xb6, 0x78, 0x7e, 0x05, 0x7b, 0x3e, 0x9a, 0x46, 0x23, 0xee, 0x10,
	0x0f, 0x1e, 0x6e, 0xf3, 0x60, 0x2b, 0x85, 0x68, 0xb2, 0xde, 0xca, 0x68, 0x5b, 0x07, 0xbb, 0x54,
	0xa9, 0x5f, 0xd0, 0xc1, 0x1d, 0x32, 0xb6, 0x3a, 0x78, 0xa3, 0xcd, 0xbd, 0xf7, 0xd0, 0xe6, 0x6d,
	0xa1, 0xdf, 0x9d, 0x77, 0xb7, 0x85, 0xfe, 0x39, 0xec, 0xaf, 0x8b, 0xb2, 0x58, 0xd7, 0xeb, 0x98,
	0xae, 0x60, 0xba, 0xb5, 0xfa, 0xc4, 0xa6, 0xa9, 0x37, 0x58, 0x46, 0xd3, 0xfd, 0xf5, 0x29, 0xb0,
	0xa2, 0x4c, 0x45, 0x9d, 0xe1, 0x26, 0x9d, 0x07, 0x6e, 0x5c, 0xbd, 0x65, 0x83, 0xd0, 0x07, 0xd0,
	0x4b, 0x65, 0x5d, 0x1a, 0x3e, 0xa4, 0xf8, 0xdd, 0xc2, 0xd2, 0xbc, 0x91, 0x23, 0x3a, 0x51, 0x61,
	0x8e, 0x57, 0x7c, 0x8f, 0x7a, 0x35, 0x6b, 0x2c, 0xd4, 0xa5, 0x1c, 0xaf, 0x6c, 0xf4, 0x56, 0x83,
	0xbc, 0x97, 0x53, 0xcb, 0xa1, 0x45, 0x9c, 0xf9, 0xe9, 0xed, 0x71, 0x9f, 0x51, 0xe4, 0xdb, 0xa3,
	0xbe, 0x80, 0x59, 0x13, 0xb6, 0xed, 0x35, 0x7d, 0x23, 0x00, 0x05, 0xbd, 0xe7, 0x71, 0xf7, 0x75,
	0xa1, 0xd9, 0x11, 0x1c, 0x34, 0x1e, 0x71, 0x85, 0x2d, 0xf3, 0xf9, 0x3e, 0xed, 0xba, 0x9f, 0x38,
	0xb7, 0xbf, 0xa2, 0xda, 0x50, 0xa4, 0x66, 0x6b, 0x92, 0xcd, 0x11, 0x6d, 0x3b, 0xf2, 0xd8, 0x37,
	0x56, 0x29, 0x1f, 0xc3, 0xa8, 0x3d, 0x5d, 0x08, 0x3e, 0x26, 0x0f, 0x68, 0x0e, 0x16, 0xc2, 0x8e,
	0x4d, 0x9a, 0xa4, 0x2b, 0x8c, 0x0b, 0x83, 0x2a, 0x31, 0x52, 0xf1, 0x09, 0xf9, 0x4c, 0x08, 0x3d,
	0xf5, 0xa0, 0xad, 0x44, 0x59, 0xaf, 0x63, 0xbd, 0x4a, 0x54, 0xa6, 0x39, 0xa3, 0x88, 0x86, 0x65,
	0xbd, 0x3e, 0x27, 0x20, 0xfc, 0x57, 0x40, 0xdf, 0x83, 0x8e, 0xdb, 0xee, 0xb2, 0x61, 0x1f, 0x43,
	0x57, 0xc8, 0x9c, 0x07, 0xc4, 0xcd, 0xbb, 0x1b, 0x2c, 0xb9, 0xf9, 0xc6, 0x88, 0xac, 0xc7, 0x06,
	0xa3, 0x3a, 0xef, 0xc1, 0xa8, 0x10, 0x26, 0x22, 0xd1, 0x26, 0x6e, 0xf9, 0xe9, 0xc8, 0x3b, 0xb2,
	0xe0, 0x89, 0xe3, 0x68, 0xf8, 0x9f, 0x80, 0x46, 0xed, 0x8d, 0xfd, 0xac, 0x89, 0x30, 0x95, 0xea,
	0xf6, 0x4c, 0x05, 0xb7, 0x86, 0xf3, 0xd6, 0x3c, 0x74, 0x5c, 0x7e, 0xff, 0x7f, 0x1e, 0xba, 0x64,
	0x6c, 0xe7, 0xa1, 0xe5, 0xd9, 0xce, 0x26, 0xcf, 0x1e, 0x01, 0x18, 0x69, 0x12, 0xe1, 0xee, 0xe1,
	0x9e, 0x9b, 0x2f, 0x42, 0xe8, 0x12, 0xe6, 0xd0, 0x57, 0x14, 0x97, 0xe6, 0xbb, 0x6e, 0x3b, 0xbf,
	0x0c, 0xff, 0xdd, 0xa1, 0x4a, 0xfa, 0xd0, 0x7f, 0x8b, 0x4c, 0xfc, 0x7c, 0xc4, 0x7b, 0xbf, 0x36,
	0xe2, 0xbd, 0xcd, 0x11, 0x9f, 0xd9, 0xcf, 0x11, 0x51, 0x1b, 0xbb, 0xf7, 0x4a, 0xd6, 0x4a, 0x53,
	0x0a, 0x93, 0xe3, 0xe0, 0xb3, 0x68, 0x7a, 0x63, 0xfa, 0xc6, 0x5a, 0xec, 0x25, 0xe3, 0x07, 0xa7,
	0xd1, 0x23, 0x97, 0xd4, 0x20, 0x9a, 0x7a, 0xdc, 0x8b, 0x0e, 0x7d, 0xa0, 0xd4, 0x36, 0xb1, 0x56,
	0xb8, 0xdc, 0xa8, 0x8f, 0x09, 0x6c, 0xa4, 0xe9, 0x29, 0x4c, 0x9a, 0x7d, 0x62, 0x59, 0x8a, 0x6b,
	0x3f, 0xe2, 0xe3, 0x06, 0x3c, 0x2b, 0xc5, 0x75, 0x78, 0x45, 0x2a, 0xed, 0xab, 0xe4, 0x09, 0x77,
	0x04, 0x3d, 0xda, 0xc8, 0x53, 0xee, 0xfe, 0x36, 0x8d, 0x36, 0xc8, 0x10, 0x39, 0x3f, 0xf6, 0x05,
	0xf4, 0x75, 0xbd, 0x5e, 0x27, 0xea, 0xda, 0x33, 0xef, 0x57, 0x5e, 0x69, 0x3c, 0xbf, 0xea, 0xfd,
	0xdd, 0x92, 0xf6, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x70, 0xd9, 0xa0, 0xf8, 0x48, 0x0d, 0x00,
	0x00,
}

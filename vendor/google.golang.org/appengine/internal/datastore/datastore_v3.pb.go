// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google.golang.org/appengine/internal/datastore/datastore_v3.proto

package datastore

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Property_Meaning int32

const (
	Property_NO_MEANING       Property_Meaning = 0
	Property_BLOB             Property_Meaning = 14
	Property_TEXT             Property_Meaning = 15
	Property_BYTESTRING       Property_Meaning = 16
	Property_ATOM_CATEGORY    Property_Meaning = 1
	Property_ATOM_LINK        Property_Meaning = 2
	Property_ATOM_TITLE       Property_Meaning = 3
	Property_ATOM_CONTENT     Property_Meaning = 4
	Property_ATOM_SUMMARY     Property_Meaning = 5
	Property_ATOM_AUTHOR      Property_Meaning = 6
	Property_GD_WHEN          Property_Meaning = 7
	Property_GD_EMAIL         Property_Meaning = 8
	Property_GEORSS_POINT     Property_Meaning = 9
	Property_GD_IM            Property_Meaning = 10
	Property_GD_PHONENUMBER   Property_Meaning = 11
	Property_GD_POSTALADDRESS Property_Meaning = 12
	Property_GD_RATING        Property_Meaning = 13
	Property_BLOBKEY          Property_Meaning = 17
	Property_ENTITY_PROTO     Property_Meaning = 19
	Property_INDEX_VALUE      Property_Meaning = 18
)

var Property_Meaning_name = map[int32]string{
	0:  "NO_MEANING",
	14: "BLOB",
	15: "TEXT",
	16: "BYTESTRING",
	1:  "ATOM_CATEGORY",
	2:  "ATOM_LINK",
	3:  "ATOM_TITLE",
	4:  "ATOM_CONTENT",
	5:  "ATOM_SUMMARY",
	6:  "ATOM_AUTHOR",
	7:  "GD_WHEN",
	8:  "GD_EMAIL",
	9:  "GEORSS_POINT",
	10: "GD_IM",
	11: "GD_PHONENUMBER",
	12: "GD_POSTALADDRESS",
	13: "GD_RATING",
	17: "BLOBKEY",
	19: "ENTITY_PROTO",
	18: "INDEX_VALUE",
}
var Property_Meaning_value = map[string]int32{
	"NO_MEANING":       0,
	"BLOB":             14,
	"TEXT":             15,
	"BYTESTRING":       16,
	"ATOM_CATEGORY":    1,
	"ATOM_LINK":        2,
	"ATOM_TITLE":       3,
	"ATOM_CONTENT":     4,
	"ATOM_SUMMARY":     5,
	"ATOM_AUTHOR":      6,
	"GD_WHEN":          7,
	"GD_EMAIL":         8,
	"GEORSS_POINT":     9,
	"GD_IM":            10,
	"GD_PHONENUMBER":   11,
	"GD_POSTALADDRESS": 12,
	"GD_RATING":        13,
	"BLOBKEY":          17,
	"ENTITY_PROTO":     19,
	"INDEX_VALUE":      18,
}

func (x Property_Meaning) Enum() *Property_Meaning {
	p := new(Property_Meaning)
	*p = x
	return p
}
func (x Property_Meaning) String() string {
	return proto.EnumName(Property_Meaning_name, int32(x))
}
func (x *Property_Meaning) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Property_Meaning_value, data, "Property_Meaning")
	if err != nil {
		return err
	}
	*x = Property_Meaning(value)
	return nil
}
func (Property_Meaning) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{2, 0}
}

type Property_FtsTokenizationOption int32

const (
	Property_HTML Property_FtsTokenizationOption = 1
	Property_ATOM Property_FtsTokenizationOption = 2
)

var Property_FtsTokenizationOption_name = map[int32]string{
	1: "HTML",
	2: "ATOM",
}
var Property_FtsTokenizationOption_value = map[string]int32{
	"HTML": 1,
	"ATOM": 2,
}

func (x Property_FtsTokenizationOption) Enum() *Property_FtsTokenizationOption {
	p := new(Property_FtsTokenizationOption)
	*p = x
	return p
}
func (x Property_FtsTokenizationOption) String() string {
	return proto.EnumName(Property_FtsTokenizationOption_name, int32(x))
}
func (x *Property_FtsTokenizationOption) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Property_FtsTokenizationOption_value, data, "Property_FtsTokenizationOption")
	if err != nil {
		return err
	}
	*x = Property_FtsTokenizationOption(value)
	return nil
}
func (Property_FtsTokenizationOption) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{2, 1}
}

type EntityProto_Kind int32

const (
	EntityProto_GD_CONTACT EntityProto_Kind = 1
	EntityProto_GD_EVENT   EntityProto_Kind = 2
	EntityProto_GD_MESSAGE EntityProto_Kind = 3
)

var EntityProto_Kind_name = map[int32]string{
	1: "GD_CONTACT",
	2: "GD_EVENT",
	3: "GD_MESSAGE",
}
var EntityProto_Kind_value = map[string]int32{
	"GD_CONTACT": 1,
	"GD_EVENT":   2,
	"GD_MESSAGE": 3,
}

func (x EntityProto_Kind) Enum() *EntityProto_Kind {
	p := new(EntityProto_Kind)
	*p = x
	return p
}
func (x EntityProto_Kind) String() string {
	return proto.EnumName(EntityProto_Kind_name, int32(x))
}
func (x *EntityProto_Kind) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EntityProto_Kind_value, data, "EntityProto_Kind")
	if err != nil {
		return err
	}
	*x = EntityProto_Kind(value)
	return nil
}
func (EntityProto_Kind) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{6, 0}
}

type Index_Property_Direction int32

const (
	Index_Property_ASCENDING  Index_Property_Direction = 1
	Index_Property_DESCENDING Index_Property_Direction = 2
)

var Index_Property_Direction_name = map[int32]string{
	1: "ASCENDING",
	2: "DESCENDING",
}
var Index_Property_Direction_value = map[string]int32{
	"ASCENDING":  1,
	"DESCENDING": 2,
}

func (x Index_Property_Direction) Enum() *Index_Property_Direction {
	p := new(Index_Property_Direction)
	*p = x
	return p
}
func (x Index_Property_Direction) String() string {
	return proto.EnumName(Index_Property_Direction_name, int32(x))
}
func (x *Index_Property_Direction) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Index_Property_Direction_value, data, "Index_Property_Direction")
	if err != nil {
		return err
	}
	*x = Index_Property_Direction(value)
	return nil
}
func (Index_Property_Direction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{8, 0, 0}
}

type CompositeIndex_State int32

const (
	CompositeIndex_WRITE_ONLY CompositeIndex_State = 1
	CompositeIndex_READ_WRITE CompositeIndex_State = 2
	CompositeIndex_DELETED    CompositeIndex_State = 3
	CompositeIndex_ERROR      CompositeIndex_State = 4
)

var CompositeIndex_State_name = map[int32]string{
	1: "WRITE_ONLY",
	2: "READ_WRITE",
	3: "DELETED",
	4: "ERROR",
}
var CompositeIndex_State_value = map[string]int32{
	"WRITE_ONLY": 1,
	"READ_WRITE": 2,
	"DELETED":    3,
	"ERROR":      4,
}

func (x CompositeIndex_State) Enum() *CompositeIndex_State {
	p := new(CompositeIndex_State)
	*p = x
	return p
}
func (x CompositeIndex_State) String() string {
	return proto.EnumName(CompositeIndex_State_name, int32(x))
}
func (x *CompositeIndex_State) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CompositeIndex_State_value, data, "CompositeIndex_State")
	if err != nil {
		return err
	}
	*x = CompositeIndex_State(value)
	return nil
}
func (CompositeIndex_State) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{9, 0}
}

type Snapshot_Status int32

const (
	Snapshot_INACTIVE Snapshot_Status = 0
	Snapshot_ACTIVE   Snapshot_Status = 1
)

var Snapshot_Status_name = map[int32]string{
	0: "INACTIVE",
	1: "ACTIVE",
}
var Snapshot_Status_value = map[string]int32{
	"INACTIVE": 0,
	"ACTIVE":   1,
}

func (x Snapshot_Status) Enum() *Snapshot_Status {
	p := new(Snapshot_Status)
	*p = x
	return p
}
func (x Snapshot_Status) String() string {
	return proto.EnumName(Snapshot_Status_name, int32(x))
}
func (x *Snapshot_Status) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Snapshot_Status_value, data, "Snapshot_Status")
	if err != nil {
		return err
	}
	*x = Snapshot_Status(value)
	return nil
}
func (Snapshot_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{12, 0}
}

type Query_Hint int32

const (
	Query_ORDER_FIRST    Query_Hint = 1
	Query_ANCESTOR_FIRST Query_Hint = 2
	Query_FILTER_FIRST   Query_Hint = 3
)

var Query_Hint_name = map[int32]string{
	1: "ORDER_FIRST",
	2: "ANCESTOR_FIRST",
	3: "FILTER_FIRST",
}
var Query_Hint_value = map[string]int32{
	"ORDER_FIRST":    1,
	"ANCESTOR_FIRST": 2,
	"FILTER_FIRST":   3,
}

func (x Query_Hint) Enum() *Query_Hint {
	p := new(Query_Hint)
	*p = x
	return p
}
func (x Query_Hint) String() string {
	return proto.EnumName(Query_Hint_name, int32(x))
}
func (x *Query_Hint) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Query_Hint_value, data, "Query_Hint")
	if err != nil {
		return err
	}
	*x = Query_Hint(value)
	return nil
}
func (Query_Hint) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15, 0}
}

type Query_Filter_Operator int32

const (
	Query_Filter_LESS_THAN             Query_Filter_Operator = 1
	Query_Filter_LESS_THAN_OR_EQUAL    Query_Filter_Operator = 2
	Query_Filter_GREATER_THAN          Query_Filter_Operator = 3
	Query_Filter_GREATER_THAN_OR_EQUAL Query_Filter_Operator = 4
	Query_Filter_EQUAL                 Query_Filter_Operator = 5
	Query_Filter_IN                    Query_Filter_Operator = 6
	Query_Filter_EXISTS                Query_Filter_Operator = 7
)

var Query_Filter_Operator_name = map[int32]string{
	1: "LESS_THAN",
	2: "LESS_THAN_OR_EQUAL",
	3: "GREATER_THAN",
	4: "GREATER_THAN_OR_EQUAL",
	5: "EQUAL",
	6: "IN",
	7: "EXISTS",
}
var Query_Filter_Operator_value = map[string]int32{
	"LESS_THAN":             1,
	"LESS_THAN_OR_EQUAL":    2,
	"GREATER_THAN":          3,
	"GREATER_THAN_OR_EQUAL": 4,
	"EQUAL":                 5,
	"IN":                    6,
	"EXISTS":                7,
}

func (x Query_Filter_Operator) Enum() *Query_Filter_Operator {
	p := new(Query_Filter_Operator)
	*p = x
	return p
}
func (x Query_Filter_Operator) String() string {
	return proto.EnumName(Query_Filter_Operator_name, int32(x))
}
func (x *Query_Filter_Operator) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Query_Filter_Operator_value, data, "Query_Filter_Operator")
	if err != nil {
		return err
	}
	*x = Query_Filter_Operator(value)
	return nil
}
func (Query_Filter_Operator) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15, 0, 0}
}

type Query_Order_Direction int32

const (
	Query_Order_ASCENDING  Query_Order_Direction = 1
	Query_Order_DESCENDING Query_Order_Direction = 2
)

var Query_Order_Direction_name = map[int32]string{
	1: "ASCENDING",
	2: "DESCENDING",
}
var Query_Order_Direction_value = map[string]int32{
	"ASCENDING":  1,
	"DESCENDING": 2,
}

func (x Query_Order_Direction) Enum() *Query_Order_Direction {
	p := new(Query_Order_Direction)
	*p = x
	return p
}
func (x Query_Order_Direction) String() string {
	return proto.EnumName(Query_Order_Direction_name, int32(x))
}
func (x *Query_Order_Direction) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Query_Order_Direction_value, data, "Query_Order_Direction")
	if err != nil {
		return err
	}
	*x = Query_Order_Direction(value)
	return nil
}
func (Query_Order_Direction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15, 1, 0}
}

type Error_ErrorCode int32

const (
	Error_BAD_REQUEST                  Error_ErrorCode = 1
	Error_CONCURRENT_TRANSACTION       Error_ErrorCode = 2
	Error_INTERNAL_ERROR               Error_ErrorCode = 3
	Error_NEED_INDEX                   Error_ErrorCode = 4
	Error_TIMEOUT                      Error_ErrorCode = 5
	Error_PERMISSION_DENIED            Error_ErrorCode = 6
	Error_BIGTABLE_ERROR               Error_ErrorCode = 7
	Error_COMMITTED_BUT_STILL_APPLYING Error_ErrorCode = 8
	Error_CAPABILITY_DISABLED          Error_ErrorCode = 9
	Error_TRY_ALTERNATE_BACKEND        Error_ErrorCode = 10
	Error_SAFE_TIME_TOO_OLD            Error_ErrorCode = 11
)

var Error_ErrorCode_name = map[int32]string{
	1:  "BAD_REQUEST",
	2:  "CONCURRENT_TRANSACTION",
	3:  "INTERNAL_ERROR",
	4:  "NEED_INDEX",
	5:  "TIMEOUT",
	6:  "PERMISSION_DENIED",
	7:  "BIGTABLE_ERROR",
	8:  "COMMITTED_BUT_STILL_APPLYING",
	9:  "CAPABILITY_DISABLED",
	10: "TRY_ALTERNATE_BACKEND",
	11: "SAFE_TIME_TOO_OLD",
}
var Error_ErrorCode_value = map[string]int32{
	"BAD_REQUEST":                  1,
	"CONCURRENT_TRANSACTION":       2,
	"INTERNAL_ERROR":               3,
	"NEED_INDEX":                   4,
	"TIMEOUT":                      5,
	"PERMISSION_DENIED":            6,
	"BIGTABLE_ERROR":               7,
	"COMMITTED_BUT_STILL_APPLYING": 8,
	"CAPABILITY_DISABLED":          9,
	"TRY_ALTERNATE_BACKEND":        10,
	"SAFE_TIME_TOO_OLD":            11,
}

func (x Error_ErrorCode) Enum() *Error_ErrorCode {
	p := new(Error_ErrorCode)
	*p = x
	return p
}
func (x Error_ErrorCode) String() string {
	return proto.EnumName(Error_ErrorCode_name, int32(x))
}
func (x *Error_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Error_ErrorCode_value, data, "Error_ErrorCode")
	if err != nil {
		return err
	}
	*x = Error_ErrorCode(value)
	return nil
}
func (Error_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{19, 0}
}

type PutRequest_AutoIdPolicy int32

const (
	PutRequest_CURRENT    PutRequest_AutoIdPolicy = 0
	PutRequest_SEQUENTIAL PutRequest_AutoIdPolicy = 1
)

var PutRequest_AutoIdPolicy_name = map[int32]string{
	0: "CURRENT",
	1: "SEQUENTIAL",
}
var PutRequest_AutoIdPolicy_value = map[string]int32{
	"CURRENT":    0,
	"SEQUENTIAL": 1,
}

func (x PutRequest_AutoIdPolicy) Enum() *PutRequest_AutoIdPolicy {
	p := new(PutRequest_AutoIdPolicy)
	*p = x
	return p
}
func (x PutRequest_AutoIdPolicy) String() string {
	return proto.EnumName(PutRequest_AutoIdPolicy_name, int32(x))
}
func (x *PutRequest_AutoIdPolicy) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PutRequest_AutoIdPolicy_value, data, "PutRequest_AutoIdPolicy")
	if err != nil {
		return err
	}
	*x = PutRequest_AutoIdPolicy(value)
	return nil
}
func (PutRequest_AutoIdPolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{23, 0}
}

type BeginTransactionRequest_TransactionMode int32

const (
	BeginTransactionRequest_UNKNOWN    BeginTransactionRequest_TransactionMode = 0
	BeginTransactionRequest_READ_ONLY  BeginTransactionRequest_TransactionMode = 1
	BeginTransactionRequest_READ_WRITE BeginTransactionRequest_TransactionMode = 2
)

var BeginTransactionRequest_TransactionMode_name = map[int32]string{
	0: "UNKNOWN",
	1: "READ_ONLY",
	2: "READ_WRITE",
}
var BeginTransactionRequest_TransactionMode_value = map[string]int32{
	"UNKNOWN":    0,
	"READ_ONLY":  1,
	"READ_WRITE": 2,
}

func (x BeginTransactionRequest_TransactionMode) Enum() *BeginTransactionRequest_TransactionMode {
	p := new(BeginTransactionRequest_TransactionMode)
	*p = x
	return p
}
func (x BeginTransactionRequest_TransactionMode) String() string {
	return proto.EnumName(BeginTransactionRequest_TransactionMode_name, int32(x))
}
func (x *BeginTransactionRequest_TransactionMode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(BeginTransactionRequest_TransactionMode_value, data, "BeginTransactionRequest_TransactionMode")
	if err != nil {
		return err
	}
	*x = BeginTransactionRequest_TransactionMode(value)
	return nil
}
func (BeginTransactionRequest_TransactionMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{36, 0}
}

type Action struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Action) Reset()         { *m = Action{} }
func (m *Action) String() string { return proto.CompactTextString(m) }
func (*Action) ProtoMessage()    {}
func (*Action) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{0}
}
func (m *Action) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Action.Unmarshal(m, b)
}
func (m *Action) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Action.Marshal(b, m, deterministic)
}
func (dst *Action) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Action.Merge(dst, src)
}
func (m *Action) XXX_Size() int {
	return xxx_messageInfo_Action.Size(m)
}
func (m *Action) XXX_DiscardUnknown() {
	xxx_messageInfo_Action.DiscardUnknown(m)
}

var xxx_messageInfo_Action proto.InternalMessageInfo

type PropertyValue struct {
	Int64Value           *int64                        `protobuf:"varint,1,opt,name=int64Value" json:"int64Value,omitempty"`
	BooleanValue         *bool                         `protobuf:"varint,2,opt,name=booleanValue" json:"booleanValue,omitempty"`
	StringValue          *string                       `protobuf:"bytes,3,opt,name=stringValue" json:"stringValue,omitempty"`
	DoubleValue          *float64                      `protobuf:"fixed64,4,opt,name=doubleValue" json:"doubleValue,omitempty"`
	Pointvalue           *PropertyValue_PointValue     `protobuf:"group,5,opt,name=PointValue,json=pointvalue" json:"pointvalue,omitempty"`
	Uservalue            *PropertyValue_UserValue      `protobuf:"group,8,opt,name=UserValue,json=uservalue" json:"uservalue,omitempty"`
	Referencevalue       *PropertyValue_ReferenceValue `protobuf:"group,12,opt,name=ReferenceValue,json=referencevalue" json:"referencevalue,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *PropertyValue) Reset()         { *m = PropertyValue{} }
func (m *PropertyValue) String() string { return proto.CompactTextString(m) }
func (*PropertyValue) ProtoMessage()    {}
func (*PropertyValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{1}
}
func (m *PropertyValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropertyValue.Unmarshal(m, b)
}
func (m *PropertyValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropertyValue.Marshal(b, m, deterministic)
}
func (dst *PropertyValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropertyValue.Merge(dst, src)
}
func (m *PropertyValue) XXX_Size() int {
	return xxx_messageInfo_PropertyValue.Size(m)
}
func (m *PropertyValue) XXX_DiscardUnknown() {
	xxx_messageInfo_PropertyValue.DiscardUnknown(m)
}

var xxx_messageInfo_PropertyValue proto.InternalMessageInfo

func (m *PropertyValue) GetInt64Value() int64 {
	if m != nil && m.Int64Value != nil {
		return *m.Int64Value
	}
	return 0
}

func (m *PropertyValue) GetBooleanValue() bool {
	if m != nil && m.BooleanValue != nil {
		return *m.BooleanValue
	}
	return false
}

func (m *PropertyValue) GetStringValue() string {
	if m != nil && m.StringValue != nil {
		return *m.StringValue
	}
	return ""
}

func (m *PropertyValue) GetDoubleValue() float64 {
	if m != nil && m.DoubleValue != nil {
		return *m.DoubleValue
	}
	return 0
}

func (m *PropertyValue) GetPointvalue() *PropertyValue_PointValue {
	if m != nil {
		return m.Pointvalue
	}
	return nil
}

func (m *PropertyValue) GetUservalue() *PropertyValue_UserValue {
	if m != nil {
		return m.Uservalue
	}
	return nil
}

func (m *PropertyValue) GetReferencevalue() *PropertyValue_ReferenceValue {
	if m != nil {
		return m.Referencevalue
	}
	return nil
}

type PropertyValue_PointValue struct {
	X                    *float64 `protobuf:"fixed64,6,req,name=x" json:"x,omitempty"`
	Y                    *float64 `protobuf:"fixed64,7,req,name=y" json:"y,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropertyValue_PointValue) Reset()         { *m = PropertyValue_PointValue{} }
func (m *PropertyValue_PointValue) String() string { return proto.CompactTextString(m) }
func (*PropertyValue_PointValue) ProtoMessage()    {}
func (*PropertyValue_PointValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{1, 0}
}
func (m *PropertyValue_PointValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropertyValue_PointValue.Unmarshal(m, b)
}
func (m *PropertyValue_PointValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropertyValue_PointValue.Marshal(b, m, deterministic)
}
func (dst *PropertyValue_PointValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropertyValue_PointValue.Merge(dst, src)
}
func (m *PropertyValue_PointValue) XXX_Size() int {
	return xxx_messageInfo_PropertyValue_PointValue.Size(m)
}
func (m *PropertyValue_PointValue) XXX_DiscardUnknown() {
	xxx_messageInfo_PropertyValue_PointValue.DiscardUnknown(m)
}

var xxx_messageInfo_PropertyValue_PointValue proto.InternalMessageInfo

func (m *PropertyValue_PointValue) GetX() float64 {
	if m != nil && m.X != nil {
		return *m.X
	}
	return 0
}

func (m *PropertyValue_PointValue) GetY() float64 {
	if m != nil && m.Y != nil {
		return *m.Y
	}
	return 0
}

type PropertyValue_UserValue struct {
	Email                *string  `protobuf:"bytes,9,req,name=email" json:"email,omitempty"`
	AuthDomain           *string  `protobuf:"bytes,10,req,name=auth_domain,json=authDomain" json:"auth_domain,omitempty"`
	Nickname             *string  `protobuf:"bytes,11,opt,name=nickname" json:"nickname,omitempty"`
	FederatedIdentity    *string  `protobuf:"bytes,21,opt,name=federated_identity,json=federatedIdentity" json:"federated_identity,omitempty"`
	FederatedProvider    *string  `protobuf:"bytes,22,opt,name=federated_provider,json=federatedProvider" json:"federated_provider,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropertyValue_UserValue) Reset()         { *m = PropertyValue_UserValue{} }
func (m *PropertyValue_UserValue) String() string { return proto.CompactTextString(m) }
func (*PropertyValue_UserValue) ProtoMessage()    {}
func (*PropertyValue_UserValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{1, 1}
}
func (m *PropertyValue_UserValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropertyValue_UserValue.Unmarshal(m, b)
}
func (m *PropertyValue_UserValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropertyValue_UserValue.Marshal(b, m, deterministic)
}
func (dst *PropertyValue_UserValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropertyValue_UserValue.Merge(dst, src)
}
func (m *PropertyValue_UserValue) XXX_Size() int {
	return xxx_messageInfo_PropertyValue_UserValue.Size(m)
}
func (m *PropertyValue_UserValue) XXX_DiscardUnknown() {
	xxx_messageInfo_PropertyValue_UserValue.DiscardUnknown(m)
}

var xxx_messageInfo_PropertyValue_UserValue proto.InternalMessageInfo

func (m *PropertyValue_UserValue) GetEmail() string {
	if m != nil && m.Email != nil {
		return *m.Email
	}
	return ""
}

func (m *PropertyValue_UserValue) GetAuthDomain() string {
	if m != nil && m.AuthDomain != nil {
		return *m.AuthDomain
	}
	return ""
}

func (m *PropertyValue_UserValue) GetNickname() string {
	if m != nil && m.Nickname != nil {
		return *m.Nickname
	}
	return ""
}

func (m *PropertyValue_UserValue) GetFederatedIdentity() string {
	if m != nil && m.FederatedIdentity != nil {
		return *m.FederatedIdentity
	}
	return ""
}

func (m *PropertyValue_UserValue) GetFederatedProvider() string {
	if m != nil && m.FederatedProvider != nil {
		return *m.FederatedProvider
	}
	return ""
}

type PropertyValue_ReferenceValue struct {
	App                  *string                                     `protobuf:"bytes,13,req,name=app" json:"app,omitempty"`
	NameSpace            *string                                     `protobuf:"bytes,20,opt,name=name_space,json=nameSpace" json:"name_space,omitempty"`
	Pathelement          []*PropertyValue_ReferenceValue_PathElement `protobuf:"group,14,rep,name=PathElement,json=pathelement" json:"pathelement,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *PropertyValue_ReferenceValue) Reset()         { *m = PropertyValue_ReferenceValue{} }
func (m *PropertyValue_ReferenceValue) String() string { return proto.CompactTextString(m) }
func (*PropertyValue_ReferenceValue) ProtoMessage()    {}
func (*PropertyValue_ReferenceValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{1, 2}
}
func (m *PropertyValue_ReferenceValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropertyValue_ReferenceValue.Unmarshal(m, b)
}
func (m *PropertyValue_ReferenceValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropertyValue_ReferenceValue.Marshal(b, m, deterministic)
}
func (dst *PropertyValue_ReferenceValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropertyValue_ReferenceValue.Merge(dst, src)
}
func (m *PropertyValue_ReferenceValue) XXX_Size() int {
	return xxx_messageInfo_PropertyValue_ReferenceValue.Size(m)
}
func (m *PropertyValue_ReferenceValue) XXX_DiscardUnknown() {
	xxx_messageInfo_PropertyValue_ReferenceValue.DiscardUnknown(m)
}

var xxx_messageInfo_PropertyValue_ReferenceValue proto.InternalMessageInfo

func (m *PropertyValue_ReferenceValue) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

func (m *PropertyValue_ReferenceValue) GetNameSpace() string {
	if m != nil && m.NameSpace != nil {
		return *m.NameSpace
	}
	return ""
}

func (m *PropertyValue_ReferenceValue) GetPathelement() []*PropertyValue_ReferenceValue_PathElement {
	if m != nil {
		return m.Pathelement
	}
	return nil
}

type PropertyValue_ReferenceValue_PathElement struct {
	Type                 *string  `protobuf:"bytes,15,req,name=type" json:"type,omitempty"`
	Id                   *int64   `protobuf:"varint,16,opt,name=id" json:"id,omitempty"`
	Name                 *string  `protobuf:"bytes,17,opt,name=name" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropertyValue_ReferenceValue_PathElement) Reset() {
	*m = PropertyValue_ReferenceValue_PathElement{}
}
func (m *PropertyValue_ReferenceValue_PathElement) String() string { return proto.CompactTextString(m) }
func (*PropertyValue_ReferenceValue_PathElement) ProtoMessage()    {}
func (*PropertyValue_ReferenceValue_PathElement) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{1, 2, 0}
}
func (m *PropertyValue_ReferenceValue_PathElement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropertyValue_ReferenceValue_PathElement.Unmarshal(m, b)
}
func (m *PropertyValue_ReferenceValue_PathElement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropertyValue_ReferenceValue_PathElement.Marshal(b, m, deterministic)
}
func (dst *PropertyValue_ReferenceValue_PathElement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropertyValue_ReferenceValue_PathElement.Merge(dst, src)
}
func (m *PropertyValue_ReferenceValue_PathElement) XXX_Size() int {
	return xxx_messageInfo_PropertyValue_ReferenceValue_PathElement.Size(m)
}
func (m *PropertyValue_ReferenceValue_PathElement) XXX_DiscardUnknown() {
	xxx_messageInfo_PropertyValue_ReferenceValue_PathElement.DiscardUnknown(m)
}

var xxx_messageInfo_PropertyValue_ReferenceValue_PathElement proto.InternalMessageInfo

func (m *PropertyValue_ReferenceValue_PathElement) GetType() string {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return ""
}

func (m *PropertyValue_ReferenceValue_PathElement) GetId() int64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *PropertyValue_ReferenceValue_PathElement) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

type Property struct {
	Meaning               *Property_Meaning               `protobuf:"varint,1,opt,name=meaning,enum=appengine.Property_Meaning,def=0" json:"meaning,omitempty"`
	MeaningUri            *string                         `protobuf:"bytes,2,opt,name=meaning_uri,json=meaningUri" json:"meaning_uri,omitempty"`
	Name                  *string                         `protobuf:"bytes,3,req,name=name" json:"name,omitempty"`
	Value                 *PropertyValue                  `protobuf:"bytes,5,req,name=value" json:"value,omitempty"`
	Multiple              *bool                           `protobuf:"varint,4,req,name=multiple" json:"multiple,omitempty"`
	Searchable            *bool                           `protobuf:"varint,6,opt,name=searchable,def=0" json:"searchable,omitempty"`
	FtsTokenizationOption *Property_FtsTokenizationOption `protobuf:"varint,8,opt,name=fts_tokenization_option,json=ftsTokenizationOption,enum=appengine.Property_FtsTokenizationOption" json:"fts_tokenization_option,omitempty"`
	Locale                *string                         `protobuf:"bytes,9,opt,name=locale,def=en" json:"locale,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                        `json:"-"`
	XXX_unrecognized      []byte                          `json:"-"`
	XXX_sizecache         int32                           `json:"-"`
}

func (m *Property) Reset()         { *m = Property{} }
func (m *Property) String() string { return proto.CompactTextString(m) }
func (*Property) ProtoMessage()    {}
func (*Property) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{2}
}
func (m *Property) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Property.Unmarshal(m, b)
}
func (m *Property) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Property.Marshal(b, m, deterministic)
}
func (dst *Property) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Property.Merge(dst, src)
}
func (m *Property) XXX_Size() int {
	return xxx_messageInfo_Property.Size(m)
}
func (m *Property) XXX_DiscardUnknown() {
	xxx_messageInfo_Property.DiscardUnknown(m)
}

var xxx_messageInfo_Property proto.InternalMessageInfo

const Default_Property_Meaning Property_Meaning = Property_NO_MEANING
const Default_Property_Searchable bool = false
const Default_Property_Locale string = "en"

func (m *Property) GetMeaning() Property_Meaning {
	if m != nil && m.Meaning != nil {
		return *m.Meaning
	}
	return Default_Property_Meaning
}

func (m *Property) GetMeaningUri() string {
	if m != nil && m.MeaningUri != nil {
		return *m.MeaningUri
	}
	return ""
}

func (m *Property) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *Property) GetValue() *PropertyValue {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *Property) GetMultiple() bool {
	if m != nil && m.Multiple != nil {
		return *m.Multiple
	}
	return false
}

func (m *Property) GetSearchable() bool {
	if m != nil && m.Searchable != nil {
		return *m.Searchable
	}
	return Default_Property_Searchable
}

func (m *Property) GetFtsTokenizationOption() Property_FtsTokenizationOption {
	if m != nil && m.FtsTokenizationOption != nil {
		return *m.FtsTokenizationOption
	}
	return Property_HTML
}

func (m *Property) GetLocale() string {
	if m != nil && m.Locale != nil {
		return *m.Locale
	}
	return Default_Property_Locale
}

type Path struct {
	Element              []*Path_Element `protobuf:"group,1,rep,name=Element,json=element" json:"element,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Path) Reset()         { *m = Path{} }
func (m *Path) String() string { return proto.CompactTextString(m) }
func (*Path) ProtoMessage()    {}
func (*Path) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{3}
}
func (m *Path) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Path.Unmarshal(m, b)
}
func (m *Path) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Path.Marshal(b, m, deterministic)
}
func (dst *Path) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Path.Merge(dst, src)
}
func (m *Path) XXX_Size() int {
	return xxx_messageInfo_Path.Size(m)
}
func (m *Path) XXX_DiscardUnknown() {
	xxx_messageInfo_Path.DiscardUnknown(m)
}

var xxx_messageInfo_Path proto.InternalMessageInfo

func (m *Path) GetElement() []*Path_Element {
	if m != nil {
		return m.Element
	}
	return nil
}

type Path_Element struct {
	Type                 *string  `protobuf:"bytes,2,req,name=type" json:"type,omitempty"`
	Id                   *int64   `protobuf:"varint,3,opt,name=id" json:"id,omitempty"`
	Name                 *string  `protobuf:"bytes,4,opt,name=name" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Path_Element) Reset()         { *m = Path_Element{} }
func (m *Path_Element) String() string { return proto.CompactTextString(m) }
func (*Path_Element) ProtoMessage()    {}
func (*Path_Element) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{3, 0}
}
func (m *Path_Element) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Path_Element.Unmarshal(m, b)
}
func (m *Path_Element) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Path_Element.Marshal(b, m, deterministic)
}
func (dst *Path_Element) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Path_Element.Merge(dst, src)
}
func (m *Path_Element) XXX_Size() int {
	return xxx_messageInfo_Path_Element.Size(m)
}
func (m *Path_Element) XXX_DiscardUnknown() {
	xxx_messageInfo_Path_Element.DiscardUnknown(m)
}

var xxx_messageInfo_Path_Element proto.InternalMessageInfo

func (m *Path_Element) GetType() string {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return ""
}

func (m *Path_Element) GetId() int64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *Path_Element) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

type Reference struct {
	App                  *string  `protobuf:"bytes,13,req,name=app" json:"app,omitempty"`
	NameSpace            *string  `protobuf:"bytes,20,opt,name=name_space,json=nameSpace" json:"name_space,omitempty"`
	Path                 *Path    `protobuf:"bytes,14,req,name=path" json:"path,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Reference) Reset()         { *m = Reference{} }
func (m *Reference) String() string { return proto.CompactTextString(m) }
func (*Reference) ProtoMessage()    {}
func (*Reference) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{4}
}
func (m *Reference) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Reference.Unmarshal(m, b)
}
func (m *Reference) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Reference.Marshal(b, m, deterministic)
}
func (dst *Reference) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Reference.Merge(dst, src)
}
func (m *Reference) XXX_Size() int {
	return xxx_messageInfo_Reference.Size(m)
}
func (m *Reference) XXX_DiscardUnknown() {
	xxx_messageInfo_Reference.DiscardUnknown(m)
}

var xxx_messageInfo_Reference proto.InternalMessageInfo

func (m *Reference) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

func (m *Reference) GetNameSpace() string {
	if m != nil && m.NameSpace != nil {
		return *m.NameSpace
	}
	return ""
}

func (m *Reference) GetPath() *Path {
	if m != nil {
		return m.Path
	}
	return nil
}

type User struct {
	Email                *string  `protobuf:"bytes,1,req,name=email" json:"email,omitempty"`
	AuthDomain           *string  `protobuf:"bytes,2,req,name=auth_domain,json=authDomain" json:"auth_domain,omitempty"`
	Nickname             *string  `protobuf:"bytes,3,opt,name=nickname" json:"nickname,omitempty"`
	FederatedIdentity    *string  `protobuf:"bytes,6,opt,name=federated_identity,json=federatedIdentity" json:"federated_identity,omitempty"`
	FederatedProvider    *string  `protobuf:"bytes,7,opt,name=federated_provider,json=federatedProvider" json:"federated_provider,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User) Reset()         { *m = User{} }
func (m *User) String() string { return proto.CompactTextString(m) }
func (*User) ProtoMessage()    {}
func (*User) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{5}
}
func (m *User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User.Unmarshal(m, b)
}
func (m *User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User.Marshal(b, m, deterministic)
}
func (dst *User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User.Merge(dst, src)
}
func (m *User) XXX_Size() int {
	return xxx_messageInfo_User.Size(m)
}
func (m *User) XXX_DiscardUnknown() {
	xxx_messageInfo_User.DiscardUnknown(m)
}

var xxx_messageInfo_User proto.InternalMessageInfo

func (m *User) GetEmail() string {
	if m != nil && m.Email != nil {
		return *m.Email
	}
	return ""
}

func (m *User) GetAuthDomain() string {
	if m != nil && m.AuthDomain != nil {
		return *m.AuthDomain
	}
	return ""
}

func (m *User) GetNickname() string {
	if m != nil && m.Nickname != nil {
		return *m.Nickname
	}
	return ""
}

func (m *User) GetFederatedIdentity() string {
	if m != nil && m.FederatedIdentity != nil {
		return *m.FederatedIdentity
	}
	return ""
}

func (m *User) GetFederatedProvider() string {
	if m != nil && m.FederatedProvider != nil {
		return *m.FederatedProvider
	}
	return ""
}

type EntityProto struct {
	Key                  *Reference        `protobuf:"bytes,13,req,name=key" json:"key,omitempty"`
	EntityGroup          *Path             `protobuf:"bytes,16,req,name=entity_group,json=entityGroup" json:"entity_group,omitempty"`
	Owner                *User             `protobuf:"bytes,17,opt,name=owner" json:"owner,omitempty"`
	Kind                 *EntityProto_Kind `protobuf:"varint,4,opt,name=kind,enum=appengine.EntityProto_Kind" json:"kind,omitempty"`
	KindUri              *string           `protobuf:"bytes,5,opt,name=kind_uri,json=kindUri" json:"kind_uri,omitempty"`
	Property             []*Property       `protobuf:"bytes,14,rep,name=property" json:"property,omitempty"`
	RawProperty          []*Property       `protobuf:"bytes,15,rep,name=raw_property,json=rawProperty" json:"raw_property,omitempty"`
	Rank                 *int32            `protobuf:"varint,18,opt,name=rank" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *EntityProto) Reset()         { *m = EntityProto{} }
func (m *EntityProto) String() string { return proto.CompactTextString(m) }
func (*EntityProto) ProtoMessage()    {}
func (*EntityProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{6}
}
func (m *EntityProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EntityProto.Unmarshal(m, b)
}
func (m *EntityProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EntityProto.Marshal(b, m, deterministic)
}
func (dst *EntityProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EntityProto.Merge(dst, src)
}
func (m *EntityProto) XXX_Size() int {
	return xxx_messageInfo_EntityProto.Size(m)
}
func (m *EntityProto) XXX_DiscardUnknown() {
	xxx_messageInfo_EntityProto.DiscardUnknown(m)
}

var xxx_messageInfo_EntityProto proto.InternalMessageInfo

func (m *EntityProto) GetKey() *Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *EntityProto) GetEntityGroup() *Path {
	if m != nil {
		return m.EntityGroup
	}
	return nil
}

func (m *EntityProto) GetOwner() *User {
	if m != nil {
		return m.Owner
	}
	return nil
}

func (m *EntityProto) GetKind() EntityProto_Kind {
	if m != nil && m.Kind != nil {
		return *m.Kind
	}
	return EntityProto_GD_CONTACT
}

func (m *EntityProto) GetKindUri() string {
	if m != nil && m.KindUri != nil {
		return *m.KindUri
	}
	return ""
}

func (m *EntityProto) GetProperty() []*Property {
	if m != nil {
		return m.Property
	}
	return nil
}

func (m *EntityProto) GetRawProperty() []*Property {
	if m != nil {
		return m.RawProperty
	}
	return nil
}

func (m *EntityProto) GetRank() int32 {
	if m != nil && m.Rank != nil {
		return *m.Rank
	}
	return 0
}

type CompositeProperty struct {
	IndexId              *int64   `protobuf:"varint,1,req,name=index_id,json=indexId" json:"index_id,omitempty"`
	Value                []string `protobuf:"bytes,2,rep,name=value" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompositeProperty) Reset()         { *m = CompositeProperty{} }
func (m *CompositeProperty) String() string { return proto.CompactTextString(m) }
func (*CompositeProperty) ProtoMessage()    {}
func (*CompositeProperty) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{7}
}
func (m *CompositeProperty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompositeProperty.Unmarshal(m, b)
}
func (m *CompositeProperty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompositeProperty.Marshal(b, m, deterministic)
}
func (dst *CompositeProperty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompositeProperty.Merge(dst, src)
}
func (m *CompositeProperty) XXX_Size() int {
	return xxx_messageInfo_CompositeProperty.Size(m)
}
func (m *CompositeProperty) XXX_DiscardUnknown() {
	xxx_messageInfo_CompositeProperty.DiscardUnknown(m)
}

var xxx_messageInfo_CompositeProperty proto.InternalMessageInfo

func (m *CompositeProperty) GetIndexId() int64 {
	if m != nil && m.IndexId != nil {
		return *m.IndexId
	}
	return 0
}

func (m *CompositeProperty) GetValue() []string {
	if m != nil {
		return m.Value
	}
	return nil
}

type Index struct {
	EntityType           *string           `protobuf:"bytes,1,req,name=entity_type,json=entityType" json:"entity_type,omitempty"`
	Ancestor             *bool             `protobuf:"varint,5,req,name=ancestor" json:"ancestor,omitempty"`
	Property             []*Index_Property `protobuf:"group,2,rep,name=Property,json=property" json:"property,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Index) Reset()         { *m = Index{} }
func (m *Index) String() string { return proto.CompactTextString(m) }
func (*Index) ProtoMessage()    {}
func (*Index) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{8}
}
func (m *Index) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Index.Unmarshal(m, b)
}
func (m *Index) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Index.Marshal(b, m, deterministic)
}
func (dst *Index) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Index.Merge(dst, src)
}
func (m *Index) XXX_Size() int {
	return xxx_messageInfo_Index.Size(m)
}
func (m *Index) XXX_DiscardUnknown() {
	xxx_messageInfo_Index.DiscardUnknown(m)
}

var xxx_messageInfo_Index proto.InternalMessageInfo

func (m *Index) GetEntityType() string {
	if m != nil && m.EntityType != nil {
		return *m.EntityType
	}
	return ""
}

func (m *Index) GetAncestor() bool {
	if m != nil && m.Ancestor != nil {
		return *m.Ancestor
	}
	return false
}

func (m *Index) GetProperty() []*Index_Property {
	if m != nil {
		return m.Property
	}
	return nil
}

type Index_Property struct {
	Name                 *string                   `protobuf:"bytes,3,req,name=name" json:"name,omitempty"`
	Direction            *Index_Property_Direction `protobuf:"varint,4,opt,name=direction,enum=appengine.Index_Property_Direction,def=1" json:"direction,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *Index_Property) Reset()         { *m = Index_Property{} }
func (m *Index_Property) String() string { return proto.CompactTextString(m) }
func (*Index_Property) ProtoMessage()    {}
func (*Index_Property) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{8, 0}
}
func (m *Index_Property) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Index_Property.Unmarshal(m, b)
}
func (m *Index_Property) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Index_Property.Marshal(b, m, deterministic)
}
func (dst *Index_Property) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Index_Property.Merge(dst, src)
}
func (m *Index_Property) XXX_Size() int {
	return xxx_messageInfo_Index_Property.Size(m)
}
func (m *Index_Property) XXX_DiscardUnknown() {
	xxx_messageInfo_Index_Property.DiscardUnknown(m)
}

var xxx_messageInfo_Index_Property proto.InternalMessageInfo

const Default_Index_Property_Direction Index_Property_Direction = Index_Property_ASCENDING

func (m *Index_Property) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *Index_Property) GetDirection() Index_Property_Direction {
	if m != nil && m.Direction != nil {
		return *m.Direction
	}
	return Default_Index_Property_Direction
}

type CompositeIndex struct {
	AppId                *string               `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id,omitempty"`
	Id                   *int64                `protobuf:"varint,2,req,name=id" json:"id,omitempty"`
	Definition           *Index                `protobuf:"bytes,3,req,name=definition" json:"definition,omitempty"`
	State                *CompositeIndex_State `protobuf:"varint,4,req,name=state,enum=appengine.CompositeIndex_State" json:"state,omitempty"`
	OnlyUseIfRequired    *bool                 `protobuf:"varint,6,opt,name=only_use_if_required,json=onlyUseIfRequired,def=0" json:"only_use_if_required,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CompositeIndex) Reset()         { *m = CompositeIndex{} }
func (m *CompositeIndex) String() string { return proto.CompactTextString(m) }
func (*CompositeIndex) ProtoMessage()    {}
func (*CompositeIndex) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{9}
}
func (m *CompositeIndex) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompositeIndex.Unmarshal(m, b)
}
func (m *CompositeIndex) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompositeIndex.Marshal(b, m, deterministic)
}
func (dst *CompositeIndex) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompositeIndex.Merge(dst, src)
}
func (m *CompositeIndex) XXX_Size() int {
	return xxx_messageInfo_CompositeIndex.Size(m)
}
func (m *CompositeIndex) XXX_DiscardUnknown() {
	xxx_messageInfo_CompositeIndex.DiscardUnknown(m)
}

var xxx_messageInfo_CompositeIndex proto.InternalMessageInfo

const Default_CompositeIndex_OnlyUseIfRequired bool = false

func (m *CompositeIndex) GetAppId() string {
	if m != nil && m.AppId != nil {
		return *m.AppId
	}
	return ""
}

func (m *CompositeIndex) GetId() int64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *CompositeIndex) GetDefinition() *Index {
	if m != nil {
		return m.Definition
	}
	return nil
}

func (m *CompositeIndex) GetState() CompositeIndex_State {
	if m != nil && m.State != nil {
		return *m.State
	}
	return CompositeIndex_WRITE_ONLY
}

func (m *CompositeIndex) GetOnlyUseIfRequired() bool {
	if m != nil && m.OnlyUseIfRequired != nil {
		return *m.OnlyUseIfRequired
	}
	return Default_CompositeIndex_OnlyUseIfRequired
}

type IndexPostfix struct {
	IndexValue           []*IndexPostfix_IndexValue `protobuf:"bytes,1,rep,name=index_value,json=indexValue" json:"index_value,omitempty"`
	Key                  *Reference                 `protobuf:"bytes,2,opt,name=key" json:"key,omitempty"`
	Before               *bool                      `protobuf:"varint,3,opt,name=before,def=1" json:"before,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *IndexPostfix) Reset()         { *m = IndexPostfix{} }
func (m *IndexPostfix) String() string { return proto.CompactTextString(m) }
func (*IndexPostfix) ProtoMessage()    {}
func (*IndexPostfix) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{10}
}
func (m *IndexPostfix) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexPostfix.Unmarshal(m, b)
}
func (m *IndexPostfix) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexPostfix.Marshal(b, m, deterministic)
}
func (dst *IndexPostfix) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexPostfix.Merge(dst, src)
}
func (m *IndexPostfix) XXX_Size() int {
	return xxx_messageInfo_IndexPostfix.Size(m)
}
func (m *IndexPostfix) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexPostfix.DiscardUnknown(m)
}

var xxx_messageInfo_IndexPostfix proto.InternalMessageInfo

const Default_IndexPostfix_Before bool = true

func (m *IndexPostfix) GetIndexValue() []*IndexPostfix_IndexValue {
	if m != nil {
		return m.IndexValue
	}
	return nil
}

func (m *IndexPostfix) GetKey() *Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *IndexPostfix) GetBefore() bool {
	if m != nil && m.Before != nil {
		return *m.Before
	}
	return Default_IndexPostfix_Before
}

type IndexPostfix_IndexValue struct {
	PropertyName         *string        `protobuf:"bytes,1,req,name=property_name,json=propertyName" json:"property_name,omitempty"`
	Value                *PropertyValue `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *IndexPostfix_IndexValue) Reset()         { *m = IndexPostfix_IndexValue{} }
func (m *IndexPostfix_IndexValue) String() string { return proto.CompactTextString(m) }
func (*IndexPostfix_IndexValue) ProtoMessage()    {}
func (*IndexPostfix_IndexValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{10, 0}
}
func (m *IndexPostfix_IndexValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexPostfix_IndexValue.Unmarshal(m, b)
}
func (m *IndexPostfix_IndexValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexPostfix_IndexValue.Marshal(b, m, deterministic)
}
func (dst *IndexPostfix_IndexValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexPostfix_IndexValue.Merge(dst, src)
}
func (m *IndexPostfix_IndexValue) XXX_Size() int {
	return xxx_messageInfo_IndexPostfix_IndexValue.Size(m)
}
func (m *IndexPostfix_IndexValue) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexPostfix_IndexValue.DiscardUnknown(m)
}

var xxx_messageInfo_IndexPostfix_IndexValue proto.InternalMessageInfo

func (m *IndexPostfix_IndexValue) GetPropertyName() string {
	if m != nil && m.PropertyName != nil {
		return *m.PropertyName
	}
	return ""
}

func (m *IndexPostfix_IndexValue) GetValue() *PropertyValue {
	if m != nil {
		return m.Value
	}
	return nil
}

type IndexPosition struct {
	Key                  *string  `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Before               *bool    `protobuf:"varint,2,opt,name=before,def=1" json:"before,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexPosition) Reset()         { *m = IndexPosition{} }
func (m *IndexPosition) String() string { return proto.CompactTextString(m) }
func (*IndexPosition) ProtoMessage()    {}
func (*IndexPosition) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{11}
}
func (m *IndexPosition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexPosition.Unmarshal(m, b)
}
func (m *IndexPosition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexPosition.Marshal(b, m, deterministic)
}
func (dst *IndexPosition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexPosition.Merge(dst, src)
}
func (m *IndexPosition) XXX_Size() int {
	return xxx_messageInfo_IndexPosition.Size(m)
}
func (m *IndexPosition) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexPosition.DiscardUnknown(m)
}

var xxx_messageInfo_IndexPosition proto.InternalMessageInfo

const Default_IndexPosition_Before bool = true

func (m *IndexPosition) GetKey() string {
	if m != nil && m.Key != nil {
		return *m.Key
	}
	return ""
}

func (m *IndexPosition) GetBefore() bool {
	if m != nil && m.Before != nil {
		return *m.Before
	}
	return Default_IndexPosition_Before
}

type Snapshot struct {
	Ts                   *int64   `protobuf:"varint,1,req,name=ts" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Snapshot) Reset()         { *m = Snapshot{} }
func (m *Snapshot) String() string { return proto.CompactTextString(m) }
func (*Snapshot) ProtoMessage()    {}
func (*Snapshot) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{12}
}
func (m *Snapshot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Snapshot.Unmarshal(m, b)
}
func (m *Snapshot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Snapshot.Marshal(b, m, deterministic)
}
func (dst *Snapshot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Snapshot.Merge(dst, src)
}
func (m *Snapshot) XXX_Size() int {
	return xxx_messageInfo_Snapshot.Size(m)
}
func (m *Snapshot) XXX_DiscardUnknown() {
	xxx_messageInfo_Snapshot.DiscardUnknown(m)
}

var xxx_messageInfo_Snapshot proto.InternalMessageInfo

func (m *Snapshot) GetTs() int64 {
	if m != nil && m.Ts != nil {
		return *m.Ts
	}
	return 0
}

type InternalHeader struct {
	Qos                  *string  `protobuf:"bytes,1,opt,name=qos" json:"qos,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InternalHeader) Reset()         { *m = InternalHeader{} }
func (m *InternalHeader) String() string { return proto.CompactTextString(m) }
func (*InternalHeader) ProtoMessage()    {}
func (*InternalHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{13}
}
func (m *InternalHeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InternalHeader.Unmarshal(m, b)
}
func (m *InternalHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InternalHeader.Marshal(b, m, deterministic)
}
func (dst *InternalHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InternalHeader.Merge(dst, src)
}
func (m *InternalHeader) XXX_Size() int {
	return xxx_messageInfo_InternalHeader.Size(m)
}
func (m *InternalHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_InternalHeader.DiscardUnknown(m)
}

var xxx_messageInfo_InternalHeader proto.InternalMessageInfo

func (m *InternalHeader) GetQos() string {
	if m != nil && m.Qos != nil {
		return *m.Qos
	}
	return ""
}

type Transaction struct {
	Header               *InternalHeader `protobuf:"bytes,4,opt,name=header" json:"header,omitempty"`
	Handle               *uint64         `protobuf:"fixed64,1,req,name=handle" json:"handle,omitempty"`
	App                  *string         `protobuf:"bytes,2,req,name=app" json:"app,omitempty"`
	MarkChanges          *bool           `protobuf:"varint,3,opt,name=mark_changes,json=markChanges,def=0" json:"mark_changes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Transaction) Reset()         { *m = Transaction{} }
func (m *Transaction) String() string { return proto.CompactTextString(m) }
func (*Transaction) ProtoMessage()    {}
func (*Transaction) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{14}
}
func (m *Transaction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Transaction.Unmarshal(m, b)
}
func (m *Transaction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Transaction.Marshal(b, m, deterministic)
}
func (dst *Transaction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Transaction.Merge(dst, src)
}
func (m *Transaction) XXX_Size() int {
	return xxx_messageInfo_Transaction.Size(m)
}
func (m *Transaction) XXX_DiscardUnknown() {
	xxx_messageInfo_Transaction.DiscardUnknown(m)
}

var xxx_messageInfo_Transaction proto.InternalMessageInfo

const Default_Transaction_MarkChanges bool = false

func (m *Transaction) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *Transaction) GetHandle() uint64 {
	if m != nil && m.Handle != nil {
		return *m.Handle
	}
	return 0
}

func (m *Transaction) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

func (m *Transaction) GetMarkChanges() bool {
	if m != nil && m.MarkChanges != nil {
		return *m.MarkChanges
	}
	return Default_Transaction_MarkChanges
}

type Query struct {
	Header               *InternalHeader   `protobuf:"bytes,39,opt,name=header" json:"header,omitempty"`
	App                  *string           `protobuf:"bytes,1,req,name=app" json:"app,omitempty"`
	NameSpace            *string           `protobuf:"bytes,29,opt,name=name_space,json=nameSpace" json:"name_space,omitempty"`
	Kind                 *string           `protobuf:"bytes,3,opt,name=kind" json:"kind,omitempty"`
	Ancestor             *Reference        `protobuf:"bytes,17,opt,name=ancestor" json:"ancestor,omitempty"`
	Filter               []*Query_Filter   `protobuf:"group,4,rep,name=Filter,json=filter" json:"filter,omitempty"`
	SearchQuery          *string           `protobuf:"bytes,8,opt,name=search_query,json=searchQuery" json:"search_query,omitempty"`
	Order                []*Query_Order    `protobuf:"group,9,rep,name=Order,json=order" json:"order,omitempty"`
	Hint                 *Query_Hint       `protobuf:"varint,18,opt,name=hint,enum=appengine.Query_Hint" json:"hint,omitempty"`
	Count                *int32            `protobuf:"varint,23,opt,name=count" json:"count,omitempty"`
	Offset               *int32            `protobuf:"varint,12,opt,name=offset,def=0" json:"offset,omitempty"`
	Limit                *int32            `protobuf:"varint,16,opt,name=limit" json:"limit,omitempty"`
	CompiledCursor       *CompiledCursor   `protobuf:"bytes,30,opt,name=compiled_cursor,json=compiledCursor" json:"compiled_cursor,omitempty"`
	EndCompiledCursor    *CompiledCursor   `protobuf:"bytes,31,opt,name=end_compiled_cursor,json=endCompiledCursor" json:"end_compiled_cursor,omitempty"`
	CompositeIndex       []*CompositeIndex `protobuf:"bytes,19,rep,name=composite_index,json=compositeIndex" json:"composite_index,omitempty"`
	RequirePerfectPlan   *bool             `protobuf:"varint,20,opt,name=require_perfect_plan,json=requirePerfectPlan,def=0" json:"require_perfect_plan,omitempty"`
	KeysOnly             *bool             `protobuf:"varint,21,opt,name=keys_only,json=keysOnly,def=0" json:"keys_only,omitempty"`
	Transaction          *Transaction      `protobuf:"bytes,22,opt,name=transaction" json:"transaction,omitempty"`
	Compile              *bool             `protobuf:"varint,25,opt,name=compile,def=0" json:"compile,omitempty"`
	FailoverMs           *int64            `protobuf:"varint,26,opt,name=failover_ms,json=failoverMs" json:"failover_ms,omitempty"`
	Strong               *bool             `protobuf:"varint,32,opt,name=strong" json:"strong,omitempty"`
	PropertyName         []string          `protobuf:"bytes,33,rep,name=property_name,json=propertyName" json:"property_name,omitempty"`
	GroupByPropertyName  []string          `protobuf:"bytes,34,rep,name=group_by_property_name,json=groupByPropertyName" json:"group_by_property_name,omitempty"`
	Distinct             *bool             `protobuf:"varint,24,opt,name=distinct" json:"distinct,omitempty"`
	MinSafeTimeSeconds   *int64            `protobuf:"varint,35,opt,name=min_safe_time_seconds,json=minSafeTimeSeconds" json:"min_safe_time_seconds,omitempty"`
	SafeReplicaName      []string          `protobuf:"bytes,36,rep,name=safe_replica_name,json=safeReplicaName" json:"safe_replica_name,omitempty"`
	PersistOffset        *bool             `protobuf:"varint,37,opt,name=persist_offset,json=persistOffset,def=0" json:"persist_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Query) Reset()         { *m = Query{} }
func (m *Query) String() string { return proto.CompactTextString(m) }
func (*Query) ProtoMessage()    {}
func (*Query) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15}
}
func (m *Query) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Query.Unmarshal(m, b)
}
func (m *Query) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Query.Marshal(b, m, deterministic)
}
func (dst *Query) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Query.Merge(dst, src)
}
func (m *Query) XXX_Size() int {
	return xxx_messageInfo_Query.Size(m)
}
func (m *Query) XXX_DiscardUnknown() {
	xxx_messageInfo_Query.DiscardUnknown(m)
}

var xxx_messageInfo_Query proto.InternalMessageInfo

const Default_Query_Offset int32 = 0
const Default_Query_RequirePerfectPlan bool = false
const Default_Query_KeysOnly bool = false
const Default_Query_Compile bool = false
const Default_Query_PersistOffset bool = false

func (m *Query) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *Query) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

func (m *Query) GetNameSpace() string {
	if m != nil && m.NameSpace != nil {
		return *m.NameSpace
	}
	return ""
}

func (m *Query) GetKind() string {
	if m != nil && m.Kind != nil {
		return *m.Kind
	}
	return ""
}

func (m *Query) GetAncestor() *Reference {
	if m != nil {
		return m.Ancestor
	}
	return nil
}

func (m *Query) GetFilter() []*Query_Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *Query) GetSearchQuery() string {
	if m != nil && m.SearchQuery != nil {
		return *m.SearchQuery
	}
	return ""
}

func (m *Query) GetOrder() []*Query_Order {
	if m != nil {
		return m.Order
	}
	return nil
}

func (m *Query) GetHint() Query_Hint {
	if m != nil && m.Hint != nil {
		return *m.Hint
	}
	return Query_ORDER_FIRST
}

func (m *Query) GetCount() int32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *Query) GetOffset() int32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return Default_Query_Offset
}

func (m *Query) GetLimit() int32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

func (m *Query) GetCompiledCursor() *CompiledCursor {
	if m != nil {
		return m.CompiledCursor
	}
	return nil
}

func (m *Query) GetEndCompiledCursor() *CompiledCursor {
	if m != nil {
		return m.EndCompiledCursor
	}
	return nil
}

func (m *Query) GetCompositeIndex() []*CompositeIndex {
	if m != nil {
		return m.CompositeIndex
	}
	return nil
}

func (m *Query) GetRequirePerfectPlan() bool {
	if m != nil && m.RequirePerfectPlan != nil {
		return *m.RequirePerfectPlan
	}
	return Default_Query_RequirePerfectPlan
}

func (m *Query) GetKeysOnly() bool {
	if m != nil && m.KeysOnly != nil {
		return *m.KeysOnly
	}
	return Default_Query_KeysOnly
}

func (m *Query) GetTransaction() *Transaction {
	if m != nil {
		return m.Transaction
	}
	return nil
}

func (m *Query) GetCompile() bool {
	if m != nil && m.Compile != nil {
		return *m.Compile
	}
	return Default_Query_Compile
}

func (m *Query) GetFailoverMs() int64 {
	if m != nil && m.FailoverMs != nil {
		return *m.FailoverMs
	}
	return 0
}

func (m *Query) GetStrong() bool {
	if m != nil && m.Strong != nil {
		return *m.Strong
	}
	return false
}

func (m *Query) GetPropertyName() []string {
	if m != nil {
		return m.PropertyName
	}
	return nil
}

func (m *Query) GetGroupByPropertyName() []string {
	if m != nil {
		return m.GroupByPropertyName
	}
	return nil
}

func (m *Query) GetDistinct() bool {
	if m != nil && m.Distinct != nil {
		return *m.Distinct
	}
	return false
}

func (m *Query) GetMinSafeTimeSeconds() int64 {
	if m != nil && m.MinSafeTimeSeconds != nil {
		return *m.MinSafeTimeSeconds
	}
	return 0
}

func (m *Query) GetSafeReplicaName() []string {
	if m != nil {
		return m.SafeReplicaName
	}
	return nil
}

func (m *Query) GetPersistOffset() bool {
	if m != nil && m.PersistOffset != nil {
		return *m.PersistOffset
	}
	return Default_Query_PersistOffset
}

type Query_Filter struct {
	Op                   *Query_Filter_Operator `protobuf:"varint,6,req,name=op,enum=appengine.Query_Filter_Operator" json:"op,omitempty"`
	Property             []*Property            `protobuf:"bytes,14,rep,name=property" json:"property,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Query_Filter) Reset()         { *m = Query_Filter{} }
func (m *Query_Filter) String() string { return proto.CompactTextString(m) }
func (*Query_Filter) ProtoMessage()    {}
func (*Query_Filter) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15, 0}
}
func (m *Query_Filter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Query_Filter.Unmarshal(m, b)
}
func (m *Query_Filter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Query_Filter.Marshal(b, m, deterministic)
}
func (dst *Query_Filter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Query_Filter.Merge(dst, src)
}
func (m *Query_Filter) XXX_Size() int {
	return xxx_messageInfo_Query_Filter.Size(m)
}
func (m *Query_Filter) XXX_DiscardUnknown() {
	xxx_messageInfo_Query_Filter.DiscardUnknown(m)
}

var xxx_messageInfo_Query_Filter proto.InternalMessageInfo

func (m *Query_Filter) GetOp() Query_Filter_Operator {
	if m != nil && m.Op != nil {
		return *m.Op
	}
	return Query_Filter_LESS_THAN
}

func (m *Query_Filter) GetProperty() []*Property {
	if m != nil {
		return m.Property
	}
	return nil
}

type Query_Order struct {
	Property             *string                `protobuf:"bytes,10,req,name=property" json:"property,omitempty"`
	Direction            *Query_Order_Direction `protobuf:"varint,11,opt,name=direction,enum=appengine.Query_Order_Direction,def=1" json:"direction,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Query_Order) Reset()         { *m = Query_Order{} }
func (m *Query_Order) String() string { return proto.CompactTextString(m) }
func (*Query_Order) ProtoMessage()    {}
func (*Query_Order) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{15, 1}
}
func (m *Query_Order) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Query_Order.Unmarshal(m, b)
}
func (m *Query_Order) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Query_Order.Marshal(b, m, deterministic)
}
func (dst *Query_Order) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Query_Order.Merge(dst, src)
}
func (m *Query_Order) XXX_Size() int {
	return xxx_messageInfo_Query_Order.Size(m)
}
func (m *Query_Order) XXX_DiscardUnknown() {
	xxx_messageInfo_Query_Order.DiscardUnknown(m)
}

var xxx_messageInfo_Query_Order proto.InternalMessageInfo

const Default_Query_Order_Direction Query_Order_Direction = Query_Order_ASCENDING

func (m *Query_Order) GetProperty() string {
	if m != nil && m.Property != nil {
		return *m.Property
	}
	return ""
}

func (m *Query_Order) GetDirection() Query_Order_Direction {
	if m != nil && m.Direction != nil {
		return *m.Direction
	}
	return Default_Query_Order_Direction
}

type CompiledQuery struct {
	Primaryscan          *CompiledQuery_PrimaryScan     `protobuf:"group,1,req,name=PrimaryScan,json=primaryscan" json:"primaryscan,omitempty"`
	Mergejoinscan        []*CompiledQuery_MergeJoinScan `protobuf:"group,7,rep,name=MergeJoinScan,json=mergejoinscan" json:"mergejoinscan,omitempty"`
	IndexDef             *Index                         `protobuf:"bytes,21,opt,name=index_def,json=indexDef" json:"index_def,omitempty"`
	Offset               *int32                         `protobuf:"varint,10,opt,name=offset,def=0" json:"offset,omitempty"`
	Limit                *int32                         `protobuf:"varint,11,opt,name=limit" json:"limit,omitempty"`
	KeysOnly             *bool                          `protobuf:"varint,12,req,name=keys_only,json=keysOnly" json:"keys_only,omitempty"`
	PropertyName         []string                       `protobuf:"bytes,24,rep,name=property_name,json=propertyName" json:"property_name,omitempty"`
	DistinctInfixSize    *int32                         `protobuf:"varint,25,opt,name=distinct_infix_size,json=distinctInfixSize" json:"distinct_infix_size,omitempty"`
	Entityfilter         *CompiledQuery_EntityFilter    `protobuf:"group,13,opt,name=EntityFilter,json=entityfilter" json:"entityfilter,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *CompiledQuery) Reset()         { *m = CompiledQuery{} }
func (m *CompiledQuery) String() string { return proto.CompactTextString(m) }
func (*CompiledQuery) ProtoMessage()    {}
func (*CompiledQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{16}
}
func (m *CompiledQuery) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledQuery.Unmarshal(m, b)
}
func (m *CompiledQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledQuery.Marshal(b, m, deterministic)
}
func (dst *CompiledQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledQuery.Merge(dst, src)
}
func (m *CompiledQuery) XXX_Size() int {
	return xxx_messageInfo_CompiledQuery.Size(m)
}
func (m *CompiledQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledQuery.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledQuery proto.InternalMessageInfo

const Default_CompiledQuery_Offset int32 = 0

func (m *CompiledQuery) GetPrimaryscan() *CompiledQuery_PrimaryScan {
	if m != nil {
		return m.Primaryscan
	}
	return nil
}

func (m *CompiledQuery) GetMergejoinscan() []*CompiledQuery_MergeJoinScan {
	if m != nil {
		return m.Mergejoinscan
	}
	return nil
}

func (m *CompiledQuery) GetIndexDef() *Index {
	if m != nil {
		return m.IndexDef
	}
	return nil
}

func (m *CompiledQuery) GetOffset() int32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return Default_CompiledQuery_Offset
}

func (m *CompiledQuery) GetLimit() int32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

func (m *CompiledQuery) GetKeysOnly() bool {
	if m != nil && m.KeysOnly != nil {
		return *m.KeysOnly
	}
	return false
}

func (m *CompiledQuery) GetPropertyName() []string {
	if m != nil {
		return m.PropertyName
	}
	return nil
}

func (m *CompiledQuery) GetDistinctInfixSize() int32 {
	if m != nil && m.DistinctInfixSize != nil {
		return *m.DistinctInfixSize
	}
	return 0
}

func (m *CompiledQuery) GetEntityfilter() *CompiledQuery_EntityFilter {
	if m != nil {
		return m.Entityfilter
	}
	return nil
}

type CompiledQuery_PrimaryScan struct {
	IndexName                  *string  `protobuf:"bytes,2,opt,name=index_name,json=indexName" json:"index_name,omitempty"`
	StartKey                   *string  `protobuf:"bytes,3,opt,name=start_key,json=startKey" json:"start_key,omitempty"`
	StartInclusive             *bool    `protobuf:"varint,4,opt,name=start_inclusive,json=startInclusive" json:"start_inclusive,omitempty"`
	EndKey                     *string  `protobuf:"bytes,5,opt,name=end_key,json=endKey" json:"end_key,omitempty"`
	EndInclusive               *bool    `protobuf:"varint,6,opt,name=end_inclusive,json=endInclusive" json:"end_inclusive,omitempty"`
	StartPostfixValue          []string `protobuf:"bytes,22,rep,name=start_postfix_value,json=startPostfixValue" json:"start_postfix_value,omitempty"`
	EndPostfixValue            []string `protobuf:"bytes,23,rep,name=end_postfix_value,json=endPostfixValue" json:"end_postfix_value,omitempty"`
	EndUnappliedLogTimestampUs *int64   `protobuf:"varint,19,opt,name=end_unapplied_log_timestamp_us,json=endUnappliedLogTimestampUs" json:"end_unapplied_log_timestamp_us,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *CompiledQuery_PrimaryScan) Reset()         { *m = CompiledQuery_PrimaryScan{} }
func (m *CompiledQuery_PrimaryScan) String() string { return proto.CompactTextString(m) }
func (*CompiledQuery_PrimaryScan) ProtoMessage()    {}
func (*CompiledQuery_PrimaryScan) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{16, 0}
}
func (m *CompiledQuery_PrimaryScan) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledQuery_PrimaryScan.Unmarshal(m, b)
}
func (m *CompiledQuery_PrimaryScan) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledQuery_PrimaryScan.Marshal(b, m, deterministic)
}
func (dst *CompiledQuery_PrimaryScan) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledQuery_PrimaryScan.Merge(dst, src)
}
func (m *CompiledQuery_PrimaryScan) XXX_Size() int {
	return xxx_messageInfo_CompiledQuery_PrimaryScan.Size(m)
}
func (m *CompiledQuery_PrimaryScan) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledQuery_PrimaryScan.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledQuery_PrimaryScan proto.InternalMessageInfo

func (m *CompiledQuery_PrimaryScan) GetIndexName() string {
	if m != nil && m.IndexName != nil {
		return *m.IndexName
	}
	return ""
}

func (m *CompiledQuery_PrimaryScan) GetStartKey() string {
	if m != nil && m.StartKey != nil {
		return *m.StartKey
	}
	return ""
}

func (m *CompiledQuery_PrimaryScan) GetStartInclusive() bool {
	if m != nil && m.StartInclusive != nil {
		return *m.StartInclusive
	}
	return false
}

func (m *CompiledQuery_PrimaryScan) GetEndKey() string {
	if m != nil && m.EndKey != nil {
		return *m.EndKey
	}
	return ""
}

func (m *CompiledQuery_PrimaryScan) GetEndInclusive() bool {
	if m != nil && m.EndInclusive != nil {
		return *m.EndInclusive
	}
	return false
}

func (m *CompiledQuery_PrimaryScan) GetStartPostfixValue() []string {
	if m != nil {
		return m.StartPostfixValue
	}
	return nil
}

func (m *CompiledQuery_PrimaryScan) GetEndPostfixValue() []string {
	if m != nil {
		return m.EndPostfixValue
	}
	return nil
}

func (m *CompiledQuery_PrimaryScan) GetEndUnappliedLogTimestampUs() int64 {
	if m != nil && m.EndUnappliedLogTimestampUs != nil {
		return *m.EndUnappliedLogTimestampUs
	}
	return 0
}

type CompiledQuery_MergeJoinScan struct {
	IndexName            *string  `protobuf:"bytes,8,req,name=index_name,json=indexName" json:"index_name,omitempty"`
	PrefixValue          []string `protobuf:"bytes,9,rep,name=prefix_value,json=prefixValue" json:"prefix_value,omitempty"`
	ValuePrefix          *bool    `protobuf:"varint,20,opt,name=value_prefix,json=valuePrefix,def=0" json:"value_prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompiledQuery_MergeJoinScan) Reset()         { *m = CompiledQuery_MergeJoinScan{} }
func (m *CompiledQuery_MergeJoinScan) String() string { return proto.CompactTextString(m) }
func (*CompiledQuery_MergeJoinScan) ProtoMessage()    {}
func (*CompiledQuery_MergeJoinScan) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{16, 1}
}
func (m *CompiledQuery_MergeJoinScan) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledQuery_MergeJoinScan.Unmarshal(m, b)
}
func (m *CompiledQuery_MergeJoinScan) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledQuery_MergeJoinScan.Marshal(b, m, deterministic)
}
func (dst *CompiledQuery_MergeJoinScan) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledQuery_MergeJoinScan.Merge(dst, src)
}
func (m *CompiledQuery_MergeJoinScan) XXX_Size() int {
	return xxx_messageInfo_CompiledQuery_MergeJoinScan.Size(m)
}
func (m *CompiledQuery_MergeJoinScan) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledQuery_MergeJoinScan.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledQuery_MergeJoinScan proto.InternalMessageInfo

const Default_CompiledQuery_MergeJoinScan_ValuePrefix bool = false

func (m *CompiledQuery_MergeJoinScan) GetIndexName() string {
	if m != nil && m.IndexName != nil {
		return *m.IndexName
	}
	return ""
}

func (m *CompiledQuery_MergeJoinScan) GetPrefixValue() []string {
	if m != nil {
		return m.PrefixValue
	}
	return nil
}

func (m *CompiledQuery_MergeJoinScan) GetValuePrefix() bool {
	if m != nil && m.ValuePrefix != nil {
		return *m.ValuePrefix
	}
	return Default_CompiledQuery_MergeJoinScan_ValuePrefix
}

type CompiledQuery_EntityFilter struct {
	Distinct             *bool      `protobuf:"varint,14,opt,name=distinct,def=0" json:"distinct,omitempty"`
	Kind                 *string    `protobuf:"bytes,17,opt,name=kind" json:"kind,omitempty"`
	Ancestor             *Reference `protobuf:"bytes,18,opt,name=ancestor" json:"ancestor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CompiledQuery_EntityFilter) Reset()         { *m = CompiledQuery_EntityFilter{} }
func (m *CompiledQuery_EntityFilter) String() string { return proto.CompactTextString(m) }
func (*CompiledQuery_EntityFilter) ProtoMessage()    {}
func (*CompiledQuery_EntityFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{16, 2}
}
func (m *CompiledQuery_EntityFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledQuery_EntityFilter.Unmarshal(m, b)
}
func (m *CompiledQuery_EntityFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledQuery_EntityFilter.Marshal(b, m, deterministic)
}
func (dst *CompiledQuery_EntityFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledQuery_EntityFilter.Merge(dst, src)
}
func (m *CompiledQuery_EntityFilter) XXX_Size() int {
	return xxx_messageInfo_CompiledQuery_EntityFilter.Size(m)
}
func (m *CompiledQuery_EntityFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledQuery_EntityFilter.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledQuery_EntityFilter proto.InternalMessageInfo

const Default_CompiledQuery_EntityFilter_Distinct bool = false

func (m *CompiledQuery_EntityFilter) GetDistinct() bool {
	if m != nil && m.Distinct != nil {
		return *m.Distinct
	}
	return Default_CompiledQuery_EntityFilter_Distinct
}

func (m *CompiledQuery_EntityFilter) GetKind() string {
	if m != nil && m.Kind != nil {
		return *m.Kind
	}
	return ""
}

func (m *CompiledQuery_EntityFilter) GetAncestor() *Reference {
	if m != nil {
		return m.Ancestor
	}
	return nil
}

type CompiledCursor struct {
	Position             *CompiledCursor_Position `protobuf:"group,2,opt,name=Position,json=position" json:"position,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *CompiledCursor) Reset()         { *m = CompiledCursor{} }
func (m *CompiledCursor) String() string { return proto.CompactTextString(m) }
func (*CompiledCursor) ProtoMessage()    {}
func (*CompiledCursor) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{17}
}
func (m *CompiledCursor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledCursor.Unmarshal(m, b)
}
func (m *CompiledCursor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledCursor.Marshal(b, m, deterministic)
}
func (dst *CompiledCursor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledCursor.Merge(dst, src)
}
func (m *CompiledCursor) XXX_Size() int {
	return xxx_messageInfo_CompiledCursor.Size(m)
}
func (m *CompiledCursor) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledCursor.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledCursor proto.InternalMessageInfo

func (m *CompiledCursor) GetPosition() *CompiledCursor_Position {
	if m != nil {
		return m.Position
	}
	return nil
}

type CompiledCursor_Position struct {
	StartKey             *string                               `protobuf:"bytes,27,opt,name=start_key,json=startKey" json:"start_key,omitempty"`
	Indexvalue           []*CompiledCursor_Position_IndexValue `protobuf:"group,29,rep,name=IndexValue,json=indexvalue" json:"indexvalue,omitempty"`
	Key                  *Reference                            `protobuf:"bytes,32,opt,name=key" json:"key,omitempty"`
	StartInclusive       *bool                                 `protobuf:"varint,28,opt,name=start_inclusive,json=startInclusive,def=1" json:"start_inclusive,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *CompiledCursor_Position) Reset()         { *m = CompiledCursor_Position{} }
func (m *CompiledCursor_Position) String() string { return proto.CompactTextString(m) }
func (*CompiledCursor_Position) ProtoMessage()    {}
func (*CompiledCursor_Position) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{17, 0}
}
func (m *CompiledCursor_Position) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledCursor_Position.Unmarshal(m, b)
}
func (m *CompiledCursor_Position) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledCursor_Position.Marshal(b, m, deterministic)
}
func (dst *CompiledCursor_Position) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledCursor_Position.Merge(dst, src)
}
func (m *CompiledCursor_Position) XXX_Size() int {
	return xxx_messageInfo_CompiledCursor_Position.Size(m)
}
func (m *CompiledCursor_Position) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledCursor_Position.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledCursor_Position proto.InternalMessageInfo

const Default_CompiledCursor_Position_StartInclusive bool = true

func (m *CompiledCursor_Position) GetStartKey() string {
	if m != nil && m.StartKey != nil {
		return *m.StartKey
	}
	return ""
}

func (m *CompiledCursor_Position) GetIndexvalue() []*CompiledCursor_Position_IndexValue {
	if m != nil {
		return m.Indexvalue
	}
	return nil
}

func (m *CompiledCursor_Position) GetKey() *Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *CompiledCursor_Position) GetStartInclusive() bool {
	if m != nil && m.StartInclusive != nil {
		return *m.StartInclusive
	}
	return Default_CompiledCursor_Position_StartInclusive
}

type CompiledCursor_Position_IndexValue struct {
	Property             *string        `protobuf:"bytes,30,opt,name=property" json:"property,omitempty"`
	Value                *PropertyValue `protobuf:"bytes,31,req,name=value" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CompiledCursor_Position_IndexValue) Reset()         { *m = CompiledCursor_Position_IndexValue{} }
func (m *CompiledCursor_Position_IndexValue) String() string { return proto.CompactTextString(m) }
func (*CompiledCursor_Position_IndexValue) ProtoMessage()    {}
func (*CompiledCursor_Position_IndexValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{17, 0, 0}
}
func (m *CompiledCursor_Position_IndexValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompiledCursor_Position_IndexValue.Unmarshal(m, b)
}
func (m *CompiledCursor_Position_IndexValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompiledCursor_Position_IndexValue.Marshal(b, m, deterministic)
}
func (dst *CompiledCursor_Position_IndexValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompiledCursor_Position_IndexValue.Merge(dst, src)
}
func (m *CompiledCursor_Position_IndexValue) XXX_Size() int {
	return xxx_messageInfo_CompiledCursor_Position_IndexValue.Size(m)
}
func (m *CompiledCursor_Position_IndexValue) XXX_DiscardUnknown() {
	xxx_messageInfo_CompiledCursor_Position_IndexValue.DiscardUnknown(m)
}

var xxx_messageInfo_CompiledCursor_Position_IndexValue proto.InternalMessageInfo

func (m *CompiledCursor_Position_IndexValue) GetProperty() string {
	if m != nil && m.Property != nil {
		return *m.Property
	}
	return ""
}

func (m *CompiledCursor_Position_IndexValue) GetValue() *PropertyValue {
	if m != nil {
		return m.Value
	}
	return nil
}

type Cursor struct {
	Cursor               *uint64  `protobuf:"fixed64,1,req,name=cursor" json:"cursor,omitempty"`
	App                  *string  `protobuf:"bytes,2,opt,name=app" json:"app,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Cursor) Reset()         { *m = Cursor{} }
func (m *Cursor) String() string { return proto.CompactTextString(m) }
func (*Cursor) ProtoMessage()    {}
func (*Cursor) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{18}
}
func (m *Cursor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Cursor.Unmarshal(m, b)
}
func (m *Cursor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Cursor.Marshal(b, m, deterministic)
}
func (dst *Cursor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cursor.Merge(dst, src)
}
func (m *Cursor) XXX_Size() int {
	return xxx_messageInfo_Cursor.Size(m)
}
func (m *Cursor) XXX_DiscardUnknown() {
	xxx_messageInfo_Cursor.DiscardUnknown(m)
}

var xxx_messageInfo_Cursor proto.InternalMessageInfo

func (m *Cursor) GetCursor() uint64 {
	if m != nil && m.Cursor != nil {
		return *m.Cursor
	}
	return 0
}

func (m *Cursor) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

type Error struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{19}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Error.Unmarshal(m, b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Error.Marshal(b, m, deterministic)
}
func (dst *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(dst, src)
}
func (m *Error) XXX_Size() int {
	return xxx_messageInfo_Error.Size(m)
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

type Cost struct {
	IndexWrites             *int32           `protobuf:"varint,1,opt,name=index_writes,json=indexWrites" json:"index_writes,omitempty"`
	IndexWriteBytes         *int32           `protobuf:"varint,2,opt,name=index_write_bytes,json=indexWriteBytes" json:"index_write_bytes,omitempty"`
	EntityWrites            *int32           `protobuf:"varint,3,opt,name=entity_writes,json=entityWrites" json:"entity_writes,omitempty"`
	EntityWriteBytes        *int32           `protobuf:"varint,4,opt,name=entity_write_bytes,json=entityWriteBytes" json:"entity_write_bytes,omitempty"`
	Commitcost              *Cost_CommitCost `protobuf:"group,5,opt,name=CommitCost,json=commitcost" json:"commitcost,omitempty"`
	ApproximateStorageDelta *int32           `protobuf:"varint,8,opt,name=approximate_storage_delta,json=approximateStorageDelta" json:"approximate_storage_delta,omitempty"`
	IdSequenceUpdates       *int32           `protobuf:"varint,9,opt,name=id_sequence_updates,json=idSequenceUpdates" json:"id_sequence_updates,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}         `json:"-"`
	XXX_unrecognized        []byte           `json:"-"`
	XXX_sizecache           int32            `json:"-"`
}

func (m *Cost) Reset()         { *m = Cost{} }
func (m *Cost) String() string { return proto.CompactTextString(m) }
func (*Cost) ProtoMessage()    {}
func (*Cost) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{20}
}
func (m *Cost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Cost.Unmarshal(m, b)
}
func (m *Cost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Cost.Marshal(b, m, deterministic)
}
func (dst *Cost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cost.Merge(dst, src)
}
func (m *Cost) XXX_Size() int {
	return xxx_messageInfo_Cost.Size(m)
}
func (m *Cost) XXX_DiscardUnknown() {
	xxx_messageInfo_Cost.DiscardUnknown(m)
}

var xxx_messageInfo_Cost proto.InternalMessageInfo

func (m *Cost) GetIndexWrites() int32 {
	if m != nil && m.IndexWrites != nil {
		return *m.IndexWrites
	}
	return 0
}

func (m *Cost) GetIndexWriteBytes() int32 {
	if m != nil && m.IndexWriteBytes != nil {
		return *m.IndexWriteBytes
	}
	return 0
}

func (m *Cost) GetEntityWrites() int32 {
	if m != nil && m.EntityWrites != nil {
		return *m.EntityWrites
	}
	return 0
}

func (m *Cost) GetEntityWriteBytes() int32 {
	if m != nil && m.EntityWriteBytes != nil {
		return *m.EntityWriteBytes
	}
	return 0
}

func (m *Cost) GetCommitcost() *Cost_CommitCost {
	if m != nil {
		return m.Commitcost
	}
	return nil
}

func (m *Cost) GetApproximateStorageDelta() int32 {
	if m != nil && m.ApproximateStorageDelta != nil {
		return *m.ApproximateStorageDelta
	}
	return 0
}

func (m *Cost) GetIdSequenceUpdates() int32 {
	if m != nil && m.IdSequenceUpdates != nil {
		return *m.IdSequenceUpdates
	}
	return 0
}

type Cost_CommitCost struct {
	RequestedEntityPuts    *int32   `protobuf:"varint,6,opt,name=requested_entity_puts,json=requestedEntityPuts" json:"requested_entity_puts,omitempty"`
	RequestedEntityDeletes *int32   `protobuf:"varint,7,opt,name=requested_entity_deletes,json=requestedEntityDeletes" json:"requested_entity_deletes,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *Cost_CommitCost) Reset()         { *m = Cost_CommitCost{} }
func (m *Cost_CommitCost) String() string { return proto.CompactTextString(m) }
func (*Cost_CommitCost) ProtoMessage()    {}
func (*Cost_CommitCost) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{20, 0}
}
func (m *Cost_CommitCost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Cost_CommitCost.Unmarshal(m, b)
}
func (m *Cost_CommitCost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Cost_CommitCost.Marshal(b, m, deterministic)
}
func (dst *Cost_CommitCost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cost_CommitCost.Merge(dst, src)
}
func (m *Cost_CommitCost) XXX_Size() int {
	return xxx_messageInfo_Cost_CommitCost.Size(m)
}
func (m *Cost_CommitCost) XXX_DiscardUnknown() {
	xxx_messageInfo_Cost_CommitCost.DiscardUnknown(m)
}

var xxx_messageInfo_Cost_CommitCost proto.InternalMessageInfo

func (m *Cost_CommitCost) GetRequestedEntityPuts() int32 {
	if m != nil && m.RequestedEntityPuts != nil {
		return *m.RequestedEntityPuts
	}
	return 0
}

func (m *Cost_CommitCost) GetRequestedEntityDeletes() int32 {
	if m != nil && m.RequestedEntityDeletes != nil {
		return *m.RequestedEntityDeletes
	}
	return 0
}

type GetRequest struct {
	Header               *InternalHeader `protobuf:"bytes,6,opt,name=header" json:"header,omitempty"`
	Key                  []*Reference    `protobuf:"bytes,1,rep,name=key" json:"key,omitempty"`
	Transaction          *Transaction    `protobuf:"bytes,2,opt,name=transaction" json:"transaction,omitempty"`
	FailoverMs           *int64          `protobuf:"varint,3,opt,name=failover_ms,json=failoverMs" json:"failover_ms,omitempty"`
	Strong               *bool           `protobuf:"varint,4,opt,name=strong" json:"strong,omitempty"`
	AllowDeferred        *bool           `protobuf:"varint,5,opt,name=allow_deferred,json=allowDeferred,def=0" json:"allow_deferred,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRequest) Reset()         { *m = GetRequest{} }
func (m *GetRequest) String() string { return proto.CompactTextString(m) }
func (*GetRequest) ProtoMessage()    {}
func (*GetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{21}
}
func (m *GetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRequest.Unmarshal(m, b)
}
func (m *GetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRequest.Marshal(b, m, deterministic)
}
func (dst *GetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRequest.Merge(dst, src)
}
func (m *GetRequest) XXX_Size() int {
	return xxx_messageInfo_GetRequest.Size(m)
}
func (m *GetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRequest proto.InternalMessageInfo

const Default_GetRequest_AllowDeferred bool = false

func (m *GetRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetRequest) GetKey() []*Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *GetRequest) GetTransaction() *Transaction {
	if m != nil {
		return m.Transaction
	}
	return nil
}

func (m *GetRequest) GetFailoverMs() int64 {
	if m != nil && m.FailoverMs != nil {
		return *m.FailoverMs
	}
	return 0
}

func (m *GetRequest) GetStrong() bool {
	if m != nil && m.Strong != nil {
		return *m.Strong
	}
	return false
}

func (m *GetRequest) GetAllowDeferred() bool {
	if m != nil && m.AllowDeferred != nil {
		return *m.AllowDeferred
	}
	return Default_GetRequest_AllowDeferred
}

type GetResponse struct {
	Entity               []*GetResponse_Entity `protobuf:"group,1,rep,name=Entity,json=entity" json:"entity,omitempty"`
	Deferred             []*Reference          `protobuf:"bytes,5,rep,name=deferred" json:"deferred,omitempty"`
	InOrder              *bool                 `protobuf:"varint,6,opt,name=in_order,json=inOrder,def=1" json:"in_order,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetResponse) Reset()         { *m = GetResponse{} }
func (m *GetResponse) String() string { return proto.CompactTextString(m) }
func (*GetResponse) ProtoMessage()    {}
func (*GetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{22}
}
func (m *GetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResponse.Unmarshal(m, b)
}
func (m *GetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResponse.Marshal(b, m, deterministic)
}
func (dst *GetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResponse.Merge(dst, src)
}
func (m *GetResponse) XXX_Size() int {
	return xxx_messageInfo_GetResponse.Size(m)
}
func (m *GetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResponse proto.InternalMessageInfo

const Default_GetResponse_InOrder bool = true

func (m *GetResponse) GetEntity() []*GetResponse_Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetResponse) GetDeferred() []*Reference {
	if m != nil {
		return m.Deferred
	}
	return nil
}

func (m *GetResponse) GetInOrder() bool {
	if m != nil && m.InOrder != nil {
		return *m.InOrder
	}
	return Default_GetResponse_InOrder
}

type GetResponse_Entity struct {
	Entity               *EntityProto `protobuf:"bytes,2,opt,name=entity" json:"entity,omitempty"`
	Key                  *Reference   `protobuf:"bytes,4,opt,name=key" json:"key,omitempty"`
	Version              *int64       `protobuf:"varint,3,opt,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetResponse_Entity) Reset()         { *m = GetResponse_Entity{} }
func (m *GetResponse_Entity) String() string { return proto.CompactTextString(m) }
func (*GetResponse_Entity) ProtoMessage()    {}
func (*GetResponse_Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{22, 0}
}
func (m *GetResponse_Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetResponse_Entity.Unmarshal(m, b)
}
func (m *GetResponse_Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetResponse_Entity.Marshal(b, m, deterministic)
}
func (dst *GetResponse_Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResponse_Entity.Merge(dst, src)
}
func (m *GetResponse_Entity) XXX_Size() int {
	return xxx_messageInfo_GetResponse_Entity.Size(m)
}
func (m *GetResponse_Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResponse_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_GetResponse_Entity proto.InternalMessageInfo

func (m *GetResponse_Entity) GetEntity() *EntityProto {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *GetResponse_Entity) GetKey() *Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *GetResponse_Entity) GetVersion() int64 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

type PutRequest struct {
	Header               *InternalHeader          `protobuf:"bytes,11,opt,name=header" json:"header,omitempty"`
	Entity               []*EntityProto           `protobuf:"bytes,1,rep,name=entity" json:"entity,omitempty"`
	Transaction          *Transaction             `protobuf:"bytes,2,opt,name=transaction" json:"transaction,omitempty"`
	CompositeIndex       []*CompositeIndex        `protobuf:"bytes,3,rep,name=composite_index,json=compositeIndex" json:"composite_index,omitempty"`
	Trusted              *bool                    `protobuf:"varint,4,opt,name=trusted,def=0" json:"trusted,omitempty"`
	Force                *bool                    `protobuf:"varint,7,opt,name=force,def=0" json:"force,omitempty"`
	MarkChanges          *bool                    `protobuf:"varint,8,opt,name=mark_changes,json=markChanges,def=0" json:"mark_changes,omitempty"`
	Snapshot             []*Snapshot              `protobuf:"bytes,9,rep,name=snapshot" json:"snapshot,omitempty"`
	AutoIdPolicy         *PutRequest_AutoIdPolicy `protobuf:"varint,10,opt,name=auto_id_policy,json=autoIdPolicy,enum=appengine.PutRequest_AutoIdPolicy,def=0" json:"auto_id_policy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PutRequest) Reset()         { *m = PutRequest{} }
func (m *PutRequest) String() string { return proto.CompactTextString(m) }
func (*PutRequest) ProtoMessage()    {}
func (*PutRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{23}
}
func (m *PutRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PutRequest.Unmarshal(m, b)
}
func (m *PutRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PutRequest.Marshal(b, m, deterministic)
}
func (dst *PutRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutRequest.Merge(dst, src)
}
func (m *PutRequest) XXX_Size() int {
	return xxx_messageInfo_PutRequest.Size(m)
}
func (m *PutRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutRequest proto.InternalMessageInfo

const Default_PutRequest_Trusted bool = false
const Default_PutRequest_Force bool = false
const Default_PutRequest_MarkChanges bool = false
const Default_PutRequest_AutoIdPolicy PutRequest_AutoIdPolicy = PutRequest_CURRENT

func (m *PutRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *PutRequest) GetEntity() []*EntityProto {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (m *PutRequest) GetTransaction() *Transaction {
	if m != nil {
		return m.Transaction
	}
	return nil
}

func (m *PutRequest) GetCompositeIndex() []*CompositeIndex {
	if m != nil {
		return m.CompositeIndex
	}
	return nil
}

func (m *PutRequest) GetTrusted() bool {
	if m != nil && m.Trusted != nil {
		return *m.Trusted
	}
	return Default_PutRequest_Trusted
}

func (m *PutRequest) GetForce() bool {
	if m != nil && m.Force != nil {
		return *m.Force
	}
	return Default_PutRequest_Force
}

func (m *PutRequest) GetMarkChanges() bool {
	if m != nil && m.MarkChanges != nil {
		return *m.MarkChanges
	}
	return Default_PutRequest_MarkChanges
}

func (m *PutRequest) GetSnapshot() []*Snapshot {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

func (m *PutRequest) GetAutoIdPolicy() PutRequest_AutoIdPolicy {
	if m != nil && m.AutoIdPolicy != nil {
		return *m.AutoIdPolicy
	}
	return Default_PutRequest_AutoIdPolicy
}

type PutResponse struct {
	Key                  []*Reference `protobuf:"bytes,1,rep,name=key" json:"key,omitempty"`
	Cost                 *Cost        `protobuf:"bytes,2,opt,name=cost" json:"cost,omitempty"`
	Version              []int64      `protobuf:"varint,3,rep,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PutResponse) Reset()         { *m = PutResponse{} }
func (m *PutResponse) String() string { return proto.CompactTextString(m) }
func (*PutResponse) ProtoMessage()    {}
func (*PutResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{24}
}
func (m *PutResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PutResponse.Unmarshal(m, b)
}
func (m *PutResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PutResponse.Marshal(b, m, deterministic)
}
func (dst *PutResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutResponse.Merge(dst, src)
}
func (m *PutResponse) XXX_Size() int {
	return xxx_messageInfo_PutResponse.Size(m)
}
func (m *PutResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutResponse proto.InternalMessageInfo

func (m *PutResponse) GetKey() []*Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PutResponse) GetCost() *Cost {
	if m != nil {
		return m.Cost
	}
	return nil
}

func (m *PutResponse) GetVersion() []int64 {
	if m != nil {
		return m.Version
	}
	return nil
}

type TouchRequest struct {
	Header               *InternalHeader   `protobuf:"bytes,10,opt,name=header" json:"header,omitempty"`
	Key                  []*Reference      `protobuf:"bytes,1,rep,name=key" json:"key,omitempty"`
	CompositeIndex       []*CompositeIndex `protobuf:"bytes,2,rep,name=composite_index,json=compositeIndex" json:"composite_index,omitempty"`
	Force                *bool             `protobuf:"varint,3,opt,name=force,def=0" json:"force,omitempty"`
	Snapshot             []*Snapshot       `protobuf:"bytes,9,rep,name=snapshot" json:"snapshot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TouchRequest) Reset()         { *m = TouchRequest{} }
func (m *TouchRequest) String() string { return proto.CompactTextString(m) }
func (*TouchRequest) ProtoMessage()    {}
func (*TouchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{25}
}
func (m *TouchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TouchRequest.Unmarshal(m, b)
}
func (m *TouchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TouchRequest.Marshal(b, m, deterministic)
}
func (dst *TouchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TouchRequest.Merge(dst, src)
}
func (m *TouchRequest) XXX_Size() int {
	return xxx_messageInfo_TouchRequest.Size(m)
}
func (m *TouchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TouchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TouchRequest proto.InternalMessageInfo

const Default_TouchRequest_Force bool = false

func (m *TouchRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *TouchRequest) GetKey() []*Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *TouchRequest) GetCompositeIndex() []*CompositeIndex {
	if m != nil {
		return m.CompositeIndex
	}
	return nil
}

func (m *TouchRequest) GetForce() bool {
	if m != nil && m.Force != nil {
		return *m.Force
	}
	return Default_TouchRequest_Force
}

func (m *TouchRequest) GetSnapshot() []*Snapshot {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

type TouchResponse struct {
	Cost                 *Cost    `protobuf:"bytes,1,opt,name=cost" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TouchResponse) Reset()         { *m = TouchResponse{} }
func (m *TouchResponse) String() string { return proto.CompactTextString(m) }
func (*TouchResponse) ProtoMessage()    {}
func (*TouchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{26}
}
func (m *TouchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TouchResponse.Unmarshal(m, b)
}
func (m *TouchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TouchResponse.Marshal(b, m, deterministic)
}
func (dst *TouchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TouchResponse.Merge(dst, src)
}
func (m *TouchResponse) XXX_Size() int {
	return xxx_messageInfo_TouchResponse.Size(m)
}
func (m *TouchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TouchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TouchResponse proto.InternalMessageInfo

func (m *TouchResponse) GetCost() *Cost {
	if m != nil {
		return m.Cost
	}
	return nil
}

type DeleteRequest struct {
	Header               *InternalHeader `protobuf:"bytes,10,opt,name=header" json:"header,omitempty"`
	Key                  []*Reference    `protobuf:"bytes,6,rep,name=key" json:"key,omitempty"`
	Transaction          *Transaction    `protobuf:"bytes,5,opt,name=transaction" json:"transaction,omitempty"`
	Trusted              *bool           `protobuf:"varint,4,opt,name=trusted,def=0" json:"trusted,omitempty"`
	Force                *bool           `protobuf:"varint,7,opt,name=force,def=0" json:"force,omitempty"`
	MarkChanges          *bool           `protobuf:"varint,8,opt,name=mark_changes,json=markChanges,def=0" json:"mark_changes,omitempty"`
	Snapshot             []*Snapshot     `protobuf:"bytes,9,rep,name=snapshot" json:"snapshot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DeleteRequest) Reset()         { *m = DeleteRequest{} }
func (m *DeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRequest) ProtoMessage()    {}
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{27}
}
func (m *DeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRequest.Unmarshal(m, b)
}
func (m *DeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRequest.Merge(dst, src)
}
func (m *DeleteRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRequest.Size(m)
}
func (m *DeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRequest proto.InternalMessageInfo

const Default_DeleteRequest_Trusted bool = false
const Default_DeleteRequest_Force bool = false
const Default_DeleteRequest_MarkChanges bool = false

func (m *DeleteRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *DeleteRequest) GetKey() []*Reference {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *DeleteRequest) GetTransaction() *Transaction {
	if m != nil {
		return m.Transaction
	}
	return nil
}

func (m *DeleteRequest) GetTrusted() bool {
	if m != nil && m.Trusted != nil {
		return *m.Trusted
	}
	return Default_DeleteRequest_Trusted
}

func (m *DeleteRequest) GetForce() bool {
	if m != nil && m.Force != nil {
		return *m.Force
	}
	return Default_DeleteRequest_Force
}

func (m *DeleteRequest) GetMarkChanges() bool {
	if m != nil && m.MarkChanges != nil {
		return *m.MarkChanges
	}
	return Default_DeleteRequest_MarkChanges
}

func (m *DeleteRequest) GetSnapshot() []*Snapshot {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

type DeleteResponse struct {
	Cost                 *Cost    `protobuf:"bytes,1,opt,name=cost" json:"cost,omitempty"`
	Version              []int64  `protobuf:"varint,3,rep,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteResponse) Reset()         { *m = DeleteResponse{} }
func (m *DeleteResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteResponse) ProtoMessage()    {}
func (*DeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{28}
}
func (m *DeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteResponse.Unmarshal(m, b)
}
func (m *DeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResponse.Merge(dst, src)
}
func (m *DeleteResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteResponse.Size(m)
}
func (m *DeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResponse proto.InternalMessageInfo

func (m *DeleteResponse) GetCost() *Cost {
	if m != nil {
		return m.Cost
	}
	return nil
}

func (m *DeleteResponse) GetVersion() []int64 {
	if m != nil {
		return m.Version
	}
	return nil
}

type NextRequest struct {
	Header               *InternalHeader `protobuf:"bytes,5,opt,name=header" json:"header,omitempty"`
	Cursor               *Cursor         `protobuf:"bytes,1,req,name=cursor" json:"cursor,omitempty"`
	Count                *int32          `protobuf:"varint,2,opt,name=count" json:"count,omitempty"`
	Offset               *int32          `protobuf:"varint,4,opt,name=offset,def=0" json:"offset,omitempty"`
	Compile              *bool           `protobuf:"varint,3,opt,name=compile,def=0" json:"compile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NextRequest) Reset()         { *m = NextRequest{} }
func (m *NextRequest) String() string { return proto.CompactTextString(m) }
func (*NextRequest) ProtoMessage()    {}
func (*NextRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{29}
}
func (m *NextRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NextRequest.Unmarshal(m, b)
}
func (m *NextRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NextRequest.Marshal(b, m, deterministic)
}
func (dst *NextRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NextRequest.Merge(dst, src)
}
func (m *NextRequest) XXX_Size() int {
	return xxx_messageInfo_NextRequest.Size(m)
}
func (m *NextRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NextRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NextRequest proto.InternalMessageInfo

const Default_NextRequest_Offset int32 = 0
const Default_NextRequest_Compile bool = false

func (m *NextRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *NextRequest) GetCursor() *Cursor {
	if m != nil {
		return m.Cursor
	}
	return nil
}

func (m *NextRequest) GetCount() int32 {
	if m != nil && m.Count != nil {
		return *m.Count
	}
	return 0
}

func (m *NextRequest) GetOffset() int32 {
	if m != nil && m.Offset != nil {
		return *m.Offset
	}
	return Default_NextRequest_Offset
}

func (m *NextRequest) GetCompile() bool {
	if m != nil && m.Compile != nil {
		return *m.Compile
	}
	return Default_NextRequest_Compile
}

type QueryResult struct {
	Cursor               *Cursor           `protobuf:"bytes,1,opt,name=cursor" json:"cursor,omitempty"`
	Result               []*EntityProto    `protobuf:"bytes,2,rep,name=result" json:"result,omitempty"`
	SkippedResults       *int32            `protobuf:"varint,7,opt,name=skipped_results,json=skippedResults" json:"skipped_results,omitempty"`
	MoreResults          *bool             `protobuf:"varint,3,req,name=more_results,json=moreResults" json:"more_results,omitempty"`
	KeysOnly             *bool             `protobuf:"varint,4,opt,name=keys_only,json=keysOnly" json:"keys_only,omitempty"`
	IndexOnly            *bool             `protobuf:"varint,9,opt,name=index_only,json=indexOnly" json:"index_only,omitempty"`
	SmallOps             *bool             `protobuf:"varint,10,opt,name=small_ops,json=smallOps" json:"small_ops,omitempty"`
	CompiledQuery        *CompiledQuery    `protobuf:"bytes,5,opt,name=compiled_query,json=compiledQuery" json:"compiled_query,omitempty"`
	CompiledCursor       *CompiledCursor   `protobuf:"bytes,6,opt,name=compiled_cursor,json=compiledCursor" json:"compiled_cursor,omitempty"`
	Index                []*CompositeIndex `protobuf:"bytes,8,rep,name=index" json:"index,omitempty"`
	Version              []int64           `protobuf:"varint,11,rep,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QueryResult) Reset()         { *m = QueryResult{} }
func (m *QueryResult) String() string { return proto.CompactTextString(m) }
func (*QueryResult) ProtoMessage()    {}
func (*QueryResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{30}
}
func (m *QueryResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryResult.Unmarshal(m, b)
}
func (m *QueryResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryResult.Marshal(b, m, deterministic)
}
func (dst *QueryResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryResult.Merge(dst, src)
}
func (m *QueryResult) XXX_Size() int {
	return xxx_messageInfo_QueryResult.Size(m)
}
func (m *QueryResult) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryResult.DiscardUnknown(m)
}

var xxx_messageInfo_QueryResult proto.InternalMessageInfo

func (m *QueryResult) GetCursor() *Cursor {
	if m != nil {
		return m.Cursor
	}
	return nil
}

func (m *QueryResult) GetResult() []*EntityProto {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *QueryResult) GetSkippedResults() int32 {
	if m != nil && m.SkippedResults != nil {
		return *m.SkippedResults
	}
	return 0
}

func (m *QueryResult) GetMoreResults() bool {
	if m != nil && m.MoreResults != nil {
		return *m.MoreResults
	}
	return false
}

func (m *QueryResult) GetKeysOnly() bool {
	if m != nil && m.KeysOnly != nil {
		return *m.KeysOnly
	}
	return false
}

func (m *QueryResult) GetIndexOnly() bool {
	if m != nil && m.IndexOnly != nil {
		return *m.IndexOnly
	}
	return false
}

func (m *QueryResult) GetSmallOps() bool {
	if m != nil && m.SmallOps != nil {
		return *m.SmallOps
	}
	return false
}

func (m *QueryResult) GetCompiledQuery() *CompiledQuery {
	if m != nil {
		return m.CompiledQuery
	}
	return nil
}

func (m *QueryResult) GetCompiledCursor() *CompiledCursor {
	if m != nil {
		return m.CompiledCursor
	}
	return nil
}

func (m *QueryResult) GetIndex() []*CompositeIndex {
	if m != nil {
		return m.Index
	}
	return nil
}

func (m *QueryResult) GetVersion() []int64 {
	if m != nil {
		return m.Version
	}
	return nil
}

type AllocateIdsRequest struct {
	Header               *InternalHeader `protobuf:"bytes,4,opt,name=header" json:"header,omitempty"`
	ModelKey             *Reference      `protobuf:"bytes,1,opt,name=model_key,json=modelKey" json:"model_key,omitempty"`
	Size                 *int64          `protobuf:"varint,2,opt,name=size" json:"size,omitempty"`
	Max                  *int64          `protobuf:"varint,3,opt,name=max" json:"max,omitempty"`
	Reserve              []*Reference    `protobuf:"bytes,5,rep,name=reserve" json:"reserve,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AllocateIdsRequest) Reset()         { *m = AllocateIdsRequest{} }
func (m *AllocateIdsRequest) String() string { return proto.CompactTextString(m) }
func (*AllocateIdsRequest) ProtoMessage()    {}
func (*AllocateIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{31}
}
func (m *AllocateIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocateIdsRequest.Unmarshal(m, b)
}
func (m *AllocateIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocateIdsRequest.Marshal(b, m, deterministic)
}
func (dst *AllocateIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocateIdsRequest.Merge(dst, src)
}
func (m *AllocateIdsRequest) XXX_Size() int {
	return xxx_messageInfo_AllocateIdsRequest.Size(m)
}
func (m *AllocateIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocateIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AllocateIdsRequest proto.InternalMessageInfo

func (m *AllocateIdsRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AllocateIdsRequest) GetModelKey() *Reference {
	if m != nil {
		return m.ModelKey
	}
	return nil
}

func (m *AllocateIdsRequest) GetSize() int64 {
	if m != nil && m.Size != nil {
		return *m.Size
	}
	return 0
}

func (m *AllocateIdsRequest) GetMax() int64 {
	if m != nil && m.Max != nil {
		return *m.Max
	}
	return 0
}

func (m *AllocateIdsRequest) GetReserve() []*Reference {
	if m != nil {
		return m.Reserve
	}
	return nil
}

type AllocateIdsResponse struct {
	Start                *int64   `protobuf:"varint,1,req,name=start" json:"start,omitempty"`
	End                  *int64   `protobuf:"varint,2,req,name=end" json:"end,omitempty"`
	Cost                 *Cost    `protobuf:"bytes,3,opt,name=cost" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AllocateIdsResponse) Reset()         { *m = AllocateIdsResponse{} }
func (m *AllocateIdsResponse) String() string { return proto.CompactTextString(m) }
func (*AllocateIdsResponse) ProtoMessage()    {}
func (*AllocateIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{32}
}
func (m *AllocateIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllocateIdsResponse.Unmarshal(m, b)
}
func (m *AllocateIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllocateIdsResponse.Marshal(b, m, deterministic)
}
func (dst *AllocateIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocateIdsResponse.Merge(dst, src)
}
func (m *AllocateIdsResponse) XXX_Size() int {
	return xxx_messageInfo_AllocateIdsResponse.Size(m)
}
func (m *AllocateIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocateIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AllocateIdsResponse proto.InternalMessageInfo

func (m *AllocateIdsResponse) GetStart() int64 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *AllocateIdsResponse) GetEnd() int64 {
	if m != nil && m.End != nil {
		return *m.End
	}
	return 0
}

func (m *AllocateIdsResponse) GetCost() *Cost {
	if m != nil {
		return m.Cost
	}
	return nil
}

type CompositeIndices struct {
	Index                []*CompositeIndex `protobuf:"bytes,1,rep,name=index" json:"index,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CompositeIndices) Reset()         { *m = CompositeIndices{} }
func (m *CompositeIndices) String() string { return proto.CompactTextString(m) }
func (*CompositeIndices) ProtoMessage()    {}
func (*CompositeIndices) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{33}
}
func (m *CompositeIndices) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompositeIndices.Unmarshal(m, b)
}
func (m *CompositeIndices) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompositeIndices.Marshal(b, m, deterministic)
}
func (dst *CompositeIndices) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompositeIndices.Merge(dst, src)
}
func (m *CompositeIndices) XXX_Size() int {
	return xxx_messageInfo_CompositeIndices.Size(m)
}
func (m *CompositeIndices) XXX_DiscardUnknown() {
	xxx_messageInfo_CompositeIndices.DiscardUnknown(m)
}

var xxx_messageInfo_CompositeIndices proto.InternalMessageInfo

func (m *CompositeIndices) GetIndex() []*CompositeIndex {
	if m != nil {
		return m.Index
	}
	return nil
}

type AddActionsRequest struct {
	Header               *InternalHeader `protobuf:"bytes,3,opt,name=header" json:"header,omitempty"`
	Transaction          *Transaction    `protobuf:"bytes,1,req,name=transaction" json:"transaction,omitempty"`
	Action               []*Action       `protobuf:"bytes,2,rep,name=action" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddActionsRequest) Reset()         { *m = AddActionsRequest{} }
func (m *AddActionsRequest) String() string { return proto.CompactTextString(m) }
func (*AddActionsRequest) ProtoMessage()    {}
func (*AddActionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{34}
}
func (m *AddActionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActionsRequest.Unmarshal(m, b)
}
func (m *AddActionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActionsRequest.Marshal(b, m, deterministic)
}
func (dst *AddActionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActionsRequest.Merge(dst, src)
}
func (m *AddActionsRequest) XXX_Size() int {
	return xxx_messageInfo_AddActionsRequest.Size(m)
}
func (m *AddActionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddActionsRequest proto.InternalMessageInfo

func (m *AddActionsRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AddActionsRequest) GetTransaction() *Transaction {
	if m != nil {
		return m.Transaction
	}
	return nil
}

func (m *AddActionsRequest) GetAction() []*Action {
	if m != nil {
		return m.Action
	}
	return nil
}

type AddActionsResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddActionsResponse) Reset()         { *m = AddActionsResponse{} }
func (m *AddActionsResponse) String() string { return proto.CompactTextString(m) }
func (*AddActionsResponse) ProtoMessage()    {}
func (*AddActionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{35}
}
func (m *AddActionsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActionsResponse.Unmarshal(m, b)
}
func (m *AddActionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActionsResponse.Marshal(b, m, deterministic)
}
func (dst *AddActionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActionsResponse.Merge(dst, src)
}
func (m *AddActionsResponse) XXX_Size() int {
	return xxx_messageInfo_AddActionsResponse.Size(m)
}
func (m *AddActionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddActionsResponse proto.InternalMessageInfo

type BeginTransactionRequest struct {
	Header               *InternalHeader                          `protobuf:"bytes,3,opt,name=header" json:"header,omitempty"`
	App                  *string                                  `protobuf:"bytes,1,req,name=app" json:"app,omitempty"`
	AllowMultipleEg      *bool                                    `protobuf:"varint,2,opt,name=allow_multiple_eg,json=allowMultipleEg,def=0" json:"allow_multiple_eg,omitempty"`
	DatabaseId           *string                                  `protobuf:"bytes,4,opt,name=database_id,json=databaseId" json:"database_id,omitempty"`
	Mode                 *BeginTransactionRequest_TransactionMode `protobuf:"varint,5,opt,name=mode,enum=appengine.BeginTransactionRequest_TransactionMode,def=0" json:"mode,omitempty"`
	PreviousTransaction  *Transaction                             `protobuf:"bytes,7,opt,name=previous_transaction,json=previousTransaction" json:"previous_transaction,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *BeginTransactionRequest) Reset()         { *m = BeginTransactionRequest{} }
func (m *BeginTransactionRequest) String() string { return proto.CompactTextString(m) }
func (*BeginTransactionRequest) ProtoMessage()    {}
func (*BeginTransactionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{36}
}
func (m *BeginTransactionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginTransactionRequest.Unmarshal(m, b)
}
func (m *BeginTransactionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginTransactionRequest.Marshal(b, m, deterministic)
}
func (dst *BeginTransactionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginTransactionRequest.Merge(dst, src)
}
func (m *BeginTransactionRequest) XXX_Size() int {
	return xxx_messageInfo_BeginTransactionRequest.Size(m)
}
func (m *BeginTransactionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginTransactionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BeginTransactionRequest proto.InternalMessageInfo

const Default_BeginTransactionRequest_AllowMultipleEg bool = false
const Default_BeginTransactionRequest_Mode BeginTransactionRequest_TransactionMode = BeginTransactionRequest_UNKNOWN

func (m *BeginTransactionRequest) GetHeader() *InternalHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *BeginTransactionRequest) GetApp() string {
	if m != nil && m.App != nil {
		return *m.App
	}
	return ""
}

func (m *BeginTransactionRequest) GetAllowMultipleEg() bool {
	if m != nil && m.AllowMultipleEg != nil {
		return *m.AllowMultipleEg
	}
	return Default_BeginTransactionRequest_AllowMultipleEg
}

func (m *BeginTransactionRequest) GetDatabaseId() string {
	if m != nil && m.DatabaseId != nil {
		return *m.DatabaseId
	}
	return ""
}

func (m *BeginTransactionRequest) GetMode() BeginTransactionRequest_TransactionMode {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return Default_BeginTransactionRequest_Mode
}

func (m *BeginTransactionRequest) GetPreviousTransaction() *Transaction {
	if m != nil {
		return m.PreviousTransaction
	}
	return nil
}

type CommitResponse struct {
	Cost                 *Cost                     `protobuf:"bytes,1,opt,name=cost" json:"cost,omitempty"`
	Version              []*CommitResponse_Version `protobuf:"group,3,rep,name=Version,json=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *CommitResponse) Reset()         { *m = CommitResponse{} }
func (m *CommitResponse) String() string { return proto.CompactTextString(m) }
func (*CommitResponse) ProtoMessage()    {}
func (*CommitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{37}
}
func (m *CommitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitResponse.Unmarshal(m, b)
}
func (m *CommitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitResponse.Marshal(b, m, deterministic)
}
func (dst *CommitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitResponse.Merge(dst, src)
}
func (m *CommitResponse) XXX_Size() int {
	return xxx_messageInfo_CommitResponse.Size(m)
}
func (m *CommitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommitResponse proto.InternalMessageInfo

func (m *CommitResponse) GetCost() *Cost {
	if m != nil {
		return m.Cost
	}
	return nil
}

func (m *CommitResponse) GetVersion() []*CommitResponse_Version {
	if m != nil {
		return m.Version
	}
	return nil
}

type CommitResponse_Version struct {
	RootEntityKey        *Reference `protobuf:"bytes,4,req,name=root_entity_key,json=rootEntityKey" json:"root_entity_key,omitempty"`
	Version              *int64     `protobuf:"varint,5,req,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CommitResponse_Version) Reset()         { *m = CommitResponse_Version{} }
func (m *CommitResponse_Version) String() string { return proto.CompactTextString(m) }
func (*CommitResponse_Version) ProtoMessage()    {}
func (*CommitResponse_Version) Descriptor() ([]byte, []int) {
	return fileDescriptor_datastore_v3_83b17b80c34f6179, []int{37, 0}
}
func (m *CommitResponse_Version) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitResponse_Version.Unmarshal(m, b)
}
func (m *CommitResponse_Version) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitResponse_Version.Marshal(b, m, deterministic)
}
func (dst *CommitResponse_Version) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitResponse_Version.Merge(dst, src)
}
func (m *CommitResponse_Version) XXX_Size() int {
	return xxx_messageInfo_CommitResponse_Version.Size(m)
}
func (m *CommitResponse_Version) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitResponse_Version.DiscardUnknown(m)
}

var xxx_messageInfo_CommitResponse_Version proto.InternalMessageInfo

func (m *CommitResponse_Version) GetRootEntityKey() *Reference {
	if m != nil {
		return m.RootEntityKey
	}
	return nil
}

func (m *CommitResponse_Version) GetVersion() int64 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

func init() {
	proto.RegisterType((*Action)(nil), "appengine.Action")
	proto.RegisterType((*PropertyValue)(nil), "appengine.PropertyValue")
	proto.RegisterType((*PropertyValue_PointValue)(nil), "appengine.PropertyValue.PointValue")
	proto.RegisterType((*PropertyValue_UserValue)(nil), "appengine.PropertyValue.UserValue")
	proto.RegisterType((*PropertyValue_ReferenceValue)(nil), "appengine.PropertyValue.ReferenceValue")
	proto.RegisterType((*PropertyValue_ReferenceValue_PathElement)(nil), "appengine.PropertyValue.ReferenceValue.PathElement")
	proto.RegisterType((*Property)(nil), "appengine.Property")
	proto.RegisterType((*Path)(nil), "appengine.Path")
	proto.RegisterType((*Path_Element)(nil), "appengine.Path.Element")
	proto.RegisterType((*Reference)(nil), "appengine.Reference")
	proto.RegisterType((*User)(nil), "appengine.User")
	proto.RegisterType((*EntityProto)(nil), "appengine.EntityProto")
	proto.RegisterType((*CompositeProperty)(nil), "appengine.CompositeProperty")
	proto.RegisterType((*Index)(nil), "appengine.Index")
	proto.RegisterType((*Index_Property)(nil), "appengine.Index.Property")
	proto.RegisterType((*CompositeIndex)(nil), "appengine.CompositeIndex")
	proto.RegisterType((*IndexPostfix)(nil), "appengine.IndexPostfix")
	proto.RegisterType((*IndexPostfix_IndexValue)(nil), "appengine.IndexPostfix.IndexValue")
	proto.RegisterType((*IndexPosition)(nil), "appengine.IndexPosition")
	proto.RegisterType((*Snapshot)(nil), "appengine.Snapshot")
	proto.RegisterType((*InternalHeader)(nil), "appengine.InternalHeader")
	proto.RegisterType((*Transaction)(nil), "appengine.Transaction")
	proto.RegisterType((*Query)(nil), "appengine.Query")
	proto.RegisterType((*Query_Filter)(nil), "appengine.Query.Filter")
	proto.RegisterType((*Query_Order)(nil), "appengine.Query.Order")
	proto.RegisterType((*CompiledQuery)(nil), "appengine.CompiledQuery")
	proto.RegisterType((*CompiledQuery_PrimaryScan)(nil), "appengine.CompiledQuery.PrimaryScan")
	proto.RegisterType((*CompiledQuery_MergeJoinScan)(nil), "appengine.CompiledQuery.MergeJoinScan")
	proto.RegisterType((*CompiledQuery_EntityFilter)(nil), "appengine.CompiledQuery.EntityFilter")
	proto.RegisterType((*CompiledCursor)(nil), "appengine.CompiledCursor")
	proto.RegisterType((*CompiledCursor_Position)(nil), "appengine.CompiledCursor.Position")
	proto.RegisterType((*CompiledCursor_Position_IndexValue)(nil), "appengine.CompiledCursor.Position.IndexValue")
	proto.RegisterType((*Cursor)(nil), "appengine.Cursor")
	proto.RegisterType((*Error)(nil), "appengine.Error")
	proto.RegisterType((*Cost)(nil), "appengine.Cost")
	proto.RegisterType((*Cost_CommitCost)(nil), "appengine.Cost.CommitCost")
	proto.RegisterType((*GetRequest)(nil), "appengine.GetRequest")
	proto.RegisterType((*GetResponse)(nil), "appengine.GetResponse")
	proto.RegisterType((*GetResponse_Entity)(nil), "appengine.GetResponse.Entity")
	proto.RegisterType((*PutRequest)(nil), "appengine.PutRequest")
	proto.RegisterType((*PutResponse)(nil), "appengine.PutResponse")
	proto.RegisterType((*TouchRequest)(nil), "appengine.TouchRequest")
	proto.RegisterType((*TouchResponse)(nil), "appengine.TouchResponse")
	proto.RegisterType((*DeleteRequest)(nil), "appengine.DeleteRequest")
	proto.RegisterType((*DeleteResponse)(nil), "appengine.DeleteResponse")
	proto.RegisterType((*NextRequest)(nil), "appengine.NextRequest")
	proto.RegisterType((*QueryResult)(nil), "appengine.QueryResult")
	proto.RegisterType((*AllocateIdsRequest)(nil), "appengine.AllocateIdsRequest")
	proto.RegisterType((*AllocateIdsResponse)(nil), "appengine.AllocateIdsResponse")
	proto.RegisterType((*CompositeIndices)(nil), "appengine.CompositeIndices")
	proto.RegisterType((*AddActionsRequest)(nil), "appengine.AddActionsRequest")
	proto.RegisterType((*AddActionsResponse)(nil), "appengine.AddActionsResponse")
	proto.RegisterType((*BeginTransactionRequest)(nil), "appengine.BeginTransactionRequest")
	proto.RegisterType((*CommitResponse)(nil), "appengine.CommitResponse")
	proto.RegisterType((*CommitResponse_Version)(nil), "appengine.CommitResponse.Version")
}

func init() {
	proto.RegisterFile("google.golang.org/appengine/internal/datastore/datastore_v3.proto", fileDescriptor_datastore_v3_83b17b80c34f6179)
}

var fileDescriptor_datastore_v3_83b17b80c34f6179 = []byte{
	// 4156 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x73, 0xe3, 0x46,
	0x76, 0x37, 0xc1, 0xef, 0x47, 0x89, 0x82, 0x5a, 0xf3, 0xc1, 0xa1, 0x3f, 0x46, 0xc6, 0xac, 0x6d,
	0xd9, 0x6b, 0x73, 0x6c, 0xf9, 0x23, 0x5b, 0x4a, 0x76, 0x1d, 0x4a, 0xc4, 0x68, 0x90, 0xa1, 0x48,
	0xb9, 0x09, 0xd9, 0x9e, 0x5c, 0x50, 0x18, 0xa2, 0x29, 0x21, 0x43, 0x02, 0x30, 0x00, 0x6a, 0x46,
	0x93, 0xe4, 0x90, 0x4b, 0x2a, 0x55, 0x5b, 0xa9, 0x1c, 0x92, 0x4a, 0x25, 0xf9, 0x07, 0x72, 0xc8,
	0x39, 0x95, 0xaa, 0x54, 0xf6, 0x98, 0x5b, 0x0e, 0x7b, 0xc9, 0x31, 0x95, 0x73, 0xf2, 0x27, 0x24,
	0x39, 0xa4, 0xfa, 0x75, 0x03, 0x02, 0x28, 0x4a, 0x23, 0x6d, 0xf6, 0x90, 0x13, 0xd1, 0xef, 0xfd,
	0xba, 0xf1, 0xfa, 0xf5, 0xfb, 0x6c, 0x10, 0xba, 0xc7, 0xbe, 0x7f, 0x3c, 0x65, 0x9d, 0x63, 0x7f,
	0x6a, 0x7b, 0xc7, 0x1d, 0x3f, 0x3c, 0x7e, 0x68, 0x07, 0x01, 0xf3, 0x8e, 0x5d, 0x8f, 0x3d, 0x74,
	0xbd, 0x98, 0x85, 0x9e, 0x3d, 0x7d, 0xe8, 0xd8, 0xb1, 0x1d, 0xc5, 0x7e, 0xc8, 0xce, 0x9f, 0xac,
	0xd3, 0xcf, 0x3b, 0x41, 0xe8, 0xc7, 0x3e, 0xa9, 0xa7, 0x13, 0xb4, 0x1a, 0x54, 0xba, 0xe3, 0xd8,
	0xf5, 0x3d, 0xed, 0x1f, 0x2b, 0xb0, 0x7a, 0x18, 0xfa, 0x01, 0x0b, 0xe3, 0xb3, 0x6f, 0xed, 0xe9,
	0x9c, 0x91, 0x77, 0x00, 0x5c, 0x2f, 0xfe, 0xea, 0x0b, 0x1c, 0xb5, 0x0a, 0x9b, 0x85, 0xad, 0x22,
	0xcd, 0x50, 0x88, 0x06, 0x2b, 0xcf, 0x7c, 0x7f, 0xca, 0x6c, 0x4f, 0x20, 0x94, 0xcd, 0xc2, 0x56,
	0x8d, 0xe6, 0x68, 0x64, 0x13, 0x1a, 0x51, 0x1c, 0xba, 0xde, 0xb1, 0x80, 0x14, 0x37, 0x0b, 0x5b,
	0x75, 0x9a, 0x25, 0x71, 0x84, 0xe3, 0xcf, 0x9f, 0x4d, 0x99, 0x40, 0x94, 0x36, 0x0b, 0x5b, 0x05,
	0x9a, 0x25, 0x91, 0x3d, 0x80, 0xc0, 0x77, 0xbd, 0xf8, 0x14, 0x01, 0xe5, 0xcd, 0xc2, 0x16, 0x6c,
	0x3f, 0xe8, 0xa4, 0x7b, 0xe8, 0xe4, 0xa4, 0xee, 0x1c, 0x72, 0x28, 0x3e, 0xd2, 0xcc, 0x34, 0xf2,
	0xdb, 0x50, 0x9f, 0x47, 0x2c, 0x14, 0x6b, 0xd4, 0x70, 0x0d, 0xed, 0xd2, 0x35, 0x8e, 0x22, 0x16,
	0x8a, 0x25, 0xce, 0x27, 0x91, 0x21, 0x34, 0x43, 0x36, 0x61, 0x21, 0xf3, 0xc6, 0x4c, 0x2c, 0xb3,
	0x82, 0xcb, 0x7c, 0x70, 0xe9, 0x32, 0x34, 0x81, 0x8b, 0xb5, 0x16, 0xa6, 0xb7, 0xb7, 0x00, 0xce,
	0x85, 0x25, 0x2b, 0x50, 0x78, 0xd9, 0xaa, 0x6c, 0x2a, 0x5b, 0x05, 0x5a, 0x78, 0xc9, 0x47, 0x67,
	0xad, 0xaa, 0x18, 0x9d, 0xb5, 0xff, 0xa9, 0x00, 0xf5, 0x54, 0x26, 0x72, 0x0b, 0xca, 0x6c, 0x66,
	0xbb, 0xd3, 0x56, 0x7d, 0x53, 0xd9, 0xaa, 0x53, 0x31, 0x20, 0xf7, 0xa1, 0x61, 0xcf, 0xe3, 0x13,
	0xcb, 0xf1, 0x67, 0xb6, 0xeb, 0xb5, 0x00, 0x79, 0xc0, 0x49, 0x3d, 0xa4, 0x90, 0x36, 0xd4, 0x3c,
	0x77, 0xfc, 0xdc, 0xb3, 0x67, 0xac, 0xd5, 0xc0, 0x73, 0x48, 0xc7, 0xe4, 0x13, 0x20, 0x13, 0xe6,
	0xb0, 0xd0, 0x8e, 0x99, 0x63, 0xb9, 0x0e, 0xf3, 0x62, 0x37, 0x3e, 0x6b, 0xdd, 0x46, 0xd4, 0x7a,
	0xca, 0x31, 0x24, 0x23, 0x0f, 0x0f, 0x42, 0xff, 0xd4, 0x75, 0x58, 0xd8, 0xba, 0xb3, 0x00, 0x3f,
	0x94, 0x8c, 0xf6, 0xbf, 0x17, 0xa0, 0x99, 0xd7, 0x05, 0x51, 0xa1, 0x68, 0x07, 0x41, 0x6b, 0x15,
	0xa5, 0xe4, 0x8f, 0xe4, 0x6d, 0x00, 0x2e, 0x8a, 0x15, 0x05, 0xf6, 0x98, 0xb5, 0x6e, 0xe1, 0x5a,
	0x75, 0x4e, 0x19, 0x71, 0x02, 0x39, 0x82, 0x46, 0x60, 0xc7, 0x27, 0x6c, 0xca, 0x66, 0xcc, 0x8b,
	0x5b, 0xcd, 0xcd, 0xe2, 0x16, 0x6c, 0x7f, 0x7e, 0x4d, 0xd5, 0x77, 0x0e, 0xed, 0xf8, 0x44, 0x17,
	0x53, 0x69, 0x76, 0x9d, 0xb6, 0x0e, 0x8d, 0x0c, 0x8f, 0x10, 0x28, 0xc5, 0x67, 0x01, 0x6b, 0xad,
	0xa1, 0x5c, 0xf8, 0x4c, 0x9a, 0xa0, 0xb8, 0x4e, 0x4b, 0x45, 0xf3, 0x57, 0x5c, 0x87, 0x63, 0x50,
	0x87, 0xeb, 0x28, 0x22, 0x3e, 0x6b, 0xff, 0x51, 0x86, 0x5a, 0x22, 0x00, 0xe9, 0x42, 0x75, 0xc6,
	0x6c, 0xcf, 0xf5, 0x8e, 0xd1, 0x69, 0x9a, 0xdb, 0x6f, 0x2e, 0x11, 0xb3, 0x73, 0x20, 0x20, 0x3b,
	0x30, 0x18, 0x5a, 0x07, 0x7a, 0x77, 0x60, 0x0c, 0xf6, 0x69, 0x32, 0x8f, 0x1f, 0xa6, 0x7c, 0xb4,
	0xe6, 0xa1, 0x8b, 0x9e, 0x55, 0xa7, 0x20, 0x49, 0x47, 0xa1, 0x9b, 0x0a, 0x51, 0x14, 0x82, 0xe2,
	0x21, 0x76, 0xa0, 0x9c, 0xb8, 0x88, 0xb2, 0xd5, 0xd8, 0x6e, 0x5d, 0xa6, 0x1c, 0x2a, 0x60, 0xdc,
	0x20, 0x66, 0xf3, 0x69, 0xec, 0x06, 0x53, 0xee, 0x76, 0xca, 0x56, 0x8d, 0xa6, 0x63, 0xf2, 0x1e,
	0x40, 0xc4, 0xec, 0x70, 0x7c, 0x62, 0x3f, 0x9b, 0xb2, 0x56, 0x85, 0x7b, 0xf6, 0x4e, 0x79, 0x62,
	0x4f, 0x23, 0x46, 0x33, 0x0c, 0x62, 0xc3, 0xdd, 0x49, 0x1c, 0x59, 0xb1, 0xff, 0x9c, 0x79, 0xee,
	0x2b, 0x9b, 0x07, 0x12, 0xcb, 0x0f, 0xf8, 0x0f, 0xfa, 0x58, 0x73, 0xfb, 0xc3, 0x65, 0x5b, 0x7f,
	0x14, 0x47, 0x66, 0x66, 0xc6, 0x10, 0x27, 0xd0, 0xdb, 0x93, 0x65, 0x64, 0xd2, 0x86, 0xca, 0xd4,
	0x1f, 0xdb, 0x53, 0xd6, 0xaa, 0x73, 0x2d, 0xec, 0x28, 0xcc, 0xa3, 0x92, 0xa2, 0xfd, 0xb3, 0x02,
	0x55, 0xa9, 0x47, 0xd2, 0x84, 0x8c, 0x26, 0xd5, 0x37, 0x48, 0x0d, 0x4a, 0xbb, 0xfd, 0xe1, 0xae,
	0xda, 0xe4, 0x4f, 0xa6, 0xfe, 0xbd, 0xa9, 0xae, 0x71, 0xcc, 0xee, 0x53, 0x53, 0x1f, 0x99, 0x94,
	0x63, 0x54, 0xb2, 0x0e, 0xab, 0x5d, 0x73, 0x78, 0x60, 0xed, 0x75, 0x4d, 0x7d, 0x7f, 0x48, 0x9f,
	0xaa, 0x05, 0xb2, 0x0a, 0x75, 0x24, 0xf5, 0x8d, 0xc1, 0x13, 0x55, 0xe1, 0x33, 0x70, 0x68, 0x1a,
	0x66, 0x5f, 0x57, 0x8b, 0x44, 0x85, 0x15, 0x31, 0x63, 0x38, 0x30, 0xf5, 0x81, 0xa9, 0x96, 0x52,
	0xca, 0xe8, 0xe8, 0xe0, 0xa0, 0x4b, 0x9f, 0xaa, 0x65, 0xb2, 0x06, 0x0d, 0xa4, 0x74, 0x8f, 0xcc,
	0xc7, 0x43, 0xaa, 0x56, 0x48, 0x03, 0xaa, 0xfb, 0x3d, 0xeb, 0xbb, 0xc7, 0xfa, 0x40, 0xad, 0x92,
	0x15, 0xa8, 0xed, 0xf7, 0x2c, 0xfd, 0xa0, 0x6b, 0xf4, 0xd5, 0x1a, 0x9f, 0xbd, 0xaf, 0x0f, 0xe9,
	0x68, 0x64, 0x1d, 0x0e, 0x8d, 0x81, 0xa9, 0xd6, 0x49, 0x1d, 0xca, 0xfb, 0x3d, 0xcb, 0x38, 0x50,
	0x81, 0x10, 0x68, 0xee, 0xf7, 0xac, 0xc3, 0xc7, 0xc3, 0x81, 0x3e, 0x38, 0x3a, 0xd8, 0xd5, 0xa9,
	0xda, 0x20, 0xb7, 0x40, 0xe5, 0xb4, 0xe1, 0xc8, 0xec, 0xf6, 0xbb, 0xbd, 0x1e, 0xd5, 0x47, 0x23,
	0x75, 0x85, 0x4b, 0xbd, 0xdf, 0xb3, 0x68, 0xd7, 0xe4, 0xfb, 0x5a, 0xe5, 0x2f, 0xe4, 0x7b, 0x7f,
	0xa2, 0x3f, 0x55, 0xd7, 0xf9, 0x2b, 0xf4, 0x81, 0x69, 0x98, 0x4f, 0xad, 0x43, 0x3a, 0x34, 0x87,
	0xea, 0x06, 0x17, 0xd0, 0x18, 0xf4, 0xf4, 0xef, 0xad, 0x6f, 0xbb, 0xfd, 0x23, 0x5d, 0x25, 0xda,
	0x8f, 0xe1, 0xf6, 0xd2, 0x33, 0xe1, 0xaa, 0x7b, 0x6c, 0x1e, 0xf4, 0xd5, 0x02, 0x7f, 0xe2, 0x9b,
	0x52, 0x15, 0xed, 0x0f, 0xa0, 0xc4, 0x5d, 0x86, 0x7c, 0x06, 0xd5, 0xc4, 0x1b, 0x0b, 0xe8, 0x8d,
	0x77, 0xb3, 0x67, 0x6d, 0xc7, 0x27, 0x9d, 0xc4, 0xe3, 0x12, 0x5c, 0xbb, 0x0b, 0xd5, 0x45, 0x4f,
	0x53, 0x2e, 0x78, 0x5a, 0xf1, 0x82, 0xa7, 0x95, 0x32, 0x9e, 0x66, 0x43, 0x3d, 0xf5, 0xed, 0x9b,
	0x47, 0x91, 0x07, 0x50, 0xe2, 0xde, 0xdf, 0x6a, 0xa2, 0x87, 0xac, 0x2d, 0x08, 0x4c, 0x91, 0xa9,
	0xfd, 0x43, 0x01, 0x4a, 0x3c, 0xda, 0x9e, 0x07, 0xda, 0xc2, 0x15, 0x81, 0x56, 0xb9, 0x32, 0xd0,
	0x16, 0xaf, 0x15, 0x68, 0x2b, 0x37, 0x0b, 0xb4, 0xd5, 0x4b, 0x02, 0xad, 0xf6, 0x67, 0x45, 0x68,
	0xe8, 0x38, 0xf3, 0x10, 0x13, 0xfd, 0xfb, 0x50, 0x7c, 0xce, 0xce, 0x50, 0x3f, 0x8d, 0xed, 0x5b,
	0x99, 0xdd, 0xa6, 0x2a, 0xa4, 0x1c, 0x40, 0xb6, 0x61, 0x45, 0xbc, 0xd0, 0x3a, 0x0e, 0xfd, 0x79,
	0xd0, 0x52, 0x97, 0xab, 0xa7, 0x21, 0x40, 0xfb, 0x1c, 0x43, 0xde, 0x83, 0xb2, 0xff, 0xc2, 0x63,
	0x21, 0xc6, 0xc1, 0x3c, 0x98, 0x2b, 0x8f, 0x0a, 0x2e, 0x79, 0x08, 0xa5, 0xe7, 0xae, 0xe7, 0xe0,
	0x19, 0xe6, 0x23, 0x61, 0x46, 0xd0, 0xce, 0x13, 0xd7, 0x73, 0x28, 0x02, 0xc9, 0x3d, 0xa8, 0xf1,
	0x5f, 0x8c, 0x7b, 0x65, 0xdc, 0x68, 0x95, 0x8f, 0x79, 0xd0, 0x7b, 0x08, 0xb5, 0x40, 0xc6, 0x10,
	0x4c, 0x00, 0x8d, 0xed, 0x8d, 0x25, 0xe1, 0x85, 0xa6, 0x20, 0xf2, 0x15, 0xac, 0x84, 0xf6, 0x0b,
	0x2b, 0x9d, 0xb4, 0x76, 0xf9, 0xa4, 0x46, 0x68, 0xbf, 0x48, 0x23, 0x38, 0x81, 0x52, 0x68, 0x7b,
	0xcf, 0x5b, 0x64, 0xb3, 0xb0, 0x55, 0xa6, 0xf8, 0xac, 0x7d, 0x01, 0x25, 0x2e, 0x25, 0x8f, 0x08,
	0xfb, 0x3d, 0xf4, 0xff, 0xee, 0x9e, 0xa9, 0x16, 0x12, 0x7f, 0xfe, 0x96, 0x47, 0x03, 0x45, 0x72,
	0x0f, 0xf4, 0xd1, 0xa8, 0xbb, 0xaf, 0xab, 0x45, 0xad, 0x07, 0xeb, 0x7b, 0xfe, 0x2c, 0xf0, 0x23,
	0x37, 0x66, 0xe9, 0xf2, 0xf7, 0xa0, 0xe6, 0x7a, 0x0e, 0x7b, 0x69, 0xb9, 0x0e, 0x9a, 0x56, 0x91,
	0x56, 0x71, 0x6c, 0x38, 0xdc, 0xe4, 0x4e, 0x65, 0x31, 0x55, 0xe4, 0x26, 0x87, 0x03, 0xed, 0x2f,
	0x15, 0x28, 0x1b, 0x1c, 0xc1, 0x8d, 0x4f, 0x9e, 0x14, 0x7a, 0x8f, 0x30, 0x4c, 0x10, 0x24, 0x93,
	0xfb, 0x50, 0x1b, 0x6a, 0xb6, 0x37, 0x66, 0xbc, 0xe2, 0xc3, 0x3c, 0x50, 0xa3, 0xe9, 0x98, 0x7c,
	0x99, 0xd1, 0x9f, 0x82, 0x2e, 0x7b, 0x2f, 0xa3, 0x0a, 0x7c, 0xc1, 0x12, 0x2d, 0xb6, 0xff, 0xaa,
	0x90, 0x49, 0x6e, 0xcb, 0x12, 0x4f, 0x1f, 0xea, 0x8e, 0x1b, 0x32, 0xac, 0x23, 0xe5, 0x41, 0x3f,
	0xb8, 0x74, 0xe1, 0x4e, 0x2f, 0x81, 0xee, 0xd4, 0xbb, 0xa3, 0x3d, 0x7d, 0xd0, 0xe3, 0x99, 0xef,
	0x7c, 0x01, 0xed, 0x23, 0xa8, 0xa7, 0x10, 0x0c, 0xc7, 0x09, 0x48, 0x2d, 0x70, 0xf5, 0xf6, 0xf4,
	0x74, 0xac, 0x68, 0x7f, 0xad, 0x40, 0x33, 0xd5, 0xaf, 0xd0, 0xd0, 0x6d, 0xa8, 0xd8, 0x41, 0x90,
	0xa8, 0xb6, 0x4e, 0xcb, 0x76, 0x10, 0x18, 0x8e, 0x8c, 0x2d, 0x0a, 0x6a, 0x9b, 0xc7, 0x96, 0x4f,
	0x01, 0x1c, 0x36, 0x71, 0x3d, 0x17, 0x85, 0x2e, 0xa2, 0xc1, 0xab, 0x8b, 0x42, 0xd3, 0x0c, 0x86,
	0x7c, 0x09, 0xe5, 0x28, 0xb6, 0x63, 0x91, 0x2b, 0x9b, 0xdb, 0xf7, 0x33, 0xe0, 0xbc, 0x08, 0x9d,
	0x11, 0x87, 0x51, 0x81, 0x26, 0x5f, 0xc1, 0x2d, 0xdf, 0x9b, 0x9e, 0x59, 0xf3, 0x88, 0x59, 0xee,
	0xc4, 0x0a, 0xd9, 0x0f, 0x73, 0x37, 0x64, 0x4e, 0x3e, 0xa7, 0xae, 0x73, 0xc8, 0x51, 0xc4, 0x8c,
	0x09, 0x95, 0x7c, 0xed, 0x6b, 0x28, 0xe3, 0x3a, 0x7c, 0xcf, 0xdf, 0x51, 0xc3, 0xd4, 0xad, 0xe1,
	0xa0, 0xff, 0x54, 0xe8, 0x80, 0xea, 0xdd, 0x9e, 0x85, 0x44, 0x55, 0xe1, 0xc1, 0xbe, 0xa7, 0xf7,
	0x75, 0x53, 0xef, 0xa9, 0x45, 0x9e, 0x3d, 0x74, 0x4a, 0x87, 0x54, 0x2d, 0x69, 0xff, 0x53, 0x80,
	0x15, 0x94, 0xe7, 0xd0, 0x8f, 0xe2, 0x89, 0xfb, 0x92, 0xec, 0x41, 0x43, 0x98, 0xdd, 0xa9, 0x2c,
	0xe8, 0xb9, 0x33, 0x68, 0x8b, 0x7b, 0x96, 0x68, 0x31, 0x90, 0x75, 0xb4, 0x9b, 0x3e, 0x27, 0x21,
	0x45, 0x41, 0xa7, 0xbf, 0x22, 0xa4, 0xbc, 0x05, 0x95, 0x67, 0x6c, 0xe2, 0x87, 0x22, 0x04, 0xd6,
	0x76, 0x4a, 0x71, 0x38, 0x67, 0x54, 0xd2, 0xda, 0x36, 0xc0, 0xf9, 0xfa, 0xe4, 0x01, 0xac, 0x26,
	0xc6, 0x66, 0xa1, 0x71, 0x89, 0x93, 0x5b, 0x49, 0x88, 0x83, 0x5c, 0x75, 0xa3, 0x5c, 0xab, 0xba,
	0xd1, 0xbe, 0x86, 0xd5, 0x64, 0x3f, 0xe2, 0xfc, 0x54, 0x21, 0x79, 0x01, 0x63, 0xca, 0x82, 0x8c,
	0xca, 0x45, 0x19, 0xb5, 0x9f, 0x41, 0x6d, 0xe4, 0xd9, 0x41, 0x74, 0xe2, 0xc7, 0xdc, 0x7a, 0xe2,
	0x48, 0xfa, 0xaa, 0x12, 0x47, 0x9a, 0x06, 0x15, 0x7e, 0x38, 0xf3, 0x88, 0xbb, 0xbf, 0x31, 0xe8,
	0xee, 0x99, 0xc6, 0xb7, 0xba, 0xfa, 0x06, 0x01, 0xa8, 0xc8, 0xe7, 0x82, 0xa6, 0x41, 0xd3, 0x90,
	0xed, 0xd8, 0x63, 0x66, 0x3b, 0x2c, 0xe4, 0x12, 0xfc, 0xe0, 0x47, 0x89, 0x04, 0x3f, 0xf8, 0x91,
	0xf6, 0x17, 0x05, 0x68, 0x98, 0xa1, 0xed, 0x45, 0xb6, 0x30, 0xf7, 0xcf, 0xa0, 0x72, 0x82, 0x58,
	0x74, 0xa3, 0xc6, 0x82, 0x7f, 0x66, 0x17, 0xa3, 0x12, 0x48, 0xee, 0x40, 0xe5, 0xc4, 0xf6, 0x9c,
	0xa9, 0xd0, 0x5a, 0x85, 0xca, 0x51, 0x92, 0x1b, 0x95, 0xf3, 0xdc, 0xb8, 0x05, 0x2b, 0x33, 0x3b,
	0x7c, 0x6e, 0x8d, 0x4f, 0x6c, 0xef, 0x98, 0x45, 0xf2, 0x60, 0xa4, 0x05, 0x36, 0x38, 0x6b, 0x4f,
	0x70, 0xb4, 0xbf, 0x5f, 0x81, 0xf2, 0x37, 0x73, 0x16, 0x9e, 0x65, 0x04, 0xfa, 0xe0, 0xba, 0x02,
	0xc9, 0x17, 0x17, 0x2e, 0x4b, 0xca, 0x6f, 0x2f, 0x26, 0x65, 0x22, 0x53, 0x84, 0xc8, 0x95, 0x22,
	0x0b, 0x7c, 0x9a, 0x09, 0x63, 0xeb, 0x57, 0xd8, 0xda, 0x79, 0x70, 0x7b, 0x08, 0x95, 0x89, 0x3b,
	0x8d, 0x51, 0x75, 0x8b, 0xd5, 0x08, 0xee, 0xa5, 0xf3, 0x08, 0xd9, 0x54, 0xc2, 0xc8, 0xbb, 0xb0,
	0x22, 0x2a, 0x59, 0xeb, 0x07, 0xce, 0xc6, 0x82, 0x95, 0xf7, 0xa6, 0x48, 0x13, 0xbb, 0xff, 0x18,
	0xca, 0x7e, 0xc8, 0x37, 0x5f, 0xc7, 0x25, 0xef, 0x5c, 0x58, 0x72, 0xc8, 0xb9, 0x54, 0x80, 0xc8,
	0x87, 0x50, 0x3a, 0x71, 0xbd, 0x18, 0xb3, 0x46, 0x73, 0xfb, 0xf6, 0x05, 0xf0, 0x63, 0xd7, 0x8b,
	0x29, 0x42, 0x78, 0x98, 0x1f, 0xfb, 0x73, 0x2f, 0x6e, 0xdd, 0xc5, 0x0c, 0x23, 0x06, 0xe4, 0x1e,
	0x54, 0xfc, 0xc9, 0x24, 0x62, 0x31, 0x76, 0x96, 0xe5, 0x9d, 0xc2, 0xa7, 0x54, 0x12, 0xf8, 0x84,
	0xa9, 0x3b, 0x73, 0x63, 0xec, 0x43, 0xca, 0x54, 0x0c, 0xc8, 0x2e, 0xac, 0x8d, 0xfd, 0x59, 0xe0,
	0x4e, 0x99, 0x63, 0x8d, 0xe7, 0x61, 0xe4, 0x87, 0xad, 0x77, 0x2e, 0x1c, 0xd3, 0x9e, 0x44, 0xec,
	0x21, 0x80, 0x36, 0xc7, 0xb9, 0x31, 0x31, 0x60, 0x83, 0x79, 0x8e, 0xb5, 0xb8, 0xce, 0xfd, 0xd7,
	0xad, 0xb3, 0xce, 0x3c, 0x27, 0x4f, 0x4a, 0xc4, 0xc1, 0x48, 0x68, 0x61, 0xcc, 0x68, 0x6d, 0x60,
	0x90, 0xb9, 0x77, 0x69, 0xac, 0x14, 0xe2, 0x64, 0xc2, 0xf7, 0x6f, 0xc0, 0x2d, 0x19, 0x22, 0xad,
	0x80, 0x85, 0x13, 0x36, 0x8e, 0xad, 0x60, 0x6a, 0x7b, 0x58, 0xca, 0xa5, 0xc6, 0x4a, 0x24, 0xe4,
	0x50, 0x20, 0x0e, 0xa7, 0xb6, 0x47, 0x34, 0xa8, 0x3f, 0x67, 0x67, 0x91, 0xc5, 0x23, 0x29, 0x76,
	0xae, 0x29, 0xba, 0xc6, 0xe9, 0x43, 0x6f, 0x7a, 0x46, 0x7e, 0x02, 0x8d, 0xf8, 0xdc, 0xdb, 0xb0,
	0x61, 0x6d, 0xe4, 0x4e, 0x35, 0xe3, 0x8b, 0x34, 0x0b, 0x25, 0xf7, 0xa1, 0x2a, 0x35, 0xd4, 0xba,
	0x97, 0x5d, 0x3b, 0xa1, 0xf2, 0xc4, 0x3c, 0xb1, 0xdd, 0xa9, 0x7f, 0xca, 0x42, 0x6b, 0x16, 0xb5,
	0xda, 0xe2, 0xb6, 0x24, 0x21, 0x1d, 0x44, 0xdc, 0x4f, 0xa3, 0x38, 0xf4, 0xbd, 0xe3, 0xd6, 0x26,
	0xde, 0x93, 0xc8, 0xd1, 0xc5, 0xe0, 0xf7, 0x2e, 0x66, 0xfe, 0x7c, 0xf0, 0xfb, 0x1c, 0xee, 0x60,
	0x65, 0x66, 0x3d, 0x3b, 0xb3, 0xf2, 0x68, 0x0d, 0xd1, 0x1b, 0xc8, 0xdd, 0x3d, 0x3b, 0xcc, 0x4e,
	0x6a, 0x43, 0xcd, 0x71, 0xa3, 0xd8, 0xf5, 0xc6, 0x71, 0xab, 0x85, 0xef, 0x4c, 0xc7, 0xe4, 0x33,
	0xb8, 0x3d, 0x73, 0x3d, 0x2b, 0xb2, 0x27, 0xcc, 0x8a, 0x5d, 0xee, 0x9b, 0x6c, 0xec, 0x7b, 0x4e,
	0xd4, 0x7a, 0x80, 0x82, 0x93, 0x99, 0xeb, 0x8d, 0xec, 0x09, 0x33, 0xdd, 0x19, 0x1b, 0x09, 0x0e,
	0xf9, 0x08, 0xd6, 0x11, 0x1e, 0xb2, 0x60, 0xea, 0x8e, 0x6d, 0xf1, 0xfa, 0x1f, 0xe1, 0xeb, 0xd7,
	0x38, 0x83, 0x0a, 0x3a, 0xbe, 0xfa, 0x63, 0x68, 0x06, 0x2c, 0x8c, 0xdc, 0x28, 0xb6, 0xa4, 0x45,
	0xbf, 0x97, 0xd5, 0xda, 0xaa, 0x64, 0x0e, 0x91, 0xd7, 0xfe, 0xcf, 0x02, 0x54, 0x84, 0x73, 0x92,
	0x4f, 0x41, 0xf1, 0x03, 0xbc, 0x06, 0x69, 0x6e, 0x6f, 0x5e, 0xe2, 0xc1, 0x9d, 0x61, 0xc0, 0xeb,
	0x5e, 0x3f, 0xa4, 0x8a, 0x1f, 0xdc, 0xb8, 0x28, 0xd4, 0xfe, 0x10, 0x6a, 0xc9, 0x02, 0xbc, 0xbc,
	0xe8, 0xeb, 0xa3, 0x91, 0x65, 0x3e, 0xee, 0x0e, 0xd4, 0x02, 0xb9, 0x03, 0x24, 0x1d, 0x5a, 0x43,
	0x6a, 0xe9, 0xdf, 0x1c, 0x75, 0xfb, 0xaa, 0x82, 0x5d, 0x1a, 0xd5, 0xbb, 0xa6, 0x4e, 0x05, 0xb2,
	0x48, 0xee, 0xc1, 0xed, 0x2c, 0xe5, 0x1c, 0x5c, 0xc2, 0x14, 0x8c, 0x8f, 0x65, 0x52, 0x01, 0xc5,
	0x18, 0xa8, 0x15, 0x9e, 0x16, 0xf4, 0xef, 0x8d, 0x91, 0x39, 0x52, 0xab, 0xed, 0xbf, 0x29, 0x40,
	0x19, 0xc3, 0x06, 0x3f, 0x9f, 0x54, 0x72, 0x71, 0x5d, 0x73, 0x5e, 0xb9, 0x1a, 0xd9, 0x92, 0xaa,
	0x81, 0x01, 0x65, 0x73, 0x79, 0xf4, 0xf9, 0xb5, 0xd6, 0x53, 0x3f, 0x85, 0x12, 0x8f, 0x52, 0xbc,
	0x43, 0x1c, 0xd2, 0x9e, 0x4e, 0xad, 0x47, 0x06, 0x1d, 0xf1, 0x2a, 0x97, 0x40, 0xb3, 0x3b, 0xd8,
	0xd3, 0x47, 0xe6, 0x30, 0xa1, 0xa1, 0x56, 0x1e, 0x19, 0x7d, 0x33, 0x45, 0x15, 0xb5, 0x9f, 0xd7,
	0x60, 0x35, 0x89, 0x09, 0x22, 0x82, 0x3e, 0x82, 0x46, 0x10, 0xba, 0x33, 0x3b, 0x3c, 0x8b, 0xc6,
	0xb6, 0x87, 0x49, 0x01, 0xb6, 0x7f, 0xb4, 0x24, 0xaa, 0x88, 0x1d, 0x1d, 0x0a, 0xec, 0x68, 0x6c,
	0x7b, 0x34, 0x3b, 0x91, 0xf4, 0x61, 0x75, 0xc6, 0xc2, 0x63, 0xf6, 0x7b, 0xbe, 0xeb, 0xe1, 0x4a,
	0x55, 0x8c, 0xc8, 0xef, 0x5f, 0xba, 0xd2, 0x01, 0x47, 0xff, 0x8e, 0xef, 0x7a, 0xb8, 0x56, 0x7e,
	0x32, 0xf9, 0x04, 0xea, 0xa2, 0x12, 0x72, 0xd8, 0x04, 0x63, 0xc5, 0xb2, 0xda, 0x4f, 0xd4, 0xe8,
	0x3d, 0x36, 0xc9, 0xc4, 0x65, 0xb8, 0x34, 0x2e, 0x37, 0xb2, 0x71, 0xf9, 0xcd, 0x6c, 0x2c, 0x5a,
	0x11, 0x55, 0x78, 0x1a, 0x84, 0x2e, 0x38, 0x7c, 0x6b, 0x89, 0xc3, 0x77, 0x60, 0x23, 0xf1, 0x55,
	0xcb, 0xf5, 0x26, 0xee, 0x4b, 0x2b, 0x72, 0x5f, 0x89, 0xd8, 0x53, 0xa6, 0xeb, 0x09, 0xcb, 0xe0,
	0x9c, 0x91, 0xfb, 0x8a, 0x11, 0x23, 0xe9, 0xe0, 0x64, 0x0e, 0x5c, 0xc5, 0xab, 0xc9, 0xf7, 0x2e,
	0x55, 0x8f, 0x68, 0xbe, 0x64, 0x46, 0xcc, 0x4d, 0x6d, 0xff, 0x52, 0x81, 0x46, 0xe6, 0x1c, 0x78,
	0xf6, 0x16, 0xca, 0x42, 0x61, 0xc5, 0x55, 0x94, 0x50, 0x1f, 0x4a, 0xfa, 0x26, 0xd4, 0xa3, 0xd8,
	0x0e, 0x63, 0x8b, 0x17, 0x57, 0xb2, 0xdd, 0x45, 0xc2, 0x13, 0x76, 0x46, 0x3e, 0x80, 0x35, 0xc1,
	0x74, 0xbd, 0xf1, 0x74, 0x1e, 0xb9, 0xa7, 0xa2, 0x99, 0xaf, 0xd1, 0x26, 0x92, 0x8d, 0x84, 0x4a,
	0xee, 0x42, 0x95, 0x67, 0x21, 0xbe, 0x86, 0x68, 0xfa, 0x2a, 0xcc, 0x73, 0xf8, 0x0a, 0x0f, 0x60,
	0x95, 0x33, 0xce, 0xe7, 0x57, 0xc4, 0x2d, 0x33, 0xf3, 0x9c, 0xf3, 0xd9, 0x1d, 0xd8, 0x10, 0xaf,
	0x09, 0x44, 0xf1, 0x2a, 0x2b, 0xdc, 0x3b, 0xa8, 0xd8, 0x75, 0x64, 0xc9, 0xb2, 0x56, 0x14, 0x9c,
	0x1f, 0x01, 0xcf, 0x5e, 0x0b, 0xe8, 0xbb, 0x22, 0x94, 0x31, 0xcf, 0xc9, 0x61, 0x77, 0xe1, 0x1d,
	0x8e, 0x9d, 0x7b, 0x76, 0x10, 0x4c, 0x5d, 0xe6, 0x58, 0x53, 0xff, 0x18, 0x43, 0x66, 0x14, 0xdb,
	0xb3, 0xc0, 0x9a, 0x47, 0xad, 0x0d, 0x0c, 0x99, 0x6d, 0xe6, 0x39, 0x47, 0x09, 0xa8, 0xef, 0x1f,
	0x9b, 0x09, 0xe4, 0x28, 0x6a, 0xff, 0x3e, 0xac, 0xe6, 0xec, 0x71, 0x41, 0xa7, 0x35, 0x74, 0xfe,
	0x8c, 0x4e, 0xdf, 0x85, 0x95, 0x20, 0x64, 0xe7, 0xa2, 0xd5, 0x51, 0xb4, 0x86, 0xa0, 0x09, 0xb1,
	0xb6, 0x60, 0x05, 0x79, 0x96, 0x20, 0xe6, 0xf3, 0x63, 0x03, 0x59, 0x87, 0xc8, 0x69, 0xbf, 0x80,
	0x95, 0xec, 0x69, 0x93, 0x77, 0x33, 0x69, 0xa1, 0x99, 0xcb, 0x93, 0x69, 0x76, 0x48, 0x2a, 0xb2,
	0xf5, 0x4b, 0x2a, 0x32, 0x72, 0x9d, 0x8a, 0x4c, 0xfb, 0x2f, 0xd9, 0x9c, 0x65, 0x2a, 0x84, 0x9f,
	0x41, 0x2d, 0x90, 0xf5, 0x38, 0x5a, 0x52, 0xfe, 0x12, 0x3e, 0x0f, 0xee, 0x24, 0x95, 0x3b, 0x4d,
	0xe7, 0xb4, 0xff, 0x56, 0x81, 0x5a, 0x5a, 0xd0, 0xe7, 0x2c, 0xef, 0xcd, 0x05, 0xcb, 0x3b, 0x90,
	0x1a, 0x16, 0x0a, 0x7c, 0x1b, 0xa3, 0xc5, 0x27, 0xaf, 0x7f, 0xd7, 0xc5, 0xb6, 0xe7, 0x34, 0xdb,
	0xf6, 0x6c, 0xbe, 0xae, 0xed, 0xf9, 0xe4, 0xa2, 0xc1, 0xbf, 0x95, 0xe9, 0x2d, 0x16, 0xcc, 0xbe,
	0xfd, 0x7d, 0xae, 0x0f, 0xca, 0x26, 0x84, 0x77, 0xc4, 0x7e, 0xd2, 0x84, 0x90, 0xb6, 0x3f, 0xf7,
	0xaf, 0xd7, 0xfe, 0x6c, 0x43, 0x45, 0xea, 0xfc, 0x0e, 0x54, 0x64, 0x4d, 0x27, 0x1b, 0x04, 0x31,
	0x3a, 0x6f, 0x10, 0x0a, 0xb2, 0x4e, 0xd7, 0x7e, 0xae, 0x40, 0x59, 0x0f, 0x43, 0x3f, 0xd4, 0xfe,
	0x48, 0x81, 0x3a, 0x3e, 0xed, 0xf9, 0x0e, 0xe3, 0xd9, 0x60, 0xb7, 0xdb, 0xb3, 0xa8, 0xfe, 0xcd,
	0x91, 0x8e, 0xd9, 0xa0, 0x0d, 0x77, 0xf6, 0x86, 0x83, 0xbd, 0x23, 0x4a, 0xf5, 0x81, 0x69, 0x99,
	0xb4, 0x3b, 0x18, 0xf1, 0xb6, 0x67, 0x38, 0x50, 0x15, 0x9e, 0x29, 0x8c, 0x81, 0xa9, 0xd3, 0x41,
	0xb7, 0x6f, 0x89, 0x56, 0xb4, 0x88, 0x77, 0xb3, 0xba, 0xde, 0xb3, 0xf0, 0xd6, 0x51, 0x2d, 0xf1,
	0x96, 0xd5, 0x34, 0x0e, 0xf4, 0xe1, 0x91, 0xa9, 0x96, 0xc9, 0x6d, 0x58, 0x3f, 0xd4, 0xe9, 0x81,
	0x31, 0x1a, 0x19, 0xc3, 0x81, 0xd5, 0xd3, 0x07, 0x86, 0xde, 0x53, 0x2b, 0x7c, 0x9d, 0x5d, 0x63,
	0xdf, 0xec, 0xee, 0xf6, 0x75, 0xb9, 0x4e, 0x95, 0x6c, 0xc2, 0x5b, 0x7b, 0xc3, 0x83, 0x03, 0xc3,
	0x34, 0xf5, 0x9e, 0xb5, 0x7b, 0x64, 0x5a, 0x23, 0xd3, 0xe8, 0xf7, 0xad, 0xee, 0xe1, 0x61, 0xff,
	0x29, 0x4f, 0x60, 0x35, 0x72, 0x17, 0x36, 0xf6, 0xba, 0x87, 0xdd, 0x5d, 0xa3, 0x6f, 0x98, 0x4f,
	0xad, 0x9e, 0x31, 0xe2, 0xf3, 0x7b, 0x6a, 0x9d, 0x27, 0x6c, 0x93, 0x3e, 0xb5, 0xba, 0x7d, 0x14,
	0xcd, 0xd4, 0xad, 0xdd, 0xee, 0xde, 0x13, 0x7d, 0xd0, 0x53, 0x81, 0x0b, 0x30, 0xea, 0x3e, 0xd2,
	0x2d, 0x2e, 0x92, 0x65, 0x0e, 0x87, 0xd6, 0xb0, 0xdf, 0x53, 0x1b, 0xda, 0xbf, 0x14, 0xa1, 0xb4,
	0xe7, 0x47, 0x31, 0xf7, 0x46, 0xe1, 0xac, 0x2f, 0x42, 0x37, 0x66, 0xa2, 0x7f, 0x2b, 0x53, 0xd1,
	0x4b, 0x7f, 0x87, 0x24, 0x1e, 0x50, 0x32, 0x10, 0xeb, 0xd9, 0x19, 0xc7, 0x29, 0x88, 0x5b, 0x3b,
	0xc7, 0xed, 0x72, 0xb2, 0x88, 0x68, 0x78, 0x85, 0x23, 0xd7, 0x2b, 0x22, 0x4e, 0x06, 0x61, 0xb9,
	0xe0, 0xc7, 0x40, 0xb2, 0x20, 0xb9, 0x62, 0x09, 0x91, 0x6a, 0x06, 0x29, 0x96, 0xdc, 0x01, 0x18,
	0xfb, 0xb3, 0x99, 0x1b, 0x8f, 0xfd, 0x28, 0x96, 0x5f, 0xc8, 0xda, 0x39, 0x63, 0x8f, 0x62, 0x6e,
	0xf1, 0x33, 0x37, 0xe6, 0x8f, 0x34, 0x83, 0x26, 0x3b, 0x70, 0xcf, 0x0e, 0x82, 0xd0, 0x7f, 0xe9,
	0xce, 0xec, 0x98, 0x59, 0xdc, 0x73, 0xed, 0x63, 0x66, 0x39, 0x6c, 0x1a, 0xdb, 0xd8, 0x13, 0x95,
	0xe9, 0xdd, 0x0c, 0x60, 0x24, 0xf8, 0x3d, 0xce, 0xe6, 0x71, 0xd7, 0x75, 0xac, 0x88, 0xfd, 0x30,
	0xe7, 0x1e, 0x60, 0xcd, 0x03, 0xc7, 0xe6, 0x62, 0xd6, 0x45, 0x96, 0x72, 0x9d, 0x91, 0xe4, 0x1c,
	0x09, 0x46, 0xfb, 0x15, 0xc0, 0xb9, 0x14, 0x64, 0x1b, 0x6e, 0xf3, 0x3a, 0x9e, 0x45, 0x31, 0x73,
	0x2c, 0xb9, 0xdb, 0x60, 0x1e, 0x47, 0x18, 0xe2, 0xcb, 0x74, 0x23, 0x65, 0xca, 0x9b, 0xc2, 0x79,
	0x1c, 0x91, 0x9f, 0x40, 0xeb, 0xc2, 0x1c, 0x87, 0x4d, 0x19, 0x7f, 0x6d, 0x15, 0xa7, 0xdd, 0x59,
	0x98, 0xd6, 0x13, 0x5c, 0xed, 0x4f, 0x14, 0x80, 0x7d, 0x16, 0x53, 0xc1, 0xcd, 0x34, 0xb6, 0x95,
	0xeb, 0x36, 0xb6, 0xef, 0x27, 0x17, 0x08, 0xc5, 0xab, 0x63, 0xc0, 0x42, 0x97, 0xa1, 0xdc, 0xa4,
	0xcb, 0xc8, 0x35, 0x11, 0xc5, 0x2b, 0x9a, 0x88, 0x52, 0xae, 0x89, 0xf8, 0x18, 0x9a, 0xf6, 0x74,
	0xea, 0xbf, 0xe0, 0x05, 0x0d, 0x0b, 0x43, 0xe6, 0xa0, 0x11, 0x9c, 0xd7, 0xdb, 0xc8, 0xec, 0x49,
	0x9e, 0xf6, 0xe7, 0x0a, 0x34, 0x50, 0x15, 0x51, 0xe0, 0x7b, 0x11, 0x23, 0x5f, 0x42, 0x45, 0x5e,
	0x44, 0x8b, 0x8b, 0xfc, 0xb7, 0x33, 0xb2, 0x66, 0x70, 0xb2, 0x68, 0xa0, 0x12, 0xcc, 0x33, 0x42,
	0xe6, 0x75, 0x97, 0x2b, 0x25, 0x45, 0x91, 0xfb, 0x50, 0x73, 0x3d, 0x4b, 0xb4, 0xd4, 0x95, 0x4c,
	0x58, 0xac, 0xba, 0x1e, 0xd6, 0xb2, 0xed, 0x57, 0x50, 0x11, 0x2f, 0x21, 0x9d, 0x54, 0xa6, 0x8b,
	0xfa, 0xcb, 0xdc, 0x1c, 0xa7, 0xc2, 0xc8, 0xc3, 0x29, 0xbd, 0x2e, 0x40, 0xb7, 0xa0, 0x7a, 0xca,
	0x9b, 0x0f, 0xbc, 0xf4, 0xe3, 0xea, 0x4d, 0x86, 0xda, 0x1f, 0x97, 0x00, 0x0e, 0xe7, 0x4b, 0x0c,
	0xa4, 0x71, 0x5d, 0x03, 0xe9, 0xe4, 0xf4, 0xf8, 0x7a, 0x99, 0x7f, 0x75, 0x43, 0x59, 0xd2, 0x69,
	0x17, 0x6f, 0xda, 0x69, 0xdf, 0x87, 0x6a, 0x1c, 0xce, 0xb9, 0xa3, 0x08, 0x63, 0x4a, 0x5b, 0x5a,
	0x49, 0x25, 0x6f, 0x42, 0x79, 0xe2, 0x87, 0x63, 0x86, 0x8e, 0x95, 0xb2, 0x05, 0xed, 0xc2, 0x65,
	0x52, 0xed, 0xb2, 0xcb, 0x24, 0xde, 0xa0, 0x45, 0xf2, 0x1e, 0x0d, 0x0b, 0x99, 0x7c, 0x83, 0x96,
	0x5c, 0xb1, 0xd1, 0x14, 0x44, 0xbe, 0x81, 0xa6, 0x3d, 0x8f, 0x7d, 0xcb, 0xe5, 0x15, 0xda, 0xd4,
	0x1d, 0x9f, 0x61, 0xd9, 0xdd, 0xcc, 0x7f, 0xaf, 0x4f, 0x0f, 0xaa, 0xd3, 0x9d, 0xc7, 0xbe, 0xe1,
	0x1c, 0x22, 0x72, 0xa7, 0x2a, 0x93, 0x12, 0x5d, 0xb1, 0x33, 0x64, 0xed, 0xc7, 0xb0, 0x92, 0x85,
	0xf1, 0x04, 0x24, 0x81, 0xea, 0x1b, 0x3c, 0x3b, 0x8d, 0x78, 0x6a, 0x1b, 0x98, 0x46, 0xb7, 0xaf,
	0x16, 0xb4, 0x18, 0x1a, 0xb8, 0xbc, 0xf4, 0x8e, 0xeb, 0xba, 0xfd, 0x03, 0x28, 0x61, 0xf8, 0x55,
	0x2e, 0x7c, 0x0f, 0xc1, 0x98, 0x8b, 0xcc, 0xbc, 0xf9, 0x15, 0xb3, 0xe6, 0xf7, 0xdf, 0x05, 0x58,
	0x31, 0xfd, 0xf9, 0xf8, 0xe4, 0xa2, 0x01, 0xc2, 0xaf, 0x3b, 0x42, 0x2d, 0x31, 0x1f, 0xe5, 0xa6,
	0xe6, 0x93, 0x5a, 0x47, 0x71, 0x89, 0x75, 0xdc, 0xf4, 0xcc, 0xb5, 0x2f, 0x60, 0x55, 0x6e, 0x5e,
	0x6a, 0x3d, 0xd1, 0x66, 0xe1, 0x0a, 0x6d, 0x6a, 0xbf, 0x50, 0x60, 0x55, 0xc4, 0xf7, 0xff, 0xbb,
	0xd2, 0x2a, 0x37, 0x0c, 0xeb, 0xe5, 0x1b, 0x5d, 0x1e, 0xfd, 0xbf, 0xf4, 0x34, 0x6d, 0x08, 0xcd,
	0x44, 0x7d, 0x37, 0x50, 0xfb, 0x15, 0x46, 0xfc, 0x8b, 0x02, 0x34, 0x06, 0xec, 0xe5, 0x92, 0x20,
	0x5a, 0xbe, 0xee, 0x71, 0x7c, 0x98, 0x2b, 0x57, 0x1b, 0xdb, 0xeb, 0x59, 0x19, 0xc4, 0xd5, 0x63,
	0x52, 0xc1, 0xa6, 0xb7, 0xa8, 0xca, 0xf2, 0x5b, 0xd4, 0xd2, 0x62, 0xb7, 0x9e, 0xb9, 0xc5, 0x2b,
	0x2e, 0xbb, 0xc5, 0xd3, 0xfe, 0xad, 0x08, 0x0d, 0x6c, 0x90, 0x29, 0x8b, 0xe6, 0xd3, 0x38, 0x27,
	0x4c, 0xe1, 0x6a, 0x61, 0x3a, 0x50, 0x09, 0x71, 0x92, 0x74, 0xa5, 0x4b, 0x83, 0xbf, 0x40, 0x61,
	0x6b, 0xfc, 0xdc, 0x0d, 0x02, 0xe6, 0x58, 0x82, 0x92, 0x14, 0x30, 0x4d, 0x49, 0x16, 0x22, 0x44,
	0xbc, 0xfc, 0x9c, 0xf9, 0x21, 0x4b, 0x51, 0x45, 0xbc, 0x4f, 0x68, 0x70, 0x5a, 0x02, 0xc9, 0xdd,
	0x37, 0x88, 0xca, 0xe0, 0xfc, 0xbe, 0x21, 0xed, 0x35, 0x91, 0x5b, 0x47, 0xae, 0xe8, 0x35, 0x91,
	0xcd, 0xbb, 0xa8, 0x99, 0x3d, 0x9d, 0x5a, 0x7e, 0x10, 0xa1, 0xd3, 0xd4, 0x68, 0x0d, 0x09, 0xc3,
	0x20, 0x22, 0x5f, 0x43, 0x7a, 0x5d, 0x2c, 0x6f, 0xc9, 0xc5, 0x39, 0xb6, 0x2e, 0xbb, 0x58, 0xa0,
	0xab, 0xe3, 0xdc, 0xfd, 0xcf, 0x92, 0x1b, 0xea, 0xca, 0x4d, 0x6f, 0xa8, 0x1f, 0x42, 0x59, 0xc4,
	0xa8, 0xda, 0xeb, 0x62, 0x94, 0xc0, 0x65, 0xed, 0xb3, 0x91, 0xb7, 0xcf, 0x5f, 0x16, 0x80, 0x74,
	0xa7, 0x53, 0x7f, 0x6c, 0xc7, 0xcc, 0x70, 0xa2, 0x8b, 0x66, 0x7a, 0xed, 0xcf, 0x2e, 0x9f, 0x41,
	0x7d, 0xe6, 0x3b, 0x6c, 0x6a, 0x25, 0xdf, 0x94, 0x2e, 0xad, 0x7e, 0x10, 0xc6, 0x5b, 0x52, 0x02,
	0x25, 0xbc, 0xc4, 0x51, 0xb0, 0xee, 0xc0, 0x67, 0xde, 0x84, 0xcd, 0xec, 0x97, 0xb2, 0x14, 0xe1,
	0x8f, 0xa4, 0x03, 0xd5, 0x90, 0x45, 0x2c, 0x3c, 0x65, 0x57, 0x16, 0x55, 0x09, 0x48, 0x7b, 0x06,
	0x1b, 0xb9, 0x1d, 0x49, 0x47, 0xbe, 0x85, 0x5f, 0x2b, 0xc3, 0x58, 0x7e, 0xb4, 0x12, 0x03, 0xfe,
	0x3a, 0xe6, 0x25, 0x9f, 0x41, 0xf9, 0x63, 0xea, 0xf0, 0xc5, 0xab, 0xe2, 0xec, 0x1e, 0xa8, 0x59,
	0x4d, 0xbb, 0x63, 0x0c, 0x36, 0xf2, 0x54, 0x0a, 0xd7, 0x3b, 0x15, 0xed, 0xef, 0x0a, 0xb0, 0xde,
	0x75, 0x1c, 0xf1, 0x77, 0xc3, 0x25, 0xaa, 0x2f, 0x5e, 0x57, 0xf5, 0x0b, 0x81, 0x58, 0x84, 0x89,
	0x6b, 0x05, 0xe2, 0x0f, 0xa1, 0x92, 0xd6, 0x5a, 0xc5, 0x05, 0x77, 0x16, 0x72, 0x51, 0x09, 0xd0,
	0x6e, 0x01, 0xc9, 0x0a, 0x2b, 0xb4, 0xaa, 0xfd, 0x69, 0x11, 0xee, 0xee, 0xb2, 0x63, 0xd7, 0xcb,
	0xbe, 0xe2, 0x57, 0xdf, 0xc9, 0xc5, 0x4f, 0x65, 0x9f, 0xc1, 0xba, 0x28, 0xe4, 0x93, 0x7f, 0x62,
	0x59, 0xec, 0x58, 0x7e, 0x9d, 0x94, 0xb1, 0x6a, 0x0d, 0xf9, 0x07, 0x92, 0xad, 0xe3, 0x7f, 0xc5,
	0x1c, 0x3b, 0xb6, 0x9f, 0xd9, 0x11, 0xb3, 0x5c, 0x47, 0xfe, 0x59, 0x06, 0x12, 0x92, 0xe1, 0x90,
	0x21, 0x94, 0xb8, 0x0d, 0xa2, 0xeb, 0x36, 0xb7, 0xb7, 0x33, 0x62, 0x5d, 0xb2, 0x95, 0xac, 0x02,
	0x0f, 0x7c, 0x87, 0xed, 0x54, 0x8f, 0x06, 0x4f, 0x06, 0xc3, 0xef, 0x06, 0x14, 0x17, 0x22, 0x06,
	0xdc, 0x0a, 0x42, 0x76, 0xea, 0xfa, 0xf3, 0xc8, 0xca, 0x9e, 0x44, 0xf5, 0xca, 0x94, 0xb8, 0x91,
	0xcc, 0xc9, 0x10, 0xb5, 0x9f, 0xc2, 0xda, 0xc2, 0xcb, 0x78, 0x6d, 0x26, 0x5f, 0xa7, 0xbe, 0x41,
	0x56, 0xa1, 0x8e, 0x1f, 0xbb, 0x97, 0x7f, 0xfb, 0xd6, 0xfe, 0xb5, 0x80, 0x57, 0x4c, 0x33, 0x37,
	0xbe, 0x59, 0x06, 0xfb, 0xcd, 0x7c, 0x06, 0x83, 0xed, 0x77, 0xf3, 0xe6, 0x9b, 0x59, 0xb0, 0xf3,
	0xad, 0x00, 0xa6, 0x41, 0xa4, 0x6d, 0x43, 0x55, 0xd2, 0xc8, 0x6f, 0xc1, 0x5a, 0xe8, 0xfb, 0x71,
	0xd2, 0x89, 0x8a, 0x0e, 0xe4, 0xf2, 0x3f, 0xdb, 0xac, 0x72, 0xb0, 0x48, 0x06, 0x4f, 0xf2, 0xbd,
	0x48, 0x59, 0xfc, 0x0d, 0x44, 0x0e, 0x77, 0x1b, 0xbf, 0x5b, 0x4f, 0xff, 0xb7, 0xfb, 0xbf, 0x01,
	0x00, 0x00, 0xff, 0xff, 0x35, 0x9f, 0x30, 0x98, 0xf2, 0x2b, 0x00, 0x00,
}

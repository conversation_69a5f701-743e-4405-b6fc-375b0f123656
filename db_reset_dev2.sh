#!/bin/sh

# ANSIエスケープコードを変数に格納します。
YELLOW='\033[1;33m'
RED='\033[0;31m'
NO_COLOR='\033[0m' # テキストの色をリセットする

# .envファイルを確認し、存在しなければ .env.example からコピー
if [ -f .env ]; then
  echo "${YELLOW}.env file found, using existing file...${NO_COLOR}"
  export $(grep -v '^#' .env | xargs)
else 
  if [ -f .env.example ]; then
    echo "${YELLOW}.env file not found, creating from .env.example...${NO_COLOR}"
    cp .env.example .env
    export $(grep -v '^#' .env | xargs)
  else
    echo "${RED}.env.example file not found. Cannot create .env file. Exiting...${NO_COLOR}"
    exit 1
  fi
fi

# Postgres CLIの存在を確認し、存在しなければインストール
if ! command -v psql &> /dev/null; then
    echo "${YELLOW}Postgres CLI not found, installing...${NO_COLOR}"
    brew install postgresql@16
fi

# 指定されたスキーマを削除する
echo "${YELLOW}Dropping existing schema...${NO_COLOR}"

# /db/init.sqlをPostgreSQLに実行する
echo "${YELLOW}Initializing DB with ./db/dev2/init.sql...${NO_COLOR}"
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/delete_all_table.sql > psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/init.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/init_smartkarte_server.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/init_import_table.sql >> psql_output.txt 2>&1
### admindb_patch.sqlとsuper_admindb_patch.sqlはスマクリのロギング機能を削除するまでの暫定対応
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/admindb_patch.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/super_admindb_patch.sql >> psql_output.txt 2>&1

# この順番で実行
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/master_data_other.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/master_data_event_mst.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/master_data_m_template_mail.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/master_data_portal_m_city.sql >> psql_output.txt 2>&1
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/master_data_portal_m_station.sql >> psql_output.txt 2>&1

echo ""
while true; do
  read -p "smartkarteマスタデータ登録をしますか？数分かかります [y/n] " answer
  case $answer in
    [yY])
      echo "${YELLOW}smartkarteマスタデータ登録します...${NO_COLOR}"
      PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/smartkarte_master_data/copy_command.sql >> psql_output.txt 2>&1
      PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/smartkarte_master_data/payment_method_mst.sql >> psql_output.txt 2>&1
      PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SET search_path TO ${DB_SCHEMA};" -f ./db/dev2/smartkarte_master_data/insert_master_data_hpid.sql >> psql_output.txt 2>&1
      break;;
    [nN])
      break;;
    *)
      echo "y または n を入力してください。";;
  esac
done

# エラーの件数をカウントする
error_count=$(grep -c "ERROR" psql_output.txt)

if [ $error_count -gt 0 ]; then
    echo "${RED}DB initialization encountered errors. Number of errors: $error_count${NO_COLOR}"
    echo "Error details:"
    grep "ERROR" psql_output.txt
else
    echo "\n\n${YELLOW}DB initialization complete with NO errors.${NO_COLOR}"
fi

# 出力ファイルの削除
rm psql_output.txt
